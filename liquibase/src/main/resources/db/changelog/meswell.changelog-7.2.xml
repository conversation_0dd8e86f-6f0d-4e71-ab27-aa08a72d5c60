<?xml version="1.1" encoding="GBK" standalone="no"?>
<databaseChangeLog
	xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
	xmlns:pro="http://www.liquibase.org/xml/ns/pro"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.9.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">
	<changeSet id="WIP-20210817-001-AD_MESSAGE" author="DaiWenBin">
       <sqlFile path="sql/WIP-20210817-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>英译中补充，暂停批次按钮权限补充</comment>
	</changeSet>
	
	<changeSet id="RAS-20210825-001_RASCONSTRAINTMOCK" author="FanChengMing">
       <sqlFile path="sql/RAS-20210825-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>工艺限制定义增加模拟限制功能</comment>
	</changeSet>
	
	<changeSet id="RAS-20210826-001_PRDStepAntipollution_update" author="HeTao">
		<sql>update AD_FIELD t set t.style = 2 where name = 'capability' and table_rrn = (select object_rrn from ad_table a where a.name = 'PRDStepAntipollution')</sql>
      	<comment>修改工步防污染界面设备能力显示不正常的问题</comment>
	</changeSet>
	
	<changeSet id="WIP-********-001_RETICLE-CONTEXT-SYSPARAM" author="HeTao">
		 <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
      	<comment>光刻板是否使用Context管理的系统参数SQL</comment>
	</changeSet>

	<changeSet id="WIP-********-001_LotDeassignMessage" author="WuFaMing">
		 <sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
      	 <comment>解绑载具批次状态校验提示信息</comment>
	</changeSet>

	<changeSet id="WIP-********-001_WIPLotBankIn" author="WuFaMing">
		 <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
      	 <comment>新增批次BankIn界面</comment>
	</changeSet>

	<changeSet id="RAS-********-001-EQP-ADD-COLOUM" author="WuFaMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="SUB_LOCATION"/>
			</not>
		</preConditions>

		<addColumn tableName="RAS_EQP">
			<column name="SUB_LOCATION" remarks="Bay" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>设备表增加SubLocation字段</comment>
	</changeSet>

	<changeSet id="RAS-********-001_RASEqp" author="WuFaMing">
		 <sqlFile path="sql/RAS-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
      	 <comment>设备管理增加bay栏位</comment>
	</changeSet>
	
	<changeSet id="MESWELL-********-LOTHIS_ADD_FIELD" author="Tony">
    	<addColumn tableName="WIP_LOT_HIS">
    		<column name="REWORK_CODE" remarks="返工码" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
		<addColumn tableName="WIP_LOT_HIS">
    		<column name="IS_PILOT" remarks="是否先行批" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
    	<comment>补充缺失栏位</comment>
    </changeSet>
    
	<changeSet id="WIP-********-ADD-COLUMN" author="HeTao">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_PREPARE" columnName="EQUIPMENT_RECIPE"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_LOT_PREPARE">
    		<column name="EQUIPMENT_RECIPE" remarks="设备Recipe" type="VARCHAR2(32 BYTE)"/>
    		<column name="EQUIPMENT_MASK" remarks="设备光刻板" type="VARCHAR2(32 BYTE)"/>
    		<column name="SUB_EQUIPMENT_IDS" remarks="设备Units" type="VARCHAR2(128 BYTE)"/>
    	</addColumn>
    </changeSet>
    
    <changeSet id="WIP-20210909-001_ProcessStateMessage" author="HeTao">
		 <sqlFile path="sql/WIP-20210909-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
      	<comment>Process State校验提示信息</comment>
	</changeSet>
	
	<changeSet id="WIP-20210909-002-ADD-FIELD" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION" columnName="IS_HOLD_PROCESS_EQP"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_ACTION">
    		<column name="IS_HOLD_PROCESS_EQP" remarks="是否暂停加工设备" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20210909-002_PARTSTEP_FIELDS_CHANGE" author="FanChengming">
		 <sqlFile path="sql/WIP-20210909-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
      	<comment>产品工步增加字段,载具任务添加手动完成按钮</comment>
	</changeSet>
	
	<changeSet id="WIP-20210910-001_RUN-BY-LOT" author="HeTao">
		 <sqlFile path="sql/WIP-20210910-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
      	<comment>按批次作业增加ComponentList显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20210910-001-ADD-FIELD" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="CONTROL_QTY_TYPE"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="RAS_EQP">
    		<column name="CONTROL_QTY_TYPE" remarks="数量卡控类型" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20210910-001-RAS-EQUIPMENT_CHANGE" author="DaiWenBin">
		 <sqlFile path="sql/WIP-20210911-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
      	<comment>进站数量卡控类型sql</comment>
	</changeSet>

	<changeSet id="WIP-20210916-001-MESSAGE" author="HeTao">
		<sqlFile path="sql/WIP-20210916-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充Message</comment>
	</changeSet>
	
	<changeSet id="WIP-20210916-002-WORKORDERBOM" author="ZhouGeLong">
		<sqlFile path="sql/WIP-20210916-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单管理字段修改，及工单下tab工单bom显示</comment>
	</changeSet>

	<changeSet id="WIP-20210917-SORT-ADD-FIELD1" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_SORTING_JOB" columnName="TO_PORT_ID"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_LOT_SORTING_JOB">
    		<column name="TO_PORT_ID" remarks="目标Port" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20210917-SORT-ADD-FIELD2" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_SORTING_JOB" columnName="TO_DURABLE_ID"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_LOT_SORTING_JOB">
    		<column name="TO_DURABLE_ID" remarks="目标载具" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20210917-SORT-ADD-TABLE" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_SORTING_JOB_HIS"/>
			</not>
		</preConditions>
		
    	<createTable remarks="SortingJob历史表" tableName="WIP_LOT_SORTING_JOB_HIS" tablespace="TS_MES_HIS_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_SORTING_JOB_HIS" primaryKeyTablespace="TS_MES_HIS_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TIME" remarks="创建时间" type="date"/>
			<column name="HIS_SEQ" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
		    <column name="HIS_SEQ_NO" remarks="事务序列号" type="NUMBER(3)"/>

			<column name="BATCH_ID" remarks="任务Batch号" type="VARCHAR2(32 BYTE)"/>
			<column name="JOB_SEQ_NO" remarks="任务序列号" type="NUMBER(3)"/>
			
			<column name="JOB_TYPE" remarks="任务类型" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_TYPE" remarks="动作类型" type="VARCHAR2(32 BYTE)"/>
			<column name="SORTING_MODE" remarks="Sort模式" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_RRN" remarks="LotRrn" type="NUMBER(19, 0)"/>
			<column name="PRIORITY" remarks="优先级" type="NUMBER(3)"/>
			<column name="DURABLE_ID" remarks="载具号" type="VARCHAR2(32 BYTE)"/>
			<column name="PART_NAME" remarks="产品号" type="VARCHAR2(32 BYTE)"/>
			<column name="MANUFACTURE_TYPE" remarks="生产类型" type="VARCHAR2(32 BYTE)"/>
			<column name="STEP_NAME" remarks="工步名" type="VARCHAR2(32 BYTE)"/>
			<column name="JOB_STATE" remarks="任务状态号" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
			<column name="FROM_PORT_ID" remarks="源Port号" type="VARCHAR2(32 BYTE)"/>
			<column name="TO_PORT_ID" remarks="目标Port号" type="VARCHAR2(32 BYTE)"/>
			<column name="TO_DURABLE_ID" remarks="目标载具号" type="VARCHAR2(32 BYTE)"/>
			<column name="SORTING_TYPE" remarks="Sort类型" type="VARCHAR2(32 BYTE)"/>
			<column name="PORT_IDS" remarks="PortIds" type="VARCHAR2(256 BYTE)"/>
			<column name="PORT_USE_TYPES" remarks="Port用途类型" type="VARCHAR2(256 BYTE)"/>
			<column name="COMMNENTS" remarks="备注" type="VARCHAR2(1024 BYTE)"/>
	    </createTable>	
	    
	    <createIndex indexName="IDX_WIP_LOT_SORTING_JOB_HIS1" tableName="WIP_LOT_SORTING_JOB_HIS" tablespace="TS_MES_HIS_IDX">
			<column name="BATCH_ID"/>
			<column name="JOB_SEQ_NO"/>
		</createIndex>
	</changeSet>
	
	<changeSet id="WIP-20210917-SORT-ADD-FIELD3" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_SORTING_JOB" columnName="SORTING_MODE"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_LOT_SORTING_JOB">
    		<column name="SORTING_MODE" remarks="Sorting方式" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20210917-SORT-ADD-FIELD4" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_SORTING_JOB" columnName="LOT_ID"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_LOT_SORTING_JOB">
    		<column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20210918-001-PARTSTEPADDFIELD" author="FanChengMing">
		<sqlFile path="sql/WIP-20210918-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>产品流程增加污染级别显示字段</comment>
	</changeSet>
	<changeSet id="WIP-20210918-002-RECIPE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20210918-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业添加recipe名称显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20210918-003-MERGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20210918-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>合批规则添加，英译中修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20210918-WOSTARTBYLOT" author="ZhouGeLong">	
        <sqlFile path="sql/WIP-20210918-004.sql" encoding="GBK" relativeToChangelogFile="true"/>
        <comment>工单批次投料</comment>
    </changeSet>
    
    <changeSet id="WIP-20210918-MESSAGE" author="ZhouGeLong">	
        <sqlFile path="sql/WIP-20210918-005.sql" encoding="GBK" relativeToChangelogFile="true"/>
        <comment>英译中</comment>
    </changeSet>
    
    <changeSet id="RAS-20210922-ADFIELD" author="FanChengMing">	
        <sqlFile path="sql/RAS-20210922-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
        <comment>补充工艺限制模拟缺失的adfield字段</comment>
    </changeSet>
    
    <changeSet id="WIP-20210923-001-Field-MESSAGE" author="HeTao">
		<sqlFile path="sql/WIP-20210923-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充Message，补充动态栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20210923-002-UPDATE-FIELD" author="HeTao">
		<sqlFile path="sql/WIP-20210923-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改错误的栏位Label，重新调整批次事物历史栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20210926-001-UPDATE-MESSAGE" author="HeTao">
		<sql>update AD_MESSAGE m set m.MESSAGE = 'Work order BOM is not exist!', MESSAGE_ZH = '工单BOM不存在!' where m.key_id = 'pp.workorder_bom_is_not_exist'</sql>
		<comment>修改错误的message提示</comment>
	</changeSet>

	<changeSet id="WIP-20210927-001-SETUP-SORTINGJOB-CARRIER" author="Clark">
		<sqlFile path="sql/WIP-20210927-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设置或修改SortingJob目标载具或位置号</comment>
	</changeSet>
	
	<changeSet id="WIP-20210929-001-TRACKOUT-ASSIGNCARRIER" author="Clark">
		<sqlFile path="sql/WIP-20210929-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>出站分配目标载具或位置号</comment>
	</changeSet>
	
	<changeSet id="RAS-20210929-001-DEL-PORT-ISDISPATCH" author="HeTao">
		<sql>delete from AD_FIELD t where t.name = 'isDispatch' and t.table_rrn = 40194</sql>
		<comment>删除Port界面中已移除的字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20210929-002-EQP-ADD-COLOUM" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="CATEGORY"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="RAS_EQP">
    		<column name="CATEGORY" remarks="设备形态" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<comment>设备表增加Category字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20210929-003-UPDATE-RUN-BY-LOT-FIELD" author="HeTao">
		<sqlFile path="sql/WIP-20210929-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>更新按批次作业界面栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20210929-CREATE-WIP_LOT_EQUIPMENT_BUFFER" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_EQUIPMENT_BUFFER"/>
			</not>
		</preConditions>
		
    	<createTable remarks="Buffer中Lot、Wafer、载具信息表" tableName="WIP_LOT_EQUIPMENT_BUFFER" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_EQUIPMENT_BUFFER" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  

			<column name="BUFFER_ID" remarks="Buffer号" type="VARCHAR2(32 BYTE)"/>
			<column name="MAIN_EQUIPMENT_ID" remarks="主设备号" type="VARCHAR2(32 BYTE)"/>
			
			<column name="DURABLE_ID" remarks="载具号" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
			<column name="COMPONENT_ID" remarks="Wafer ID" type="VARCHAR2(32 BYTE)"/>
			<column name="POSITION" remarks="位置" type="VARCHAR2(32 BYTE)"/>
	    </createTable>	
	    
	    <createIndex indexName="IDX_WIP_LOT_EQUIPMENT_BUFFER_ID" tableName="WIP_LOT_EQUIPMENT_BUFFER" tablespace="TS_MES_IDX">
			<column name="BUFFER_ID"/>
		</createIndex>
	</changeSet>
	
	<changeSet id="WIP-20211008-DROP-WIP_LOT_EQUIPMENT_BUFFER" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_EQUIPMENT_BUFFER"/>
			</not>
		</preConditions>
		
		<dropTable cascadeConstraints="true"  tableName="WIP_LOT_EQUIPMENT_BUFFER"/>  
	</changeSet>
	
	<changeSet id="WIP-20211008-CREATE-WIP_LOT_EQUIPMENT_UNIT" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_EQUIPMENT_UNIT"/>
			</not>
		</preConditions>
		
    	<createTable remarks="Unit中Lot、Wafer、载具信息表" tableName="WIP_LOT_EQUIPMENT_UNIT" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_EQUIPMENT_UNIT" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  

			<column name="UNIT_ID" remarks="Unit号" type="VARCHAR2(32 BYTE)"/>
			<column name="MAIN_EQUIPMENT_ID" remarks="主设备号" type="VARCHAR2(32 BYTE)"/>
			
			<column name="DURABLE_ID" remarks="载具号" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
			<column name="COMPONENT_ID" remarks="片号" type="VARCHAR2(32 BYTE)"/>
			<column name="POSITION" remarks="位置" type="VARCHAR2(32 BYTE)"/>
			<column name="UNIT_IN_TIME" remarks="仅Unit时间" type="date"/>
	    </createTable>	
	    
	    <createIndex indexName="IDX_WIP_LOT_EQUIPMENT_UNIT_ID" tableName="WIP_LOT_EQUIPMENT_UNIT" tablespace="TS_MES_IDX">
			<column name="UNIT_ID"/>
		</createIndex>
	</changeSet>
	
	<changeSet id="WIP-20211008-WIP_LOT_EQUIPMENT_UNIT_HIS1" author="Tony">
    	<preConditions onFail="MARK_RAN">
			<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT_HIS" columnName="LOT_RRN"/>
		</preConditions> 
    	
    	<dropColumn tableName="WIP_LOT_EQUIPMENT_UNIT_HIS">  
        	<column name="LOT_RRN"/>  
        	<column name="COMPONENT_RRN"/>  
        	<column name="CHAMBER_IN_TIME"/>  
        	<column name="CHAMBER_OUT_TIME"/>  
        	<column name="JUDGE1"/>  
        	<column name="WO_ID"/>  
        	<column name="OWNER"/>  
    	</dropColumn>  
	</changeSet>
	
	<changeSet id="WIP-20211008-WIP_LOT_EQUIPMENT_UNIT_HIS2" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT_HIS" columnName="UNIT_ID"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT_EQUIPMENT_UNIT_HIS">  
        	<column name="UNIT_ID" remarks="Unit号" type="VARCHAR2(32 BYTE)"/>
        	<column name="DURABLE_ID" remarks="载具号" type="VARCHAR2(32 BYTE)"/>
        	<column name="POSITION" remarks="位置" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20211008-001-ADMESSAGE" author="Tony">
		<sqlFile path="sql/WIP-20211008-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加LotEquipemntUnit异常提示</comment>
	</changeSet>
	
	<changeSet id="WIP-20211008-002-ADMESSAGE" author="Tony">
		<sqlFile path="sql/WIP-20211008-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加FutureTimer异常提示</comment>
	</changeSet>
	
	<changeSet id="WIP-20211008-WIP_FUTURE_ACTION" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION" columnName="ACTION_PATH"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_ACTION">
    		<column name="ACTION_PATH" remarks="timerAction动作路径" type="VARCHAR2(256 BYTE)"/>
    		<column name="ACTION_STEP_NAME" remarks="timerAction工步名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_STATE_NAME" remarks="timerAction工步节点名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_PLACEMENT" remarks="timerAction动作位置" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20211008-WIP_FUTURE_ACTION_HIS" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION_HIS" columnName="ACTION_PATH"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_ACTION_HIS">
    		<column name="ACTION_PATH" remarks="timerAction动作路径" type="VARCHAR2(256 BYTE)"/>
    		<column name="ACTION_STEP_NAME" remarks="timerAction工步名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_STATE_NAME" remarks="timerAction工步节点名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_PLACEMENT" remarks="timerAction动作位置" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20211008-WIP_FUTURE_TIMER_INSTANCE" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE" columnName="TIMER_ACTION"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE">
    		<column name="TIMER_ACTION" remarks="timerAction动作类型" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_PATH" remarks="timerAction动作路径" type="VARCHAR2(256 BYTE)"/>
    		<column name="ACTION_STEP_NAME" remarks="timerAction工步名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_STATE_NAME" remarks="timerAction工步节点名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="REWORK_CODE" remarks="返工码" type="VARCHAR2(32 BYTE)"/>
    		<column name="REWORK_PROCEDURE" remarks="返工流程" type="VARCHAR2(32 BYTE)"/>
    		<column name="REWORK_PROCEDURE_VERSION" remarks="返工流程版本" type="NUMBER(19, 0)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20211008-WIP_FUTURE_TIMER_INSTANCE_HIS" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE_HIS" columnName="TIMER_ACTION"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE_HIS">
    		<column name="TIMER_ACTION" remarks="timerAction动作类型" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_PATH" remarks="timerAction动作路径" type="VARCHAR2(256 BYTE)"/>
    		<column name="ACTION_STEP_NAME" remarks="timerAction工步名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_STATE_NAME" remarks="timerAction工步节点名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="REWORK_CODE" remarks="返工码" type="VARCHAR2(32 BYTE)"/>
    		<column name="REWORK_PROCEDURE" remarks="返工流程" type="VARCHAR2(32 BYTE)"/>
    		<column name="REWORK_PROCEDURE_VERSION" remarks="返工流程版本" type="NUMBER(19, 0)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="***********-001-CARRIERGLC" author="ZhouGeLong">
		<sqlFile path="sql/***********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具批量操作Glc</comment>
	</changeSet>
	
	<changeSet id="WIP-20211009-001-FORWARDLOT_GLC" author="FanChengMing">
		<sqlFile path="sql/WIP-20211009-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>未来到达批次Glc页面</comment>
	</changeSet>
	
	<changeSet id="***********-001-CARRIER-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/***********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具批量操作清洗功能AD_MESSAGE</comment>
	</changeSet>
	
	<changeSet id="WIP-20211012-001-QTIME_ADD_TIMER_ACTION" author="FanChengMing">
		<sqlFile path="sql/WIP-20211012-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>QTime增加timerAction设置</comment>
	</changeSet>
	
	<changeSet id="WIP-20211012-002-LOT-EQUIPMENT-RECIPE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211012-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业界面和进站选择设备添加PPID显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20211012-003-BY-Location" author="HeTao">
		<sqlFile path="sql/WIP-20211012-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按区域作业功能</comment>
	</changeSet>

	<changeSet id="WIP-20211012-004-FORWARDLOT_ADDFIELD" author="FanChengMing">
		<sqlFile path="sql/WIP-20211012-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>未来到达批次页面未来工步字段增加</comment>
	</changeSet>
	
	<changeSet id="WIP-20211012-005-LOT-QTIME-QXPIRE-TIME" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211012-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业Qtime超出时间格式转换</comment>
	</changeSet>
	
	<changeSet id="WIP-20211013-001——Buffer—Carrier" author="Clark">
		<sqlFile path="sql/WIP-20211013-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备BufferCarrier</comment>
	</changeSet>
	<changeSet id="WIP-20211012-004-BY-EQP-UPDATE" author="HeTao">
		<sqlFile path="sql/WIP-20211012-006.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改按设作业功能菜单英文描述</comment>
	</changeSet>
	
	<changeSet id="WIP-20211013-002-LOT-BY-LOCATON" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211013-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按区域作业区域设备选择颜色sql</comment>
	</changeSet>
	
	<changeSet id="WIP-20211013-003-LOCATION-EQP-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211013-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按区域作业区域设备按钮英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20211013-004-LOCATION-AUTOSIZE" author="HeTao">
		<sqlFile path="sql/WIP-20211013-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>一些界面AutoSize处理</comment>
	</changeSet>
	
	<changeSet id="MM-20211014-001-MULIT-CARRIER-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/MM-20211014-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具批量操作AVL事件添加限制，英译中完善。</comment>
	</changeSet>
	
	<changeSet id="MM-20211014-002-DURABLE-SPLIT-FUTERSTEP" author="DaiWenBin">
		<sqlFile path="sql/MM-20211014-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具分批未来合批工步弹框</comment>
	</changeSet>
	
	<changeSet id="MM-20211014-003-MULIT-CARRIER-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/MM-20211014-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具批量操作字段修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211014-001-RECOMMEND-RECTILE-ID" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211014-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Manual作业选择设备添加推荐光刻板提示</comment>
	</changeSet>
	
	<changeSet id="RAS-20211014-001-MESSAGE" author="zhougelong">
		<sqlFile path="sql/RAS-20211014-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>端口导入message英译中</comment>
	</changeSet>
	
	<changeSet id="BAS-20211015-001-EDCITEM-BATCH" author="DaiWenBin">
		<sqlFile path="sql/MM-20211014-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>数据采集项集改为批量操作</comment>
	</changeSet>
	
	<changeSet id="RAS-20211015-001-EQUIPMENT-BATCH" author="DaiWenBin">
		<sqlFile path="sql/RAS-20211015-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备界面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20211015-WIP_FUTURE_TIMER_INSTANCE" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE" columnName="ACTION_PATH"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE">
    		<column name="ACTION_PATH" remarks="timerAction动作路径" type="VARCHAR2(256 BYTE)"/>
    		<column name="ACTION_STEP_NAME" remarks="timerAction工步名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_PLACEMENT" remarks="timerAction工步定位" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_STATE_NAME" remarks="timerAction工步节点名称" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20211015-WIP_FUTURE_TIMER_INSTANCE_HIS" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE_HIS" columnName="ACTION_PATH"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE_HIS">
    		<column name="ACTION_PATH" remarks="timerAction动作路径" type="VARCHAR2(256 BYTE)"/>
    		<column name="ACTION_STEP_NAME" remarks="timerAction工步名称" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_PLACEMENT" remarks="timerAction工步定位" type="VARCHAR2(32 BYTE)"/>
    		<column name="ACTION_STEP_STATE_NAME" remarks="timerAction工步节点名称" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20211015-001-WOSTARBYLOT" author="zhougelong">
		<sqlFile path="sql/WIP-20211015-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>选组件批次工单投料英译中</comment>
	</changeSet>

	<changeSet id="WIP-20211015-WIP_LOT_RESERVED" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_RESERVED" columnName="IS_FORCE"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_LOT_RESERVED">
    		<column name="IS_FORCE" remarks="强制指定设备作业" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
	</changeSet>
	
    <changeSet id="WIP-20211018-001-MESSAGEUPADTE" author="zhougelong">
		<sqlFile path="sql/WIP-20211018-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>特殊返工、分批返工及防污染英译中修改</comment>
	</changeSet>
	
	 <changeSet id="BAS-20211018-001-BATCH-ID=RULE" author="tangjiacheng">
		<sqlFile path="sql/BAS-20211018-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Batch ID默认生成规则</comment>
	</changeSet>
	
	<changeSet id="MM-20211018-001-PARTS-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/MM-20211018-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>零部件入库调整和batchId生成规则事务类型添加</comment>
	</changeSet>
	
	<changeSet id="AUTO-20211018-001-ACCESS-STATE" author="LiTao">
		<sqlFile path="sql/AUTO-20211018-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>增加设备端口搬运状态</comment>
	</changeSet>
	
	 <changeSet id="WIP-20211018-002-LOT-HOLD-ADD-RULE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211018-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次暂停DISP状态校验添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20211018-002-CREATE-WIP_LOT_TECN" author="HeTao">
		<createTable remarks="批次临时工程变更" tableName="WIP_LOT_TECN" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_TECN"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />
	
			<column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)" />
			<column name="DESCRIPTION" remarks="描述" type="VARCHAR2(64 BYTE)" />
			<column name="VERSION" remarks="版本号" type="NUMBER(19, 0)" />
			<column name="STATUS" remarks="状态" type="VARCHAR2(32 BYTE)" />
			<column name="ACTIVE_TIME" remarks="激活时间" type="date" />
			<column name="ACTIVE_USER" remarks="激活人" type="VARCHAR2(32 BYTE)" />
			
			<column name="TENANT_ID" remarks="租户ID" type="VARCHAR2(32 BYTE)" />
			<column name="ECN_TYPE" remarks="变更单类型" type="VARCHAR2(32 BYTE)" />
			<column name="ECN_SOURCE" remarks="变更源" type="VARCHAR2(32 BYTE)" />
			<column name="ECN_SOURCE_NAME" remarks="变更源名称" type="VARCHAR2(32 BYTE)" />
			<column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)" />
			<column name="STEP_NAME" remarks="工步号" type="VARCHAR2(32 BYTE)" />
			<column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)" />
			<column name="CONTEXT_RRN" remarks="Context RRN" type="NUMBER(19, 0)" />
			<column name="CONTEXT_VALUE_RRN" remarks="Context Value RRN" type="NUMBER(19, 0)" />
			<column name="OWNER" remarks="责任人" type="VARCHAR2(32 BYTE)" />
		</createTable>	
		<comment>批次临时工程变更表</comment>
	</changeSet>
	
	 <changeSet id="MM-20211018-002-PARTS-INFO-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/MM-20211018-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>零部件信息更新数据报错问题</comment>
	</changeSet>
	
	<changeSet id="WIP-20211018-003-ALL-FIELD-DESC-ENGLISH-UPDATE" author="tangjiacheng">
	<sql>DELETE DATABASECHANGELOG WHERE ID = 'WIP-20211014-002-ALL-FIELD-DESC-ENGLISH-UPDATE'</sql>
	<sql>DELETE DATABASECHANGELOG WHERE ID = 'WIP-20211015-002-ALL-FIELD-DESC-ENGLISH-UPDATE'</sql>
		<sqlFile path="sql/WIP-20211018-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>AD_AUTHORITY表和AD_FIELD表英文描述更正</comment>
	</changeSet>

	<changeSet id="PRD-20211017-001-MULTIQTIME-CHANGE" author="FanChengMing">
		<sqlFile path="sql/PRD-20211017-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>多工步和跨流程QTime页面调整</comment>
	</changeSet>
	
	<changeSet id="AUTO-20211019-001-CARRIER-DISPATCH" author="LiTao">
		<sqlFile path="sql/AUTO-20211019-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>载具增加是否派工</comment>
	</changeSet>
	
	<changeSet id="WIP-20211015-003-WORKORDE-LOTSTART" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211015-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>选组件批次工单投料英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20211019-001-CONTAMINATION-SQL" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211019-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>防污染按钮名称修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211019-002-ALL-FIELD-DESC-ENGLISH-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211019-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>AD_AUTHORITY表和AD_FIELD表英文描述更正</comment>
	</changeSet>
	
	<changeSet id="RAS-20211019-001-ADD_RAS_EQP_COLUMN" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="CURRENT_PROCESS_MODE"/>
			</not>
		</preConditions>
		
		<addColumn tableName="RAS_EQP">
			<column name="CURRENT_PROCESS_MODE" remarks="当前作业模式" type="VARCHAR2(32 BYTE)" />
		</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20211019-003-LOTTECNGLC" author="FanChengMing">
		<sqlFile path="sql/WIP-20211019-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次临时工程变更</comment>
	</changeSet>
	
	<changeSet id="WIP-20211019-004-ABORT-LOT-BATCH-ID" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211019-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次Abort不清除当前batchID</comment>
	</changeSet>
	
	<changeSet id="WIP-20211019-005-CREATE-WIP_LOT_TECN_HIS" author="FanChengMing">
	    <preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_LOT_TECN_HIS"/>
			</not>
		</preConditions>
		<createTable remarks="批次临时工程变更历史" tableName="WIP_LOT_TECN_HIS" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_TECN_HIS"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TIME" remarks="创建时间" type="date"/>
			<column name="HISTORY_SEQ" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
		    <column name="HISTORY_SEQ_NO" remarks="事务序列号" type="NUMBER(19, 0)"/>
			<column name="LOT_TECN_RRN" remarks="TECN主键" type="NUMBER(19, 0)"/>
			<column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)" />
			<column name="DESCRIPTION" remarks="描述" type="VARCHAR2(64 BYTE)" />
			<column name="VERSION" remarks="版本号" type="NUMBER(19, 0)" />
			<column name="STATUS" remarks="状态" type="VARCHAR2(32 BYTE)" />
			
			<column name="TENANT_ID" remarks="租户ID" type="VARCHAR2(32 BYTE)" />
			<column name="ECN_TYPE" remarks="变更单类型" type="VARCHAR2(32 BYTE)" />
			<column name="ECN_SOURCE" remarks="变更源" type="VARCHAR2(32 BYTE)" />
			<column name="ECN_SOURCE_NAME" remarks="变更源名称" type="VARCHAR2(32 BYTE)" />
			<column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)" />
			<column name="STEP_NAME" remarks="工步号" type="VARCHAR2(32 BYTE)" />
			<column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)" />
			<column name="CONTEXT_RRN" remarks="Context RRN" type="NUMBER(19, 0)" />
			<column name="CONTEXT_VALUE_RRN" remarks="Context Value RRN" type="NUMBER(19, 0)" />
			<column name="OWNER" remarks="责任人" type="VARCHAR2(32 BYTE)" />
			
			<column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)" />
			<column name="RECIPE_NAME" remarks="流程菜单" type="VARCHAR2(32 BYTE)" />
			<column name="RETICLE_NAME" remarks="光刻版号" type="VARCHAR2(32 BYTE)" />
			<column name="EDC_NAME" remarks="数据收集" type="VARCHAR2(32 BYTE)" />
		</createTable>	
		<comment>批次临时工程变更表</comment>
	</changeSet>
	
	<changeSet id="WIP-20211019-006-CREATE-WIP_LOT_RESERVED_HIS" author="FanChengMing">
	    <preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_LOT_RESERVED_HIS"/>
			</not>
		</preConditions>
		<createTable remarks="批次预留历史" tableName="WIP_LOT_RESERVED_HIS" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_RESERVED_HIS"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TIME" remarks="创建时间" type="date"/>
			<column name="HISTORY_SEQ" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
		    <column name="HISTORY_SEQ_NO" remarks="事务序列号" type="NUMBER(19, 0)"/>
			<column name="LOT_RESERVED_RRN" remarks="批次预留主键" type="NUMBER(19, 0)"/>
			<column name="STATUS" remarks="状态" type="VARCHAR2(32 BYTE)" />
			<column name="RESERVED_SOURCE" remarks="预留来源" type="VARCHAR2(32 BYTE)" />
			<column name="RESERVED_SOURCE_NAME" remarks="预留来源名称" type="VARCHAR2(32 BYTE)" />
			<column name="RESERVED_TYPE" remarks="预留类型" type="VARCHAR2(32 BYTE)" />
			<column name="CHECK_FLAG" remarks="检查标记ONLY/REJECT" type="VARCHAR2(32 BYTE)" />
			<column name="BATCH_ID" remarks="批量ID" type="VARCHAR2(32 BYTE)" />
			<column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)" />
			<column name="WO_ID" remarks="工单号" type="VARCHAR2(32 BYTE)" />
			<column name="SEQ_NO" remarks="序号" type="VARCHAR2(32 BYTE)" />
			<column name="STEP_NAME" remarks="工步号" type="VARCHAR2(32 BYTE)" />
			<column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)" />
			<column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)" />
			<column name="RECIPE_NAME" remarks="流程菜单" type="VARCHAR2(32 BYTE)" />
			<column name="MASK" remarks="光刻版号" type="VARCHAR2(32 BYTE)" />
			<column name="IS_FORCE" remarks="强制指定设备作业" type="VARCHAR2(1 BYTE)" />
		</createTable>	
		<comment>批次预留历史</comment>
	</changeSet>  
	<changeSet id="RAS-20211019-001_RELEASEEQPCONTEXT" author="zhougelong">
       <sqlFile path="sql/RAS-20211019-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>工艺限制定义增加模拟限制功能</comment>
	</changeSet>
	
	<changeSet id="RAS-20211019-007_LOTTECNFIELDCHANGE" author="FanChengMing">
       <sqlFile path="sql/WIP-20211019-005.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>修改lottecn的一些栏位</comment>
	</changeSet>
	
	<changeSet id="RAS-20211021-001_CAPSHOWEQPLIST" author="LiuTao">
       <sqlFile path="sql/RAS-20211020-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>设备能力增加设备显示功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20211020-001_WIPTABLEINDEX" author="liTao">
       <sqlFile path="sql/WIP-20211020-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>调整在制品模块下相关页面表格栏位排序</comment>
	</changeSet>
	
	<changeSet id="WIP-20211020-002_CREATELOTTECNID" author="FanChengMing">
       <sqlFile path="sql/WIP-20211020-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>增加LotTecn id生成规则</comment>
	</changeSet>
	
	<changeSet id="WIP-20211019-006_BATCHRULE" author="zhougelong">
       <sqlFile path="sql/WIP-20211019-006.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Batch规则新增勾选</comment>
    </changeSet>
 
    <changeSet id="WIP-20211019-007_MESSAGE" author="zhougelong">
       <sqlFile path="sql/WIP-20211019-007.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>修改英译中</comment>
    </changeSet> 
    <changeSet id="WIP-20211020-006-SKIP_BACKUP-HOLD" author="zoubinjie">
		<sqlFile path="sql/WIP-20211019-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次跳步和退步时，批次需要hold</comment>
	</changeSet>
	
	<changeSet id="MM-20211020-001--AD-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/MM-20211020-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>一些MESSAGE提交</comment>
	</changeSet>
	
	<changeSet id="WIP-20211021-001--AD-FIELD" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211021-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>一些字段修改提交</comment>
	</changeSet>
	
	<changeSet id="WIP-20211021-002_ADDSVG" author="FanChengMing">
       <sqlFile path="sql/WIP-20211021-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>增加svg是否显示系统参数</comment>
	</changeSet>
	
	<changeSet id="WIP-20211021-003--AD-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211021-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Batch组批message</comment>
	</changeSet>
	
	<changeSet id="WIP-20211022-WIP_FUTURE_TIMER_INSTANCE" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE" columnName="SUSPENDED_TIME"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE">
    		<column name="SUSPENDED_TIME" remarks="暂停时间" type="date"/>
    	</addColumn>
	</changeSet>
	
	
	<changeSet id="MM-20211021-001_MESSAGEANDTABLE" author="zhougelong">
		<sqlFile path="sql/MM-20211021-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中调整及动态表新增调整</comment>
	</changeSet>
		
	<changeSet id="UPDATE-QTIME-COM-TASK" author="tangjiacheng">
		<sql>UPDATE COM_TASK SET IS_PAUSED = 'N' WHERE NAME = 'QTimer' </sql>
	</changeSet>
	
	<changeSet id="WIP-20211022-001_MESSAGEANDTABLE" author="zhougelong">
		<sqlFile path="sql/WIP-20211022-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中调整及警报参考表表调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20211025-001-WIP-LOT-QUERY-AD-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211025-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>在制品查询新增message</comment>
	</changeSet>
	
	<changeSet id="WIP-20211025-002_SPLITRECOVERYSORTING" author="FanChengMing">
		<sqlFile path="sql/WIP-20211025-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加分批返工(Sorter)页面</comment>
	</changeSet>
	
	<changeSet id="RAS-20211025-001-EQP-UPLOAD-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/RAS-20211025-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>在制品查询新增message</comment>
	</changeSet>
	
	<changeSet id="WIP-20211026-001-MERGE-RULE-ADD" author="HeTao">
		<sqlFile path="sql/WIP-20211026-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>新增两个MergeRule</comment>
	</changeSet>
	
	<changeSet id="WIP-20211026-WIP_LOT_EQUIPMENT_UNIT" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT" columnName="POSITION_TYPE"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT_EQUIPMENT_UNIT">  
        	<column name="POSITION_TYPE" remarks="位置类型" type="VARCHAR2(32 BYTE)"/>
        	<column name="LOT_ID_LIST" remarks="批号列表" type="VARCHAR2(256 BYTE)"/>
        	<column name="CAPACITY" remarks="容量" type="NUMBER(19, 0)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20211026-WIP_LOT_PREPARE" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT_PREPARE" columnName="JOB_STATE"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT_PREPARE">  
        	<column name="JOB_STATE" remarks="任务状态" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="BAS-20211027-001-EDCITEM-BATCH" author="DaiWenBin">
		<sqlFile path="sql/BAS-20211027-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>数据采集项集sql补充，英译中补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20211027-001-COMPOENT-ALIAS" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="MM_COMPONENTUNIT" columnName="MCOMPONENT_ALIAS"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_COMPONENTUNIT">  
        	<column name="MCOMPONENT_ALIAS" remarks="组件别名" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20211027-001-COMPOENT-HIS-ALIAS" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="MM_COMPONENTUNIT_HIS" columnName="MCOMPONENT_ALIAS"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_COMPONENTUNIT_HIS">  
        	<column name="MCOMPONENT_ALIAS" remarks="组件别名" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20211027-001-WOLOTSTART" author="Clark">
		<sqlFile path="sql/WIP-20211027-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单投料增加SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20211027-002-ACTIONCHANGE" author="Clark">
		<sqlFile path="sql/WIP-20211027-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>所有暂停栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211027-003-BYEQP" author="tangjicheng">
		<sqlFile path="sql/WIP-20211027-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按设备作业字段描述更正</comment>
	</changeSet>
	
	<changeSet id="WIP-20211027-004-IDGENERATE" author="FanChengMing">
		<sqlFile path="sql/WIP-20211027-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>idgenerate增加PARAMETERLIST字段</comment>
	</changeSet>
	
	<changeSet id="MM-********-001-DURABLECLEAN" author="zhougelong">
		<sqlFile path="sql/MM-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具清洗弹框</comment>
	</changeSet>
	
	<changeSet id="WIP-********-001-ALL-FIELD-DESC-ENGLISH-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>AD_AUTHORITY表和AD_FIELD表英文描述更正</comment>
	</changeSet>
	
	<changeSet id="WIP-********-002-QTIME-ALL" author="tangjiacheng">
		<sqlFile path="sql/WIP-********-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>QTIME功能整合</comment>
	</changeSet>
	
	<changeSet id="WIP-20211101-001-BYEQP-PPID" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211101-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按设备作业PPID显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20211101-002-BYEQP-BUFFER" author="HeTao">
		<sqlFile path="sql/WIP-20211101-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>by Eqp Buffer管理功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20211101-003-STEP-CONTAMINATIONLEVEL" author="zhougelong">
		<sqlFile path="sql/WIP-20211101-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步导入栏位增加污染级别</comment>
	</changeSet>
	
	<changeSet id="WIP-20211102-001-LOTTECN-HIS-EQUIPMENTRECIPENAME" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT_TECN_HIS" columnName="EQUIPMENT_RECIPE_NAME"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT_TECN_HIS">  
        	<column name="EQUIPMENT_RECIPE_NAME" remarks="设备菜单" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20211103-001-WIP_QUERY" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211103-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>在制品查询添加查询条件</comment>
	</changeSet>
	
	<changeSet id="MM-20211103-001-CONSUMABLE-ADD-COLUMN" author="tangjiacheng">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="MM_CONSUMABLE_EQP" columnName="IS_ENABLE"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_CONSUMABLE_EQP">  
        	<column name="IS_ENABLE" remarks="" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20211103-002-LOT-TECN" author="zhougelong">
		<sqlFile path="sql/WIP-20211103-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>LotTecn页面及context表变更</comment>
	</changeSet>
	
	<changeSet id="WIP-20211103-002-LOT-TECN2" author="zhougelong">
		<sqlFile path="sql/WIP-20211103-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改上个sql文件sql不对问题</comment>
	</changeSet>
	
	<changeSet id="WIP-20211103-004-LOTTECN" author="Clark">
		<sqlFile path="sql/WIP-20211103-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加LotTecn客制化组件</comment>
	</changeSet>
	
	<changeSet id="WIP-20211104-001-AD_MESSSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211104-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20211104-001-CHANGE_REWORK_TABLE" author="FanChengMing">
		<sqlFile path="sql/WIP-20211104-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>未来定时器和QTIME添加返工节点流程修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211104-002-BYLOCATION-PPID" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211104-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按设备作业PPID显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20211104-005-STEPIMPUT" author="zhougelong">
		<sqlFile path="sql/WIP-20211104-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步导入修改</comment>
	</changeSet>
	
	<changeSet id="WIP-********-002-LOT_ADD_COLUMN" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT" columnName="CONTROL_ID"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT">  
        	<column name="CONTROL_ID" remarks="控制项" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-********-002-LOTHIS_ADD_COLUMN" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT_HIS" columnName="CONTROL_ID"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT_HIS">  
        	<column name="CONTROL_ID" remarks="控制项" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-********-002-LOTSTART" author="Clark">
		<sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单批次投料增加栏位</comment>
	</changeSet>
	
	<changeSet id="SPC-********-EDC_RESULT_SPC_JOB" author="YanChao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_RESULT_SPC_JOB" columnName="OCAP_ID"/>
			</not>
		</preConditions> 
    	<addColumn tableName="EDC_RESULT_SPC_JOB">
    		<column name="OCAP_ID" remarks="Ocap ID" type="VARCHAR2(64)"/>
    	</addColumn>
    	<comment>OCAP ID</comment>
    </changeSet>

	<changeSet id="PRD-********-001-N2STOCK" author="WuFaMing">
		<sqlFile path="sql/PRD-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步管理增加栏位</comment>
	</changeSet>
	
	<changeSet id="PRD-20211109-001-PARAMETERIMPUT" author="zhougelong">
		<sqlFile path="sql/PRD-20211109-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步管理工步属性导入</comment>
	</changeSet>
	
	<changeSet id="WIP-20211109-001-BY-EQP-QUERY-PPID" author="HeTao">
		<sqlFile path="sql/WIP-20211109-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按设备作业查询批次列表的PPID</comment>
	</changeSet>

	<changeSet id="WIP-20211109-002-QTIMEMODIFYCOLUMNLENGTH" author="WuFaMing">
		<modifyDataType tableName="WIP_FUTURE_ACTION" columnName="NAME" newDataType="VARCHAR2(128 BYTE)"/>
		<modifyDataType tableName="WIP_FUTURE_ACTION_HIS" columnName="NAME" newDataType="VARCHAR2(128 BYTE)"/>
		<modifyDataType tableName="WIP_FUTURE_ACTION_HIS" columnName="HOLD_REASON" newDataType="VARCHAR2(256 BYTE)"/>
		<sqlFile path="sql/WIP-20211109-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改QTime字段长度</comment>
	</changeSet>
		
	<changeSet id="WIP-20211109-004-PART_STEP_ADD_FIELD" author="FanChengMing">
		<sqlFile path="sql/WIP-20211109-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>产品流程增加N2Stock和stepAttributeString的字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20211109-005-BY-LOT-SELECT-EQP-PAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211109-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按设备作业进站向导页修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211110-001-MONTH-CODE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211110-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>MONTH_CODE转换</comment>
	</changeSet>
	
	<changeSet id="EDC-20211110-001-EDCITEMCHANGE" author="zhougelong">
		<sqlFile path="sql/EDC-20211110-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>数据采集项正则表达式修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211110-001-SORTERUPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20211110-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Sorter查询增加条件类型为SORTER</comment>
	</changeSet>
	
	<changeSet id="WIP-20211110-001-IDGENERATOR-RULE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211110-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>ID生成规则添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20211110-001-SORTERUPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20211110-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Sorter查询增加条件类型为SORTER</comment>
	</changeSet>
	
	<changeSet id="WIP-20211110-001-WORKORDELOTSTART" author="zhougelong">
		<sqlFile path="sql/WIP-20211110-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单批次投料页面整理</comment>
	</changeSet>
	
	<changeSet id="WIP-20211110-051-WORKORDELOTSTART" author="HeTao">
		<sqlFile path="sql/WIP-20211110-051.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单批次投料表格字段调整</comment>
	</changeSet>

	<changeSet id="WIP-20211110-003-RELEASELOT" author="WuFaMing">
		<sqlFile path="sql/WIP-20211110-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次放行界面布局调整</comment>
	</changeSet>
	
	<changeSet id="PILOT-20211110-001-EDCSET-ADD-COLUMN" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="EDC_SET" columnName="COMPONENT_SAMPLING_PLAN"/>	
			</not>
		</preConditions> 
    	<addColumn tableName="EDC_SET">  
        	<column name="COMPONENT_SAMPLING_PLAN" remarks="抽样计划" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="EDC-20211110-002-CHANGESQL" author="Clark">
		<sqlFile path="sql/EDC-20211110-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>EdcSet增加抽样计划栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20211111-001-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211111-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改动态表英文描述</comment>
	</changeSet>
	
	<changeSet id="WIP-20211111-001-NPW-LOT-CREATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211111-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>NPW批次创建</comment>
	</changeSet>
	
	<changeSet id="MM-20211111-001-LOTSORTINGMERGE" author="Zhougelong">
		<sqlFile path="sql/MM-20211111-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具合批菜单英文修改</comment>
	</changeSet>

	<changeSet id="WIP-20211112-003-RELEASELOT" author="WuFaMing">
		<sqlFile path="sql/WIP-20211112-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次放行页面布局调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20211112-001-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211112-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>添加载具分批合批动态表</comment>
	</changeSet>
	
	<changeSet id="WIP-20211111-001-SELECT-EQP-PAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211112-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>NPW批次创建</comment>
	</changeSet>
	
	<changeSet id="RAS-202101111-001-ADD-COLUMN" author="LiuTao">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_PORT" columnName="PORT_NUM"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="RAS_PORT">
    		<column name="PORT_NUM" remarks="Port序列号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    </changeSet>
    
    <changeSet id="RAS-202101111-002-ADD-COLUMN" author="LiuTao">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_PORT_HIS" columnName="PORT_NUM"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="RAS_PORT_HIS">
    		<column name="PORT_NUM" remarks="Port序列号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    </changeSet>
    
    <changeSet id="RAS-202101111-003-ADD-COLUMN" author="LiuTao">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_PORT_EVENT_HIS" columnName="PORT_NUM"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="RAS_PORT_EVENT_HIS">
    		<column name="PORT_NUM" remarks="Port序列号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    </changeSet>
	
	<changeSet id="RAS-20211112-001-AD_FILED" author="LIUTAO">
		<sqlFile path="sql/RAS-20211112-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备端口增加 Port Num 栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20211112-004-ADD_RECYCLE_COUNT" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="WIP_LOT" columnName="RECYCLE_COUNT"/>	
			</not>
		</preConditions> 
    	<addColumn tableName="WIP_LOT">  
        	<column name="RECYCLE_COUNT" remarks="回收次数" type="NUMBER(3, 0)"/>
    	</addColumn> 
	</changeSet>
	
	<changeSet id="WIP-20211112-005-ADD_RECYCLE_COUNT" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="WIP_LOT_HIS" columnName="RECYCLE_COUNT"/>	
			</not>
		</preConditions> 
    	<addColumn tableName="WIP_LOT_HIS">  
        	<column name="RECYCLE_COUNT" remarks="回收次数" type="NUMBER(3, 0)"/>
    	</addColumn> 
	</changeSet>
	
	<changeSet id="WIP-20211112-006-ADD_RECYCLE_COUNT" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="WIP_COMPONENTUNIT" columnName="RECYCLE_COUNT"/>	
			</not>
		</preConditions> 
    	<addColumn tableName="WIP_COMPONENTUNIT">  
        	<column name="RECYCLE_COUNT" remarks="回收次数" type="NUMBER(3, 0)"/>
    	</addColumn> 
	</changeSet>
	
	<changeSet id="WIP-20211112-007-ADD_RECYCLE_COUNT" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="WIP_COMPONENTUNIT_HIS" columnName="RECYCLE_COUNT"/>	
			</not>
		</preConditions> 
    	<addColumn tableName="WIP_COMPONENTUNIT_HIS">  
        	<column name="RECYCLE_COUNT" remarks="回收次数" type="NUMBER(3, 0)"/>
    	</addColumn> 
	</changeSet>
	
	<changeSet id="WIP-20211115-001-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211115-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具分批动态表完善</comment>
	</changeSet>
	
	<changeSet id="RAS-20211115-EQP-ADD-COLUMN" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="IS_MULTI_SUB_EQP" />
			</not>
		</preConditions>
		<addColumn tableName="RAS_EQP">
			<column name="IS_MULTI_SUB_EQP" remarks="" type="VARCHAR2(1 BYTE)" />
		</addColumn>
		<comment>RAS_EQP表增加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20211116-001-SELECT-EQP-PAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211116-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业选择设备页面调整</comment>
	</changeSet>
	
	<changeSet id="RAS-20211116-002-RAS_EQP_ADD_SUBLOCATION" author="FanChengMing">
		<sqlFile path="sql/RAS-20211116-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备管理界面增加子location配置栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20211116-002-LOT_TECN_ADD_PPID" author="FanChengMing">
		<sqlFile path="sql/WIP-20211116-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>LotTecn增加PPID显示和编辑</comment>
	</changeSet>

	<changeSet id="WIP-20211116-003-LOT_DETAIL_ADD_COLUMN" author="FanChengMing">
		<sqlFile path="sql/WIP-20211116-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次详细信息增加使用次数和回收次数栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20211116-004-LOT_TECN_CONTEXT_UPDATE" author="FanChengMing">
		<sql>update COM_CONTEXT T set t.context_field_rrn5 = null, t.result_field_rrn5 = 5127413401901129734, t.result_field_id5 = 'equipmentRecipe' where t.object_rrn = 230 </sql>
      	<comment>lotTecnContext更新内容</comment>
	</changeSet>

	<changeSet id="WIP-20211116-004-MONITOR_LOT_QUERY" author="WuFaMing">
		<sqlFile path="sql/WIP-20211116-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>控制批详情增加修改按钮权限</comment>
	</changeSet>
	
	<changeSet id="BAS-20211116-001-AD_REFNAME" author="DaiWenBin">
		<sqlFile path="sql/BAS-20211116-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>系统栏位参考值添加</comment>
	</changeSet>

	<changeSet id="WIP-20211117-001-LOT-COMP-HIS" author="WuFaMing">
		<sqlFile path="sql/WIP-20211117-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次组件事务增加栏位</comment>
	</changeSet>

	<changeSet id="WIP-20211117-002-LOT-COMP-HIS" author="WuFaMing">
		<sqlFile path="sql/WIP-20211117-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次组件事务-批次号栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211117-003-SPILT-RECOVERR-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211117-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次返工界面英译中错误</comment>
	</changeSet>
	
	<changeSet id="PRD-2021111701-PRDLINK-ADD-COLUMN" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="PRD_LINK" columnName="LINK_SOURCE" />
			</not>
		</preConditions>
		<addColumn tableName="PRD_LINK">
			<column name="LINK_SOURCE" remarks="" type="VARCHAR2(32 BYTE)" />
		</addColumn>
		<comment>PRD_LINK表增加字段</comment>
	</changeSet>
	
	<changeSet id="PRD-2021111702-PRDLINK-ADD-COLUMN" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="PRD_LINK" columnName="LINK_SOURCE_NAME" />
			</not>
		</preConditions>
		<addColumn tableName="PRD_LINK">
			<column name="LINK_SOURCE_NAME" remarks="" type="VARCHAR2(32 BYTE)" />
		</addColumn>
		<comment>PRD_LINK表增加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20211118-001-LOT-CHECK-Recipe-Reticle" author="HeTao">
		<sqlFile path="sql/WIP-20211118-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>前台卡控Recipe Reticle的message</comment>
	</changeSet>
	
	<changeSet id="WIP-20211118-002-WORKERORDER-LOT-START" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211118-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单批次投料一些message和字段调整</comment>
	</changeSet>

	<changeSet id="RAS-20211122-001-ADD-EQUIPMENT-GLC-FORM" author="WuFaMing">
		<sqlFile path="sql/RAS-20211122-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备管理界面升级为GlcForm</comment>
	</changeSet>
	
	<changeSet id="WIP-20211122-002-ADD—SYS-PARAMETER" author="Clark">
		<sqlFile path="sql/WIP-20211122-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>添加系统参数</comment>
	</changeSet>
	
	<changeSet id="WIP-20211122-003-IS-AVL-LOT-TYPE" author="Clark">
		<sqlFile path="sql/WIP-20211122-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>可用批次类型字段添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20211123-001-IS-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20211123-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>添加Message</comment>
	</changeSet>

	<changeSet id="RAS-20211124-001-EQUIPMENT" author="WuFaMing">
		<sqlFile path="sql/RAS-20211124-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备管理修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211125-001-WIPFORWARDLOT" author="zhougelong">
		<sqlFile path="sql/WIP-20211125-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次未来到达查询字段类型异常修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211125-SORTTING_JOB" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211125-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>sorint job界面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20211125-CARRIER" author="zhougelong">
		<sqlFile path="sql/***********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具编号页面修改</comment>
	</changeSet>
	
	<changeSet id="***********-001-WAREHOUSE-EQP" author="tangjiacheng">
		<sqlFile path="sql/***********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单批次投料一些message和字段调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20211130-001-CHANGE_LOT_PROPERTY" author="FanChengMing">
		<sqlFile path="sql/WIP-20211130-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改批次信息修改栏位改成可配置的</comment>
	</changeSet>
	
	<changeSet id="WIP-20211130-002-CHANGE_AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211130-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次详情动态表英文调整</comment>
	</changeSet>

	<changeSet id="MM-20211201-001-MODIFY-WAREHOUSE" author="WuFaMing">
		<sqlFile path="sql/MM-20211201-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具编号-仓库和库位栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211203-001-LOTFUTUREHOLD" author="zhougelong">
		<sqlFile path="sql/WIP-20211203-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次未来暂停是否重复属性</comment>
	</changeSet>
	
	<changeSet id="WIP-20211206-001-CHANG_AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211206-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业动态表添加qime字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20211207-003-RECOVER-HOLDLOT" author="tangjiacheng">
		<sqlFile path="sql/WIP-20211207-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>返工hold批次</comment>
	</changeSet>
	
	<changeSet id="PRD-20211208-WFNODE-ADD-COLUMN" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_NODE" columnName="HI_SUPER_INSTRUCTION" />
			</not>
		</preConditions>
		<addColumn tableName="WF_NODE">
			<column name="HI_SUPER_INSTRUCTION" remarks="" type="VARCHAR2(32 BYTE)" />
		</addColumn>
		<comment>WF_NODE表增加字段</comment>
	</changeSet>
	
	<changeSet id="PRD-20211208-001-REWORK" author="Clark">
		<sqlFile path="sql/PRD-20211208-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>ReworkState增加栏位</comment>
	</changeSet>
	
	<changeSet id="RAS-20211208-001-REWORK" author="HeTao">
		<sqlFile path="sql/RAS-20211208-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次进站时，支持按设备卡控批次必须先做JobPrepare</comment>
	</changeSet>
	
	<changeSet id="PRD-20211208-002-REWORK" author="Clark">
		<sqlFile path="sql/PRD-20211208-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>ReworkState检查Message</comment>
	</changeSet>
	
	<changeSet id="PRD-20211213-WF_PROCESSDEFINITION" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WF_PROCESSDEFINITION" columnName="REPLACED_PROCEDURE_STATE"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WF_PROCESSDEFINITION">  
        	<column name="LOT_RRN" remarks="批次主键" type="NUMBER(19, 0)"/>
        	<column name="REPLACED_PROCEDURE_STATE" remarks="被替换流程节点名称" type="VARCHAR2(32 BYTE)"/>
        	<column name="REPLACED_PROCEDURE_STATE_PATH" remarks="被替换流程节点路径" type="VARCHAR2(256 BYTE)"/>
        	<column name="IS_REPEAT" remarks="是否重复" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>  
	</changeSet>
	<changeSet id="EDC-20211216-EDC_DATA-ADD-COLOUM" author="YanChao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_DATA" columnName="PASS_QTY"/>
			</not>
		</preConditions>

		<addColumn tableName="EDC_DATA">
			<column name="PASS_QTY" remarks="良品数量" type="NUMBER(19, 0)"/>
		</addColumn>
		<comment>EDC DATA表增加PASS_QTY字段</comment>
	</changeSet>
	
	<changeSet id="EDC-20211216-EDC_DATA_HIS-ADD-COLOUM" author="YanChao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_DATA_HIS" columnName="PASS_QTY"/>
			</not>
		</preConditions>

		<addColumn tableName="EDC_DATA_HIS">
			<column name="PASS_QTY" remarks="良品数量" type="NUMBER(19, 0)"/>
		</addColumn>
		<comment>EDC DATA表增加PASS_QTY字段</comment>
	</changeSet>
	
	<changeSet id="PRD-20211216-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211216-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="RAS-EQUPMENT-IMPUT" author="zhougelong">
		<sqlFile path="sql/RAS-20211217-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备导入修改为配置模式新增导入字段sublocation</comment>
	</changeSet>
	
	<changeSet id="WIP-FUTURE-ARRIVE-LOT-QUERY" author="zhougelong">
		<sqlFile path="sql/WIP-20211220-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>未来到达批次字段英译中修改</comment>
	</changeSet>

	<changeSet id="PRD-20211220-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20211220-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充英译中</comment>
	</changeSet>

	<changeSet id="MM-20211220-001-AD_MESSAGE" author="WuFaMing">
		<sqlFile path="sql/MM-20211220-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Buffer信息同步Add Message</comment>
	</changeSet>
	
	<changeSet id="WIP-20211221-001-WIPQUERYADDGROUP" author="zhougelong">
		<sqlFile path="sql/WIP-20211221-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>在制品查询新增按钮按MyLotGroup只能查询绑定批次</comment>
	</changeSet>
	
	<changeSet id="WIP-20211221-002-MYLOTGROUP" author="zhougelong">
		<sqlFile path="sql/WIP-20211221-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>MyGroupLot管理Sql</comment>
	</changeSet>
	
	<changeSet id="BAS-20211221-003-ADD—SYS-PARAMETER" author="FanChengMing">
		<sqlFile path="sql/BAS-20211220-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>添加系统参数</comment>
	</changeSet>
	
	<changeSet author="zhougelong" id="WIP-20211221-WIP_LOT_MY_GROUP">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_LOT_MY_GROUP"/>
			</not>
		</preConditions>
        <createTable remarks="MyLot管理表" tableName="WIP_LOT_MY_GROUP" tablespace="TS_MES_DAT">
           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_MY_GROUP"
					primaryKeyTablespace="TS_MES_DAT" />
			</column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />
            
            <column name="GROUP_ID" remarks="组名" type="VARCHAR2(64 BYTE)"/>
            <column name="LOT_ID" remarks="批次号" type="VARCHAR2(64 BYTE)"/>
            <column name="OWNER" remarks="用户" type="VARCHAR2(64 BYTE)"/>
        </createTable>
    </changeSet>
    
    <changeSet id="WIP-20211221-004-MYLOTGROUPDIALOG" author="zhougelong">
		<sqlFile path="sql/WIP-20211221-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>选择MyGroupLot中GroupId弹窗sql</comment>
	</changeSet>
	
	 <changeSet id="WIP-20211221-004-LOT-DETAILCHANGE" author="zhougelong">
		<sqlFile path="sql/WIP-20211221-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次详细信息、与显示的组件信息使用与回收次数栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20211222-WIP-SBD-HIS-POSITION" author="zhougelong">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_SBD_HIS" columnName="POSITION"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_SBD_HIS">
			<column name="POSITION" remarks="位置号" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>WIP_SBD_HIS表增加POSITION字段</comment>
	</changeSet>
	
	<changeSet id="MM-20211222-MM-PR-KITTING" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="MM_EQUIPMENT_MATERIAL" columnName="LAST_MLOT_ID"/>
			</not>
		</preConditions>

		<addColumn tableName="MM_EQUIPMENT_MATERIAL">
			<column name="LAST_MLOT_ID" remarks="上次KITTING物料批次ID" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		
		<addColumn tableName="MM_EQUIPMENT_MATERIAL">
			<column name="LAST_ATTACH_DATE" remarks="上次绑定日期" type="date"/>
		</addColumn>
		<comment>MM_EQUIPMENT_MATERIAL添加上次kitting mlotId和date</comment>
	</changeSet>
	
	 <changeSet id="WIP-20211227-001-MASSAGE" author="zhougelong">
		<sqlFile path="sql/WIP-20211227-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>一些英译中调整，批次历史报废信息增加字段，导入导出表格异常表头修复等</comment>
	</changeSet>
	
	<changeSet id="WIP-20211227-ADD-UK-INDEX" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_WIP_SORT_RULE_LINE_RRN" tableName="WIP_SORT_RULE"/>
			</not>
		</preConditions>
		
        <addUniqueConstraint columnNames="ORG_RRN, NAME" constraintName="UK_WIP_LOT_TECN" tableName="WIP_LOT_TECN" tablespace="TS_MES_IDX"/>
        
        <createIndex indexName="IDX_WIP_SORT_RULE_LINE_RRN" tableName="WIP_SORT_RULE_LINE" tablespace="TS_MES_IDX" unique="false">
            <column name="RULE_RRN"/>
        </createIndex>
        
        <createIndex indexName="IDX_WIP_LOT_SORT_JOB_BATCH" tableName="WIP_LOT_SORTING_JOB" tablespace="TS_MES_IDX" unique="false">
             <column name="BATCH_ID"/>
        </createIndex>
        
        <createIndex indexName="IDX_WIP_LOT_SORT_JOB_EQP" tableName="WIP_LOT_SORTING_JOB" tablespace="TS_MES_IDX" unique="false">
             <column name="EQUIPMENT_ID"/>
             <column name="DURABLE_ID"/>
        </createIndex>
        
        <createIndex indexName="IDX_WIP_LOT_ST_JOB_DE_RRN" tableName="WIP_LOT_SORTING_JOB_DETAIL" tablespace="TS_MES_IDX" unique="false">
             <column name="JOB_RRN"/>
        </createIndex>
        
        <createIndex indexName="IDX_WIP_LOT_TECN_ECN_SOURCE" tableName="WIP_LOT_TECN" tablespace="TS_MES_IDX" unique="false">
             <column name="ECN_SOURCE"/>
             <column name="ECN_SOURCE_NAME"/>
             <column name="LOT_ID"/>
        </createIndex>
        
        <createIndex indexName="IDX_WIP_LOT_MY_GROUP_ID" tableName="WIP_LOT_MY_GROUP" tablespace="TS_MES_IDX" unique="false">
             <column name="GROUP_ID"/>
        </createIndex>
        
        <createIndex indexName="IDX_WIP_LOT_TECN_HIS_NAME" tableName="WIP_LOT_TECN_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
             <column name="NAME"/>
        </createIndex>
        
        <createIndex indexName="IDX_WIP_LOT_TECN_HIS_LOT_ID" tableName="WIP_LOT_TECN_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
             <column name="LOT_ID"/>
        </createIndex>
        
        <comment>增加SORT唯一键和索引</comment>
        
    </changeSet>
    
	<changeSet id="WIP-********-001-ADD_UNSCRAP_BY_SORTER" author="FanChengMing">
		<sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加批次反报废（Sorter）页面</comment>
	</changeSet>
	
	<changeSet id="RAS-********-001-PHOTO-EQP-TYPE" author="HeTao">
		<sqlFile path="sql/RAS-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>光刻机设备类型</comment>
	</changeSet>
	
	<changeSet id="WIP-20211230-002-SORT-MERGE" author="Clark">
		<sqlFile path="sql/WIP-20211230-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Sort合批增加按钮</comment>
	</changeSet>
	
	<changeSet id="WIP-20211230-012-FIELD-LABEL-UPDATE" author="HeTao">
		<sqlFile path="sql/WIP-20211230-012.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改按批次作业界面的批次列表中英文描述</comment>
	</changeSet>
	
	<changeSet id="RAS-20211230-001-PARTSTEPIMPORT_CHANGE" author="FanChengMing">
		<sqlFile path="sql/WIP-20211230-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>产品流程导入修改</comment>
	</changeSet>

	<changeSet id="***********-001-ADD-TRANSFERSTATE" author="WuFaMing">
		<sqlFile path="sql/***********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>更新载具搬送状态</comment>
	</changeSet>
	
	<changeSet id="WIP-20220105-011-UPDATE-ADQUERY-ComponentHoldList" author="HeTao">
		<sqlFile path="sql/WIP-20220105-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>ComponentHoldList查询条件修改parent_lot_rrn改为parent_unit_rrn</comment>
	</changeSet>
	
	<changeSet id="WIP-********-001-UPDATE_AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>动态表错误栏位名称调整，英译中调整</comment>
	</changeSet>
	
	<changeSet id="WIP-********-003-UPDATE_AD_AUTHORITY" author="FanChengMing">
		<sqlFile path="sql/WIP-********-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>菜单名称调整，英译中调整</comment>
	</changeSet>
	
	<changeSet id="MM-********-001-CARRIER-MESSAGE" author="zhougelong">
		<sqlFile path="sql/MM-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中提示</comment>
	</changeSet>
	
	<changeSet id="BAS-********-002-AD_SYS_PARAMETER_VALUE" author="DaiWenBin">
		<sqlFile path="sql/BAS-********-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充系统参数</comment>
	</changeSet>

	<changeSet id="PRD-20210107-WF_PROCESSDEFINITION_HIS" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WF_PROCESSDEFINITION_HIS" columnName="REPLACED_PROCEDURE_STATE"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WF_PROCESSDEFINITION_HIS">  
        	<column name="LOT_RRN" remarks="批次主键" type="NUMBER(19, 0)"/>
        	<column name="PROCESS_DEFINITION_RRN" remarks="PROCESSDEFINITION主键" type="NUMBER(19, 0)"/>
        	<column name="REPLACED_PROCEDURE_STATE" remarks="被替换流程节点名称" type="VARCHAR2(32 BYTE)"/>
        	<column name="REPLACED_PROCEDURE_STATE_PATH" remarks="被替换流程节点路径" type="VARCHAR2(256 BYTE)"/>
        	<column name="IS_REPEAT" remarks="是否重复" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>  
	</changeSet>

	<changeSet id="WIP-********-002-BUG-FIX" author="WuFaMing">
		<sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>BankIn界面bug修改</comment>
	</changeSet>

	<changeSet id="PRD-********-001-ADD-PRD-PARAMETER" author="WuFaMing">
		<sqlFile path="sql/PRD-********-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加工艺参数$DUMMY_MAXCOUNT</comment>
	</changeSet>
	<changeSet id="WIP-********-002_OnlineRework" author="MoYouMing">
       <sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>在线返工相关优化修改</comment>
	</changeSet>
	
	<changeSet id="WIP-********-002_OnlineRework" author="MoYouMing">
       <sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>在线返工相关优化修改</comment>
	</changeSet>
	
	<changeSet id="WIP-********-004_scarpsorting" author="MoYouMing">
       <sqlFile path="sql/WIP-********-004.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>sorting界面目标卡位置有wafer提示报错优化</comment>
	</changeSet>
	
	<changeSet id="WIP-********-001_adhocrework" author="MoYouMing">
       <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>返工界面增加自动hold</comment>
	</changeSet>
	
	<changeSet id="WIP-20220108-001_AD_MESSAGE" author="DaiWenBin">
       <sqlFile path="sql/WIP-20220108-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="PRD-20220108-002_ADD_PARTSTEP_FIELD" author="FanChengMing">
       <sqlFile path="sql/PRD-20220108-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>产品流程查看增加四个向导页字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220110-001-BY-LOCATION" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220110-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>by location trackIn 变更区域按钮功能调整至location和subLocation分开选择</comment>
	</changeSet>
	
	<changeSet id="WIP-20220110-001-WIPEXCEPTIONBUNDLE-MESSAGE" author="zhougelong">
       <sqlFile path="sql/WIP-20220110-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>WipExceptionBundle异常翻译英译中</comment>
	</changeSet>
	
	<changeSet id="PRD-20220110-001_ADD_PARTSTEP_FIELD" author="FanChengMing">
       <sqlFile path="sql/PRD-20220110-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>产品流程查看增加缺少的字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220110-001-BY-lot" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220110-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改按批次作业栏位</comment>
	</changeSet>
	
	<changeSet id="RAS-20220110-001-EQUIPMENT-IMPUT-ADD" author="zhougelong">
		<sqlFile path="sql/RAS-20220110-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备导入栏位新增</comment>
	</changeSet>
	<changeSet id="WIP-********-001_qtime_dialog" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
		 <sqlCheck expectedResult="0">
			select count(*) from ad_form t where t.name = 'WIPVProcMuiltQtimerDialogForm'		 
		 </sqlCheck>
		</preConditions>
       <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>qtime dialog</comment>
	</changeSet>
	
	<changeSet id="PRD-20220111-001-STEP-IMPUT-ADD-FIELD" author="zhougelong">
		<sqlFile path="sql/PRD-20220111-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备导入栏位新增</comment>
	</changeSet>
	
	<changeSet id="RAS-20220111-001-EQUIPMENT-IMPUT-ADD" author="tangjiacheng">
		<sqlFile path="sql/RAS-20220111-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备导入栏位新增</comment>
	</changeSet>

	<changeSet id="WIP-20220111-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220111-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中补充</comment>
	</changeSet>
	
	<changeSet id="WIP-LOTDETAIL-COMPONENTFIELD-SUBQTY" author="zhougelong">
		<sqlFile path="sql/WIP-20220111-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次详细信息Component取消subqty、载具作业设备下拉排序</comment>
	</changeSet>
	
	<changeSet id="EDC-20220111-001-UPDATE-EDC-LINE" author="tangjiacheng">
		<sqlFile path="sql/EDC-20220111-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备导入栏位新增</comment>
	</changeSet>

	<changeSet id="WIP-20220112-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220112-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中描述调整</comment>
	</changeSet>
	<changeSet id="WIP-20220112-002-AD_MESSAGE" author="MoYouMing">
		<sqlFile path="sql/WIP-20220112-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中描述调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20220112-003-AD_SYS_PARAMETER_VALUE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220112-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>系统参数修改</comment>
	</changeSet>
	
	<changeSet id="RAS-20220113-001-ADFILED-UPDATE" author="HeTao">
		<sqlFile path="sql/RAS-20220113-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>系统参数修改</comment>
	</changeSet>
	
	<changeSet id="MM-20220113-001-CARRIERIMPORT" author="zhougelong">
		<sqlFile path="sql/MM-20220113-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具导入修改</comment>
	</changeSet>
	
	<changeSet id="RAS-20220113-001-EQUIPMENTIMPORT" author="zhougelong">
		<sqlFile path="sql/RAS-20220113-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备导入修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220113-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220113-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20220113-010-BY-LOCATION" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220113-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按区域作业区域变更</comment>
	</changeSet>
	
	<changeSet id="WIP-20220113-020-BY-EQPBUFFER" author="Clark">
		<sqlFile path="sql/WIP-20220113-020.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按作业作业Buffer修改</comment>
	</changeSet>
	
	<changeSet id="EDC-20220114-001-EDC-SET-IMPORT" author="tangjiacheng">
		<sqlFile path="sql/EDC-20220114-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按区域作业区域变更</comment>
	</changeSet>
	
	<changeSet id="WIP-20220114-showfuture10step" author="MoYouMing">
		<sqlFile path="sql/WIP-20220114-055.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次进站增加未来10工步显示</comment>
	</changeSet>
	<changeSet id="WIP-20220114-noshowfuture10step" author="MoYouMing">
		<sqlFile path="sql/WIP-20220114-066.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次去除未来10工步显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20220114-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220114-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>完善英译中</comment>
	</changeSet>
	
	<changeSet id="RAS-20220117-001-AD_IMPORT" author="Clark">
		<sqlFile path="sql/RAS-20220117-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加导入字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220118-001-FUTURE-EQP-INFO" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220118-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业查看未来设备</comment>
	</changeSet>
	
	<changeSet id="EDC-20220118-001-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/EDC-20220118-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>edc相关动态表调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20220118-002_ADD_UNSCRAP_MESSAGE_AND_HISFIELD" author="FanChengMing">
       <sqlFile path="sql/WIP-20220118-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>反报废描述优化，历史查询增加SlotPos</comment>
	</changeSet>
	
	<changeSet id="WIP-20220119-001_FIELD_MESSAGE" author="zhougelong">
       <sqlFile path="sql/WIP-20220119-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>动态表英译中描述修改</comment>
	</changeSet>
	
	<changeSet id="PP-20220119-001-BATCH-CANCEL-CHECK" author="HeTao">
       <sqlFile path="sql/PP-20220119-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Lot batch取消时检查是否存在PrepareJob</comment>
	</changeSet>
	
	<changeSet id="WIP-20220118-001-FUTURE-STEP-SHOW" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220119-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业未来工步栏位添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20220119-001-PREPARE-JOB-BATCH-CHECK" author="HeTao">
       <sqlFile path="sql/WIP-20220119-022.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>PrepareJob创建时，检查Batch是否完整</comment>
	</changeSet>
	
	<changeSet id="WIP-20220120-001-WIP_MERGE_AD_MESSAGE" author="DaiWenBin">
       <sqlFile path="sql/WIP-20220120-022.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>控制批合批英译中修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220120-001-LOT-STATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220120-012.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次状态显示栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220120-LOT-ADD-SUBLOCATION" author="HeTao">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT" columnName="SUB_LOCATION"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT">  
        	<column name="SUB_LOCATION" remarks="子区域" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
    	
    	<addColumn tableName="WIP_LOT_HIS">  
        	<column name="SUB_LOCATION" remarks="子区域" type="VARCHAR2(32 BYTE)"/>
    	</addColumn> 
	</changeSet>
	
	<changeSet id="WIP-20220120-LOT-ADD-SUBLOCATION-AD-FIELD" author="HeTao">
    	<sqlFile path="sql/WIP-20220120-033.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>批次增加SubLocation管理</comment>
    </changeSet>
    
    <changeSet id="WIP-20220120-005_Dummy" author="Clark">
       <sqlFile path="sql/WIP-20220120-005.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>动态表英译中描述修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220120-010-LOT-FUTURE-STEP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220120-013.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次未来工步流程查询</comment>
	</changeSet>
	
	<changeSet id="WIP-20220121-010-LOT-FUTURE-STEP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220121-013.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步修改信息新增</comment>
	</changeSet>

	<changeSet id="WIP-20220121-005-DUMMY" author="Calrk">
		<sqlFile path="sql/WIP-20220121-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Dummy修改</comment>
	</changeSet>
	
	<changeSet id="MM-20220121-001-CARRIER-FIELD" author="zhougelong">
		<sqlFile path="sql/MM-20220121-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具列表新增当前数量显示</comment>
	</changeSet>
	
	<changeSet id="PRD-20220121-005-STEP" author="Clark">
		<sqlFile path="sql/PRD-20220121-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Step UseCategory属性修改</comment>
	</changeSet>
	
	<changeSet id="PRD-20220124-001-WIP_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220124-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>字段错误修改</comment>
	</changeSet>
	
	<changeSet id="PRD-20220124-001-STEP-BUTTON-LABEL-MODIFY" author="HeTao">
		<sqlFile path="sql/PRD-20220124-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改工步管理英文界面按钮显示不全问题，把按钮的Label缩写</comment>
	</changeSet>
	
	<changeSet id="WIP-20220125-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220125-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-202201216-001-MM-WAREHOUSETYPE" author="Wangluopeng">
		<sqlFile path="sql/WIP-20220126-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加n2stock类型的仓库</comment>
	</changeSet>
	
	<changeSet id="WIP-202201216-002-MM-WAREHOUSET_add_cloumn" author="Wangluopeng">
		<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="MM_WAREHOUSE" columnName="MIN_USE_TIME"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_WAREHOUSE">  
        	<column name="MIN_USE_TIME" remarks="最小在库时间 单位(小时)" type="NUMBER(19,3)"/>
    	</addColumn>  
	
		<comment>增加仓库最小在库时间字段</comment>
	</changeSet>
	
	<changeSet id="WIP-202201216-003-AD-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220126-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20220121-001_Stage_Import" author="yanjiazhi">
       <sqlFile path="sql/WIP-20220121-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Stage增加导入功能</comment>
	</changeSet>
	
	<changeSet id="PRD-20220121-001_Stage_Import" author="yanjiazhi">
       <sqlFile path="sql/PRD-20220121-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Stage导入功能改为GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20220127-007-LOT-FUTURE-ACTION" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220127-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>隐藏批次未来流程变更功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220127-008-PRD-STEP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220127-008.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步管理隐藏InActive状态工步</comment>
	</changeSet>
	
	<changeSet id="WIP-20220128-001-MM-WAREHOUSET_DROP_cloumn" author="Wangluopeng">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="MM_WAREHOUSE" columnName="MIN_USE_TIME"/>
		</preConditions> 
    	
    	<dropColumn tableName="MM_WAREHOUSE">  
        	<column name="MIN_USE_TIME" />
    	</dropColumn>  
		<comment>删除不需要仓库字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220128-002-MM-WAREHOUSET_ADD_cloumn" author="Wangluopeng">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="MM_WAREHOUSE" columnName="STYLE"/>
				<columnExists tableName="MM_WAREHOUSE" columnName="IS_N2STOCKER"/>
				<columnExists tableName="MM_WAREHOUSE" columnName="TIMER_TYPE"/>
				<columnExists tableName="MM_WAREHOUSE" columnName="TIMER_DURATION"/>
				<columnExists tableName="MM_WAREHOUSE" columnName="EARLY_PERIOD"/>
				<columnExists tableName="MM_WAREHOUSE" columnName="TIMER_ACTION"/>
				<columnExists tableName="MM_WAREHOUSE" columnName="IS_SUSPEND_TIMER"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_WAREHOUSE">  
        	<column name="STYLE" remarks="" type="NUMBER(19)"/>
        	<column name="IS_N2STOCKER" remarks="是否是n2stocker类型" type="VARCHAR2(1 BYTE)"/>
        	<column name="TIMER_TYPE" remarks="定时器类型" type="VARCHAR2(32 BYTE)"/>
        	<column name="TIMER_DURATION" remarks="" type="NUMBER(19)"/>
        	<column name="EARLY_PERIOD" remarks="提前警告时间" type="NUMBER(19)"/>
        	<column name="TIMER_ACTION" remarks="" type="VARCHAR2(32 BYTE)"/>
        	<column name="IS_SUSPEND_TIMER" remarks="是否暂停Timer(批次上普通Timer,非Stocker Timer)" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>  
		<comment>增加仓库字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220128-003-MM_STORAGE_ADD_cloumn" author="Wangluopeng">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="MM_STORAGE" columnName="STYLE"/>
				<columnExists tableName="MM_STORAGE" columnName="TIMER_TYPE"/>
				<columnExists tableName="MM_STORAGE" columnName="TIMER_DURATION"/>
				<columnExists tableName="MM_STORAGE" columnName="EARLY_PERIOD"/>
				<columnExists tableName="MM_STORAGE" columnName="TIMER_ACTION"/>
				<columnExists tableName="MM_STORAGE" columnName="IS_SUSPEND_TIMER"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_STORAGE">  
        	<column name="STYLE" remarks="" type="NUMBER(19)"/>
        	<column name="TIMER_TYPE" remarks="定时器类型" type="VARCHAR2(32 BYTE)"/>
        	<column name="TIMER_DURATION" remarks="" type="NUMBER(19)"/>
        	<column name="EARLY_PERIOD" remarks="提前警告时间" type="NUMBER(19)"/>
        	<column name="TIMER_ACTION" remarks="" type="VARCHAR2(32 BYTE)"/>
        	<column name="IS_SUSPEND_TIMER" remarks="是否暂停Timer(批次上普通Timer,非Stocker Timer)" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>  
		<comment>增加库房字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220205-001-LOT-ABORT" author="HeTao">
		<sqlFile path="sql/WIP-20220205-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次、设备、区域作业增加Abort按钮权限控制</comment>
	</changeSet>
	
	<changeSet id="MM-20220207-001-warehouse-filed" author="Wangluopeng">
		<sqlFile path="sql/MM-20220207-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>warehouse增加最小停留时间</comment>
	</changeSet>
	
	<changeSet id="WIP-20220207-001-STEP-CHANGE-INFO" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220207-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步修改信息按钮</comment>
	</changeSet>
	
	<changeSet id="EDC-20220207-001-EDC-SET-LINE-ADD" author="tangjiacheng">
		<sqlFile path="sql/EDC-20220207-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>数据采集项集添加采集项栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220207-001-LOT-CHANGE-PROPERTY" author="HeTao">
		<sqlFile path="sql/WIP-20220207-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次修改备注时，栏位太小</comment>
	</changeSet>
	
	<changeSet id="WIP-20220208-001-STEP-CHANGE-INFO" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220208-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步修改信息按钮栏位描述修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220208-005-LOT-COMMENT-CHANGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220208-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次备注修改功能新增</comment>
	</changeSet>
	
	<changeSet id="WIP-20220208-022-LOT-ID-NAMING-RULE" author="HeTao">
		<sqlFile path="sql/WIP-20220208-022.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次命名规则（默认大小写字母+数字+点），放到系统参数中，在校验时使用</comment>
	</changeSet>
	
	<changeSet id="MM-20220208-001-warehouse-filed" author="Wangluopeng">
		<sqlFile path="sql/MM-20220208-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改warehouse动态表字段名称</comment>
	</changeSet>

	<changeSet id="WIP-20220211-WIP-COMPONENTUNIT-EQP-UNIT-HIS-ADD-FIELD" author="taolee">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS" columnName="EQUIPMENT_RECIPE"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS">
			<column name="EQUIPMENT_RECIPE" remarks="PPID" type="VARCHAR2(128 BYTE)"/>
		</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20220209-005-SORTED_SPLIT_MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20220209-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改几个AD_MESSAGE提示</comment>
	</changeSet>
	
	<changeSet id="WIP-20220208-005-LOT-COMMENT-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220209-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次备注修改功能新增Mssage提示</comment>
	</changeSet>
	
	<changeSet id="WIP-20220209-011-LOT-LOT-HIS-ADD-LOT-COMMENT" author="HeTao">
		<sqlFile path="sql/WIP-20220209-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次事物历史中增加批次备注信息</comment>
	</changeSet>
	
	<changeSet id="WIP-20220209-012-LOT-PROPERTY-CHANGE" author="HeTao">
		<sqlFile path="sql/WIP-20220209-012.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次信息修改时，控制备注输入字符数</comment>
	</changeSet>
	
	<changeSet id="WIP-20220210-011-LOT-PREPARE" author="HeTao">
		<sqlFile path="sql/WIP-20220210-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次作业准备时，如果设备不支持批量作业，则不允许选择多个批次做作业准备</comment>
	</changeSet>
	
	<changeSet id="WIP-20220209-013-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220209-013.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>界面UI调整</comment>
	</changeSet>
	
    <changeSet id="WIP-20220210-012-LOT-PREPARE" author="HeTao">
		<sqlFile path="sql/WIP-20220210-012.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>调整LotPrepare显示信息，设备详细信息与设备管理界面同步</comment>
	</changeSet>
	
	<changeSet id="EDC-20220210-001-EDC-SET-UPLOAD" author="tangjiacheng">
		<sqlFile path="sql/EDC-20220210-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>edcset导入激活添加弹框校验</comment>
	</changeSet>
	
	<changeSet id="WIP-20220210-002-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220210-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次事务历史动态表修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220211-001-COM_CONTEXT_MERGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220211-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>合批规则添加事务类型，用来独立载具绑定的合批规则校验</comment>
	</changeSet>
	
	<changeSet id="WIP-20220211-011-BATCH-PPID" author="HeTao">
		<sqlFile path="sql/WIP-20220211-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加batch情况下的一些提示Message</comment>
	</changeSet>

	<changeSet id="WIP-20220211-002-AD_SYS_PARAMETER_VALUE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220211-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充系统参数</comment>
	</changeSet>
	
	<changeSet id="MM-20220214-001-CARRIER-UPADTE-SPEC" author="zhougelong">
		<sqlFile path="sql/MM-20220214-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具批量操作，新增按钮载具spec修改，对载具上保存的spec信息进行升版</comment>
	</changeSet>
	
	<changeSet id="MM-20220214-002-EQUIPMENT-MESSAGE" author="zhougelong">
		<sqlFile path="sql/MM-20220214-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中提交</comment>
	</changeSet>
	
	<changeSet id="WIP_20220215-001-SQL" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220215-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中提交</comment>
	</changeSet>
	
	<changeSet id="MM-20220215-001-CARRIER-MESSAGE" author="zhougelong">
		<sqlFile path="sql/MM-20220215-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>英译中提交</comment>
	</changeSet>
	
	<changeSet id="WIP-20220215-010-MANUALMOVE-MODIFY" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20220215-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20220216-001-CHANGEPROCESS" author="zhougelong">
		<sqlFile path="sql/WIP-20220216-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>更改生产工步message提交、组件历史查询打开，及Port口查询条件新增</comment>
	</changeSet>
	
	<changeSet id="WIP-20220216-011-UPDATE-MESSAGE" author="HeTao">
		<sqlFile path="sql/WIP-20220216-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改Message</comment>
	</changeSet>
	
	<changeSet id="WIP-20220216-004-EQP-BUFFER-CARRIER" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220216-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备buffer载具页面优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20220216-005-LOT_UNSCRAP_BY_SORTER_ADD_DURABLE" author="FanChengMing">
		<sqlFile path="sql/WIP-20220216-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按SORTER的批次报废增加显示durable栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20220216-012-CHECK-BATCH" author="HeTao">
		<sqlFile path="sql/WIP-20220216-012.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Batch规则支持默认的规则</comment>
	</changeSet>
	
	<changeSet id="BAS-20220216-001-VARIABLERULELINE_ADD_SPLIT_STRING" author="FanChengMing">
		<sqlFile path="sql/BAS-20220216-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>参数类型的规则支持固定字符的截取</comment>
	</changeSet>
	
	<changeSet id="WIP-20220217-003-WAFER-PROCESSING-HIS" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220217-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>晶圆加工历史查询功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220217-004-STEP-FIELD" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220217-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工步管理功能状态查询栏位调整为下拉</comment>
	</changeSet>
	
	<changeSet id="WIP-20220217-008-STEP-MESSAGE" author="CLark">
		<sqlFile path="sql/WIP-20220217-008.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>特殊工步提示Message</comment>
	</changeSet>
	
	<changeSet id="WIP-20220217-009-CHANGE-STEP-MESSAGE" author="CLark">
		<sqlFile path="sql/WIP-20220217-009.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>CHANGESTEP提示Message</comment>
	</changeSet>

	<changeSet id="BAS-20220217-011-JOB-PREPARE" author="HeTao">
		<sqlFile path="sql/WIP-20220217-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Job Prepare时提示信息优化</comment>
	</changeSet>
	
	<changeSet id="BAS-20220218-001-AD-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220218-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Message 修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220218-001-EQP-BUFFER-POSITION" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220218-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备buffer position维护功能新增</comment>
	</changeSet>
	
	<changeSet id="MM-20220218-001-EQP-PORT-ADDFIELD" author="zhougelong">
		<sqlFile path="sql/MM-20220218-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备新增通信状态字段、设备端口新增搬送状态字段显示</comment>
	</changeSet>
	
	<changeSet id="WIP-20220218-003-WAFER-PROCESSING-HIS" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220218-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>晶圆加工历史查询功能改为查询两张表组合</comment>
	</changeSet>
	
	<changeSet id="WIP-20220221-003-WAFER-PROCESSING-HIS" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220221-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>晶圆加工历史查询页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20220222-WIP-LOT-EQUIPMENT-UNIT-ADD-FIELD" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT" columnName="LOT_TYPE"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_EQUIPMENT_UNIT">
			<column name="LOT_TYPE" remarks="批次类型" type="VARCHAR2(128 BYTE)"/>
		</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20220222-003-EQP-BUFFER-POSION" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220222-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备buffer position页面调整</comment>
	</changeSet>
	
	<changeSet id="PRD-20220222-001-QTIME_IMPORT" author="FanChengMing">
		<sqlFile path="sql/PRD-20220222-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Qtime增加导入功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220222-017-BUTTON-AUTHORY" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220222-017.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>部分功能按钮权限添加</comment>
	</changeSet>

	<changeSet id="WIP-20220222-010-AD_BUTTON_AUTHORITY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220222-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>二次校验弹框补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20220222-011-LOT-HOLD-RELEASE" author="zhougelong">
		<sqlFile path="sql/WIP-20220222-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次暂停与放行动作码修改英文描述与不可编辑</comment>
	</changeSet>
	
	<changeSet id="WIP-20220223-007-AD_BUTTON_AUTHORITY" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220223-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>二次校验弹框补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20220224-001-AD_AUTHORITY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220224-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>权限按钮添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20220224-007-EQP-BUFFER" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220224-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>两个设备buffer页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20220224-008-QTIME_ERROR_FIELD_CHANGE" author="FanChengMing">
		<sql>update ad_field t set t.reftable_rrn = '10509' where t.name = 'processName' and t.table_rrn in (173370796464463872,6160)</sql>
      	<comment>QTime查询页面工艺参考表错误修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220224-010-QTIMEBUTTON" author="zhougelong">
		<sqlFile path="sql/WIP-20220224-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>QTime查询页面按钮英译中，及Step Timer绑定的tab页面英文显示名称修改</comment>
	</changeSet>

	<changeSet id="WIP-20220224-CHANGE_DATA_TYPE" author="DaiWenBin">
		<modifyDataType tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS" columnName="UNIT_ID" newDataType="VARCHAR2(64 BYTE)"/>
		<comment>修改WIP_COMPONENTUNIT_EQP_UNIT_HIS字段的长度</comment>
	</changeSet>
	
	<changeSet id="WIP-20220225-007-EQP-BUFFER" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220225-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备buffer position字段描述修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220225-010-LOT-CHANGEID" author="zhougelong">
		<sqlFile path="sql/WIP-20220225-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次号修改新增卡控及返工流程message</comment>
	</changeSet>
	
	<changeSet id="EDC-20220225-001-EDC_TECN_ADD_COLOUM" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_TECN" columnName="STEP_NAME"/>
			</not>
		</preConditions>

		<addColumn tableName="EDC_TECN">
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(64 BYTE)"/>
		</addColumn>
		<comment>EDC的临时工程变更表增加stepName字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220225-011-EDCRETABLE-AD_SYS_PARAMETER_VALUE" author="zhougelong">
		<sqlFile path="sql/WIP-20220225-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>EDC临时工程变更table、系统参数</comment>
	</changeSet>
	
	<changeSet id="WIP-20220228-001-EDCRETABLE" author="zhougelong">
		<sqlFile path="sql/EDC-20220228-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>EDC临时工程变更子抽样计划名称参考表修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220228-002-SAMPLINGSETUP" author="FanChengMing">
		<sqlFile path="sql/WIP-20220228-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按批次作业增加作业前的抽样设置功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220301-007-EQP-BUFFER" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220301-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备buffer position字段描述修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220301-001-UNIT_RESERVED_ADD" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_COMPONENTUNIT_RESERVED" columnName="PROCEDURE_NAME"/>
				<columnExists tableName="WIP_COMPONENTUNIT_RESERVED" columnName="POSITION"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_COMPONENTUNIT_RESERVED">         
        	<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>       
        	<column name="POSITION" remarks="位置" type="VARCHAR2(32 BYTE)"/>       
    	</addColumn>  
		<comment>组件预留增加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220301-001-UNIT_RESERVED_RENAME" author="Clark">
  		<renameColumn tableName="WIP_COMPONENTUNIT_RESERVED" newColumnName="EQUIPMENT_RECIPE_NAME" oldColumnName="RECIPE_NAME" remarks="设备Recipe" />
	</changeSet>
	
	<changeSet id="WIP-20220301-001-UNIT_RESERVED_HIS_ADD" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_COMPONENTUNIT_RESERVED_HIS" columnName="PROCEDURE_NAME"/>
				<columnExists tableName="WIP_COMPONENTUNIT_RESERVED_HIS" columnName="POSITION"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_COMPONENTUNIT_RESERVED_HIS">         
        	<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>       
        	<column name="POSITION" remarks="位置" type="VARCHAR2(32 BYTE)"/>       
    	</addColumn>  
		<comment>组件预留历史增加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220301-001-UNIT_RESERVED_HIS_RENAME" author="Clark">
  		<renameColumn tableName="WIP_COMPONENTUNIT_RESERVED_HIS" newColumnName="EQUIPMENT_RECIPE_NAME" oldColumnName="RECIPE_NAME" remarks="设备Recipe" />
	</changeSet>
	
	<changeSet id="WIP-20220301-008-WIP-LOT" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220301-008.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次历史，按批次作业页面批次未来动作栏位调整</comment>
	</changeSet>
	
	<changeSet id="MM-20220302-001-CARRIER-CHANGE-LOCATION" author="HeTao">
		<sqlFile path="sql/MM-20220302-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具位置变更功能</comment>
	</changeSet>

	<changeSet id="WIP-20220302-001-AD_BUTTON_AUTHORITY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220302-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充权限按钮</comment>
	</changeSet>
	
	<changeSet id="WIP-20220302-002-LOT_PREPARE_ADD_SAMPLING" author="FanChengMing">
		<sqlFile path="sql/WIP-20220302-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>按设备作业LotPrepare增加Sampling</comment>
	</changeSet>
	
	<changeSet id="WIP-20220303-001_ADD_WIPLOTSAMPLING" author="FanChengMing">
		<sqlFile path="sql/WIP-20220303-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>提交WIPLotSampling的动态表单</comment>
	</changeSet>
	<changeSet id="MM-20220303-001-CARRIER-CHANGE-LOCATION" author="HeTao">
		<sqlFile path="sql/MM-20220303-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具位置变更功能遗漏SQL</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20220303-EDC_TECN_EDITCLOUMN" author="FanChengMing">
		<modifyDataType tableName="EDC_TECN" columnName="COMPONENT_LIST" newDataType="VARCHAR2(512 BYTE)"/>
		<modifyDataType tableName="EDC_TECN" columnName="COMP_SLOT_SRC" newDataType="VARCHAR2(128 BYTE)"/>
	</changeSet>
	
	<changeSet id="WIP-20220307-010-FLOW-FUTURE-EQP-QUERY" author="HeTao">
		<sqlFile path="sql/WIP-20220307-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>流程未来可用设备查询</comment>
	</changeSet>
	
	<changeSet id="EDC-20220307-001-EDC_PROCESS_DATA_LINE_ADD" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_PROCESS_DATA_LINE" columnName="PROCESS_DATA_TIME"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="EDC_PROCESS_DATA_LINE">         
        	<column name="PROCESS_DATA_TIME" remarks="ProcessData时间" type="date"/>       
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20220307-005-SORT" author="Clark">
		<sqlFile path="sql/WIP-20220307-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>一个批次支持连续建多个SortingJob</comment>
	</changeSet>
	
	<changeSet id="WIP-20220308-005-SORT" author="Clark">
		<sqlFile path="sql/WIP-20220308-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>一个批次支持连续建多个SortingJob，系统参数默认为N</comment>
	</changeSet>	
	
	<changeSet id="WIP-20220308-010-LOTREWORK-MESSAGE" author="zhougelong">
		<sqlFile path="sql/WIP-20220308-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>返工流程卡控英译中</comment>
	</changeSet>	
	
	<changeSet id="WIP-20220308-010-CHANGE-ADFILED-LABEL" author="HeTao">
		<sql>update AD_FIELD set label = 'Rack ID' where OBJECT_RRN = '176302891592585217'</sql>
		<comment>载具位置修改功能的一个ADField英文label修改，Locator ID改成Rack ID</comment>
	</changeSet>
	
	<changeSet id="WIP-20220308-007-LOTHIS" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220308-007.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次事务历史字段调整</comment>
	</changeSet>
	
	<changeSet id="RAS-20220309-001-PORT" author="zhougelong">
		<sqlFile path="sql/RAS-20220309-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备端口字段调整</comment>
	</changeSet>
	
	<changeSet id="RAS-20220311-001-AD_MESSAG" author="FanChengMing">
		<sqlFile path="sql/WIP-20220311-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>更新message信息</comment>
	</changeSet>
	
		<changeSet id="RAS-20220314-001-EQP_STATUS_QUERY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220314-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>新增设备状态查询功能</comment>
	</changeSet>
	
	<changeSet id="RAS-20220314-020-EQP_PORT_UPLOAD" author="zhougelong">
		<sqlFile path="sql/RAS-20220314-020.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备端口导入，修改为配置导入</comment>
	</changeSet>
	
	<changeSet id="BAS-20220311-001-UPDATE_EDC_TECN_CONTEXT_RULE" author="FanChengMing">
		<sqlFile path="sql/BAS-20220314-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>更新edctecn的规则</comment>
	</changeSet>
	
	<changeSet id="WIP-20220315-001-ADD_AD_MESSAGE" author="FanChengMing">
		<sqlFile path="sql/WIP-20220315-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加警报的message信息</comment>
	</changeSet>
	
	<changeSet id="MM-20220315-002_MM_CARRIER" author="WangJiaXin">
       <sql>UPDATE AD_FIELD SET NAMING_RULE = '^$|^[^- ]+$' WHERE OBJECT_RRN = 109121</sql>
       <comment>FOUP创建限制不能输入空格、下划线</comment>
	</changeSet>
	
	<changeSet author="WangLuoPeng" id="ras-20220315001_eqpHoldtable">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="RAS_EQP_HLD"/>
			</not>
		</preConditions>
		
        <createTable remarks="Eqp Hold" tableName="RAS_EQP_HLD" tablespace="TS_MES_DAT">
           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_RAS_EQP_HLD"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />
			
            
            <column name="EQUIPMENT_RRN" remarks="设备主键" type="NUMBER(19, 0)"/>
            <column name="SEQ_NO" remarks="序列号" type="NUMBER(19, 0)"/>
            <column name="HOLD_USER_RRN" remarks="用户组" type="NUMBER(19, 0)"/>
            <column name="HOLD_USER_NAME" remarks="用户名" type="VARCHAR2(32 BYTE)"/>
            <column name="HOLD_CODE" remarks="hold码" type="VARCHAR2(128 BYTE)"/>
            <column name="HOLD_REASON" remarks="hold原因" type="VARCHAR2(2560 BYTE)"/>
            <column name="HOLD_PWD" remarks="hold密码" type="VARCHAR2(128 BYTE)"/>
            <column name="HOLD_OCAP_ID" remarks="hold异常单号" type="VARCHAR2(128 BYTE)"/>
            <column name="HOLD_COMMENT" remarks="hold备注" type="VARCHAR2(256 BYTE)"/>
            <column name="HOLD_LEVEL" remarks="hold等级" type="VARCHAR2(32 BYTE)"/>
            <column name="HOLD_OWNER" remarks="hold管理者" type="VARCHAR2(32 BYTE)"/>
            <column name="HOLD_TIME" remarks="hold时间" type="date" />
            <column name="PRE_COM_CLASS" remarks="上次操作状态大类" type="VARCHAR2(128 BYTE)"/>
            <column name="PRE_STATE" remarks="上次操作状态" type="VARCHAR2(64 BYTE)" />
            <column name="PRE_SUB_STATE" remarks="上次操作子状态" type="VARCHAR2(64 BYTE)" />
        </createTable>
        
        <createIndex indexName="IDX_RAS_EQP_HLD" tableName="RAS_EQP_HLD" tablespace="TS_MES_IDX" unique="false">
             <column name="EQUIPMENT_RRN"/>
        </createIndex>
    </changeSet>
    
    <changeSet id="WIP-20220316-001_eqphold_adtable" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
		 <sqlCheck expectedResult="0">
			select count(*) from ad_table t where t.name = 'RasEquipmentHoldInfo'		 
		 </sqlCheck>
		</preConditions>
       <sqlFile path="sql/RAS-20220316-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>eqphold adtable</comment>
	</changeSet>
	
	<changeSet id="WIP-20220316-002-AD_FORM" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220316-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次分批sort动态页面更新</comment>
	</changeSet>
	
	<changeSet author="HeTao" id="WIP-20220515-RAS_EQP_BATCH_CONTROL">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="RAS_EQP_BATCH_CONTROL"/>
			</not>
		</preConditions>
        <createTable remarks="设备Batch控制表" tableName="RAS_EQP_BATCH_CONTROL" tablespace="TS_MES_DAT">
           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_RAS_EQP_BATCH_CONTROL"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />
            
            <column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
            <column name="EQUIPMENT_RRN" remarks="设备RRN" type="NUMBER(19, 0)"/>
            <column name="CONTROL_QTY_TYPE" remarks="控制类型" type="VARCHAR2(32 BYTE)"/>
            
            <column name="BATCH_SIZE_MIN" remarks="最小值" type="NUMBER(19, 0)"/>
            <column name="BATCH_SIZE_MAX" remarks="最大值" type="NUMBER(19, 0)"/>
        </createTable>
    </changeSet>
    
   	<changeSet id="WIP-20220316-003-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220316-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充英译中和批次未来动作查询添加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220317-001-ADD_FUTURE_ACTION_HIS_QUERY" author="YanJiaZhi">
		<sqlFile path="sql/WIP-20220317-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>增加未来动作设置历史查询功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220317-002-UPDATE_FUTURE_ACTION_HIS_QUERY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220317-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改未来动作设置历史查询功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220317-001_port_change_state" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
		 <sqlCheck expectedResult="0">
			select count(*) from ad_form t where t.name = 'RASPortLogEventForm'		 
		 </sqlCheck>
		</preConditions>
       <sqlFile path="sql/RAS-20220317-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>port change state</comment>
	</changeSet>
	
	<changeSet id="WIP-20220318-001-UPDATE_EQUIPMENT" author="WangJiaXin">
		<sqlFile path="sql/RAS-20220318-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改设备界面显示，增加Batch控制Tab</comment>
	</changeSet>
	
	<changeSet id="WIP-20220321-001-UPDATE_EQUIPMENT" author="WangJiaXin">
		<sqlFile path="sql/RAS-20220321-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备界面显示补充SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20220321-001-UPDATE_AD_TAB" author="DaiWenBin">
		<sql>update ad_tab set TAB_TYPE = 'Group',LABEL = 'QTime setting (optional)',LABEL_ZH = 'QTime设置（非必须）' where OBJECT_RRN = 151000348268470272</sql>		
		<comment>设备界面显示补充SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20220321-002-UPDATE_EQUIPMENT" author="WangJiaXin">
		<sqlFile path="sql/RAS-20220321-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备界面显示字段名称调整</comment>
	</changeSet>
	
		
	<changeSet id="WIP-20220322-001-UPDATE_AD_FIELD" author="DaiWenBin">
		<sql>update ad_field set LABEL = 'EIN ID',LABEL_ZH = 'EIN 编号' where OBJECT_RRN = 181451895670378496;</sql>		
		<comment>未来动作查询runcard字段描述修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220322-001-UPDATE_EQUIPMENT_ACTION" author="WangLuoPeng">
		<sqlFile path="sql/RAS-20220322-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备Hold动态表调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20220322-002-UPDATE_AD_FIELD" author="WangJiaXin">
		<sql>update ad_field set SEQ_NO = '159' where OBJECT_RRN = 368232101;</sql>		
		<comment>设备IsBatch字段显示位置修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220322-003-UPDATE_AD_MESSAGE" author="WangJiaXin">
		<sqlFile path="sql/WIP-20220322-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次进站校验报错信息修改</comment>
	</changeSet>
	
	<changeSet id="RAS-20220323-001-ADD_RETICLE_CONTEXT" author="FanChengMing">
		<sqlFile path="sql/RAS-20220323-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>独立出来Reticle Context页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20220323-001-SORTINGJOB-ADD-COLOUM" author="WangJiaXin">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_SORTING_JOB" columnName="HOLD_STATE"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_SORTING_JOB">
			<column name="HOLD_STATE" remarks="暂停状态" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>SortingJob表新增字段</comment>
	</changeSet>
	<changeSet id="WIP-20220323-002-LOTHIS-ADD-COLOUM" author="MOYOUMING">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_HIS" columnName="POSITION"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_HIS">
			<column name="POSITION" remarks="POSITION" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>LOTHIS增加position栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20220324-001-UPDATE_LOT_SCRAP_UI" author="YanJanZhi">
		<sqlFile path="sql/WIP-20220324-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Lot Scrap UI修改</comment>
	</changeSet>

	<changeSet author="DaiWenBin" id="RAS-20220322-RAS_EQP_MAT_TYPE">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="RAS_EQP_MAT_TYPE"/>
			</not>
		</preConditions>
        <createTable remarks="" tableName="RAS_EQP_MAT_TYPE" tablespace="TS_MES_DAT">
           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_RAS_EQP_MAT_TYPE"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />
            
            <column name="MAIN_MAT_TYPE" remarks="主物料类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SUB_MAT_TYPE" remarks="子物料类型" type="VARCHAR2(32 BYTE)"/>
            <column name="EQUIPMENT_RRN" remarks="设备Rrn" type="NUMBER(19, 0)"/>
            <column name="EQUIPMENT_ID" remarks="设备Id" type="VARCHAR2(32 BYTE)"/>
        </createTable>
    </changeSet>
    
    <changeSet id="RAS-20220324-001-UPDATE_CARRIER_CLEAN_CONSTRAINT" author="DaiWenBin">
		<sqlFile path="sql/RAS-20220324-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>载具清洗限制新功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220324-002-UPDATE_HOLD_REASON" author="YanJanZhi">
		<sqlFile path="sql/WIP-20220324-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Hold Reason 栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20220325-001-ADD_LOT_SCRAP_ACTION_CODE_NAME" author="YanJanZhi">
		<sqlFile path="sql/WIP-20220325-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Lot Scrap 新增动作码名称</comment>
	</changeSet>
	
	<changeSet author="HeTao" id="EDC-20220324-EDC_SET_LINE_FORMULA_VARIABLE">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="EDC_SET_LINE_FORMULA_VARIABLE"/>
			</not>
		</preConditions>
        <createTable remarks="数据收集公式表" tableName="EDC_SET_LINE_FORMULA_VARIABLE" tablespace="TS_MES_DAT">
           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_EDC_SET_LINE_FORMULA_VARIABLE"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
            
            <column name="EDC_SET_LINE_RRN" remarks="采集箱集Line RRN" type="NUMBER(19, 0)"/>
            <column name="VARIABLE_NAME" remarks="变量名称" type="VARCHAR2(32 BYTE)"/>
            <column name="PROCESS_NAME" remarks="采集项集名称" type="VARCHAR2(32 BYTE)"/>
            
            <column name="ITEM_NAME" remarks="采集项Line Name" type="VARCHAR2(32 BYTE)"/>
            <column name="ITEM_STR" remarks="Item点位" type="VARCHAR2(32 BYTE)"/>
            <column name="COMP_STR" remarks="COMP点位" type="VARCHAR2(32 BYTE)"/>
            <column name="SITE_STR" remarks="Site点位" type="VARCHAR2(32 BYTE)"/>
            <column name="BUMP_STR" remarks="BUMP点位" type="VARCHAR2(32 BYTE)"/>
            <column name="ATTRIBUTE_NAME" remarks="参数名" type="VARCHAR2(32 BYTE)"/>
        </createTable>
    </changeSet>
    
    <changeSet id="WIP-************-TRACKIN-BATCH-CHECK" author="HeTao">
		<sqlFile path="sql/WIP-************.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Track In新增batch控制，设置参数可以控制batch必须是完整batch进站</comment>
	</changeSet>
	
	<changeSet id="WIP-************-SOLVE_GARBLED_CHARACTERS" author="YanJiaZhi">
		<sqlFile path="sql/WIP-************.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>解决乱码</comment>
	</changeSet>
	
	<changeSet id="WIP-************_abnormal_sortingjob" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
		 <sqlCheck expectedResult="0">
			select count(*) from ad_form t where t.name = 'WIPAbnormalSortingJobFormGlc'		 
		 </sqlCheck>
		</preConditions>
       <sqlFile path="sql/WIP-************.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>port change state</comment>
	</changeSet>
	
	<changeSet id="WIP-************_abnormal_sortingjob" author="WangLuoPeng">
       <sqlFile path="sql/WIP-************.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>abnormal_sortingjob button</comment>
	</changeSet>
	
	<changeSet id="WIP-************_UPDATE_HOLD_REASON" author="YanJiaZhi">
       <sqlFile path="sql/WIP-************.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Hold Reason 字段长度修改</comment>
	</changeSet>
	
	<changeSet id="WIP-************_UPDATE_LOT_SCRAP" author="YanJiaZhi">
       <sqlFile path="sql/WIP-************.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Lot Scrap UI修改</comment>
	</changeSet>
	
	<changeSet id="WIP-************_ADD_MESSAGE" author="DaiWenBin">
       <sqlFile path="sql/WIP-************.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="EDC-************_UPDATE_AD_REFTABLE" author="DaiWenBin">
       <sql>update ad_reftable set where_clause = 'status In (''Active'',''InActive'')' where name = 'EDCItemSetNameListQuery';</sql>
       <comment>修改EDCData查询条件的动态栏位参考表</comment>
	</changeSet>
	
	<changeSet id="WIP-************_LOTFUTUREQUERY_ADD_FIELD" author="zhougelong">
       <sqlFile path="sql/WIP-20220329-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次未来动作查询新增用户与部门显示</comment>
	</changeSet>
	
	<changeSet id="RAS-RAS-************_ADD_RETICLE_CONTEXT_TABLE" author="FanChengMing">
       <sqlFile path="sql/RAS-************.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>补充reticleContextTable</comment>
	</changeSet>
	
	<changeSet id="WIP-20220329-003_UPDATE_LOT_SCRAP" author="YanJiaZhi">
       <sqlFile path="sql/WIP-20220329-003.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Scrap Code字段修改成Action Code</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220329-EDC_PROCESS_GROUP">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="EDC_PROCESS_GROUP"/>
			</not>
		</preConditions>
		<createTable remarks="制程工步和量测工步关联表" tableName="EDC_PROCESS_GROUP" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_EDC_PROCESS_GROUP"
							 primaryKeyTablespace="TS_MES_IDX" />
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />

			<column name="GROUP_ID" remarks="组ID" type="VARCHAR2(32 BYTE)"/>
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_TYPE" remarks="工艺类型（P/M）" type="VARCHAR2(1 BYTE)"/>
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
			<column name="PROCESS" remarks="工艺名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_VERSION" remarks="工艺版本" type="NUMBER(19, 0)"/>
			<column name="PART" remarks="产品名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PART_VERSION" remarks="产品版本" type="NUMBER(19, 0)"/>
			<column name="PROCEDURE" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_VERSION" remarks="流程版本" type="NUMBER(19, 0)"/>
		</createTable>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220329-EDC_PROCESS_GROUP_DATA">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="EDC_PROCESS_GROUP_DATA"/>
			</not>
		</preConditions>
		<createTable remarks="工步加工数据表" tableName="EDC_PROCESS_GROUP_DATA" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_EDC_PROCESS_GROUP_DATA"
							 primaryKeyTablespace="TS_MES_IDX" />
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />

			<column name="GROUP_ID" remarks="组ID" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_TYPE" remarks="工艺类型" type="VARCHAR2(1 BYTE)"/>
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
			<column name="LOT_RRN" remarks="批次RRN" type="NUMBER(19, 0)" />
			<column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)"/>
			<column name="ROOT_LOT_ID" remarks="" type="VARCHAR2(32 BYTE)"/>
			<column name="COMPONENT_ID" remarks="" type="VARCHAR2(32 BYTE)"/>
			<column name="BATCH_ID" remarks="" type="VARCHAR2(32 BYTE)"/>
			<column name="BATCH_LOTS" remarks="" type="VARCHAR2(255 BYTE)"/>
			<column name="ITEM_SET_NAME" remarks="" type="VARCHAR2(32 BYTE)"/>
			<column name="ITEM_SET_VERSION" remarks="" type="NUMBER(19, 0)" />
			<column name="EQUIPMENT" remarks="" type="VARCHAR2(32 BYTE)"/>
			<column name="OPERATOR" remarks="" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_TIME" remarks="" type="date" />
			<column name="RECEIVE_TIME" remarks="" type="date" />
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)"/>
			<column name="PROCESS" remarks="工艺名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_VERSION" remarks="工艺版本" type="NUMBER(19, 0)"/>
			<column name="PART" remarks="产品名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PART_VERSION" remarks="产品版本" type="NUMBER(19, 0)"/>
			<column name="PROCEDURE" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_VERSION" remarks="流程版本" type="NUMBER(19, 0)"/>
			<column name="LOGIC_RECIPE_NAME" remarks="RECIPE" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_RECIPE_NAME" remarks="PPID" type="VARCHAR2(32 BYTE)"/>
			<column name="TEXT" remarks="预留" type="CLOB"/>
		</createTable>
	</changeSet>

	<changeSet author="LiTao" id="EDC-************-EDC_DATA_ADD">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_DATA" columnName="PROCESS_TIME"/>
				<columnExists tableName="EDC_DATA" columnName="PROCESS_STEP_NAME"/>
				<columnExists tableName="EDC_DATA" columnName="PROCESS_OPERATOR"/>
			</not>
		</preConditions>

		<addColumn tableName="EDC_DATA">
			<column name="PROCESS_TIME" remarks="生产时间" type="date"/>
			<column name="PROCESS_STEP_NAME" remarks="生产工步" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_OPERATOR" remarks="生产人员" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>EDCData增加栏位</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-************-EDC_DATA_HIS_ADD">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_DATA_HIS" columnName="PROCESS_TIME"/>
				<columnExists tableName="EDC_DATA_HIS" columnName="PROCESS_STEP_NAME"/>
				<columnExists tableName="EDC_DATA_HIS" columnName="PROCESS_OPERATOR"/>
			</not>
		</preConditions>

		<addColumn tableName="EDC_DATA_HIS">
			<column name="PROCESS_TIME" remarks="生产时间" type="date"/>
			<column name="PROCESS_STEP_NAME" remarks="生产工步" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_OPERATOR" remarks="生产人员" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>EDCData历史增加栏位</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-************_PROCESS_GROUP">
		<sqlFile path="sql/EDC-************.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>制程工步与量测工步关联功能</comment>
	</changeSet>
	
	<changeSet id="CHJS-20220328-005-ADD_REQUIRE_PREPARE_INFO_EQPS_SQL" author="ZouBinjie">
		<sqlFile path="sql/WIP-20220328-005.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>LotPrepare时发送Lot信息给设备</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220330-001_PROCESS_GROUP">
		<sqlFile path="sql/EDC-20220330-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>制程工步与量测工步关联--使用系统的导出</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220330-EDC_PROCESS_GROUP_IDX">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_EDC_PROCESS_GROUP_PART" tableName="EDC_PROCESS_GROUP"/>
			</not>
		</preConditions>

		<createIndex indexName="IDX_EDC_PROCESS_GROUP_PART" tableName="EDC_PROCESS_GROUP" tablespace="TS_MES_IDX" unique="false">
			<column name="PART"/>
			<column name="GROUP_ID"/>
			<column name="PROCEDURE"/>
			<column name="STEP_NAME"/>
		</createIndex>

		<createIndex indexName="IDX_EDC_PROCESS_GROUP_DATA_LOT" tableName="EDC_PROCESS_GROUP_DATA" tablespace="TS_MES_IDX" unique="false">
			<column name="GROUP_ID"/>
			<column name="LOT_RRN"/>
		</createIndex>

		<createIndex indexName="IDX_EDC_PROCESS_GROUP_DATA_GROUP" tableName="EDC_PROCESS_GROUP_DATA" tablespace="TS_MES_IDX" unique="false">
			<column name="GROUP_ID"/>
		</createIndex>
	</changeSet>
	
	<changeSet author="HeTao" id="EDC-20220331-010-EDC-FORMULA">
		<sqlFile path="sql/EDC-20220331-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>数据收集支持设置公式</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220331-020_PROCESS_GROUP">
		<sqlFile path="sql/EDC-20220331-020.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>制程工步与量测工步关联--补充form attribute</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220401-010_PROCESS_GROUP">
		<sqlFile path="sql/EDC-20220401-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>制程工步与量测工步关联--补充export template mapping</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220404-001_PROCESS_GROUP">
		<sqlFile path="sql/EDC-20220404-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>edcDataQuery增加生产站点，生产时间，生产设备栏位</comment>
	</changeSet>

	<changeSet author="LiTao" id="WIP-20220404-001_RE_COMMIT">
		<sqlFile path="sql/WIP-20220404-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>重提交王骆鹏提交的被覆盖的sql</comment>
	</changeSet>
	
	<changeSet id="WIP-20220406-010_special_skip" author="WangLuoPeng">
       <sqlFile path="sql/WIP-20220406-010.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Special Skip</comment>
	</changeSet>
	
	<changeSet id="WIP-20220406-011_special_skip" author="WangLuoPeng">
       <sqlFile path="sql/WIP-20220406-011.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Special Skip</comment>
	</changeSet>
	<changeSet author="zoubinjie" id="WIP-20220407-010-_ADD">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_SORTING_JOB_HIS" columnName="ACTION_CODE"/>
				<columnExists tableName="WIP_LOT_SORTING_JOB_HIS" columnName="ACTION_REASON"/>
				<columnExists tableName="WIP_LOT_SORTING_JOB_HIS" columnName="ACTION_COMMENT"/>
				<columnExists tableName="WIP_LOT_SORTING_JOB_HIS" columnName="HIS_COMMENT"/>
				<columnExists tableName="WIP_LOT_SORTING_JOB_HIS" columnName="OCAPID"/>
			</not>
		</preConditions>
		<addColumn tableName="WIP_LOT_SORTING_JOB_HIS">
			<column name="ACTION_CODE" remarks="动作代码" type="VARCHAR2(128 BYTE)"/>
			<column name="ACTION_REASON" remarks="动作原因" type="VARCHAR2(2560 BYTE)"/>
			<column name="ACTION_COMMENT" remarks="动作备注" type="VARCHAR2(256 BYTE)"/>
			<column name="HIS_COMMENT" remarks="历史备注" type="VARCHAR2(256 BYTE)"/>
			<column name="OCAP_ID" remarks="异常单号" type="VARCHAR2(128 BYTE)"/>
			<column name="HOLD_STATE" remarks="HOLD状态" type="VARCHAR2(32 BYTE)"/>
			<column name="OPERATOR1" remarks="操作人" type="VARCHAR2(100 BYTE)"/>
		</addColumn>
		<comment>WIP_LOT_SORTING_JOB_HIS增加栏位</comment>
	</changeSet>
	<changeSet id="RAS-20220407-001_CARRIER_CLEAN" author="zhougelong">
       <sqlFile path="sql/RAS-20220407-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>按设备清洗载具功能新增</comment>
	</changeSet>
	
	
	<changeSet author="zhougelong" id="RAS-20220407-MM_CARRIER_PREPARE">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="MM_CARRIER_PREPARE"/>
			</not>
		</preConditions>
		<createTable remarks="载具清洗任务表" tableName="MM_CARRIER_PREPARE" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_CARRIER_PREPARE"
							 primaryKeyTablespace="TS_MES_IDX" />
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />

			<column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
			<column name="JOB_ID" remarks="预约job号" type="VARCHAR2(32 BYTE)"/>
			<column name="JOB_STATE" remarks="预约job状态" type="VARCHAR2(32 BYTE)"/>
			<column name="DURABLE_ID" remarks="载具状态" type="VARCHAR2(32 BYTE)" />
			<column name="EQUIPMENT_RECIPE" remarks="设备recipe" type="VARCHAR2(32 BYTE)"/>
		</createTable>
	</changeSet>

	<changeSet id="WIP-20220406-011_ADD_AD_FIELD" author="DaiWenBin">
       <sqlFile path="sql/WIP-20220407-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>载具分批添加动作备注栏位</comment>
	</changeSet>

	<changeSet author="DaiWenBin" id="WIP-20220407-002-LOTHISSMC-ADDCOLUMN">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_SMC_HIS" columnName="FROM_SLOT"/>
			</not>
		</preConditions>
	    <addColumn tableName="WIP_SMC_HIS" >
	        <column name="FROM_SLOT" remarks="源组件号" type="VARCHAR2(32 BYTE)"/>
	        <column name="FROM_DURABLE" remarks="源载具号" type="VARCHAR2(32 BYTE)"/>
	        <column name="TO_SLOT" remarks="目标组件号" type="VARCHAR2(32 BYTE)"/>
	        <column name="TO_DURABLE" remarks="目标组件号" type="VARCHAR2(32 BYTE)"/>
	    </addColumn>
	</changeSet>
	
	<changeSet author="HeTao" id="EDC-20220401-WIP_LOT_BATCH_JOB">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_LOT_BATCH_JOB"/>
			</not>
		</preConditions>
		<createTable remarks="Lot Batch Job" tableName="WIP_LOT_BATCH_JOB" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_BATCH_JOB"
							 primaryKeyTablespace="TS_MES_IDX" />
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />

			<column name="BATCH_ID" remarks="batch ID" type="VARCHAR2(32 BYTE)"/>
			<column name="BATCH_TYPE" remarks="batch类型" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_TYPE" remarks="批次类型" type="VARCHAR2(32 BYTE)"/>
			<column name="STATE" remarks="状态" type="VARCHAR2(32 BYTE)"/>
			<column name="FROM_STEP_NAME" remarks="来源工步名" type="VARCHAR2(32 BYTE)"/>
			<column name="FROM_EQUIPMENT_ID" remarks="来源设备号" type="VARCHAR2(32 BYTE)"/>
			<column name="FROM_EQUIPMENT_RECIPE" remarks="来源设备PPID" type="VARCHAR2(32 BYTE)"/>
			<column name="RESUME_STEP_NAME" remarks="恢复工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="RESUME_PATH" remarks="恢复工步Path" type="VARCHAR2(128 BYTE)"/>
			<column name="MEASURE_STEP_NAME" remarks="量测工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="MEASURE_PATH" remarks="量测工步Path" type="VARCHAR2(128 BYTE)"/>
			<column name="MEASURE_FLAG" remarks="是否量测批次" type="VARCHAR2(1 BYTE)"/>
		</createTable>
		
		<createIndex indexName="IDX_WIP_LOT_BATCH_JOB_BATCHID" tableName="WIP_LOT_BATCH_JOB" tablespace="TS_MES_IDX" unique="false">
			<column name="BATCH_ID"/>
		</createIndex>
		
		<createIndex indexName="IDX_WIP_LOT_BATCH_JOB_LOTID" tableName="WIP_LOT_BATCH_JOB" tablespace="TS_MES_IDX" unique="true">
			<column name="LOT_ID"/>
		</createIndex>
	</changeSet>
	
	<changeSet author="HeTao" id="EDC-20220401-WIP_LOT_BATCH_JOB_HIS">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_LOT_BATCH_JOB_HIS"/>
			</not>
		</preConditions>
		<createTable remarks="Lot Batch Job His" tableName="WIP_LOT_BATCH_JOB_HIS" tablespace="TS_MES_HIS_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TIME" remarks="创建时间" type="date"/>
			<column name="HISTORY_SEQ" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
		    <column name="HISTORY_SEQ_NO" remarks="事务序列号" type="NUMBER(19, 0)"/>

			<column name="BATCH_ID" remarks="batch ID" type="VARCHAR2(32 BYTE)"/>
			<column name="BATCH_TYPE" remarks="batch类型" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_TYPE" remarks="批次类型" type="VARCHAR2(32 BYTE)"/>
			<column name="STATE" remarks="状态" type="VARCHAR2(32 BYTE)"/>
			<column name="FROM_STEP_NAME" remarks="来源工步名" type="VARCHAR2(32 BYTE)"/>
			<column name="FROM_EQUIPMENT_ID" remarks="来源设备号" type="VARCHAR2(32 BYTE)"/>
			<column name="FROM_EQUIPMENT_RECIPE" remarks="来源设备PPID" type="VARCHAR2(32 BYTE)"/>
			<column name="RESUME_STEP_NAME" remarks="恢复工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="RESUME_PATH" remarks="恢复工步Path" type="VARCHAR2(128 BYTE)"/>
			<column name="MEASURE_STEP_NAME" remarks="量测工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="MEASURE_PATH" remarks="量测工步Path" type="VARCHAR2(128 BYTE)"/>
			<column name="MEASURE_FLAG" remarks="是否量测批次" type="VARCHAR2(1 BYTE)"/>
		</createTable>
		
		<createIndex indexName="IDX_WIP_LOT_BATCH_JOB_HIS_BATCHID" tableName="WIP_LOT_BATCH_JOB_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
			<column name="BATCH_ID"/>
		</createIndex>
	</changeSet>
	
	<changeSet id="WIP-20220407-010_batch-job" author="HeTao">
       <sqlFile path="sql/WIP-20220407-010.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Batch Job</comment>
	</changeSet>
	
	<changeSet id="WIP-20220411-001_AD_FIELD" author="DaiWenBin">
       <sqlFile path="sql/WIP-20220411-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>修改栏位属性，补充用户栏位参考名称</comment>
	</changeSet>
	
	<changeSet id="EDC-20220412-001_EDC_FORMULA" author="HeTao">
       <sqlFile path="sql/EDC-20220412-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>EDC Formula SQL补充</comment>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20220411-002-MODIFYDATATYPE">
	    <modifyDataType columnName="HOLD_OWNER" newDataType="VARCHAR2(64 BYTE)" tableName="WIP_FUTURE_ACTION"/>
	    <modifyDataType columnName="HOLD_OWNER" newDataType="VARCHAR2(64 BYTE)" tableName="WIP_FUTURE_ACTION_HIS"/>
	</changeSet>
	
	<changeSet id="WIP-20220413-001_AD_MESSAGE" author="DaiWenBin">
       <sqlFile path="sql/WIP-20220413-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>补充英译中</comment>
	</changeSet>
	
	  <changeSet id="WIP-20220413-010_FUTUREACTION_DIALOG" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
		 <sqlCheck expectedResult="0">
			select count(*) from ad_table t where t.name = 'WIPLotFutureActionDialog'		 
		 </sqlCheck>
		</preConditions>
       <sqlFile path="sql/WIP-20220413-010.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>FutureActionDialog</comment>
	</changeSet>
	
	<changeSet id="WIP-20220414-020_LOT_PREPARE" author="HeTao">
       <sqlFile path="sql/WIP-20220414-020.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Lot Prepare优化并增加Chamber设备可选择PPID</comment>
	</changeSet>
	
	<changeSet id="MM-20220415-01_WAREHOUSE_EQP" author="WangLuoPeng">
       <sqlFile path="sql/MM-20220415-010.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>迁移设备仓库关联</comment>
	</changeSet>
	
	<changeSet author="HeTao" id="WIP-20220414-021-LOT-ADDCOLUMN">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT" columnName="EQUIPMENT_MASK"/>
			</not>
		</preConditions>
	    <addColumn tableName="WIP_LOT" >
	        <column name="EQUIPMENT_MASK" remarks="光刻版" type="VARCHAR2(32 BYTE)"/>
	        <column name="EQUIPMENT_RECIPE" remarks="PPID" type="VARCHAR2(32 BYTE)"/>
	    </addColumn>
	    
	    <addColumn tableName="WIP_LOT_HIS" >
	        <column name="EQUIPMENT_MASK" remarks="光刻版" type="VARCHAR2(32 BYTE)"/>
	        <column name="EQUIPMENT_RECIPE" remarks="PPID" type="VARCHAR2(32 BYTE)"/>
	    </addColumn>
	</changeSet>
	
	<changeSet id="PRD-20220414-001_ADD_STEP_BULK_OPERATIONS" author="YanJiaZhi">
       <sqlFile path="sql/PRD-20220414-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>新增工步状态批量操作功能</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220419-012-EDC_PROCESS_GROUP_DATA-ADDCOLUMN">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_PROCESS_GROUP_DATA" columnName="RETICLE"/>
			</not>
		</preConditions>
		<addColumn tableName="EDC_PROCESS_GROUP_DATA" >
			<column name="RETICLE" remarks="光刻版" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220419-013-EDC_PROCESS_GROUP-ADDCOLUMN">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_PROCESS_GROUP" columnName="CATEGORY"/>
			</not>
		</preConditions>

		<addColumn tableName="EDC_PROCESS_GROUP" >
			<column name="CATEGORY" remarks="group类型(PM/MC)" type="VARCHAR2(16 BYTE)"/>
		</addColumn>

		<insert tableName="EDC_PROCESS_GROUP">
			<column name="CATEGORY" value="PM" />
		</insert>

		<comment>EDC_PROCESS_GROUP增加category栏位，之前的数据category为PM</comment>
	</changeSet>

	<changeSet author="LiTao" id="EDC-20220419-014_ADD_PROCESS_GROUP_CATEGORY">
		<sqlFile path="sql/EDC-20220419-014.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>制程工步和量测工步关联表增加Category栏位，相应的方法和页面需要调整</comment>
	</changeSet>
	
	<changeSet author="HeTao" id="WIP-20220418-021-LOT_PREPARE-ADDCOLUMN">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_PREPARE" columnName="LAYOUT_RECIPE"/>
			</not>
		</preConditions>
	    <addColumn tableName="WIP_LOT_PREPARE" >
	        <column name="LAYOUT_RECIPE" remarks="layout recipe" type="VARCHAR2(32 BYTE)"/>
	    </addColumn>
	    <comment>作业准备表中新增layout recipe</comment>
	</changeSet>
	
	<changeSet id="WIP-20220419-010_LOT_PREPARE" author="HeTao">
       <sqlFile path="sql/WIP-20220419-010.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Lot Prepare增加layout recipe显示</comment>
	</changeSet>
	
	<changeSet id="EDC-20220420-001_FORMULA" author="HeTao">
       <sqlFile path="sql/EDC-20220420-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>EDC Formula Variable增加正则校验</comment>
	</changeSet>
	
	<changeSet id="WIP-20220420-001_TRACKOUTHISTORY" author="FanChengMing">
       <sqlFile path="sql/WIP-20220420-010.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次加工历史栏位优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20220422-ADD-EQUIPMENTALARM-INDEX" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_RAS_EQP_ALARM_EQUIPMENT_ID" tableName="RAS_EQP_ALARM"/>
			</not>
		</preConditions>
        
        <createIndex indexName="IDX_RAS_EQP_ALARM_EQUIPMENT_ID" tableName="RAS_EQP_ALARM" tablespace="TS_MES_IDX" unique="false">
             <column name="EQUIPMENT_ID"/>
        </createIndex>
        <comment>增加EquipmentAlarm索引</comment>
    </changeSet>
    
	
	<changeSet id="WIP-20220422-011-CHANGE-PRIORITY" author="HeTao">
       <sqlFile path="sql/WIP-20220422-011.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>修改批次优先级优化，改为动态表配置</comment>
	</changeSet>
	
	<changeSet id="EDC-20220424-001-EDC-IMPORT" author="FanChengMing">
       <sqlFile path="sql/EDC-20220424-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>EDC采集项集合导入支持导入采集项</comment>
	</changeSet>
	
	<changeSet id="WIP-20220425-020-LOT-FUTURE-EQP-QUERY" author="HeTao">
       <sqlFile path="sql/WIP-20220425-020.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次未来设备查询优化</comment>
	</changeSet>
	
	<changeSet id="RAS-20220426-001-EQUIPMENT_VIEW" author="YanChao">
       <sqlFile path="sql/RAS-20220426-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>解决设备视图功能显示问题</comment>
	</changeSet>

	<changeSet id="WIP-20220428-001-BATCH-JOB" author="HeTao">
       <sqlFile path="sql/WIP-20220428-020.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>batch job功能完善</comment>
	</changeSet>
	
	<changeSet author="Clark" id="WIP-20220428-001_ADDCOLUMN_MATERIAL_REQ">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_WO_MATERIAL_REQ" columnName="WAREHOUSE_ID"/>
			</not>
		</preConditions>
		<addColumn tableName="WIP_WO_MATERIAL_REQ">  
        	<column name="WAREHOUSE_ID" remarks="仓库ID" type="VARCHAR2(32 BYTE)"/>
        	<column name="REQUIRE_DATE" remarks="需求时间" type="date"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet author="Clark" id="WIP-20220428-002_ADDCOLUMN_MATERIAL_REQ_LINE">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_WO_MATERIAL_REQ_LINE" columnName="EQUIPMENT_ID"/>
			</not>
		</preConditions>
		<addColumn tableName="WIP_WO_MATERIAL_REQ_LINE">  
        	<column name="EQUIPMENT_ID" remarks="设备ID" type="VARCHAR2(32 BYTE)"/>
        	<column name="UNIT_ID" remarks="设备UNITID" type="VARCHAR2(32 BYTE)"/>
        	<column name="PORT_ID" remarks="Port口" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet author="Clark" id="WIP-20220428-003_ADDCOLUMN_MATERIAL_REQ_DETAIL">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_WO_MATERIAL_REQ_DETAIL" columnName="DURABLE_ID"/>
			</not>
		</preConditions>
		<addColumn tableName="WIP_WO_MATERIAL_REQ_DETAIL">  
        	<column name="DURABLE_ID" remarks="载具" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20220428-004_ADDTABLE_WAREHOUSE_MATERIAL" author="Clark">	
	       <createTable remarks="仓库物料管理表" tableName="MM_WAREHOUSE_MATERIAL" tablespace="TS_MES_DAT">
	           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                  <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_WAREHOUSE_MATERIAL"/>
               </column>	
                   <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
                   <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
                   <column name="CREATED" remarks="创建时间" type="date"/>
                   <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
                   <column name="UPDATED" remarks="更新时间" type="date"/>
                   <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
                   <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
                   <column name="WAREHOUSE_RRN" remarks="仓库RRN" type="NUMBER(19, 0)"/>
                   <column name="WAREHOUSE_ID" remarks="仓库ID" type="VARCHAR2(32 BYTE)"/>
                   <column name="MATERIAL_RRN" remarks="物料RRN" type="NUMBER(19)"/>
                   <column name="MATERIAL_NAME" remarks="物料名称" type="VARCHAR2(32 BYTE)"/>
                   <column name="COUNT_TYPE" remarks="统计库存类型Durable/Lot/MainQty" type="VARCHAR2(32 BYTE)"/>
                   <column name="ALM_STOCK_QTY" remarks="预警库存数量" type="NUMBER(19, 0)"/>
			       <column name="SAFETY_STOCK_QTY" remarks="安全库存数量" type="NUMBER(19, 0)"/>
		       	   <column name="MAX_STOCK_QTY" remarks="最大库存数量" type="NUMBER(19, 0)"/>
		       	   <column name="STORAGE_TIME" remarks="存放时间(缓冲时间)" type="NUMBER(19, 0)"/>
		       	   <column name="MAX_STORAGE_TIME" remarks="最大存放时间" type="NUMBER(19, 0)"/>
		       	   <column name="STORAGE_TIME_UNIT" remarks="存放时间单位" type="VARCHAR2(32 BYTE)"/>
              </createTable>
    </changeSet>
    
	<changeSet id="WIP-20220504-001-AD_MESSAGE" author="DaiWenBin">
       <sqlFile path="sql/WIP-20220504-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>补充Admessage</comment>
	</changeSet>
	
	<changeSet id="MM-20220505-002-MATERIAL-WAREHOUSE-RULE" author="tangjiacheng">
       <sqlFile path="sql/MM-20220505-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>物料仓库预警</comment>
	</changeSet>
	
	<changeSet id="RAS-20220506-001-ADD-WAFER-MARKING-EQP-TYPE" author="HeTao">
       <sqlFile path="sql/RAS-20220506-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>增加打标机设备类型的用户栏位参考值</comment>
	</changeSet>
	
	<changeSet id="RAS-20220506-002-DROP-FOREIGNKEY" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<foreignKeyConstraintExists foreignKeyName="FK_WIP_LOT_EQUIPMENT" foreignKeyTableName="WIP_LOT"/>
		</preConditions>
		
		<dropForeignKeyConstraint baseTableName="WIP_LOT" constraintName="FK_WIP_LOT_EQUIPMENT"/>
		
       <comment>删除WIP_LOT表外键FK_WIP_LOT_EQUIPMENT</comment>
	</changeSet>
	
	<changeSet id="WIP-20220506-010-LOT_EXIST_WHEN_DELETE_EQUIPMENT" author="HeTao">
       <sqlFile path="sql/WIP-20220506-010.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>删除设备时校验是否正在加工批次</comment>
	</changeSet>
	
	<changeSet id="EDC-20220510-ADD-IDX_EDC_DATA_SET_NAME" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists tableName="EDC_DATA" indexName="IDX_EDC_DATA_SET_NAME"/>
			</not>
		</preConditions>
		
	    <createIndex indexName="IDX_EDC_DATA_SET_NAME" tableName="EDC_DATA" tablespace="TS_MES_IDX">
			<column name="EDC_SET_NAME"/>
			<column name="ITEM_NAME"/>
		</createIndex>
		
		<comment>EDC_DATA表增加索引，供采集数据查询功能使用</comment>
	</changeSet>
	
	<changeSet id="WIP-20220510-CHANGE_DATA_TYPE" author="WangJiaXin">
		<modifyDataType tableName="WIP_SMC_HIS" columnName="FROM_SLOT" newDataType="VARCHAR2(256 BYTE)"/>
		<comment>修改WIP_SMC_HIS字段的长度</comment>
	</changeSet>
	
	<changeSet id="WIP-20220510-CHANGE_DATA_TYPE_TO_SLOT" author="WangJiaXin">
		<modifyDataType tableName="WIP_SMC_HIS" columnName="TO_SLOT" newDataType="VARCHAR2(256 BYTE)"/>
		<comment>修改WIP_SMC_HIS表TO_SLOT字段的长度</comment>
	</changeSet>
	
	<changeSet id="WIP-20220510-001-UPDATE_AD_MESSAGE" author="WangJiaXin">
       <sqlFile path="sql/WIP-20220510-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>修改Lot Merge (Sorter) 功能的报错信息</comment>
	</changeSet>
	
	<changeSet id="EDC-20220511-001-EDC-PROCESSGROUP_CLOUMN" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="EDC_PROCESS_GROUP" columnName="CREATED"/>
		</preConditions> 
		
    	<dropColumn tableName="EDC_PROCESS_GROUP">  
        	<column name="CREATED" />
        	<column name="CREATED_BY" />
        	<column name="UPDATED" />
        	<column name="UPDATED_BY" />
        	<column name="LOCK_VERSION" />
    	</dropColumn>  
		<comment>删除不需要的ProcessGroup字段</comment>
	</changeSet>
	
	<changeSet author="FanChengMing" id="EDC-20220511-EDC_PROCESS_GROUP_IDX">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_EDC_PROCESS_GROUP_ID" tableName="EDC_PROCESS_GROUP"/>
			</not>
		</preConditions>
		
		<dropIndex tableName="EDC_PROCESS_GROUP" indexName="IDX_EDC_PROCESS_GROUP_PART"/>
		
		<createIndex indexName="IDX_EDC_PROCESS_GROUP_ID" tableName="EDC_PROCESS_GROUP" tablespace="TS_MES_IDX" unique="false">
			<column name="GROUP_ID"/>
		</createIndex>
		
		<createIndex indexName="IDX_EDC_PROCESS_GROUP_PART" tableName="EDC_PROCESS_GROUP" tablespace="TS_MES_IDX" unique="false">
			<column name="STEP_NAME"/>
			<column name="PROCEDURE"/>
			<column name="PROCESS"/>
			<column name="PART"/>
		</createIndex>
		
		<dropIndex tableName="EDC_PROCESS_GROUP_DATA" indexName="IDX_EDC_PROCESS_GROUP_DATA_GROUP"/>
		<dropIndex tableName="EDC_PROCESS_GROUP_DATA" indexName="IDX_EDC_PROCESS_GROUP_DATA_LOT"/>
		<createIndex indexName="IDX_EDC_PROCESS_GROUP_DATA_LOT" tableName="EDC_PROCESS_GROUP_DATA" tablespace="TS_MES_IDX" unique="false">
			<column name="LOT_RRN"/>
			<column name="GROUP_ID"/>
		</createIndex>
	</changeSet>
	
	<changeSet id="WIP-20220511-001-UPDATE_AD_BUTTON" author="DaiWenBin">
       <sqlFile path="sql/WIP-20220511-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>修改载具任务的按钮</comment>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20220512-001-ADD-UK-INDEX">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="UK_AD_USERGROUP_AUTHORITY" tableName="AD_USERGROUP_AUTHORITY"/>
			</not>
		</preConditions>
   		<createIndex indexName="UK_AD_USERGROUP_AUTHORITY" tableName="AD_USERGROUP_AUTHORITY" tablespace="TS_MES_IDX" unique="true">
        	<column name="USERGROUP_RRN"/>
        	<column name="AUTHORITY_RRN"/>
    	</createIndex>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20220517-DROP-INDEX">
		<preConditions onFail="MARK_RAN">
			<and>
				<indexExists indexName="IDX_PRD_USERGROUP_STEP_HIS" tableName="PRD_USERGROUP_STEP_HIS"/>
			</and>
			<and>
				<indexExists indexName="IDX_RAS_EQP_ALARM_HIS_EQP_ID" tableName="RAS_EQP_ALARM_HIS"/>
			</and>
			<and>
				<indexExists indexName="IDX_RAS_EQP_ALARM_HIS_UPDATED" tableName="RAS_EQP_ALARM_HIS"/>
			</and>
			<and>
    			<indexExists indexName="IDX_WF_PROCESSDEF_HIS_NAME" tableName="WF_PROCESSDEFINITION_HIS"/>
			</and>
		</preConditions>
		
    	<dropIndex indexName="IDX_PRD_USERGROUP_STEP_HIS" tableName="PRD_USERGROUP_STEP_HIS"/>
    	<dropIndex indexName="IDX_RAS_EQP_ALARM_HIS_EQP_ID" tableName="RAS_EQP_ALARM_HIS"/>
    	<dropIndex indexName="IDX_RAS_EQP_ALARM_HIS_UPDATED" tableName="RAS_EQP_ALARM_HIS"/>
    	<dropIndex indexName="IDX_WF_PROCESSDEF_HIS_NAME" tableName="WF_PROCESSDEFINITION_HIS"/>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20220512-002-ADD-UK-INDEX">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_PRD_USERGROUP_STEP_HIS" tableName="PRD_USERGROUP_STEP_HIS"/>
			</not>
		</preConditions>
   		<createIndex indexName="IDX_PRD_USERGROUP_STEP_HIS" tableName="PRD_USERGROUP_STEP_HIS" tablespace="TS_MES_HIS_DAT" unique="false">
        	<column name="USERGROUP_RRN"/>
        	<column name="STEP_NAME"/>
    	</createIndex>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20220512-003-ADD-UK-INDEX">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_RAS_EQP_ALARM_HIS_EQP_ID" tableName="RAS_EQP_ALARM_HIS"/>
			</not>
		</preConditions>
   		<createIndex indexName="IDX_RAS_EQP_ALARM_HIS_EQP_ID" tableName="RAS_EQP_ALARM_HIS" tablespace="TS_MES_HIS_DAT" unique="false">
        	<column name="EQUIPMENT_ID"/>
    	</createIndex>
    	<createIndex indexName="IDX_RAS_EQP_ALARM_HIS_UPDATED" tableName="RAS_EQP_ALARM_HIS" tablespace="TS_MES_HIS_DAT" unique="false">
        	<column name="UPDATED"/>
    	</createIndex>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20220512-004-ADD-UK-INDEX">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_WF_PROCESSDEF_HIS_NAME" tableName="WF_PROCESSDEFINITION_HIS"/>
			</not>
		</preConditions>
   		<createIndex indexName="IDX_WF_PROCESSDEF_HIS_NAME" tableName="WF_PROCESSDEFINITION_HIS" tablespace="TS_MES_HIS_DAT" unique="false">
        	<column name="NAME"/>
    	</createIndex>
	</changeSet>

	<changeSet id="EDC-20220512-010-STEP-GROUP" author="LiTao">
		<sqlFile path="sql/EDC-20220512-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>将量测工步与清洗工步关联功能与制程工步与量测工步关联功能拆开</comment>
	</changeSet>
	
	<changeSet id="WIP-20220512-100-FUTURE_ACTION_ADD_COLUMN" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
			 <sqlCheck expectedResult="1">
				select count(*) from AD_TABLE t where t.name = 'WIPADVProcedureFutureActionQuery';		 
			 </sqlCheck>
		</preConditions>
		<sqlFile path="sql/WIP-20220512-100.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>future Action显示父批次字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20220512-100-EQP_HIS_ADD_COLUMN" author="LiTao">
		<preConditions onFail="MARK_RAN">
			 <sqlCheck expectedResult="1">
				select count(*) from AD_TABLE t where t.name = 'RASEquipmentEventQuery';		 
			 </sqlCheck>
		</preConditions>
		<sqlFile path="sql/RAS-20220512-100.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment></comment>
	</changeSet>

	<changeSet id="EDC-20220513-001-STEP-GROUP-ORDER" author="LiTao">
		<sqlFile path="sql/EDC-20220513-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>调整量测工步与清洗工步关联功能字段顺序</comment>
	</changeSet>

	<changeSet id="EDC-20220513-002-STEP-SELECT-TYPE" author="LiTao">
		<sqlFile path="sql/EDC-20220513-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>量测工步与清洗工步关联功能,step选择：reftable--search</comment>
	</changeSet>
	
	<changeSet id="WIP-20220513-001-ADD_MESSAGE" author="WangJiaXin">
		<sqlFile path="sql/WIP-20220513-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加AdMessage</comment>
	</changeSet>
	
	<changeSet id="WIP-20220519-001-CHANGE-COMPALIAS" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220519-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加wafer修改别名（T7Code）功能</comment>
	</changeSet>

	<changeSet id="WIP-20220519-002-SORT-DISPLAY-COMMUNICATION-STATE" author="WuFaMing">
		<sqlFile path="sql/WIP-20220519-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>Sorter相关界面显示通信状态</comment>
	</changeSet>

	<changeSet id="WIP-20220523-001-CHANGE-CARRIER" author="WuFaMing">
		<sqlFile path="sql/WIP-20220523-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>载具变更界面布局调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20220524-001-SORTINGJOBHISQUERY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220524-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>载具任务历史查询</comment>
	</changeSet>
	
	<changeSet id="WIP-20220525-003-MLOT_ADD_COLUMN" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="MM_LOT" columnName="SLOT_ID"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_LOT">  
        	<column name="SLOT_ID" remarks="载具位置号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20220525-004-MLOTHIS_ADD_COLUMN" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="MM_LOT_HIS" columnName="SLOT_ID"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="MM_LOT_HIS">  
        	<column name="SLOT_ID" remarks="载具位置号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="BAS-20220525-001-ADD_SMAPLING_JUDGE_TYPE" author="FanChengMing">
		<sqlFile path="sql/BAS-20220525-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加抽检批次判定位置系统参数</comment>
	</changeSet>

	<changeSet id="BAS-20220527-001-ADD_DIFFUSION_EQP_TYPE_UREF" author="LiTao">
		<sqlFile path="sql/BAS-20220527-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加Diffusion设备类型（UREFNAME）</comment>
	</changeSet>
	
	<changeSet id="WIP-20220527-WIP_WO_SOURCE" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_WO_SOURCE" columnName="PRIORITY"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_WO_SOURCE">
    		<column name="PRIORITY" remarks="物料批使用优先级" type="NUMBER(19, 0)"/>
    		<column name="STARTED_MAIN_QTY" remarks="原物料批已投主数量" type="NUMBER(19, 0)"/>
    		<column name="STARTED_SUB_QTY" remarks="原物料批已投子数量" type="NUMBER(19, 0)"/>
    	</addColumn>
	</changeSet>

	<changeSet id="WIP-20220527-001-MODIFY_AD_MESSAGE" author="WuFaMing">
		<sqlFile path="sql/WIP-20220527-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>修改AD_MESSAGE</comment>
	</changeSet>
	
	<changeSet id="RAS-20220531-001-CHANGE-ADTABLE-WHERECLAUSE" author="WangJiaXin">
		<sql>UPDATE AD_TABLE SET WHERE_CLAUSE = 'category in (''MainEqp'',''Chamber'')' WHERE OBJECT_RRN = 180637080953671680 AND NAME = 'RASEquipmentQuery'</sql>
		<comment>修改设备状态查询动态表查询条件</comment>
	</changeSet>
	
	<changeSet id="WIP-20220531-001-ADD_AD_BUTTON_AUTHORITY" author="WangJiaXin">
		<sqlFile path="sql/WIP-20220531-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>RECIPE、设备recipe、ChamberRecipe界面增加弹框</comment>
	</changeSet>
	
	<changeSet id="RAS-20220531-001-CHANGE-ADFIELD" author="WangJiaXin">
		<sql>UPDATE AD_FIELD SET DISPLAY_TYPE = 'reftablecombo' WHERE OBJECT_RRN IN (369242794756218881,369242794772996096)</sql>
		<comment>修改动态栏位显示类型</comment>
	</changeSet>
	
	<changeSet id="WIP-20220531-001-CHANGE-ADTAB" author="WangJiaXin">
		<sql>UPDATE AD_TAB SET GRID_Y = '4' WHERE OBJECT_RRN = 60310 AND TABLE_RRN = 603</sql>
		<comment>修改tab显示</comment>
	</changeSet>

	<changeSet id="WIP-20220606-001-ADD_SYS_PARAM" author="WuFaMing">
		<sqlFile path="sql/WIP-20220606-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加系统参数，控制搬送时是否修改批次位置信息</comment>
	</changeSet>
	
	<changeSet id="RAS-20220608-001-UPDATE_INDEX" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<indexExists indexName="IDX_RAS_EQP_EVENT_HIS1" tableName="RAS_EQP_EVENT_HIS"/>
		</preConditions>
		
		<dropPrimaryKey tableName="RAS_EQP_EVENT_HIS" constraintName="PK_RAS_EQP_EVENT_HIS"/>
		<dropIndex tableName="RAS_EQP_EVENT_HIS" indexName="IDX_RAS_EQP_EVENT_HIS1"/>
		
		<addPrimaryKey columnNames="OBJECT_RRN" tableName="RAS_EQP_EVENT_HIS" constraintName="PK_RAS_EQP_EVENT_HIS" tablespace="TS_MES_HIS_IDX"></addPrimaryKey>
    	<createIndex indexName="IDX_RAS_EQP_EVENT_HIS1" tableName="RAS_EQP_EVENT_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
        	<column name="ORG_RRN"/>
        	<column name="EQUIPMENT_ID"/>
        	<column name="EVENT_ID"/>
    	</createIndex>
    	
    	<comment>修改索引表空间</comment>
	</changeSet>
	
	<changeSet id="RAS-20220608-001-ADD_INDEX" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_RAS_EQP_EVENT_HIS2" tableName="RAS_EQP_EVENT_HIS"/>
			</not>
		</preConditions>
   		<createIndex indexName="IDX_RAS_EQP_EVENT_HIS2" tableName="RAS_EQP_EVENT_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
        	<column name="UPDATED"/>
    	</createIndex>
    	<comment>创建时间索引</comment>
	</changeSet>
	<changeSet id="WIP-20220609-001-ADD_CHECK" author="moyouming">
		<sqlFile path="sql/WIP-20220609-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加系统参数，解除hold是是否检验owner</comment>
	</changeSet>
	
	<changeSet id="WIP-20220610-101_QTIME_IMPROVE" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20220610-101.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>QTIME 增加NOTE类型</comment>
	</changeSet>
	
	<changeSet id="WIP-20220610-102_NOTE_QTIME_CLEAR" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
			 <sqlCheck expectedResult="0">
				select count(*) from AD_FORM t where t.name = 'WIPLotNoteQtimeQueryForm';		 
			 </sqlCheck>
		</preConditions>
		<sqlFile path="sql/WIP-20220610-102.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加NOTE QTime Clear 功能</comment>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20220614-PRD_STEP_BATCH_CONTROL">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="PRD_STEP_BATCH_CONTROL"/>
			</not>
		</preConditions>
        <createTable remarks="工步Batch控制表" tableName="PRD_STEP_BATCH_CONTROL" tablespace="TS_MES_DAT">
           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_PRD_STEP_BATCH_CONTROL"
					primaryKeyTablespace="TS_MES_IDX" />
			</column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)" />
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)" />
			<column name="CREATED" remarks="创建时间" type="date" />
			<column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)" />
			<column name="UPDATED" remarks="更新时间" type="date" />
			<column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)" />
			<column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)" />
            
            <column name="STEP_RRN" remarks="工步Rrn" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)"/>
            <column name="CONTROL_QTY_TYPE" remarks="控制类型" type="VARCHAR2(32 BYTE)"/>
            
            <column name="BATCH_SIZE_MIN" remarks="最小值" type="NUMBER(19, 0)"/>
            <column name="BATCH_SIZE_MAX" remarks="最大值" type="NUMBER(19, 0)"/>
        </createTable>
    </changeSet>
    
    <changeSet id="RAS-20220614-001-STEP-ADD-COLOUM" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_PROCESSDEFINITION" columnName="CONTROL_QTY_TYPE"/>
			</not>
		</preConditions>

		<addColumn tableName="WF_PROCESSDEFINITION">
		 	<column name="CONTROL_QTY_TYPE" remarks="控制类型" type="VARCHAR2(32 BYTE)"/>
            <column name="BATCH_SIZE_MIN" remarks="最小值" type="NUMBER(19, 0)"/>
            <column name="BATCH_SIZE_MAX" remarks="最大值" type="NUMBER(19, 0)"/>
		</addColumn>
		<comment>工步添加Batch字段</comment>
	</changeSet>
	
	 <changeSet id="RAS-20220615-001-WF_PROCESSDEFINITION_HIS-ADD-COLOUM" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_PROCESSDEFINITION_HIS" columnName="HIS_COMMENT"/>
			</not>
		</preConditions>

		<addColumn tableName="WF_PROCESSDEFINITION_HIS">
		 	<column name="HIS_COMMENT" remarks="历史备注" type="VARCHAR2(128 BYTE)"/>
		</addColumn>
		<comment>添加历史备注字段</comment>
	</changeSet>

	<changeSet id="EDC-20220614-001-PROCESS_TYPE_LENGTH" author="LiTao">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="EDC_PROCESS_GROUP" columnName="PROCESS_TYPE"/>
		</preConditions>

		<modifyDataType tableName="EDC_PROCESS_GROUP" columnName="PROCESS_TYPE" newDataType="VARCHAR2(32 BYTE)"/>
		<modifyDataType tableName="EDC_PROCESS_GROUP_DATA" columnName="PROCESS_TYPE" newDataType="VARCHAR2(32 BYTE)"/>
	</changeSet>

	<changeSet id="EDC-20220614-001-ATTACH-MAPPING" author="LiTao">
		<sqlFile path="sql/EDC-20220614-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>edc attach mapping(EDC数据工步关系映射)</comment>
	</changeSet>
	
	<changeSet id="WIP-20220615-101_SPECIAL_REWORK_IMPROVE" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20220615-101.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>REWORK 增加特殊功能</comment>
	</changeSet>

	<changeSet id="WIP-20220615-001-FUTURE-ACTION-ADD-COLUMN" author="WuFaMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION" columnName="HOLD_COMMENT"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_FUTURE_ACTION">
			<column name="HOLD_COMMENT" remarks="hold备注" type="VARCHAR2(256 BYTE)"/>
		</addColumn>
		<comment>FutureAction增加HOLD_COMMENT字段</comment>
	</changeSet>

	<changeSet id="WIP-20220615-002-FUTURE-ACTION-HIS-ADD-COLUMN" author="WuFaMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION_HIS" columnName="HOLD_COMMENT"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_FUTURE_ACTION_HIS">
			<column name="HOLD_COMMENT" remarks="hold备注" type="VARCHAR2(256 BYTE)"/>
		</addColumn>
		<comment>FutureActionHis增加HOLD_COMMENT字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220615-001-ADD_AD_TABLE" author="WangJiaXin">
		<sqlFile path="sql/WIP-20220615-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加wafer明细动态表</comment>
	</changeSet>

	<changeSet id="RAS-20220616-CREATE-RAS_EQP_CURRENT" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="RAS_EQP_CURRENT"/>
			</not>
		</preConditions>
		
		<addColumn tableName="RAS_EQP_EVENT_HIS">
			<column name="CURRENT_EQUIPMENT_RECIPE" remarks="设备当前PPID" type="VARCHAR2(32 BYTE)"/>
			<column name="TARGET_EQUIPMENT_RECIPE" remarks="设备目标PPID" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		
    	<createTable remarks="Buffer中Lot、Wafer、载具信息表" tableName="RAS_EQP_CURRENT" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_RAS_EQP_CURRENT" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  
			<column name="EQUIPMENT_RRN" remarks="设备主键" type="NUMBER(19, 0)"/>
			<column name="CURRENT_RECIPE_NAME" remarks="当前Recipe名称" type="VARCHAR2(32 BYTE)"/>
			<column name="CURRENT_EQUIPMENT_RECIPE" remarks="当前PPID名称" type="VARCHAR2(32 BYTE)"/>
			<column name="CURRENT_PART_NAME" remarks="当前产品" type="VARCHAR2(32 BYTE)"/>
			<column name="CURRENT_SHEET_QTYPE" remarks="当前Sheet Q-Type" type="VARCHAR2(32 BYTE)"/>
			<column name="CURRENT_PROCESS_MODE" remarks="当前作业模式" type="VARCHAR2(32 BYTE)"/>
	    </createTable>	
	    
	    <createIndex indexName="IDX_RAS_EQP_CURRENT_EQP_RRN" tableName="RAS_EQP_CURRENT" tablespace="TS_MES_IDX">
			<column name="EQUIPMENT_RRN"/>
		</createIndex>
	</changeSet>
	
	<changeSet id="WIP-20220616-001-UPDATE_PROCEDURE_EXPORT_TEMPLATE" author="FanChengMing">
		<sqlFile path="sql/WIP-20220616-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>流程导出模板区域号改为0</comment>
	</changeSet>
	<changeSet id="WIP-20220616-002-UPDATE_AD_QUERY_BeachHoldList" author="zoubinjie">
		<sqlFile path="sql/WIP-20220616-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>batchRelease列表查询sql优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20220617-003-TIMERINSTANCE-COLOR-TABLE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20220617-003.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>已开始QTime查询设置列表颜色</comment>
	</changeSet>
	
	<changeSet id="RAS-20220617-011-STEP-DROP-COLOUM" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="WF_PROCESSDEFINITION" columnName="CONTROL_QTY_TYPE"/>
		</preConditions>

		<dropColumn tableName="WF_PROCESSDEFINITION">
		 	<column name="CONTROL_QTY_TYPE"/>
            <column name="BATCH_SIZE_MIN"/>
            <column name="BATCH_SIZE_MAX"/>
		</dropColumn>
		<comment>工步移除Batch字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20220617-012-WF_PROCESSDEFINITION_HIS-DROP-COLOUM" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="WF_PROCESSDEFINITION_HIS" columnName="HIS_COMMENT"/>
		</preConditions>

		<dropColumn tableName="WF_PROCESSDEFINITION_HIS">
		 	<column name="HIS_COMMENT"/>
		</dropColumn>
		<comment>移除历史备注字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220617-013-STEPBATCHCONTORL" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220617-013.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>StepBatchContorl新界面</comment>
	</changeSet>
	
	<changeSet id="WIP-20220619-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220619-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	<changeSet id="CHJS-20220621-010-manual-confirm" author="HeTao">
       	<sqlFile path="sql/WIP-20220621-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>当设备状态为ONLINE时，提示用户手动操作会影响自动化事件</comment>
	</changeSet>
	
	<changeSet id="WIP-2-220621-ADDCOLUMN_WIP_LOT_SCRAP" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_LOT_SCRAP" columnName="EQUIPMENT_UNIT_ID"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT_SCRAP">  
    		<column name="EQUIPMENT_UNIT_ID" remarks="设备UnitID" type="VARCHAR2(32 BYTE)"/>
        	<column name="ACTION_STEP_NAME" remarks="实际报废工步" type="VARCHAR2(32 BYTE)"/>    	
        	<column name="ACTION_OPERATOR" remarks="责任人" type="VARCHAR2(32 BYTE)"/>
        	<column name="ACTION_OWNER" remarks="责任组" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-2-220621-ADDCOLUMN_WIP_SBD_HIS" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
			<columnExists tableName="WIP_SBD_HIS" columnName="EQUIPMENT_UNIT_ID"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_SBD_HIS">  
    		<column name="EQUIPMENT_UNIT_ID" remarks="设备UnitID" type="VARCHAR2(32 BYTE)"/>
        	<column name="ACTION_STEP_NAME" remarks="实际报废工步" type="VARCHAR2(32 BYTE)"/>       	
        	<column name="ACTION_OPERATOR" remarks="责任人" type="VARCHAR2(32 BYTE)"/>
        	<column name="ACTION_OWNER" remarks="责任组" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="RAS-20220622-001-ADTABLE-RASEquipmentStateCascadeQuery" author="ShiJianPing">
    	<preConditions onFail="MARK_RAN">
    		<sqlCheck expectedResult="0">
			select count(*) from ad_table t where t.name = 'RASEquipmentStateCascadeQuery'
		 	</sqlCheck>
		</preConditions> 
    	<sqlFile path="sql/RAS-20220622-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充设备状态级联的动态表</comment>
	</changeSet>
	
   <changeSet id="WIP-20220620-002-BATCH_JOB_ADD_COLUMN" author="Zoubinjie">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_BATCH_JOB" columnName="RESUME_GROUP"/>
			</not>
		</preConditions>
		<addColumn tableName="WIP_LOT_BATCH_JOB">
			<column name="RESUME_GROUP" remarks="恢复组" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>BatchJob增加Resume_group字段</comment>
	</changeSet>
	<changeSet id="WIP-20220622-001_Batch_resume_management" author="zoubinjie">
		<sqlFile path="sql/WIP-20220622-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>batch恢复信息管理</comment>
	</changeSet>

	<changeSet id="WIP-20220624-001-BATCH_JOB_HIS_ADD_COLUMN" author="Zoubinjie">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_BATCH_JOB_HIS" columnName="RESUME_GROUP"/>
			</not>
		</preConditions>
		<addColumn tableName="WIP_LOT_BATCH_JOB_HIS">
			<column name="RESUME_GROUP" remarks="恢复组" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>BatchJobHis增加Resume_group字段</comment>
	</changeSet>

	<changeSet id="WIP-20220624-001-ADD_INDEX" author="WuFaMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="WIP_LOT_BATCH_JOB_LOT_ID_IDX" tableName="WIP_LOT_BATCH_JOB"/>
			</not>
		</preConditions>
		<createIndex indexName="WIP_LOT_BATCH_JOB_LOT_ID_IDX" tableName="WIP_LOT_BATCH_JOB" tablespace="TS_MES_DAT"
					 unique="true">
			<column name="LOT_ID"/>
			<column name="RESUME_STEP_NAME"/>
		</createIndex>
		<comment>创建索引</comment>
	</changeSet>

	<changeSet id="WIP-20220624-002-DROP_INDEX" author="WuFaMing">
		<preConditions onFail="MARK_RAN">
			<indexExists indexName="IDX_WIP_LOT_BATCH_JOB_LOTID" tableName="WIP_LOT_BATCH_JOB"/>
		</preConditions>
		<dropIndex tableName="WIP_LOT_BATCH_JOB" indexName="IDX_WIP_LOT_BATCH_JOB_LOTID"/>
		<comment>删除索引</comment>
	</changeSet>

	<changeSet id="WIP-20220624-001_ADD_MESSAGE" author="WuFaMing">
		<sqlFile path="sql/WIP-20220624-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>Furnace batch add message</comment>
	</changeSet>
	
	<changeSet id="MM-20220624-DROP-MM_LOT_SCRAP" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="MM_LOT_SCRAP"/>
		</preConditions>
		
		<dropTable cascadeConstraints="true"  tableName="MM_LOT_SCRAP"/>  
	</changeSet>
	
	<changeSet id="MM-20220624-CREATE-MM_LOT_SCRAP" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="MM_LOT_SCRAP"/>
			</not>
		</preConditions>
				
    	<createTable remarks="物料批报废信息表" tableName="MM_LOT_SCRAP" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_LOT_SCRAP" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  
			<column name="MLOT_RRN" remarks="物料批主键" type="NUMBER(19, 0)"/>
			<column name="MLOT_ID" remarks="物料批批号" type="VARCHAR2(32 BYTE)"/>
			<column name="MCOMPONENT_RRN" remarks="组件RRN" type="NUMBER(19, 0)"/>
			<column name="MCOMPONENT_ID" remarks="组件ID" type="VARCHAR2(32 BYTE)"/>
			<column name="MATERIAL_NAME" remarks="物料名称" type="VARCHAR2(32 BYTE)"/>
			<column name="MATERIAL_VERSION" remarks="物料版本" type="NUMBER(19, 0)"/>
			<column name="MAIN_QTY" remarks="主数量" type="NUMBER(19, 0)"/>
			<column name="SUB_QTY" remarks="子数量" type="NUMBER(19, 0)"/>
			<column name="WAREHOUSE_RRN" remarks="仓库RRN" type="NUMBER(19, 0)"/>
			<column name="WAREHOUSE_ID" remarks="仓库ID" type="VARCHAR2(32 BYTE)"/>
			<column name="STORAGE_TYPE	" remarks="储位类型" type="VARCHAR2(32 BYTE)"/>
			<column name="STORAGE_ID" remarks="储位ID" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_ID" remarks="主设备" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_UNIT_ID" remarks="子设备" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_STEP_NAME" remarks="动作工步" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_OPERATOR" remarks="操作人" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_OWNER" remarks="操作组" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_CODE" remarks="动作码" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_REASON" remarks="动作原因" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_COMMENT" remarks="动作备注" type="VARCHAR2(32 BYTE)"/>
	    </createTable>	
	</changeSet>
	
	<changeSet id="MM-20220624-CREATE-MM_LOT_SBD_HIS" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="MM_LOT_SBD_HIS"/>
			</not>
		</preConditions>
				
    	<createTable remarks="物料批报废信息历史表" tableName="MM_LOT_SBD_HIS" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_LOT_SBD_HIS" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TIME" remarks="事务时间" type="date"/>
            <column name="HIS_SEQ" remarks="历史序列号" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ_NO" remarks="历史序列号次序" type="NUMBER(19, 0)"/>
			<column name="MLOT_RRN" remarks="物料批主键" type="NUMBER(19, 0)"/>
			<column name="MLOT_ID" remarks="物料批批号" type="VARCHAR2(32 BYTE)"/>
			<column name="MCOMPONENT_RRN" remarks="组件RRN" type="NUMBER(19, 0)"/>
			<column name="MCOMPONENT_ID" remarks="组件ID" type="VARCHAR2(32 BYTE)"/>
			<column name="POSITION" remarks="位置" type="VARCHAR2(32 BYTE)"/>
			<column name="MATERIAL_NAME" remarks="物料名称" type="VARCHAR2(32 BYTE)"/>
			<column name="MATERIAL_VERSION" remarks="物料版本" type="NUMBER(19, 0)"/>
			<column name="MAIN_QTY" remarks="主数量" type="NUMBER(19, 0)"/>
			<column name="SUB_QTY" remarks="子数量" type="NUMBER(19, 0)"/>
			<column name="WAREHOUSE_RRN" remarks="仓库RRN" type="NUMBER(19, 0)"/>
			<column name="WAREHOUSE_ID" remarks="仓库ID" type="VARCHAR2(32 BYTE)"/>
			<column name="STORAGE_TYPE	" remarks="储位类型" type="VARCHAR2(32 BYTE)"/>
			<column name="STORAGE_ID" remarks="储位ID" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_ID" remarks="主设备" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_UNIT_ID" remarks="子设备" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_STEP_NAME" remarks="动作工步" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_OPERATOR" remarks="操作人" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_OWNER" remarks="操作组" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_CODE" remarks="动作码" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_REASON" remarks="动作原因" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_COMMENT" remarks="动作备注" type="VARCHAR2(32 BYTE)"/>
	    </createTable>	
	</changeSet>
	
	<changeSet id="MM-20220624-004-MMLOT-SCRAP" author="tangjiacheng">
		<sqlFile path="sql/MM-20220624-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>物料批报废，取消报废功能完善</comment>
	</changeSet>
	
	<changeSet id="MM-20220629-002-MM-UNSCRAP" author="tangjiacheng">
		<sqlFile path="sql/MM-20220629-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>物料取消报废message添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20220701-001-SYSPARAM-MOVENEXTHIS" author="Clark">
		<sqlFile path="sql/WIP-20220701-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加是否出站保存MoveNext历史的系统参数</comment>
	</changeSet>
	
	<changeSet id="WIP-20220704-001-SYSPARAM-MOVENEXTHIS" author="Clark">
		<sqlFile path="sql/WIP-20220704-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加出站不保存MoveNext历史的系统参数</comment>
	</changeSet>
	
	<changeSet id="RAS-20220704-001-SYSPARAM-MOVENEXTHIS" author="HeTao">
		<sqlFile path="sql/RAS-20220704-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>CMP设备类型用户栏位参考值</comment>
	</changeSet>
	
	<changeSet id="WIP-20220704-001-SORTINGJOB-AD_FROM01" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220704-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>SortingJobAction</comment>
	</changeSet>
	
	<changeSet id="WIP-20220704-001-SORTINGJOB-AD_FROM02" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220704-012.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>SortingJobAssignCarrierDialog</comment>
	</changeSet>
	
	<changeSet id="WIP-20220704-001-SORTINGJOB-AD_FROM03" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220704-013.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>SortingJobMergeReserveDialog</comment>
	</changeSet>
	
	<changeSet id="WIP-20220704-001-SORTINGJOB-AD_FROM04" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220704-014.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>SortingJobOtherReserveDialog</comment>
	</changeSet>
	
	<changeSet id="WIP-20220704-001-SORTINGJOB-AD_FROM05" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220704-015.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>SortingJobOtherReserveDialog</comment>
	</changeSet>
	
	
	<changeSet id="MM-20220706-CREATE-CARRIER_BATCH" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="MM_CARRIER_BATCH"/>
			</not>
		</preConditions>
		
    	<createTable remarks="载具组Batch表" tableName="MM_CARRIER_BATCH" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_CARRIER_BATCH" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  

			<column name="BATCH_ID" remarks="Batch号" type="VARCHAR2(32 BYTE)"/>
			<column name="DURABLE_TYPE" remarks="载具类型" type="VARCHAR2(32 BYTE)"/>
			<column name="DURABLE_ID" remarks="载具号" type="VARCHAR2(32 BYTE)"/>
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
			<column name="PALLET_ID" remarks="PALLET ID" type="VARCHAR2(32 BYTE)"/>
	    </createTable>	
	    
	    <createIndex indexName="IDX_MM_CARRIER_BATCH_BATCHID" tableName="MM_CARRIER_BATCH" tablespace="TS_MES_IDX">
			<column name="BATCH_ID"/>
		</createIndex>
		<createIndex indexName="IDX_MM_CARRIER_BATCH_DURABLEID" tableName="MM_CARRIER_BATCH" tablespace="TS_MES_IDX">
			<column name="DURABLE_ID"/>
		</createIndex>
	</changeSet>
	
	<changeSet id="MM-20220706-001-CARRIER_BATCH" author="Clark">
		<sqlFile path="sql/MM-20220706-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>载具组batch</comment>
	</changeSet>
	
	<changeSet id="WIP-20220707-001-SORTINGJOB-UPDATE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220707-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>SortingJobUpdate</comment>
	</changeSet>
	
	<changeSet id="WIP-20220707-002-SORTINGJOB-FROM" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220707-012.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>SortingJobDetailDialog</comment>
	</changeSet>
	
	<changeSet id="WIP-20220709-001-RAS_PORT_ADD_CLOUMN" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_PORT" columnName="TRANSFER_STATEENTRY_TIME"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="RAS_PORT">  
        	<column name="TRANSFER_STATEENTRY_TIME" remarks="搬送状态时间" type="date"/>
        	<column name="MAIN_MAT_TYPE" remarks="主物料类型" type="VARCHAR2(32 BYTE)"/>
        	<column name="SUB_MAT_TYPE" remarks="子物料类型" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	
    	<addColumn tableName="RAS_PORT_HIS">  
        	<column name="TRANSFER_STATEENTRY_TIME" remarks="搬送状态时间" type="date"/>
        	<column name="MAIN_MAT_TYPE" remarks="主物料类型" type="VARCHAR2(32 BYTE)"/>
        	<column name="SUB_MAT_TYPE" remarks="子物料类型" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>  
		<comment>增加port表字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220709-002-LONGPORTSTATETHREAD" author="FanChengMing">
		<sqlFile path="sql/RAS-20220709-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加port长时间状态检查定时器</comment>
	</changeSet>
	
	<changeSet id="BAS-20220711-001_ID_Generator" author="Clark">
		<sqlFile path="sql/BAS-20220711-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>ID生成规则增强MSG</comment>
	</changeSet>
	
	<changeSet id="WIP-20220711-001-LOT-PREPARE_ADD_CLOUMN" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_PREPARE" columnName="GROUP_LOT_IDS"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="WIP_LOT_PREPARE">  
        	<column name="GROUP_LOT_IDS" remarks="组批次IDs" type="VARCHAR2(512 BYTE)"/>
    	</addColumn>
		<comment>增加WIP_LOT_PREPARE表字段GROUP_LOT_IDS，保存一起Prepare的批次ID</comment>
	</changeSet>
	
	<changeSet id="WIP-20220711-001-REFRESH_CURRENT_STEP" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220711-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>REFRESHCURRENTSTEP功能相关sql</comment>
	</changeSet>
	
	<changeSet id="WIP-20220713-001-CLOSE_REFRESH_CURRENT_STEP" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220713-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>暂时关闭RefreshCurrentStep功能</comment>
	</changeSet>
	
	<changeSet id="RAS-20220714-001-EQP_ADD_CLOUMN" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="IS_USE_RTD"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="RAS_EQP">  
        	<column name="IS_USE_RTD" remarks="是否启用RTD" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="RAS_EQP_HIS">  
        	<column name="IS_USE_RTD" remarks="是否启用RTD" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
		<comment>设备和设备历史表增加IS_USE_RTD字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20220714-001-ADD_EQUIPMENT_AND_PORT_OPERATION" author="FanChengMing">
		<sqlFile path="sql/RAS-20220714-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加设备和端口功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220715-002-ADD_LOT_FUTURE_SKIP" author="Clark">
		<sqlFile path="sql/WIP-20220715-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加批次未来跳步功能</comment>
	</changeSet>
	<changeSet id="MM-20220715-001-CARRIERBATCH_ADD_CLOUMN" author="MOYOUMING">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="MM_CARRIER_BATCH" columnName="IS_DISPATCH"/>
			</not>
		</preConditions> 
    	<addColumn tableName="MM_CARRIER_BATCH">  
        	<column name="IS_DISPATCH" remarks="是否允许派工" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
    	
		<comment>载具组Batch表增加IS_DISPATCH字段</comment>
	</changeSet>
	
	<changeSet id="BAS-20220717-001-SAMPLE_REWORK_SPLIT_LOT_ID_GENERATOR" author="Bing.Ma">
       <sqlFile path="sql/BAS-20220717-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
	   <comment>Sample_ReWork子批分批规则生成SQL</comment>
	</changeSet>
	
	<changeSet id="BAS-20220717-002-SAMPLE_REWORK_SPLIT_LOT_ID_GENERATOR" author="HeTao">
       <sqlFile path="sql/BAS-20220717-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
	   <comment>Sample_ReWork子批分批规则生成SQL，参考值修改</comment>
	</changeSet>
	
	<changeSet id="BAS-20220717-003-SAMPLE_REWORK_SPLIT_LOT_ID_GENERATOR" author="HeTao">
		<sql>update BAS_ID_GENERATOR_RULE_LINE SET FIXED_STRING = '.R' where OBJECT_RRN = 225961315548631040</sql>
	   <comment>修改REWORK SPLIT SubLot ID生成规则防止冲突</comment>
	</changeSet>
	
	<changeSet id="BAS-20220718-001-Capalibity_Step" author="baipenghua">
		<sqlFile path="sql/RAS-20220718-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
	   <comment>增加Capability查找关联工步的功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220719-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220719-088.sql" relativeToChangelogFile="true" encoding="GBK"/>
	   <comment>补充英译中，打开Refresh功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220722-001-LOTRESERVED_ADD_CLOUMN" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_RESERVED" columnName="PORT_ID"/>
			</not>
		</preConditions> 
    	<addColumn tableName="WIP_LOT_RESERVED">  
        	<column name="PORT_ID" remarks="设备端口ID" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_LOT_RESERVED_HIS">  
        	<column name="PORT_ID" remarks="设备端口ID" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
		<comment>批次预留表和对应历史表增加PORT_ID字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220722-002-LOTRESERVED_AND_PORT_ADD_ADFIELD" author="FanChengMing">
		<sqlFile path="sql/WIP-20220722-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>批次预留页面增加端口和port页面增加主材类型</comment>
	</changeSet>
	
	<changeSet id="WIP-20220728-001-UPDATE_AD_TABLE" author="DaiWenBin">
		<sql>UPDATE AD_FIELD SET NAME = 'attribute1' WHERE OBJECT_RRN = 194772160698478601 AND TABLE_RRN = 194772160698478592;</sql>
		<comment>修改变更批次优先级的动态表</comment>
	</changeSet>
	
	<changeSet id="WIP-20220802-001-CARRIER_QUERY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220802-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>载具查询界面</comment>
	</changeSet>
	
	<changeSet id="WIP-20220802-001-FUTURE_ACTION" author="ZouYiSong">
		<sqlFile path="sql/WIP-20220802-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>批次未来动作查询界面增加未来跳步相关处理</comment>
	</changeSet>

	<changeSet id="WIP-20220804-001-CLOSE-STEP_BATCH_CONTROL" author="DaiWenBin">
		<sql>update ad_authority set is_active = 'N' where object_rrn = 20220616077;</sql>
		<comment>关闭stepBatchControl功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20220804-002-AD_SYS_PARAMETER_VALUE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220804-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>添加系统参数和英译中</comment>
	</changeSet>

	<changeSet id="WIP-20220808-001-LOTEQUIPMENTUNIT_ADD_CLOUMN" author="WuFaMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT" columnName="BATCH_END_STATE"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_EQUIPMENT_UNIT">
			<column name="BATCH_END_STATE" remarks="Batch结束状态" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<addColumn tableName="WIP_LOT_EQUIPMENT_UNIT_HIS">
			<column name="BATCH_END_STATE" remarks="Batch结束状态" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>WIP_LOT_EQUIPMENT_UNIT表增加BATCH_END_STATE字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220809-001-AD_SYS_PARAMETER_VALUE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220808-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>添加系统参数和英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20220808-010-CHANGE-LOT-ID" author="HeTao">
		<sqlFile path="sql/WIP-20220808-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>后台卡控只有WAIT状态能修改批次ID</comment>
	</changeSet>
	
	<changeSet id="CHJS-20220805-001-WIP_LOT_EQUIPMENT_UNIT_ADD_COLUMN" author="Bing.Ma">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT" columnName="DUMMY_CAPACITY"/>
			</not>
		</preConditions>		
		<addColumn tableName="WIP_LOT_EQUIPMENT_UNIT">
			<column name="DUMMY_CAPACITY" remarks="Dummy容量" type="NUMBER(19, 0)"/>
		</addColumn>
	   <comment>WIP_LOT_EQUIPMENT_UNIT表增加DUMMY_CAPACITY字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220805-002-EQP_BUFFER_POSITION_ENHANCE" author="Bing.Ma">
		<sqlFile path="sql/WIP-20220805-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Equipment Buffer Position功能增强与优化</comment>
	</changeSet>
	
	<changeSet id="AD-20220811-001-AD_TABLE_MMSPAREPARTS" author="ShiJianPing">
		<sql>update AD_TABLE t set t.is_view = 'N' where t.object_rrn = 13871874;</sql>
		<comment>零部件信息界面的name查询无效的问题</comment>
	</changeSet>
	
	<changeSet id="WIP-20220810-041-EQP_BUFFER_POSITION_ENHANCE" author="Bing.Ma">
		<sqlFile path="sql/WIP-20220810-041.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Equipment Buffer Position功能MaxUseCount列取值方式变更</comment>
	</changeSet>
	
	<changeSet id="WIP-20220810-041-WIP_LOT_RESERVED_ADD_COLUMN" author="Bing.Ma">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_RESERVED" columnName="RESERVE_TIME"/>
			</not>
		</preConditions>		
		<addColumn tableName="WIP_LOT_RESERVED">
			<column name="RESERVE_TIME" remarks="预留时间" type="date"/>
		</addColumn>   
		<comment>WIP_LOT_RESERVED表增加RESERVE_TIME字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220810-043-LOT_RESERVE_ORDER_BY_RESERVE_TIME" author="Bing.Ma">
		<sqlFile path="sql/WIP-20220810-043.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Lot Reserve EQP功能可按预留时间排序</comment>
	</changeSet>

	<changeSet id="WIP-20220813-CHANGE_DATA_TYPE" author="WuFaMing">
		<modifyDataType tableName="COM_CON_LOCKS" columnName="LOCK_NAME" newDataType="VARCHAR2(64 BYTE)"/>
		<comment>修改COM_CON_LOCKS字段的长度</comment>
	</changeSet>
	
	<changeSet id="RAS-20220816-101-EQP_ADD_CLOUMN" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="IS_AUTO_PREPARE"/>
			</not>
		</preConditions> 
    	
    	<addColumn tableName="RAS_EQP">  
        	<column name="IS_AUTO_PREPARE" remarks="是否启用自动Prepare" type="VARCHAR2(1 BYTE)" />
    	</addColumn>
    	<addColumn tableName="RAS_EQP_HIS">  
        	<column name="IS_AUTO_PREPARE" remarks="是否启用自动Prepare" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
    	<sql>
    		update RAS_EQP set IS_AUTO_PREPARE = 'N' where IS_AUTO_PREPARE IS NULL;
    	    update RAS_EQP_HIS set IS_AUTO_PREPARE = 'N' where IS_AUTO_PREPARE IS NULL;
   	    </sql>
		<comment>设备和设备历史表增加IS_AUTO_PREPARE字段</comment>
	</changeSet>
	
	 <changeSet id="RAS-20220816-102_EQP_RTD_FLAG_FORM" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
			 <sqlCheck expectedResult="0">
				select count(*) from ad_form t where t.name = 'RTDEquipmentForm';		 
			 </sqlCheck>
		</preConditions>
    	<sqlFile path="sql/RAS-20220816-102.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备派工开关页面</comment>
	</changeSet>
	 <changeSet id="RAS-20220816-101_EQP_RTD_FLAG_FORM" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
			 <sqlCheck expectedResult="0">
				select count(*) from ad_form t where t.name = 'RTDEquipmentForm';		 
			 </sqlCheck>
		</preConditions>
    	<sqlFile path="sql/RAS-20220816-101.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备派工开关页面</comment>
	</changeSet>
	 <changeSet id="RAS-20220819-101_EQP_RTD_FLAG_FORM" author="WangLuoPeng">
    	<sqlFile path="sql/RAS-20220819-101.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备派工开关页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20220822-001-ADD_AD_FIELD" author="WangJiaXin">
		<sqlFile path="sql/WIP-20220822-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Lot Reserve EQP功能界面显示增加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220824-041-LOT_RESERVE_ENHANCE" author="Bing.Ma">
		<sql>update ad_field t set t.is_editable='Y' where t.table_rrn=93505781 and (t.name ='portId' or t.name='checkFlag' or t.name='equipmentId' or t.name='reserveTime');</sql>   
		<comment>Lot Reserve EQP栏位可编辑</comment>
	</changeSet>

	<changeSet id="BAS-20220826-001-ADD_SYS_PARAM" author="WuFaMing">
		<sqlFile path="sql/BAS-20220826-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加系统参数</comment>
	</changeSet>
	<changeSet id="WIP-20220829-101-Add_USER_REF_NAME_OUTFROMMANUALPORT" author="zoubinjie">
		<sqlFile path="sql/WIP-20220829-101.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加从MaunualPort出来的LotType(只适用与Furnace设备)</comment>
	</changeSet>
	<changeSet id="WIP-20220829-001-ADD_AD_MESSAGE" author="WangJiaXin">
		<sqlFile path="sql/WIP-20220829-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>增加AD_Message报错信息</comment>
	</changeSet>

	<changeSet id="WIP-20220901-001-ADD_AD_FROM" author="WuFaMing">
		<sqlFile path="sql/WIP-20220901-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加AD_FROM</comment>
	</changeSet>

	<changeSet id="WIP-20220905-001-ADD_Column" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_SORTING_JOB" columnName="TO_MAIN_MAT_TYPE"/>	
			</not>		
		</preConditions>
				
		<addColumn tableName="WIP_LOT_SORTING_JOB">
			<column name="TO_MAIN_MAT_TYPE" remarks="目标物料" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20220906-001-SORTACTION" author="DaiWenBin">
		<sqlFile path="sql/WIP-20220906-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改Sort界面</comment>
	</changeSet>
	
	<changeSet id="WIP-20220908-002-EQP_PORT_STATE_UI_ENHANCE" author="Bing.Ma">
		<sqlFile path="sql/RAS-20220908-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>EquipmentAndPortChangeState界面优化</comment>
	</changeSet>

	<changeSet id="WIP-20220917-001-ADD_AD_MESSAGE" author="Bing.Ma">
		<sqlFile path="sql/WIP-20220917-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>添加ad_message</comment>
	</changeSet>
	
	<changeSet id="BAS-20220930-001-ADD_AD_MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/BAS-20220930-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>添加ad_message</comment>
	</changeSet>

	<changeSet id="WIP-20221025-COMPONENT_HIS_MODIFY" author="WuFaMing">
		<sqlFile path="sql/WIP-20221025-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>组件加工历史查询增加查询条件</comment>
	</changeSet>

	<changeSet id="WIP-20221031-ADD-COLUMN" author="HeTao">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_SET_LINE" columnName="IS_PROCESS_FORMULA"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="EDC_SET_LINE">
    		<column name="IS_PROCESS_FORMULA" remarks="是否流程公式" type="VARCHAR2(1 BYTE)"/>
    		<column name="IS_MATCH_COMPONENT" remarks="强制匹配Component" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
    </changeSet>
    
    <changeSet id="EDC-20221031-001_PROCESS_FORMULA" author="HeTao">
		<sqlFile path="sql/EDC-20221031-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>流程数据收集功能</comment>
	</changeSet>

	<changeSet id="EDC-20221031-002_PROCESS_EDC_FORMULA" author="HeTao">
		<sqlFile path="sql/EDC-20221031-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>流程数据收集功能SQL补充</comment>
	</changeSet>
	
	<changeSet id="MM-20221027-001-RENAME-MM_LOT_SCRAP-STORAGE_TYPE" author="yangchenxing">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="MM_LOT_SCRAP" columnName="STORAGE_TYPE "/>
		</preConditions>
		<renameColumn tableName="MM_LOT_SCRAP" oldColumnName="STORAGE_TYPE " newColumnName="STORAGE_TYPE"/>
		<comment>解决STORAGE_TYPE字段后带空格问题</comment>
	</changeSet>
	
	<changeSet id="MM-20221027-002-RENAME-MM_LOT_SBD_HIS-STORAGE_TYPE" author="yangchenxing">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="MM_LOT_SBD_HIS" columnName="STORAGE_TYPE "/>
		</preConditions>
		<renameColumn tableName="MM_LOT_SBD_HIS" oldColumnName="STORAGE_TYPE " newColumnName="STORAGE_TYPE"/>
		<comment>解决STORAGE_TYPE字段后带空格问题</comment>
	</changeSet>
	
	<changeSet id="WIP-20221026-001-UPDATE-VIEW" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221026-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>页面修改调整规范</comment>
	</changeSet>
	
	<changeSet id="WIP-20221026-002-UPDATE-VIEW" author="zhougelong">
		<sqlFile path="sql/WIP-20221026-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>页面修改调整规范</comment>
	</changeSet>
	
	<changeSet id="WIP-20221026-003-UPDATE-ENGLISH-MESSAGE" author="zhougelong">
		<sqlFile path="sql/WIP-20221026-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>动态对象管理模块英文信息错误修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221101-001-UPDATE-VIEW" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221101-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>页面修改调整规范</comment>
	</changeSet>
	
	<changeSet id="EDC-20221102-001_EDC_FORMULA" author="HeTao">
		<sqlFile path="sql/EDC-20221102-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>EDC Formula功能增强</comment>
	</changeSet>

	<changeSet id="WIP-20221103-001-UPDATE-VIEW" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221103-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工步流程注入，工程连接限制，设备和设备端口状态修改功能页面修改规范</comment>
	</changeSet>
	
	<changeSet id="WIP-20221107-001-UPDATE-ENGLISH-MESSAGE" author="zhougelong">
		<sqlFile path="sql/WIP-20221107-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改动态表乱码问题</comment>
	</changeSet>
	
	<changeSet id="WIP-20221107-002-UPDATE-UPDATE-VIEW" author="zhougelong">
		<sqlFile path="sql/WIP-20221107-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>页面修改调整规范乱码问题</comment>
	</changeSet>

	<changeSet id="WIP-20221107-001-LOTTECN-ADD-STEPSTATENAME" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
    		<not>
				<columnExists tableName="WIP_LOT_TECN_HIS" columnName="STEP_STATE_NAME"/>
			</not>
		</preConditions>
		
		<addColumn tableName="WIP_LOT_TECN">  
        	<column name="STEP_STATE_NAME" remarks="工步节点名称" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>  
    	
    	<addColumn tableName="WIP_LOT_TECN_HIS">  
        	<column name="STEP_STATE_NAME" remarks="工步节点名称" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>  
	</changeSet>
	
	<changeSet id="WIP-20221107-001-LOTTECN-CONTEXT-ADDRULE" author="FanChengMing">
		<sqlFile path="sql/WIP-20221107-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次临时工程变更增加规则</comment>
	</changeSet>
	
	<changeSet id="WIP-20221113-ADD-COLUMN" author="Zouyisong">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_TECN" columnName="PROCEDURE_NAME"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WIP_LOT_TECN">
    		<column name="PROCEDURE_NAME" remarks="模块名称" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    </changeSet>
    
    <changeSet id="WIP-20221114-001-LOTTECN-CONTEXT-ADDRULE" author="Zouyisong">
		<sqlFile path="sql/WIP-20221114-011.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>LOTTECN增加支持按批次+工步名称+模块+工步节点名匹配</comment>
	</changeSet>
	
	<changeSet id="WIP-20221114-002-LOTTECN-CONTEXT-ADDRULE" author="Zouyisong">
		<sqlFile path="sql/WIP-20221114-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>LOTTECN增加支持按批次调整检查顺序</comment>
	</changeSet>
	
	<changeSet id="WIP-20221117-010-MODIFY_WAFERHIS" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20221117-010.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>调整waferProcessHis页面工步栏位</comment>
	</changeSet>
    
</databaseChangeLog> 