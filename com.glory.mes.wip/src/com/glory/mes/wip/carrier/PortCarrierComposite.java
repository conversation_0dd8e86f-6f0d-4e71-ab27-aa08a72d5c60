package com.glory.mes.wip.carrier;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.comp.ComponentComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.client.CarrierLotManager;

/**
 * ��ʾPort��Ϣ���ؾ�(�����ؾ��е�Component)��Ϣ
 */
public class PortCarrierComposite extends Composite {

	private static final Logger logger = Logger.getLogger(PortCarrierComposite.class);

	private static final String TABLE_PORT_INFO = "RASPortInfo";
	
	private static final String FILED_CARRIER_ID = "durableId";
	
	private List<ICarrierChangeListener> carrerChangeListeners = new ArrayList<ICarrierChangeListener>();
	
	private Port port;

	private int count;
	private Carrier carrier;

	private List<ComponentUnit> componentUnits;
	
	private EntityForm portForm;
	private ComponentComposite componentComposite;

	/**
	 * 
	 */
	public PortCarrierComposite(Composite parent, int style, Port port, Carrier carrier) {
		super(parent, style);
		this.port = port;
		this.count = carrier.getCapacity().intValue();
		this.carrier = carrier;
	}
	
	/**
	 * 
	 */
	public PortCarrierComposite(Composite parent, int style, Port port, int count) {
		super(parent, style);
		this.port = port;
		this.count = count;
	}
	
	public void createForm() {
		try {
			GridLayout layout = new GridLayout(1, true);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(GridData.FILL_BOTH));
						
			//������ʾPort��Ϣ
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable portAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_PORT_INFO);
			EntityForm portForm = new EntityForm(this, SWT.NONE, port, portAdTable, null);
			
			componentComposite = new ComponentComposite(this, count, false, true);
			componentComposite.init();
			
			if (carrier != null) {
				searchCarrier(carrier.getDurableId());
			}
			
			TextField textField = (TextField)portForm.getFields().get(FILED_CARRIER_ID);
			if (textField != null) {
				textField.getTextControl().addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						Text tLotId = ((Text) event.widget);
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
						switch (event.keyCode) {
						case SWT.CR:
						case SWT.KEYPAD_CR:
							String carrierId = textField.getTextControl().getText().toUpperCase();
							searchCarrier(carrierId);
						}
					}
				});
				if (carrier != null) {
					textField.setText(carrier.getDurableId());
				}
			}
		} catch (Exception e) {
			logger.error("PortCarrierComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void searchCarrier(String carrierId) {
		try {
			if (!StringUtil.isEmpty(carrierId)) {
				DurableManager durableManager = Framework.getService(DurableManager.class);
				carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
				if (carrier == null) {
					UI.showError(Message.getString("mm.carrier_is_not_exist"));
					notifyCarrierChangeListeners(port, null, null);
					componentComposite.initComponents(new ArrayList<ComponentUnit>());
					return;
				}
				CarrierLotManager compManager = Framework.getService(CarrierLotManager.class);
				componentUnits = compManager.getComponentByCarrierId(Env.getOrgRrn(),carrierId);
				notifyCarrierChangeListeners(port, carrier, componentUnits);
				componentComposite.initComponents(componentUnits);
			}
		} catch(Exception e) {
			logger.error("PortCarrierComposite createPartControl:", e);
		}
	}
	
	public void reset() {
		if (carrier != null) {
			searchCarrier(carrier.getDurableId());
		} else {
			componentComposite.initComponents(new ArrayList<ComponentUnit>());
		}
	}
	
	public ComponentComposite getComponentComposite() {
		return componentComposite;
	}

	public void setComponentComposite(ComponentComposite componentComposite) {
		this.componentComposite = componentComposite;
	}
	
	public void notifyCarrierChangeListeners(Port port, Carrier carrier, List<ComponentUnit> componentUnits){
		synchronized (carrerChangeListeners) {
			for (ICarrierChangeListener carrerChangeListener : carrerChangeListeners) {
				carrerChangeListener.carrierChanged(port, carrier, componentUnits);
			}
		}
	}
	
	public void addCarrerChangeListener(ICarrierChangeListener listener) {
		carrerChangeListeners.add(listener);
	}
	
	public void changePort(Port port) {
		this.port = port;
		this.portForm.setObject(this.port);
		this.portForm.refresh();
	}
	
	public Port getPort() {
		return port;
	}

	public void setPort(Port port) {
		this.port = port;
	}
	
	public Carrier getCarrier() {
		return carrier;
	}

	public void setCarrier(Carrier carrier) {
		this.carrier = carrier;
	}
}
