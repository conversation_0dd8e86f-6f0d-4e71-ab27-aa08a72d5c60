package com.glory.mes.wip.changeshift.refer;

import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.query.QueryDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.core.util.StringUtil;

public class ChangeShiftRefQueryDialog extends QueryDialog {
	
	protected ChangeShiftReferEditor editor;
	
	public ChangeShiftRefQueryDialog(Shell parent, ListTableManager tableManager, IRefresh iRefresh, ChangeShiftReferEditor editor) {
		super(parent, tableManager, iRefresh);
		this.editor = editor;
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
		String whereClause = StringUtil.relpaceWildcardCondition(getWhereClause());
		editor.refresh(whereClause + " And docStatus = 'CREATED'");
	}

}
