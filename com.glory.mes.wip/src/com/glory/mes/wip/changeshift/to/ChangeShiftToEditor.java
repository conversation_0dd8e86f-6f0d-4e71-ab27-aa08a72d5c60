package com.glory.mes.wip.changeshift.to;

import java.util.List;
import java.util.stream.Collectors;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ChangeShift;
import com.glory.mes.wip.model.ChangeShiftLine;
import com.google.common.collect.Lists;

public class ChangeShiftToEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.to.ChangeShiftToEditorGlc";

	private static final String FIELD_CHANGESHIFTLIST = "changeShiftList";
	private static final String FIELD_CHANGESHIFTGLC = "changeShiftGlc";
	private static final String FIELD_CHANGESHIFTINFO = "changeShiftInfo";
	private static final String FIELD_CHANGESHIFTLINECOMMON = "changeShiftLineCommon";
	private static final String FIELD_CHANGESHIFTLINEWO = "changeShiftLineWo";
	private static final String FIELD_CHANGESHIFTLINELOT = "changeShiftLineLot";
	private static final String FIELD_CHANGESHIFTLINEEQP = "changeShiftLineEqp";

	private static final String BUTTON_SEARCH = "search";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_CONFIRM = "confirm";
	private static final String BUTTON_CLOSE = "close";
	private static final String BUTTON_ENTITYREFRESH = "entityRefresh";

	protected ListTableManagerField changeShiftListField;
	protected GlcFormField changeShiftGlcField;
	protected EntityFormField changeShiftInfoField;
	protected ListTableManagerField changeShiftLineCommonField;
	protected ListTableManagerField changeShiftLineWoField;
	protected ListTableManagerField changeShiftLineLotField;
	protected ListTableManagerField changeShiftLineEqpField;

	public static String SUCCESSION_TYPE_LOT = "LOT";
	public static String SUCCESSION_TYPE_EQP = "EQP";
	public static String SUCCESSION_TYPE_WO = "WO";
	public static String SUCCESSION_TYPE_COMMENT = "COMMENT";
	
	protected ChangeShiftToQueryDialog queryDialog;
	protected String whereClause;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		changeShiftListField = form.getFieldByControlId(FIELD_CHANGESHIFTLIST, ListTableManagerField.class);
		changeShiftGlcField = form.getFieldByControlId(FIELD_CHANGESHIFTGLC, GlcFormField.class);
		changeShiftInfoField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTINFO, EntityFormField.class);
		changeShiftLineCommonField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINECOMMON, ListTableManagerField.class);
		changeShiftLineWoField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINEWO, ListTableManagerField.class);
		changeShiftLineLotField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINELOT, ListTableManagerField.class);
		changeShiftLineEqpField = changeShiftGlcField.getFieldByControlId(FIELD_CHANGESHIFTLINEEQP, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, changeShiftListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::changeShiftListSelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SEARCH), this::searchAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CONFIRM), this::confirmAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLOSE), this::closeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ENTITYREFRESH), this::entityRefreshAdapter);
	}

	private void searchAdapter(Object object) {
		try {
			if (queryDialog != null) {
				queryDialog.setVisible(true);
			} else {
				queryDialog =  new ChangeShiftToQueryDialog(UI.getActiveShell(), changeShiftListField.getListTableManager(), null, this);
				queryDialog.open();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void refreshAdapter(Object object) {
		try {
			refresh(this.whereClause);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void refresh(String whereClause) {
		try {
			if (StringUtil.isEmpty(whereClause)) {
				this.whereClause = " docStatus = 'APPROVED'";
			} else {
				this.whereClause = whereClause;
			}
			ADManager adManager = Framework.getService(ADManager.class);
			List<ChangeShift> changeShifts = adManager.getEntityList(Env.getOrgRrn(), ChangeShift.class, Env.getMaxResult(), this.whereClause, "");
			changeShiftListField.getListTableManager().setInput(changeShifts);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void confirmAdapter(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftInfoField.getValue();
			if (succession == null || succession.getObjectRrn() == null) {
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			succession.setDocStatus(Documentation.STATUS_COMPLETED);
			lotManager.changeShift(succession, null, Documentation.STATUS_COMPLETED, Env.getSessionContext());
			UI.showInfo(Message.getString("alm.add_data_comment_successed"));// ������ʾ��
			entityRefresh(null);
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	
	}

	private void closeAdapter(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftInfoField.getValue();
			if (succession == null || succession.getObjectRrn() == null) {
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			succession.setDocStatus(Documentation.STATUS_CREATED);
			lotManager.changeShift(succession, null, "", Env.getSessionContext());
			UI.showInfo(Message.getString("common.reject_successed"));// ������ʾ��
			entityRefresh(null);
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void entityRefreshAdapter(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftInfoField.getValue();
			entityRefresh(succession);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void entityRefresh(ChangeShift succession) {
		try {
			if (succession != null && succession.getObjectRrn() != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				succession = (ChangeShift) adManager.getEntity(succession);
				changeShiftInfoField.setValue(succession);
				changeShiftInfoField.refresh();
				setInputTableManager(succession);
			} else {
				changeShiftInfoField.setValue(new ChangeShift());
				changeShiftInfoField.refresh();
				changeShiftLineCommonField.setValue(Lists.newArrayList());
				changeShiftLineCommonField.refresh();
				changeShiftLineWoField.setValue(Lists.newArrayList());
				changeShiftLineWoField.refresh();
				changeShiftLineLotField.setValue(Lists.newArrayList());
				changeShiftLineLotField.refresh();
				changeShiftLineEqpField.setValue(Lists.newArrayList());
				changeShiftLineEqpField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void changeShiftListSelectionChanged(Object object) {
		try {
			ChangeShift succession = (ChangeShift) changeShiftListField.getListTableManager().getSelectedObject();
			if (succession != null && succession.getObjectRrn() != null) {
				changeShiftInfoField.setValue(succession);
				changeShiftInfoField.refresh();
				setInputTableManager(succession);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void setInputTableManager(ChangeShift succession) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<ChangeShiftLine> successionLines = adManager.getEntityList(Env.getOrgRrn(), ChangeShiftLine.class,
					Env.getMaxResult(), "changeShiftRrn = '" + succession.getObjectRrn() + "'", "objectRrn");
			List<ChangeShiftLine> commonLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_COMMENT.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineCommonField.setValue(commonLines);
			changeShiftLineCommonField.refresh();
			List<ChangeShiftLine> woLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_WO.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineWoField.setValue(woLines);
			changeShiftLineWoField.refresh();
			List<ChangeShiftLine> lotLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_LOT.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineLotField.setValue(lotLines);
			changeShiftLineLotField.refresh();
			List<ChangeShiftLine> eqpLines = successionLines.stream().filter(line -> SUCCESSION_TYPE_EQP.equals(line.getType())).collect(Collectors.toList());
			changeShiftLineEqpField.setValue(eqpLines);
			changeShiftLineEqpField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

}