package com.glory.mes.wip.changeshift.to;

import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.query.QueryDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.core.util.StringUtil;

public class ChangeShiftToQueryDialog extends QueryDialog {
	
	protected ChangeShiftToEditor editor;
	
	public ChangeShiftToQueryDialog(Shell parent, ListTableManager tableManager, IRefresh iRefresh, ChangeShiftToEditor editor) {
		super(parent, tableManager, iRefresh);
		this.editor = editor;
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
		String whereClause = StringUtil.relpaceWildcardCondition(getWhereClause());
		editor.refresh(whereClause + " And docStatus = 'APPROVED'");
	}

}
