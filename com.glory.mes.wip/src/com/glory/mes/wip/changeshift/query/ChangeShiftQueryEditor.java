package com.glory.mes.wip.changeshift.query;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.mes.wip.model.ChangeShift;
import com.glory.framework.base.entitymanager.glc.GlcEvent;

public class ChangeShiftQueryEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.query.ChangeShiftQueryEditorGlc";

	private static final String FIELD_QUERY = "query";

	protected QueryFormField queryField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		queryField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);

		subscribeAndExecute(eventBroker, queryField.getFullTopic(GlcEvent.EVENT_DOUBLE_CLICK), this::queryDoubleClick);
	}

	private void queryDoubleClick(Object object) {
		Object obj = queryField.getSelectedObject();
		ChangeShift successcion = (ChangeShift) obj;
		ChangeShiftQueryDetailDialog dialog = new ChangeShiftQueryDetailDialog(successcion);
		dialog.open();
	}

}