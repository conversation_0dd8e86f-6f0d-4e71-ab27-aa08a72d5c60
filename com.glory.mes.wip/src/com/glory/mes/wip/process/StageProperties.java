package com.glory.mes.wip.process;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ImportToolItem;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;
import org.eclipse.swt.widgets.Event;

import com.glory.framework.base.entitymanager.forms.EntityProperties;

public class StageProperties extends EntityProperties {

	public static final String TEMPLATE_FILE_NAME = "stage_template.xlsx";

	protected ToolItem itemImport;

	public StageProperties() {
		super();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	private void createToolItemImport(ToolBar tBar) {
		itemImport = new ImportToolItem(tBar, TEMPLATE_FILE_NAME, this.getClass());
		itemImport.addListener(ImportToolItem.EVENT_TYPE_IMPORT, new Listener() {
			@Override
			public void handleEvent(Event event) {
				StageUpload upload = new StageUpload(getTable().getName());
				if (upload.getUploadProgress().init()) {
					if (upload.run()) {
						refresh();
						getMasterParent().refresh();
					}
				}
			}
		});
	}

}