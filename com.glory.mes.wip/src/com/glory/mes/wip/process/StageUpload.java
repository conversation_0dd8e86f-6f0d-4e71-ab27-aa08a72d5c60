package com.glory.mes.wip.process;

import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.msg.StringUtil;
import com.glory.mes.prd.model.Stage;

public class StageUpload extends Upload {

	public StageUpload(String name) {
		super(name);
	}

	@Override
	protected void cudEntityList() {
		try {
			List<ADBase> uploadList = progress.getUploadList();

			ADManager adManager = Framework.getService(ADManager.class);
			if (uploadList != null && uploadList.size() > 0) {
				List<Stage> listStage = uploadList.stream().map(o -> ((Stage) o)).collect(Collectors.toList());
				// �����׶����Ʋ�����Ϊ��
				Optional<Stage> f = listStage.stream().filter(e -> StringUtil.isEmptyString(e.getName())).findFirst();
				if (f.isPresent()) {
					UI.showError(Message.getString("wip.stage_status_not_null"));
					return;
				}
				// ˳���ֻ������������
				Pattern pattern = Pattern.compile("[0-9]*");
				f = listStage.stream().filter(e -> !pattern.matcher(e.getSeqNo()).matches()).findFirst();
				if (f.isPresent()) {
					UI.showError(Message.getString("spc.chart_only _numbers"));
					return;
				}
				// �ж������׶����������ݿ��Ƿ����
				for (Stage stage : listStage) {
					String queryTerm = String.format("name = '%s'", stage.getName());
					List<Stage> listStageResult = adManager.getEntityList(Env.getOrgRrn(), Stage.class,
							Env.getMaxResult(), queryTerm, "");
					if (listStageResult.size() > 0) {
						UI.showError(Message.getString("wip.stage_status_existed"));
						return;
					}
				}
				// ����
				adManager.saveEntityList(listStage, Env.getSessionContext());
				UI.showInfo(Message.getString("com.import_success"));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
