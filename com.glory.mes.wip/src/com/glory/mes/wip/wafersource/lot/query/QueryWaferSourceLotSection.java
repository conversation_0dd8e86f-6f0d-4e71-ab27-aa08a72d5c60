package com.glory.mes.wip.wafersource.lot.query;

import org.eclipse.nebula.widgets.nattable.export.NatExporter;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class QueryWaferSourceLotSection extends QueryEntityListSection implements IRefresh {
	
	public QueryWaferSourceLotSection(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
//		createToolItemExport(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}	
	
	protected void exportAdapter() {
		try {
			new NatExporter(natTable.getShell()).exportSingleLayer(
					tableManager.getLayer(), natTable.getConfigRegistry());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
}
