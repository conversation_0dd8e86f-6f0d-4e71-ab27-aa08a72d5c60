package com.glory.mes.wip.wafersource.receive;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.IntSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ExportToolItemGlc;
import org.eclipse.swt.widgets.ImportToolItemGlc;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
//import com.glory.mes.mm.bom.BomLineUpload;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class ReceiveWaferSourceSection extends EntitySection {

	//public static final String WAFER_LOT_TABLE_NAME = "MMMComponentUnit";
	public static final String TABLE_NAME = "MMMComponentUnit";
	public static final String RECIEVE_WAFER_TEMPLATE_FILE_NAME = "waferrecieve_template.xlsx";


	public CheckBoxFixEditorTableManager manager;

	private ToolItem itemReceive;
	protected ToolItem waferLineImport;
	protected ExportToolItemGlc itemExport;
	
	public List<MLot> upLoadLots  = new ArrayList<MLot>() ; 

	public ReceiveWaferSourceSection() {
		super();
	}

	public ReceiveWaferSourceSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		initAdObject();
	}

	private void initAdObject() {
		MLot mlot = new MLot();
		mlot.setMainQty(null);
		setAdObject(mlot);
		refresh();
	}

	@Override
	protected void createSectionContent(Composite client) {
		try {
			final FormToolkit toolkit = form.getToolkit();
			mmng = form.getMessageManager();
			super.createSectionContent(client);

			Composite btnComposite = toolkit.createComposite(client, SWT.NONE);
			GridLayout layoutBtn = new GridLayout(3, false);
			btnComposite.setLayout(layoutBtn);
			GridData gd1 = new GridData(GridData.FILL_BOTH);
			gd1.horizontalAlignment = SWT.RIGHT;
			btnComposite.setLayoutData(gd1);
			SquareButton add = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonAdd()), null);
			add.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					addAdapter();
				}
			});

			SquareButton delete = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonDelete()), null);
			delete.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					delAdapter();
				}
			});

			Composite bodyComposite = toolkit.createComposite(client, SWT.NONE);
			bodyComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
			GridLayout layoutBody = new GridLayout(1, true);
			bodyComposite.setLayout(layoutBody);

			GridData gdLeftLest = new GridData(GridData.FILL_BOTH);
			gdLeftLest.heightHint = 350;
			Composite parameterCompLeft = toolkit.createComposite(bodyComposite, SWT.NONE);
			parameterCompLeft.setLayoutData(gdLeftLest);
			GridLayout layoutLeft = new GridLayout(1, true);
			parameterCompLeft.setLayout(layoutLeft);

			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			manager = new CheckBoxFixEditorTableManager(adTable);
			manager.newViewer(parameterCompLeft);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemReceive(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemImportLine(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemReceive(ToolBar tBar) {
		itemReceive = new ToolItem(tBar, SWT.PUSH);
		itemReceive.setText(Message.getString("common.receive"));
		itemReceive.setImage(SWTResourceCache.getImage("currentstep"));
		itemReceive.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				//receiveMLotAdapter();
				receiveMLotsAdapter();
			}
		});
	}

	protected void addAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			upLoadLots.clear();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
								detailForm.getCopyProperties());
					}
					MLot mlot = (MLot) getAdObject();
					BigDecimal averageQty = new BigDecimal(1);
					BigDecimal modQty = new BigDecimal(0);
					if (mlot.getTransSubQty() != null && BigDecimal.ZERO.compareTo(mlot.getTransMainQty()) == -1) {
						averageQty = mlot.getTransSubQty().divide(mlot.getTransMainQty(), 0, BigDecimal.ROUND_HALF_UP);
						modQty = (mlot.getTransSubQty().divideAndRemainder(mlot.getTransMainQty()))[1];
					}

					if (BigDecimal.ZERO.compareTo(mlot.getTransMainQty()) == -1) {
						List<MComponentUnit> units = new ArrayList<MComponentUnit>();
						for (int i = 1; i <= mlot.getTransMainQty().intValue(); i++) {		
							Map<String, Object> unitMap = new HashMap<String, Object>();
							unitMap.put("objectType", "MComponentUnit");	
							ComponentUnit componentUnit = new ComponentUnit();
							componentUnit.setLotId(mlot.getPartnerLotId());
							ComponentManager componentManager = Framework.getService(ComponentManager.class);
							List<String> componentIds = componentManager.generateComponentIds(componentUnit, unitMap, 1, Env.getSessionContext());
							MComponentUnit unit = new MComponentUnit();
							unit.setmComponentId(componentIds.get(0));
							unit.setMaterialName(mlot.getMaterialName());	
							unit.setMainQty(BigDecimal.ONE);
							if (mlot.getTransSubQty() != null
									&& BigDecimal.ZERO.compareTo(mlot.getTransMainQty()) == -1) {
								if (i == mlot.getTransMainQty().intValue()) {
									unit.setSubQty(averageQty.add(modQty));
								} else {
									unit.setSubQty(averageQty);
								}
							}
							unit.setPosition(String.valueOf(i));
							unit.setPartnerLotId(mlot.getPartnerLotId());
							unit.setDurable(mlot.getDurable());
							unit.setPartnerCode(mlot.getPartnerCode());
							unit.setLotComment(mlot.getLotComment());
							units.add(unit);
						}
						manager.setInput(units);
						upLoadLots.add(mlot);
					} else {
						UI.showInfo(Message.getString("mm.mlot_receive_must_more_than_zero"));
						return;
					}
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}

	private void delAdapter() {
		manager.getInput().removeAll(manager.getCheckedObject());
		manager.getCheckedObject().clear();
	}

	@SuppressWarnings("unchecked")
	protected void receiveMLotAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
								detailForm.getCopyProperties());
					}
					MLot mlot = (MLot) getAdObject();
					mlot.setSubUnitType(MLot.UNIT_TYPE_COMPONENT);

					if (BigDecimal.ZERO.compareTo(mlot.getTransMainQty()) == -1) {
						List<MComponentUnit> units = Lists.newArrayList((List<MComponentUnit>) manager.getInput());
						if (units.size() != mlot.getTransMainQty().intValue()) {
							UI.showInfo(Message.getString("mm.mlot_receive_mainqty_must_equal_component_size"));
							return;
						}

						/*BigDecimal totalQty = BigDecimal.ZERO;
						List<MComponentUnit> newUnits = new ArrayList<MComponentUnit>();
						for (int i = 1; i <= units.size(); i++) {
							//����������25����
							int lotSize = 25;
							
							if (units.get(i-1).getMainQty() == null || BigDecimal.ZERO.compareTo(units.get(i-1).getMainQty()) >= 0) {
								UI.showInfo(Message.getString("mm.mlot_receive_component_qty_must_more_than_zero"));
								return;
							}
							//��ComponentId��ȡ����.�����������
							String lotPosition = units.get(i-1).getmComponentId().substring(units.get(i-1).getmComponentId().indexOf(".") + 1, units.get(i-1).getmComponentId().length());
							int position = (Integer.parseInt(lotPosition) % lotSize != 0) ? (Integer.parseInt(lotPosition) % lotSize) : 25;
							
							units.get(i-1).setParentMLotId(mlot.getmLotId());
							units.get(i-1).setPosition(String.valueOf(position));
							newUnits.add(units.get(i-1));
							totalQty = totalQty.add(units.get(i-1).getMainQty());
						}*/
						
						IntSummaryStatistics a = units.stream().collect(Collectors.summarizingInt(u -> u.getMainQty().intValue()));
						mlot.setTransMainQty(new BigDecimal(a.getSum()));

						MMManager mmManager = Framework.getService(MMManager.class);
						Warehouse warehouse = null;
						if (mlot.getTransWarehouseRrn() != null || mlot.getTransWarehouseId() != null) {
							warehouse = new Warehouse();
							warehouse.setOrgRrn(Env.getOrgRrn());
							warehouse.setObjectRrn(mlot.getTransWarehouseRrn());
							warehouse.setWarehouseId(mlot.getTransWarehouseId());
							warehouse = mmManager.getWarehouse(warehouse);
							mmManager.receiveMLots2WarehouseByComponent(mlot, units, new MLotAction(), warehouse.getObjectRrn(), warehouse.getWarehouseId(), null, null, Env.getSessionContext());
						} else {
							mmManager.receiveMLotByComponent(mlot, units, new MLotAction(), Env.getSessionContext());
						}	

						UI.showInfo(Message.getString("common.receive_success"));
						this.setAdObject(createAdObject());
						refresh();

						manager.setInput(units);
						manager.refresh();
					} else {
						UI.showInfo(Message.getString("mm.mlot_receive_must_more_than_zero"));
						return;
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@SuppressWarnings("unchecked")
	protected void receiveMLotsAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
								detailForm.getCopyProperties());
					}
					
					for(MLot mlot : upLoadLots) {
						mlot.setSubUnitType(MLot.UNIT_TYPE_COMPONENT);

						if (BigDecimal.ZERO.compareTo(mlot.getTransMainQty()) == -1) {
							List<MComponentUnit> unitsAll = Lists.newArrayList((List<MComponentUnit>) manager.getInput());
							List<MComponentUnit> units =  unitsAll.stream().filter(person -> person.getPartnerLotId().equals(mlot.getPartnerLotId())).collect(Collectors.toList());

							IntSummaryStatistics a = units.stream().collect(Collectors.summarizingInt(u -> u.getMainQty().intValue()));
							mlot.setTransMainQty(new BigDecimal(a.getSum()));

							MMManager mmManager = Framework.getService(MMManager.class);
							Warehouse warehouse = null;
							if (mlot.getTransWarehouseRrn() != null || mlot.getTransWarehouseId() != null) {
								warehouse = new Warehouse();
								warehouse.setOrgRrn(Env.getOrgRrn());
								warehouse.setObjectRrn(mlot.getTransWarehouseRrn());
								warehouse.setWarehouseId(mlot.getTransWarehouseId());
								warehouse = mmManager.getWarehouse(warehouse);
								mmManager.receiveMLots2WarehouseByComponent(mlot, units, new MLotAction(), warehouse.getObjectRrn(), warehouse.getWarehouseId(), null, null, Env.getSessionContext());
							} else {
								mmManager.receiveMLotByComponent(mlot, units, new MLotAction(), Env.getSessionContext());
							}	

							UI.showInfo(Message.getString("common.receive_success"));
							this.setAdObject(createAdObject());
							refresh();
						} else {
							UI.showInfo(Message.getString("mm.mlot_receive_must_more_than_zero"));
							return;
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void createToolItemImportLine(ToolBar tBar) {
		waferLineImport = new ImportToolItemGlc(tBar, null, getTable().getAuthorityKey(), null, null);
		waferLineImport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				RecieveWaferUpload upload = new RecieveWaferUpload(getTable().getAuthorityKey(), null, TABLE_NAME, manager, upLoadLots);
				if (upload.getUploadProgress().init()) {
					if (upload.run()) {
						refresh();
					}
				}
			}
		});
	}
	
	protected void createToolItemExport(ToolBar tBar) {
		itemExport = new ExportToolItemGlc(tBar, null, getTable().getAuthorityKey(), null, null);
		itemExport.setImage(SWTResourceCache.getImage("export"));
		itemExport.setText(Message.getString(ExceptionBundle.bundle.CommonExport()));
		itemExport.setEnabled(true);
		itemExport.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				exportAdapter();
			}
		});
	}
	
	protected void exportAdapter() {
		try {
			itemExport.exportAdapter(manager);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
