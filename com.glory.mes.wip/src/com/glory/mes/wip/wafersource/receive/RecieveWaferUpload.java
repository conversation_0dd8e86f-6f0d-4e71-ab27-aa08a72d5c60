package com.glory.mes.wip.wafersource.receive;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.Material;

public class RecieveWaferUpload extends Upload {
	protected CheckBoxFixEditorTableManager manager;
	protected List<MLot> upLoadLots = new ArrayList<MLot>();
	
	public RecieveWaferUpload(String authorityName, String buttonName,String name,CheckBoxFixEditorTableManager manager,List<MLot> upLoadLots) {
		super(authorityName, buttonName, name);
		this.manager = manager;
		this.upLoadLots = upLoadLots;
	}

	@Override
	protected void cudEntityList() {
		try {
			List<ADBase> uploadList = progress.getUploadList(); 
			upLoadLots.clear();
	        
	        // ��������һ�����ݲ����ϣ�������β�����
	        List<MComponentUnit> units = new ArrayList<MComponentUnit>();
	        String tempKhLotId  = ""; 
	        //�����е�������ת����MComponentUnit
	        if (uploadList != null && uploadList.size() > 0) {
	        	for(int i = 0; i < uploadList.size(); i++) {
	        		MComponentUnit loadLotWafer =  (MComponentUnit) uploadList.get(i);
	        		loadLotWafer.setMainQty(BigDecimal.ONE);
	        		units.add(loadLotWafer);
	        	}
	        }
	        units.sort((MComponentUnit unit1,MComponentUnit unit2) -> unit1.getPartnerLotId().compareTo(unit2.getPartnerLotId()));
	        //�������е�Lot
	        for(MComponentUnit compunit :units) {
	        	String newLotId = compunit.getPartnerLotId();
        		if(!newLotId.equals(tempKhLotId)) {
        			tempKhLotId = newLotId;
        			MLot mlot = new MLot();
        			mlot.setPartnerLotId(compunit.getPartnerLotId());
        			mlot.setPartnerCode(compunit.getPartnerCode());
        			mlot.setLotComment(compunit.getLotComment());
        			mlot.setDurable(compunit.getDurable());
        			mlot.setMaterialName(compunit.getMaterialName());
        			upLoadLots.add(mlot);
        		}
	        }
	        //check���ݣ�Material
	        List<String> emptyMaterialsLot = new ArrayList<String>();
	        MMManager mmManager = Framework.getService(MMManager.class);
	        List<MComponentUnit> delComUnits = new ArrayList<MComponentUnit>();
	        List<MLot> delMLots = new ArrayList<MLot>();
	        if(units != null && units.size() > 0) {
	        	for(MComponentUnit compunit: units) {
	        		Material material = mmManager.getMaterial(Env.getOrgRrn(), compunit.getMaterialName());
	        		if(material == null) {
	        			emptyMaterialsLot.add(compunit.getPartnerLotId());
	        			delComUnits.addAll(units.stream().filter(person -> person.getPartnerLotId().equals(compunit.getPartnerLotId())).collect(Collectors.toList()));
	        			delMLots.addAll(upLoadLots.stream().filter(person -> person.getPartnerLotId().equals(compunit.getPartnerLotId())).collect(Collectors.toList()));
	        		}
	        	}
	        }
	        //�Ƴ�������������lot�Լ�MComponentUnit
        	if(delComUnits.size() != 0) {
        		units.removeAll(delComUnits);
        		upLoadLots.removeAll(delMLots);
        	}
	        //����Lot����
	        for(MLot mlot : upLoadLots) {
	        	List<MComponentUnit> lotUnits =  units.stream().filter(person -> person.getPartnerLotId().equals(mlot.getPartnerLotId())).collect(Collectors.toList());
				mlot.setTransMainQty(new BigDecimal(lotUnits.size()));
				mlot.setReceiveMainQty(new BigDecimal(lotUnits.size()));
	        }
	        manager.setInput(null);
        	manager.setInput(units);
	        if(emptyMaterialsLot.size() != 0) {
	        	StringBuffer sb = new StringBuffer("Lot��");
        		for(String mt : emptyMaterialsLot) {
        			sb.append(mt + ",");
        		}
        		sb.append("\n" + Message.getString("wip.receive_material_not_exist"));
        		UI.showInfo(sb.toString());
	        }
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
