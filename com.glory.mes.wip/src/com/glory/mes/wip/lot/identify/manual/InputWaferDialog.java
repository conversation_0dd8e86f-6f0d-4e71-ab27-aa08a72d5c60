package com.glory.mes.wip.lot.identify.manual;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.IManagedForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.lot.run.trackout.identify.manual.ManualIdentifyTrackOutForm;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.QtyUnit;

public class InputWaferDialog extends Dialog {

	private IManagedForm form;
	protected Lot lot;
	protected List<ComponentUnit> componentUnits;
	protected ManualIdentifyTrackOutForm inputWaferForm;
	
	public InputWaferDialog(Shell parentShell) {
		super(parentShell);
	}
		
	public InputWaferDialog(Shell parentShell, IManagedForm form, 
			Lot lot, List<ComponentUnit> componentUnits) {
		this(parentShell);
		this.form = form;
		this.lot = lot;
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(380, 90, 780, 595);
		getShell().setMinimumSize(780,595);
		getShell().setText(Message.getString("wip.identify.manual_wafer_info"));
	}
	
	@Override
	protected Control createDialogArea(Composite parent) {
		Composite content = new Composite(parent, SWT.BORDER);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		if (componentUnits == null || componentUnits.size() == 0) {
			if (ComponentUnit.getUnitType().equals(lot.getUnitType())) {
				try {
					ADManager adManager = Framework.getService(ADManager.class);
					componentUnits = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, Integer.MAX_VALUE, "parentUnitRrn = " + lot.getObjectRrn(), "position");
				} catch (Exception e) {
				}
			} else {
				componentUnits = createComponentUnit(BigDecimal.ONE, null);
			}
		}
		inputWaferForm = new ManualIdentifyTrackOutForm(content,SWT.NONE, getComponentUnits(), form.getMessageManager());
		inputWaferForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		return content;
	}

	public List<ComponentUnit> createComponentUnit(BigDecimal mainQty, BigDecimal subQty) {
		List<ComponentUnit> componentUnits = new ArrayList<ComponentUnit>(); 
		BigDecimal lotQty = lot.getMainQty();
		BigDecimal[] lotComp = lotQty.divideAndRemainder(mainQty);
		int componentNumber = lotComp[0].intValue();
		BigDecimal mainRemainder = lotComp[1];
		BigDecimal subRemainder = lot.getSubQty();
		
		long position = 1;
		for (int i = 1; i <= componentNumber; i++) {
			ComponentUnit component = new ComponentUnit();
			component.setIsActive(true);
			component.setCreatedBy(Env.getUserName());
			component.setCreated(new Date());
			component.setOrgRrn(lot.getOrgRrn());
			component.setPosition(String.valueOf(position));
			component.setParentUnitRrn(lot.getObjectRrn());
			component.setMainQty(mainQty);
			component.setState(lot.getState());
			if (subRemainder != null) {
				if (subQty != null) {
					component.setSubQty(subQty);
				} else {
					component.setSubQty(lot.getSubQty().multiply(mainQty).divide(lot.getMainQty(),3,BigDecimal.ROUND_HALF_EVEN));
				}
				subRemainder = subRemainder.subtract(component.getSubQty());
			}
			componentUnits.add(component);
			position++;
		}
		if (mainRemainder.compareTo(BigDecimal.ZERO) > 0) {
			ComponentUnit component = new ComponentUnit();
			component.setIsActive(true);
			component.setCreatedBy(Env.getUserName());
			component.setCreated(new Date());
			component.setOrgRrn(lot.getOrgRrn());
			component.setPosition(String.valueOf(position));
			component.setParentUnitRrn(lot.getObjectRrn());
			component.setMainQty(mainRemainder);
			component.setState(lot.getState());
			if (subRemainder != null) {
				component.setSubQty(subRemainder);
			}
			componentUnits.add(component);
			position++;
		}
		return componentUnits;
	}
	
	@Override
	public void okPressed(){
		try {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			setComponentUnits((List<ComponentUnit>)inputWaferForm.getComponentUnitList());
			for (ComponentUnit componentUnit : getComponentUnits()) {
				if (componentUnit.getComponentId() == null || componentUnit.getComponentId().trim().length() == 0) {
					UI.showError(Message.getString("wip.identify.manual_null_error"));
					return;
				}
			}
			if (lot.getObjectRrn() != null) {
				if (QtyUnit.getUnitType().equals(lot.getUnitType())) {
					componentManager.identityComponent(lot, getComponentUnits(), Env.getSessionContext());
				} else {
					componentManager.saveComponents(getComponentUnits(), Env.getSessionContext());
				}
				UI.showInfo(Message.getString("wip.identify.manual_input_success"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		super.okPressed();
	}

	public void setComponentUnits(List<ComponentUnit> componentUnits) {
		this.componentUnits = componentUnits;
	}

	public List<ComponentUnit> getComponentUnits() {
		return componentUnits;
	};

}
