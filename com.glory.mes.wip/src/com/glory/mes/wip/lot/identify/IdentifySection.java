package com.glory.mes.wip.lot.identify;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;

public class IdentifySection extends LotSection {
	private static final Logger logger = Logger.getLogger(IdentifySection.class);

	protected AuthorityToolItem itemIdentify;
	
	public static final String KEY_IDENTIFY = "identify";

	public IdentifySection() {
		super();
	}

	public IdentifySection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.identify_section"));
		initAdObject();
	}

	
	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemIdentify(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemIdentify(ToolBar tBar) {
		itemIdentify = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_IDENTIFY);
		itemIdentify.setText(Message.getString("wip.identify"));
		itemIdentify.setImage(SWTResourceCache.getImage("idendity"));
		itemIdentify.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				identifyAdapter(event);
			}
		});
	}


	protected void identifyAdapter(SelectionEvent event) {
		try {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			Lot lot = (Lot) getAdObject();
			if (lot != null && lot.getObjectRrn() != null) {
				lot.setOperator1(Env.getUserName());
			    lot = componentManager.identityComponent(lot, Env.getSessionContext());
				List<ProcessUnit> components = lot.getSubProcessUnit();
				IdentifyLotDialog dialog = new IdentifyLotDialog(UI.getActiveShell(), components, this);
				if (dialog.open() == Dialog.OK) {
					UI.showError(Message.getString("com_success"));
					refresh();
				}
			} else {
				UI.showError(Message.getString("wip.wo_add_lot"));
			}
		} catch (Exception e) {
			logger.error("Method identifyAdapter() in IdentifySection",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void statusChanged(String newStatus){}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
				if (carrierLotComposite != null && carrierLotComposite.getLotTableManager() != null) {
					carrierLotComposite.loadComponentUnits(Lists.newArrayList((Lot)adBase));
				}
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
	}
}
