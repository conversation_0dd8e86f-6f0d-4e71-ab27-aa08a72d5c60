package com.glory.mes.wip.lot.identify.manual;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Table;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.viewers.FixEditorTableManager;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.ComponentUnit;

public class InputWaferForm extends EntityForm {

	private static final Logger logger = Logger.getLogger(InputWaferForm.class);
	
	private static final String TABLE_NAME = "WIPComponentUnitManual";
	
	private TableViewer viewer;
	private Table table;
	private List<ComponentUnit> componentUnitList;
	private FixEditorTableManager tableManager;

	public InputWaferForm(Composite parent, int style, Object object,
			IMessageManager mmng) {
		super(parent, style, object, mmng);
		this.componentUnitList = (List<ComponentUnit>)object;
		this.createForm();
	}

	@Override
	public void createForm() {
		try {
			super.createForm();
			Composite body = form.getBody();
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new FixEditorTableManager(adTable);
			viewer = (TableViewer) tableManager.createViewer(body, new FormToolkit(getShell().getDisplay()),50);
			table = viewer.getTable();
			table.addListener(SWT.MeasureItem, new Listener() {
				public void handleEvent(Event event) {
					event.width = table.getGridLineWidth();
				 // ���ÿ��
					event.height = (int) Math.floor(event.gc .getFontMetrics().getHeight() * 1.5);
				} 
			});
			tableManager.setInput(componentUnitList);
			table = viewer.getTable();
			GridData gd = new GridData(GridData.FILL_BOTH);
			table.setLayoutData(gd);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public TableViewer getViewer() {
		return viewer;
	}
	
	public List<ComponentUnit> getComponentUnitList() {
		return (List<ComponentUnit>)tableManager.getInput();
	}

}
