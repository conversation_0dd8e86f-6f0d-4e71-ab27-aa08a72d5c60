package com.glory.mes.wip.lot.identify;

import java.util.List;

import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;

public class IdentifyLotDialog extends BaseTitleDialog {
	
	private static int MIN_DIALOG_WIDTH = 400;
	private static int MIN_DIALOG_HEIGHT = 300;
	private List input;
	private EntitySection parentSection;
	
	public IdentifyLotDialog(Shell parentShell, List input,EntitySection parentSection) {
		super(parentShell);
		this.input = input;
		this.parentSection = parentSection;
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
		setTitle(Message.getString("wip.identify_dialog_title"));
		setMessage(Message.getString("wip.identify_dialog_messege"));
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "WIPComponentUnitAuto");
			ListTableManager tableManager = new ListTableManager(adTable);
			tableManager.setIndexFlag(true);
			tableManager.newViewer(parent);
			tableManager.setInput(input);
		} catch (Exception e) {
			e.printStackTrace();
		}
		setTitle(Message.getString("wip.wafer_id"));
		setMessage(Message.getString("wip.generated_wafer_id"));
		return parent;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}

	@Override
	protected void okPressed() {
		super.okPressed();
		refresh();
	}

	private void refresh() {
		if(parentSection != null)
			parentSection.refresh();
	}
	
}
