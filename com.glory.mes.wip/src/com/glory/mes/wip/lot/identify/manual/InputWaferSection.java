package com.glory.mes.wip.lot.identify.manual;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class InputWaferSection extends LotSection {

	private static final Logger logger = Logger.getLogger(LotSection.class);
	protected static final int OK = 0;
	protected ToolItem input; 
	
	public InputWaferSection(ADTable table) {
		super(table);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);		
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemInput(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	public void createToolItemInput(ToolBar tBar){
		input = new ToolItem(tBar, SWT.NULL);
		input.setText(Message.getString("wip.identify.manual_inputButton"));
		input.setImage(SWTResourceCache.getImage("idendity"));
		input.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				inputAdapter(e);
			}
		});
	}
	
	protected void inputAdapter(SelectionEvent event) {
		InputWaferDialog inputWaferDialog = new InputWaferDialog(Display.getCurrent().getActiveShell(),form, (Lot)getAdObject(), null);
		inputWaferDialog.open();
		refresh();
	}
	
	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.identify.manual_input_wafer"));
		initAdObject();
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Lot newBase = (Lot)this.getAdObject();
		if (newBase != null ){
			statusChanged(newBase.getState());
		} else {
			statusChanged("");
		}
	}
	
	@Override
	public void statusChanged(String newStatus) {
		if(newStatus == null || "".equals(newStatus.trim())) {
			input.setEnabled(false);
		} else {
			input.setEnabled(true);
		}
	}

}
