package com.glory.mes.wip.lot.changehold;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeholdForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(ChangeholdForm.class);
	List<LotHold> lotHolds = new ArrayList<LotHold>();
	private IField holdLots;
	private static final String TABLECODE = "HoldLot";
	private static final String TABLE_ID = "holdLots";
	
	private IField fieldHoldCode;
	private static final String HOLDCODE = "HoldCode";
	private static final String HOLDCODE_ID = "holdCode";
	
	private IField fieldHoldOwner;
	private static final String HOLDOWNER = "HoldOwner";
	private static final String HOLDOWNER_ID = "holdOwner";
	
	private IField fieldHoldReason;
	private static final String HOLDREASON = "HoldReason";
	private static final String HOLDREASON_ID = "holdReason";
	
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";

	private LotAction lotAction = new LotAction();
	
	private ChangeholdSection changeHoldSection;
	private Lot lot;
	private ListTableManager tableManager;
	protected LotHold lotHold;
	
	public ChangeholdForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			holdLots = new ChangeholdTableField(TABLE_ID, Message.getString("common.please_select"), createTableViewer());
			ADField adField = new ADField();
			adField.setIsSameline(true); 
			holdLots.setADField(adField);
			
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(adField);
			
			fieldHoldCode = createOwnerRefList(HOLDCODE_ID, Message.getString("wip.holdcode") + "*", "HoldCode", false);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADUserGroup");
			ADRefTable refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setKeyField("name");
			refTable.setTextField("name");
			fieldHoldOwner = createSearchField(HOLDOWNER_ID, Message.getString("wip.changehold_owner") + "*", adTable, refTable, "", 12);
			
			fieldHoldReason = createText(HOLDREASON_ID, Message.getString("wip.changehold_reason"), "", 1000);
			fieldHoldReason.setADField(adField);
			
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 1000);
			fieldComment.setADField(adField);
			
    	

			addField(TABLECODE, holdLots);
			addField("Separator", separatorField);
			addField(HOLDCODE, fieldHoldCode);
			addField(HOLDOWNER, fieldHoldOwner);			
			addField(HOLDREASON, fieldHoldReason);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("ReleaseForm : Init listItem", e);
		}
	}
	
	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField)
					&& !f.equals(fieldHoldCode)
					&& !f.equals(fieldHoldOwner)
					&& !f.equals(fieldHoldReason)
					&& !f.equals(fieldComment) 
					&& !f.equals(holdLots)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public void setEnabled() {
		if (object != null && object instanceof ADBase) {
			ADBase base = (ADBase) object;
			for (IField f : fields.values()) {
				ADField adField = adFields.get(f.getId());
				if (adField != null && !adField.getIsEditable()) {
					if (base.getObjectRrn() == null || base.getObjectRrn() == 0) {
						f.setEnabled(true);
					} else {
						f.setEnabled(false);
					}
				}
			}
			fieldHoldCode.setEnabled(true);
			fieldHoldReason.setEnabled(true);
			fieldHoldOwner.setEnabled(true);
			fieldComment.setEnabled(true);
		}
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			this.getAllLotHolds();
			tableManager.setInput(this.getLotHolds());
			fieldComment.setEnabled(true);
		} catch (Exception e) {
			logger.error("ReleaseForm : refresh()", e);
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			lotHold = getSelectedLotHold();
			if (lotHold == null || lotHold.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.release_no_exist_lot"));
				return false;
			}

			Object holdCode = fieldHoldCode.getValue();
			if(holdCode != null){
				String holdCodeStr = holdCode.toString();
				if(holdCodeStr.trim().length() > 0){
					lotHold.setHoldCode(holdCodeStr);
				}
			}
			
			Object holdComment = fieldComment.getValue();
			if(holdComment != null){
				String holdCommentStr = holdComment.toString();
				if(holdCommentStr.trim().length() > 0){
					lotHold.setHoldComment(holdCommentStr);
				}
			}
			
			Object holdReason = fieldHoldReason.getValue();
			if(holdReason != null){
				String holdReasonStr = holdReason.toString();
				if(holdReasonStr.trim().length() > 0){
					lotHold.setHoldReason(holdReasonStr);
				}
			}
			
			lotHold.setHoldOwner(fieldHoldOwner.getValue().toString());
			return true;
		}
		return false;
	}

	@Override
	public boolean validate() {
		IMessageManager mmng = form.getMessageManager();
		mmng.removeAllMessages();
		boolean validFlag = true;
		if (fieldHoldOwner.getValue() == null 
			|| "".equals(String.valueOf(fieldHoldOwner.getValue()).trim())) {
			mmng.addMessage(Message.getString("wip.changehold_owner"), String
					.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()),
							Message.getString("wip.changehold_owner")), null,
					IMessageProvider.ERROR, fieldHoldOwner.getControls()[1]);
			validFlag = false;
		}
		if (fieldHoldCode.getValue() == null 
				|| "".equals(String.valueOf(fieldHoldCode.getValue()).trim())) {
			mmng.addMessage(Message.getString("wip.holdcode"), String
					.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()),
							Message.getString("wip.holdcode")), null,
					IMessageProvider.ERROR, fieldHoldCode.getControls()[1]);
			validFlag = false;
		}
		return validFlag;
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	public void getAllLotHolds() {
		try {
			ADManager lotManager = Framework.getService(ADManager.class);
			List<LotHold> lotHolds = new ArrayList<LotHold>();
			if(!StringUtil.isEmpty(lot.getLotId())) {
				lotHolds =lotManager.getEntityList(Env.getOrgRrn(), LotHold.class, 
						Env.getMaxResult(), " lotRrn = " + lot.getObjectRrn(), "");
			}
			this.setLotHolds(lotHolds);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public List<LotHold> getLotHolds() {
		return lotHolds;
	}

	public void setLotHolds(List<LotHold> lotHolds) {
		this.lotHolds = lotHolds;
	}

	public ListTableManager createTableViewer() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTableHold = adManager.getADTable(Env.getOrgRrn(), "WIPLotChangehold");
			tableManager = new ListTableManager(adTableHold);
			tableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					if (tableManager.getSelectedObject() != null) {
						Object obj = tableManager.getSelectedObject();;
						if (obj != null) {
							LotHold lotHold = (LotHold)obj;
							if (fieldHoldCode != null) {
								PrdManager prdManager;
								try {
									prdManager = Framework.getService(PrdManager.class);
									Lot lot = (Lot) object;
						            if (lot != null && lot.getObjectRrn() != null) {
						                Step step = new Step();
						                step.setObjectRrn(lot.getStepRrn());
						                step = (Step) prdManager.getSimpleProcessDefinition(step);
						                XCombo combo = (XCombo) fieldHoldCode.getControls()[fieldHoldCode.getControls().length - 1];
						                ADManager adManager = Framework.getService(ADManager.class);
						                ADTable adTable  = adManager.getADTable(Env.getOrgRrn(), RefTableField.TABLENAME_USER_REFLIST);
							    		
						                String whereClause = " referenceName = '" + step.getHoldCodeSrc() + "'";
										List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(),
												Env.getMaxResult(), whereClause, null);
										ADURefList adURefList = new ADURefList();
										adURefList.setKey(lotHold.getHoldCode());
										adURefList.setText(lotHold.getHoldCode());
										adURefList.setDescription(lotHold.getHoldCode());
										list.add(adURefList);
										combo.setInput(list);
						            } 
						            fieldHoldCode.setValue(lotHold.getHoldCode());
						            fieldHoldCode.refresh();
								} catch (Exception e) {
									e.printStackTrace();
								}
							}

							if (fieldHoldReason != null) {
								fieldHoldReason.setValue(lotHold.getHoldReason());
								fieldHoldReason.refresh();
							}

							if (fieldComment != null) {
								fieldComment.setValue(lotHold.getHoldComment());
								fieldComment.refresh();
							}
						}
					}
				}
			});
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return tableManager;
	}
	 
	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public ChangeholdSection getReleaseSection() {
		return changeHoldSection;
	}

	public void setReleaseSection(ChangeholdSection releaseSection) {
		this.changeHoldSection = releaseSection;
	}

	public LotHold getSelectedLotHold() {
		if (tableManager.getSelectedObject() != null) {
			Object object = tableManager.getSelectedObject();
			return (LotHold)object;
		}
		return null;
	}
	
	public LotHold getLotHold() {
		return lotHold;
	}
	
}
