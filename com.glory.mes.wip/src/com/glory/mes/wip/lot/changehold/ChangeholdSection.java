package com.glory.mes.wip.lot.changehold;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.framework.security.model.ADUserGroup;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.mes.wip.client.LotManager;

public class ChangeholdSection extends LotSection {

	protected AuthorityToolItem itemChangehold;
	protected ChangeholdForm changeholdForm;

	public static String KEY_CHANGEHOLD = "changehold";
	
	public ChangeholdSection() {
		super();
	}

	public ChangeholdSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.changehold_section"));
		initAdObject();
		changeholdForm.setReleaseSection(this);
	}

	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemchangehold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemchangehold(ToolBar tBar) {
		itemChangehold = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_CHANGEHOLD);
		itemChangehold.setText(Message.getString("wip.changehold"));
		itemChangehold.setImage(SWTResourceCache.getImage("hold-lot"));
		itemChangehold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changehold(event);
			}
		});
	}

	protected void changehold(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null && null != getAdObject().getObjectRrn()) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					
					LotHold lotHold = changeholdForm.getSelectedLotHold();
					
					if (lotHold == null) {
						UI.showInfo(Message.getString("wip.release_no_exist_lot"));
						return;
					} else {//��鵱ǰ�û���û�е�ǰ���ε�changHoldȨ��
						ADManager adManager = Framework.getService(ADManager.class);
						LotHold checkedHold = (LotHold) adManager.getEntity(lotHold);
						String holdOwner =  checkedHold.getHoldOwner();
						if(holdOwner != null && holdOwner.trim().length() > 0){//û�б����������ʱ��������
							List<ADUserGroup> adUserGroups = adManager.getEntityList(Env.getOrgRrn(), ADUserGroup.class,
									Env.getMaxResult(), " name = '" + holdOwner + "'", "");
							if (CollectionUtils.isEmpty(adUserGroups)) {
								if (!UI.showConfirm(Message.getString("error.usergroup_is_not_exist") + Message.getString("common.whether_to_continue"))) {
									return;
								}
							} else {
								ADUserGroup userGroup = (ADUserGroup) adManager.getEntity(adUserGroups.get(0));
								boolean flag = false;
								for (ADUser adUser : userGroup.getUsers()) {
									if(adUser.getObjectRrn() == Env.getUserRrn()){
										flag = true;
										break;
									}
								} 
								if(!flag){
									UI.showError(Message.getString("wip.changehold_permission"));
									return;
								}
							}
						}
					}
					Lot lot = changeholdForm.getLot();
					lot.setOperator1(Env.getUserName());
					
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.changeHoldLot(lot, lotHold, Env.getSessionContext());
					
					UI.showInfo(Message.getString("wip.changehold_successed"));// ������ʾ��
					refresh();
				}
			} else {
				UI.showInfo(Message.getString("wip.release_not_null"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		changeholdForm = new ChangeholdForm(composite, SWT.NONE, tab, mmng);
		return changeholdForm;
	}

	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		Lot lot = (Lot) getAdObject();
		changeholdForm.setLot(lot);
		super.refresh();
	}

	public void statusChanged(String newStatus) {
		if (null == newStatus || "".equals(newStatus)) {
			itemChangehold.setEnabled(false);
		} else {
			itemChangehold.setEnabled(true);
		}
	}

}
