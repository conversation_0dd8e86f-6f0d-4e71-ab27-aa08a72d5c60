package com.glory.mes.wip.lot.changehold;

import java.util.ArrayList;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class ChangeholdTableField extends AbstractField {

	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
    protected ListTableManager tableManager;
    
    public ChangeholdTableField(String id, String label, ListTableManager tableManager) {
        super(id);
        this.tableManager = tableManager;
        this.label = label;
    }
    
    public void clear() {
    	tableManager.setInput(new ArrayList<Object>());
	}
   
	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		int i = 0;
		String labelStr = getLabel();
        if (labelStr != null) {
        	mControls = new Control[2];
        	Label label = toolkit.createLabel(composite, labelStr);
            mControls[0] = label;
            i = 1;
        } else {
        	mControls = new Control[1];
        }
        
        Composite tableContainer = toolkit.createComposite(composite, SWT.NONE);
        GridData gd = new GridData(GridData.FILL_BOTH);
        gd.grabExcessHorizontalSpace = true;
        gd.horizontalAlignment = SWT.FILL;
        gd.heightHint = 100;
		gd.widthHint = 420;
        tableContainer.setLayoutData(gd);
        tableContainer.setLayout(new GridLayout());
       
        tableManager.newViewer(tableContainer);
    	tableContainer.setLayoutData(gd);
        mControls[i] = tableContainer;
	}

    public Label getLabelControl() {
        Control[] ctrl = getControls();
        if(ctrl.length >  1) {
            return (Label)ctrl[0];
        } else {
            return null;
        }
    }
    
	@Override
	public void refresh() {

	}
	
	public String getFieldType() {
		return "tablelist";
	}
}

