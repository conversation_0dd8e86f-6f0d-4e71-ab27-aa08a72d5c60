package com.glory.mes.wip.lot.scrap;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.wip.model.Lot;

public abstract class ScrapDialog extends BaseTitleDialog {
	
	protected Lot lot;

	public ScrapDialog(Shell parentShell) {
		super(parentShell);
	}

	public ScrapDialog(Shell parent, Lot lot){
		this(parent);
		this.lot = lot;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("trackin-dialog"));
	    setTitle(Message.getString("wip.scrap_lot"));
	    setMessage(Message.getString("wip.scrap_lot_Info"));

	    createTableContent(parent);
		return parent;
	}
		
	protected Control createTableContent(Composite parent) {
		FormToolkit toolkit = new FormToolkit(UI.getActiveShell().getDisplay());
		
	    Composite content = toolkit.createComposite(parent);
	    content.setLayoutData(new GridData(GridData.FILL_BOTH));
	    content.setLayout(new GridLayout(1, false));
	    
	    
	    createFrom(content, toolkit);    
	    return parent;
	}

	public abstract void createFrom(Composite composite, FormToolkit toolkit);
	
}