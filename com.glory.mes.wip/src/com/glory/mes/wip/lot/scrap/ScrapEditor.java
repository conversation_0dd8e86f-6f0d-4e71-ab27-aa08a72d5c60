package com.glory.mes.wip.lot.scrap;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.Message;
import com.glory.mes.wip.lot.LotEditor;

public class ScrapEditor extends LotEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.scrap.ScrapEditor";
	
	@Inject
	protected IEventBroker eventBroker;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		super.postConstruct(parent);
		
		ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);
		createSection(adTable, adEditor);
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);
		ManagedForm mform = new ManagedForm(toolkit, form);
		
		Composite body = form.getBody();
		configureBody(body);
		section.createContents(mform, body);
				
//		mPart.setLabel(Message.getString("wip.scrap_lotcn"));
	}
	
	protected void createSection(ADTable adTable, ADEditor adEditor) {
		section = new ScrapSection(adTable, this);
	}

	public IEventBroker getEventBroker() {
		return eventBroker;
	}

	public MPart getMpart() {
		return mPart;
	}
}
