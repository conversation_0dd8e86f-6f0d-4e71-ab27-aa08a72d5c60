package com.glory.mes.wip.lot.scrap.supplement;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.LotScrap;

public class ScrapSupplementEntityForm extends EntityForm {

	private static final Logger logger = Logger.getLogger(ScrapSupplementEntityForm.class);

	private static final String SCRAP_CODE = "ScrapCodeComp";

	protected IField fieldDefectCode;
	protected static final String DEFECTCODE = "DefectCode";
	protected static final String DEFECTCODE_ID = "defectCode";
	protected IField fieldReasonCode;
	protected static final String REASONCODE = "ReasonCode";
	protected static final String REASONCODE_ID = "reasonCode";
	protected IField fieldComments;
	protected static final String COMMENTS = "Comments";
	protected static final String COMMENTS_ID = "comments";

	public ScrapSupplementEntityForm(ADManager adManager, Composite parent, int style, Object object,
			List<ADField> adfields, int gridY, IMessageManager mmng) {
		super(adManager, parent, style, object, adfields, gridY, mmng);
	}

	@Override
	protected void createContent() {
		super.createContent();
		getForm().getBody().setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
	}

	@Override
	public void addFields() {
		try {
			ADField mandatoryADField = new ADField();
			mandatoryADField.setIsMandatory(true);

			fieldDefectCode = createUserRefList(DEFECTCODE_ID, Message.getString("wip.scrapcode_lot") + "*",
					getScrapCode(), true);
			fieldDefectCode.setADField(mandatoryADField);

			fieldReasonCode = createText(REASONCODE, Message.getString("wip.scrap_reason") + "*", 64);
			fieldReasonCode.setADField(mandatoryADField);

			fieldComments = createText(COMMENTS_ID, Message.getString("wip.remark"), "", 128);

			addField(DEFECTCODE, fieldDefectCode);
			addField(REASONCODE, fieldReasonCode);
			addField(COMMENTS, fieldComments);
		} catch (Exception e) {
			logger.error("ScrapSupplementEntityForm : addFields()", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected String getScrapCode() {
		return SCRAP_CODE;
	}

	@Override
	public void refresh() {
		super.refresh();
		((RefTableField) fieldDefectCode).setValue(null, true);
		((TextField) fieldReasonCode).setText("");
		((TextField) fieldComments).setText("");
	}

	public LotScrap getScrapAction() {
		if (saveToObject()) {
			LotScrap lotAction = new LotScrap();
			lotAction.setActionCode(DBUtil.toString(fieldDefectCode.getValue()));
			lotAction.setActionReason(DBUtil.toString(fieldReasonCode.getValue()));
			lotAction.setActionComment(DBUtil.toString(fieldComments.getValue()));
			return lotAction;
		}

		return null;
	}

	@Override
	public boolean validate() {
		mmng.removeAllMessages();

		boolean defectCodeIsNull = GenericValidator.isBlankOrNull((String) fieldDefectCode.getValue());
		boolean reasonCodeIsNull = GenericValidator.isBlankOrNull((String) fieldReasonCode.getValue());
		boolean validFlag = true;
		if (!defectCodeIsNull) {
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(DEFECTCODE, String.format(Message.getString("common.ismandatry"), DEFECTCODE), null,
					IMessageProvider.ERROR, fieldDefectCode.getControls()[1]);
		}

		if (!reasonCodeIsNull) {
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(REASONCODE, String.format(Message.getString("common.ismandatry"), REASONCODE), null,
					IMessageProvider.ERROR, fieldReasonCode.getControls()[1]);
		}

		return validFlag;
	}

	@Override
	public void loadFromObject() {
	}

	@Override
	public boolean saveToObject() {
		if (!validate()) {
			return false;
		}
		return true;
	}

	@Override
	public Color getBackground() {
		return SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE);
	}
}
