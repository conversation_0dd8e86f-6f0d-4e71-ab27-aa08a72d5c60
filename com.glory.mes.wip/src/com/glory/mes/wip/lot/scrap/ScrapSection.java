package com.glory.mes.wip.lot.scrap;

import java.util.function.Consumer;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class ScrapSection extends LotSection  {

	public static final String KEY_SCRAP = "scrap";
	
	protected AuthorityToolItem itemScrap;
	protected EntityForm entityForm;
	protected ScrapEditor scrapEditor;

	public ScrapSection() {
		super();
	}

	public ScrapSection(ADTable table, ScrapEditor scrapEditor) {
		super(table);
		this.scrapEditor = scrapEditor;
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.scrap_sectiontitle"));
		initAdObject();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemScrap(ToolBar tBar) {
		itemScrap = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_SCRAP);
		itemScrap.setText(Message.getString("wip.scrap_lotcn"));
		itemScrap.setImage(SWTResourceCache.getImage("scrap-lot"));
		itemScrap.setAuthEventAdaptor(new Consumer<SelectionEvent>() {
			
			@Override
			public void accept(SelectionEvent event) {
				scrapAdapter(event);
			}
		});
	}

	protected void scrapAdapter(SelectionEvent event) {
		try {
			if (!event.doit) {
        		return;
        	}
			Lot lot = (Lot)getAdObject();
			if (lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.noLotToSplit"));
			} else {
				ScrapDialog sld = null;
				
				String operator = Env.getUserName();
				if (itemScrap.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
					operator = (String) itemScrap.getData(LotAction.ACTION_TYPE_OPERATOR);
				}
				
				if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
					sld= new ScrapComponentUnitDialog(event.widget.getDisplay().getActiveShell(), lot, scrapEditor, operator);
				} else {
					sld= new ScrapQtyUnitDialog(event.widget.getDisplay().getActiveShell(), lot, operator);
				}
				if (sld != null && sld.open() == Dialog.OK) {
					refreshAdapter();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}	
	}	
	
	public void statusChanged(String newStatus){
		if(LotStateMachine.STATE_WAIT.equals(newStatus)
				|| LotStateMachine.STATE_RUN.equalsIgnoreCase(newStatus)
				|| LotStateMachine.STATE_FIN.equals(newStatus)) {
			itemScrap.setEnabled(true);
		} else {
			itemScrap.setEnabled(false);
		}
	}
	
}
