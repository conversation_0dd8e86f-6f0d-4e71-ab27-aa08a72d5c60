package com.glory.mes.wip.lot.scrap.supplement;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.HorizontalSection;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class ScrapSupplementSection extends HorizontalSection {

	private static final String TABLE_NAME = "WIPScrapSupplementSource";

	protected ScrapSupplementEntityForm entityForm;

	protected ListTableManager targetTableManager;
	protected ListTableManager sourceTableManager;
	private Text targetTxt;
	private Text sourceTxt;

	protected Lot targetLot;
	protected Lot sourceLot;

	protected Map<ComponentUnit, ComponentUnit> scrapSupplementMap = Maps.newHashMap();

	public ScrapSupplementSection(ADTable adTable, String rightTitle, String leftTitle) {
		super(adTable, rightTitle, leftTitle);
	}

	@Override
	public void createContents(IManagedForm managedForm, Composite parent) {
		super.createContents(managedForm, parent);

		final FFormToolKit toolkit = (FFormToolKit) managedForm.getToolkit();
		Composite content = toolkit.createComposite(parent);
		content.setLayout(new GridLayout(2, true));
		GridData td = new GridData(GridData.FILL_HORIZONTAL);
		content.setLayoutData(td);

		toolkit.paintBordersFor(content);
		createBottomSecion(content, toolkit);
	}

	protected void createBottomSecion(Composite content, FormToolkit toolkit) {
		// Form
		ScrolledForm bottomForm = toolkit.createScrolledForm(content);
		bottomForm.setLayout(new GridLayout(1, false));
		bottomForm.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite bottomFormBody = bottomForm.getBody();
		bottomFormBody.setLayout(new GridLayout(1, false));
		bottomFormBody.setLayoutData(new GridData(GridData.FILL_BOTH));

		entityForm = getForm(bottomFormBody);
		entityForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		entityForm.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));

		// ��ť
		Composite btnComp = toolkit.createComposite(bottomFormBody);
		GridData inbtnGd = new GridData(GridData.FILL_HORIZONTAL);
		inbtnGd.verticalAlignment = SWT.BOTTOM;
		inbtnGd.horizontalAlignment = SWT.RIGHT;
		btnComp.setLayout(new GridLayout(1, false));
		btnComp.setLayoutData(inbtnGd);

		SquareButton addBtn = createButton(btnComp, Message.getString(ExceptionBundle.bundle.CommonOk()));
		addBtn.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				addAdapter();
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}
		});
	}
	
	protected ScrapSupplementEntityForm getForm(Composite bottomFormBody) {
		return new ScrapSupplementEntityForm(getADManger(), bottomFormBody, SWT.NONE, null,
				getAdTable().getFields(), 1, getManagedForm().getMessageManager());
	}

	@Override
	protected void createRightContent(Composite client, FormToolkit toolkit) {
		// ɨ��
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(4, false));
		top.setLayoutData(gd);

		GridData gText = new GridData();
		gText.widthHint = 216;

		Label label = toolkit.createLabel(top, Message.getString("wip.source_carrier_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
		sourceTxt = toolkit.createText(top, "", SWT.BORDER);
		sourceTxt.setLayoutData(gText);
		sourceTxt.setTextLimit(64);
		sourceTxt.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();

					if (!StringUtil.isEmpty(lotId)) {
						tLotId.setText(lotId);
						lot = searchLot(lotId);
						tLotId.selectAll();
						if (lot == null || LotStateMachine.COMCLASS_COM.equals(lot.getComClass())) {
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							lot = new Lot();
						} else {
							loadSource(lot);
						}
					}
					break;
				}
			}
		});

		Composite outTableComposite = toolkit.createComposite(client);
		outTableComposite.setLayout(new GridLayout(2, false));
		outTableComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

		sourceTableManager = new ListTableManager(searchADTable(TABLE_NAME), true);
		sourceTableManager.setSortFlag(false);
		sourceTableManager.newViewer(outTableComposite);

		CheckBoxTableViewerManager manager = (CheckBoxTableViewerManager) sourceTableManager.getTableManager();
		manager.setCheckBoxDisableListener(new ICheckBoxDisableListener() {
			public boolean isDisable(Object object) {
				return true;
			}
		});
	}

	protected void loadSource(Lot lot) {
		try {
			if (StringUtil.isEmpty(lot.getDurable())) {
				UI.showWarning(Message.getString("wip.lot_not_assign_durable"));
				return;
			}

			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier sourceCarrier = durableManager.getCarrierById(Env.getOrgRrn(), lot.getDurable());
			if (sourceCarrier == null) {
				UI.showWarning(Message.getString("mm.durable_not_exist"));
				return;
			}

			LotManager lotManager = Framework.getService(LotManager.class);
			lot = lotManager.getLotWithComponent(lot.getObjectRrn());
			this.sourceLot = lot;

			Map<String, ComponentUnit> sourceUnitsMap = Maps.newLinkedHashMap();
			BigDecimal capacity = sourceCarrier.getCapacity();
			for (int i = capacity.intValue(); i >= 1; i--) {
				ComponentUnit component = new ComponentUnit();
				component.setPosition(String.valueOf(i));
				sourceUnitsMap.put(String.valueOf(i), component);
			}

			for (ProcessUnit processUnit : lot.getSubProcessUnit()) {
				ComponentUnit component = (ComponentUnit) processUnit;
				sourceUnitsMap.put(component.getPosition(), component);
			}

			if (MapUtils.isNotEmpty(scrapSupplementMap)) {
				scrapSupplementMap.clear();
				loadTarget(targetLot);
			}

			sourceTableManager.setInput(Lists.newArrayList(sourceUnitsMap.values()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected void createLeftContent(Composite client, FormToolkit toolkit) {
		// ɨ��
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(4, false));
		top.setLayoutData(gd);

		GridData gText = new GridData();
		gText.widthHint = 216;

		Label label = toolkit.createLabel(top, Message.getString("wip.supplement_carrier_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
		targetTxt = toolkit.createText(top, "", SWT.BORDER);
		targetTxt.setLayoutData(gText);
		targetTxt.setTextLimit(64);
		targetTxt.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();

					if (!StringUtil.isEmpty(lotId)) {
						tLotId.setText(lotId);
						lot = searchLot(lotId);
						tLotId.selectAll();
						if (lot == null) {
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							lot = new Lot();
						} else {
							loadTarget(lot);
						}
					}
					break;
				}
			}
		});

		Composite outTableComposite = toolkit.createComposite(client);
		outTableComposite.setLayout(new GridLayout(2, false));
		outTableComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

		targetTableManager = new ListTableManager(getAdTable(), true);
		targetTableManager.newViewer(outTableComposite);

		CheckBoxTableViewerManager manager = (CheckBoxTableViewerManager) targetTableManager.getTableManager();
		manager.setCheckBoxDisableListener(new ICheckBoxDisableListener() {
			public boolean isDisable(Object object) {
				ADBase adBase = (ADBase) object;
				if (adBase.getObjectRrn() == null) {
					return true;
				}
				return false;
			}
		});

		targetTableManager.addICheckChangedListener(new ICheckChangedListener() {

			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				try {
					List<? extends Object> sourceObjects = sourceTableManager.getInput();

					CheckBoxTableViewerManager targetManager = (CheckBoxTableViewerManager) targetTableManager
							.getTableManager();
					if (CollectionUtils.isEmpty(sourceObjects)) {
						eventObjects.forEach(e -> targetManager.unCheckObject(e));
						throw new ClientException(Message.getString("wip.scan_source_carrier_id_first"));
					}

					LotScrap lotScrap = getEntityForm().getScrapAction();
					if (lotScrap == null) {
						eventObjects.forEach(e -> targetManager.unCheckObject(e));
						throw new ClientException("wip.code_reason_required");
					}

					CheckBoxTableViewerManager sourceManager = (CheckBoxTableViewerManager) sourceTableManager
							.getTableManager();
					if (checked) {
						// ������ǰ���û�б�ѡ�е�Wafer
						for (Object object : eventObjects) {
							boolean getFlag = false;
							if (!scrapSupplementMap.containsKey(object)) {
								for (Object source : sourceObjects) {
									List<ComponentUnit> checkedSources = Lists
											.newArrayList(scrapSupplementMap.values());
									if (!checkedSources.contains(source)) {
										ADBase adBase = (ADBase) source;
										if (adBase.getObjectRrn() != null) {
											sourceManager.checkObject(source);
											scrapSupplementMap.put((ComponentUnit) object, (ComponentUnit) source);
											getFlag = true;
											break;
										}
									}
								}
							}

							if (!getFlag && !scrapSupplementMap.containsKey(object)) {
								targetManager.unCheckObject(object);
							} else {
								ComponentUnit target = (ComponentUnit) object;
								target.setActionCode(lotScrap.getActionCode());
								target.setAttribute1(lotScrap.getActionReason());
								targetManager.update(target);
							}
						}

					} else {
						for (Object object : eventObjects) {
							if (object != null) {
								Object checkedSource = scrapSupplementMap.get(object);
								sourceManager.unCheckObject(checkedSource);
								scrapSupplementMap.remove(object);

								ComponentUnit target = (ComponentUnit) object;
								target.setActionCode(null);
								target.setAttribute1(null);
								targetManager.update(target);
							}
						}
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		});
	}

	protected void loadTarget(Lot lot) {
		try {
			if (StringUtil.isEmpty(lot.getDurable())) {
				UI.showWarning(Message.getString("wip.lot_not_assign_durable"));
				return;
			}

			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), lot.getDurable());
			if (targetCarrier == null) {
				UI.showWarning(Message.getString("mm.durable_not_exist"));
				return;
			}

			LotManager lotManager = Framework.getService(LotManager.class);
			lot = lotManager.getLotWithComponent(lot.getObjectRrn());
			this.targetLot = lot;

			Map<String, ComponentUnit> targetUnitsMap = Maps.newLinkedHashMap();
			BigDecimal capacity = targetCarrier.getCapacity();
			for (int i = capacity.intValue(); i >= 1; i--) {
				ComponentUnit component = new ComponentUnit();
				component.setPosition(String.valueOf(i));
				targetUnitsMap.put(String.valueOf(i), component);
			}

			for (ProcessUnit processUnit : lot.getSubProcessUnit()) {
				ComponentUnit component = (ComponentUnit) processUnit;
				targetUnitsMap.put(component.getPosition(), component);
			}

			if (MapUtils.isNotEmpty(scrapSupplementMap)) {
				scrapSupplementMap.clear();
				loadSource(sourceLot);
			}
			targetTableManager.setInput(Lists.newArrayList(targetUnitsMap.values()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void addAdapter() {
		try {
			getManagedForm().getMessageManager().removeAllMessages();
			if (getEntityForm().saveToObject()) {
				LotScrap lotScrap = getEntityForm().getScrapAction();

				if (scrapSupplementMap.size() == 0) {
					UI.showWarning(String.format(Message.getString("common.please_select"),
							ComponentUnit.class.getSimpleName()));
					return;
				}

				Map<String, String> unitPositionMap = Maps.newHashMap();
				Set<ComponentUnit> scrapUnits = scrapSupplementMap.keySet();
				List<LotAction> scrapLotActions = new ArrayList<LotAction>();
				for (ComponentUnit scrapUnit : scrapUnits) {
					LotAction lotAction = new LotAction();
					lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
					lotAction.setActionReason(DBUtil.toString(scrapUnit.getAttribute1()));
					lotAction.setLotRrn(scrapUnit.getParentUnitRrn());
					lotAction.setActionCode(scrapUnit.getActionCode());
					lotAction.setActionUnits(Lists.newArrayList(scrapUnit));
					lotAction.setActionComment(lotScrap.getActionComment());
					scrapLotActions.add(lotAction);

					// source waferʹ��scrap wafer��position
					ComponentUnit sourceUnit = scrapSupplementMap.get(scrapUnit);
					unitPositionMap.put(sourceUnit.getComponentId(), scrapUnit.getPosition());
				}

				LotAction commentAction = new LotAction();
				commentAction.setActionComment(lotScrap.getActionComment());

				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.lotScrapSupplement(targetLot, scrapSupplementMap, scrapLotActions, commentAction,
						unitPositionMap, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void refresh() {
		// ���¼���Target��Ϣ
		loadTarget(targetLot);
		// ˢ��������Ϣ
		targetTxt.selectAll();
		targetTxt.setFocus();
		getEntityForm().refresh();
	}

	public ScrapSupplementEntityForm getEntityForm() {
		return entityForm;
	}

	public void setEntityForm(ScrapSupplementEntityForm entityForm) {
		this.entityForm = entityForm;
	}

}
