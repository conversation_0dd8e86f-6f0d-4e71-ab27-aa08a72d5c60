package com.glory.mes.wip.lot.scrap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class ScrapQtyUnitDialog extends ScrapDialog {
    private static final Logger logger = Logger.getLogger(ScrapQtyUnitDialog.class);
    protected ScrapQtyUnitForm form;
    protected String operator;

    public ScrapQtyUnitDialog(Shell parentShell) {
        super(parentShell);
    }

    @Override
    public void createFrom(Composite composite, FormToolkit toolkit) {
        form = new ScrapQtyUnitForm(composite, SWT.NONE, lot);
        form.setBackground(new Color(null, 255, 255, 255));
    }

    public ScrapQtyUnitDialog(Shell parentShell, Lot lot, String operator) {
        super(parentShell, lot);
        this.operator = operator;
    }

    @Override
    public Point getMinSize() {
        return new Point(600, 400);
    }

    @Override
    protected void okPressed() {
        try {
            LotManager lotManager = Framework.getService(LotManager.class);
            lot.setOperator1(Env.getUserName());

            Map<String, List<ProcessUnit>> lotActionsMap = new HashMap<String, List<ProcessUnit>>();
            for (ProcessUnit unit : form.getScrapLots()) {
                QtyUnit qtyUnit = (QtyUnit) unit;
                if (lotActionsMap.containsKey(qtyUnit.getActionCode())) {
                    List<ProcessUnit> units = lotActionsMap.get(qtyUnit.getActionCode());
                    units.add(qtyUnit);
                    lotActionsMap.put(qtyUnit.getActionCode(), units);
                } else {
                    List<ProcessUnit> units = new ArrayList<ProcessUnit>();
                    units.add(qtyUnit);
                    lotActionsMap.put(qtyUnit.getActionCode(), units);
                }
            }

            List<LotAction> scrapLotActions = new ArrayList<LotAction>();
            for (String key : lotActionsMap.keySet()) {
                LotAction lotAction = form.getLotAction();
                lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
                lotAction.setLotRrn(lot.getObjectRrn());
                lotAction.setActionCode(key);
                lotAction.setActionComment(form.getLotAction().getActionComment());
                lotAction.setActionUnits(lotActionsMap.get(key));
                if (StringUtil.isEmpty(form.getLotAction().getActionComment())) {
         	        UI.showInfo(Message.getString("wip.abort_comments_null"));
         	        return;
         	    }
                scrapLotActions.add(lotAction);
            }
            LotAction lotAction = form.getLotAction();
            lotAction.setActionComment(form.getLotAction().getActionComment());
            lotAction.setActionCode(form.getLotAction().getActionCode());

            if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
				return;
			}
            SessionContext sc = Env.getSessionContext();
            sc.setUserName(operator);
            lotManager.scrapLot(lot, scrapLotActions, lotAction, sc);
            UI.showInfo(Message.getString("wip.scraplot_success"));
        } catch (Exception e) {
            logger.error("Scrap Lot Failure at ScrapLotDialog : " + e);
            UI.showError(Message.getString("wip.scrapLot_failure"));
            return;
        }
        super.okPressed();
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == IDialogConstants.OK_ID) {
            if (form.getScrapLots() == null || form.getScrapLots().size() == 0) {
                UI.showError(Message.getString("wip.scrapLot_failure"));
                // combo.setFocus();
                return;
            }
        }
        super.buttonPressed(buttonId);
    }

}
