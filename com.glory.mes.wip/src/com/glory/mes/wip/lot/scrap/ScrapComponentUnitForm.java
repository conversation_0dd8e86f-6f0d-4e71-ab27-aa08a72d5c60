package com.glory.mes.wip.lot.scrap;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;

public class ScrapComponentUnitForm extends Composite {

    private static final Logger logger = Logger.getLogger(ScrapComponentUnitForm.class);

    protected static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");
    public static final String DEFAULT_SCRAP_TABLE = "ScrapCode";
    public static final String TABLE_SCRAP = "WIPLotScrapunitl";
    public static final String DEFAULT_DEPT_TABLE = "DEPT";

    protected Lot lot;
    protected XCombo comboScrapCode;
    protected XCombo comboScrapDept;
    protected Text commentText;
    protected ManagedForm mform;
    public CheckBoxTableViewerManager tableManager;
    
    public ScrapComponentUnitForm(Composite parent, int style) {
        super(parent, style);
    }

    public ScrapComponentUnitForm(Composite parent, int style, Lot lot) {
        this(parent, style);
        this.setLot(lot);
        try {
            LotManager manager = Framework.getService(LotManager.class);
            lot = manager.getLotWithComponent(lot.getObjectRrn());
            this.setLot(lot);
            createForm();
        } catch (Exception e) {
            logger.error("Constructor in ScrapIdentifiedLotDialog", e);
        }
    }

    public void createForm() {
        FormToolkit toolkit = new FormToolkit(getDisplay());

        this.setLayoutData(new GridData(GridData.FILL_BOTH));
        GridLayout layout = new GridLayout(1, false);
        layout.verticalSpacing = 0;
        layout.horizontalSpacing = 0;
        layout.marginWidth = 0;
        layout.marginHeight = 0;
        setLayout(layout);

        ScrolledForm sform = toolkit.createScrolledForm(this);
        sform.setLayoutData(new GridData(GridData.FILL_BOTH));
        mform = new ManagedForm(toolkit, sform);

        Composite body = sform.getBody();
        layout = new GridLayout();
        body.setLayout(layout);
        body.setLayoutData(new GridData(GridData.FILL_BOTH));

        createTableComponent(body, toolkit);
        createLowerComponent(body, toolkit);
    }

    protected void createTableComponent(Composite composite, FormToolkit toolkit) {
        Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_SCRAP);
			
			tableManager = new CheckBoxTableViewerManager(adTable);
			tableManager.newViewer(tableContainer);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

        List<ProcessUnit> subProcessUnit = getLot().getSubProcessUnit();
        for (int i = subProcessUnit.size() - 1; i >= 0; i--) {
            ComponentUnit componentUnit = (ComponentUnit) subProcessUnit.get(i);
            if (LotStateMachine.STATE_SCRAP.equals(componentUnit.getState())) {
                subProcessUnit.remove(i);
            }
        }
        tableManager.setInput(subProcessUnit);
        
        tableManager.addICheckChangedListener(new ICheckChangedListener() {
			
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (checked) {
					boolean flag = true;
					if(comboScrapCode.getText() == null
	                          || "".equals(comboScrapCode.getText().trim())){
						flag = false;
						UI.getActiveShell().getDisplay().asyncExec(new Runnable() {
		                    @Override
		                    public void run() {
		                    	UI.showWarning(String.format(Message.getString("wip.scrap_code_required"), HEADER_SCRAP_CODE));
		  						for (Object object : eventObjects) {
		  							ComponentUnit compositeUnit = (ComponentUnit) object;
	  								tableManager.unCheckObject(object);
	  								compositeUnit.setActionCode("");
		  						}
		                    }
		                });
					}
					for (Object object : eventObjects) {
						ComponentUnit compositeUnit = (ComponentUnit) object;
						if (flag) {
							compositeUnit.setActionCode(comboScrapCode.getText());
						} else {
							tableManager.unCheckObject(object);
							compositeUnit.setActionCode("");
						}
					}
				} else {
					for (Object object : eventObjects) {
						((ComponentUnit)object).setActionCode("");
					}
				}
			}
		});
    }

    protected void createLowerComponent(Composite composite, FormToolkit toolkit) {
        Composite scrapComp = toolkit.createComposite(composite, SWT.NULL);
        scrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
        GridLayout gl = new GridLayout(5, false);
        scrapComp.setLayout(gl);

		toolkit.createLabel(scrapComp, Message.getString("wip.scrap_code") + "*", SWT.NULL);
		comboScrapCode = RCPUtil.getUserRefListCombo(scrapComp, getScrapCode(), Env.getOrgRrn(), true);
		GridData cGd = new GridData(GridData.FILL_HORIZONTAL);
		cGd.horizontalSpan = 4;
		comboScrapCode.setLayoutData(cGd);
		
		toolkit.createLabel(scrapComp, Message.getString("wip.comment") + "*", SWT.NULL);
		commentText = toolkit.createText(scrapComp, "", SWT.BORDER);
		commentText.setLayoutData(cGd);
		
		toolkit.createLabel(scrapComp, Message.getString("wip.reason_sector") + "*", SWT.NULL);
		comboScrapDept = RCPUtil.getUserRefListCombo(scrapComp, DEFAULT_DEPT_TABLE, Env.getOrgRrn(), true);
		comboScrapDept.setLayoutData(cGd);
    }

    protected String getScrapCode() {
        String scrapCode = null;
        try {
            PrdManager prdManager = Framework.getService(PrdManager.class);
            if (getLot() != null && getLot().getObjectRrn() != null
                    && getLot().getStepRrn() != null) {
                Step step = new Step();
                step.setObjectRrn(getLot().getStepRrn());
                step = (Step) prdManager.getSimpleProcessDefinition(step);
                scrapCode = step.getScrapCodeSrc();
            } else {
                scrapCode = DEFAULT_SCRAP_TABLE;
            }
            if (scrapCode == null || scrapCode.trim().length() == 0) {
                scrapCode = DEFAULT_SCRAP_TABLE;
            }
        } catch (Exception e) {
            logger.error("ScrapLotDialog : initComoContent() ", e);
        }
        return scrapCode;
    }

    public boolean validate() {
        IMessageManager mmng = mform.getMessageManager();
        mmng.removeAllMessages();
        boolean validateFlag = true;
        boolean sourceIsNull = GenericValidator.isBlankOrNull(comboScrapCode.getText());
        if (sourceIsNull) {
            mmng.addMessage(HEADER_SCRAP_CODE,
                    String.format(Message.getString("common.ismandatry"), HEADER_SCRAP_CODE), null,
                    IMessageProvider.ERROR, comboScrapCode);
            validateFlag = false;
        }
        return validateFlag;
    }

    public List<ProcessUnit> getScrapUnits() {
        List<ProcessUnit> scrapUnits = new ArrayList<ProcessUnit>();
        List<Object> objs = tableManager.getCheckedObject();
        for (Object obj : objs) {
            ProcessUnit unit = (ProcessUnit) obj;
            unit.setEquipmentId(lot.getEquipmentId());
            scrapUnits.add(unit);
        }
        return scrapUnits;
    }

    public LotAction getScrapAction() {
    	LotAction lotAction = new LotAction();
    	lotAction.setActionComment(commentText.getText());
    	lotAction.setActionOperator(comboScrapDept.getText());
        return lotAction;
    }

    public void setLot(Lot lot) {
        this.lot = lot;
    }

    public Lot getLot() {
        return lot;
    }
    
    protected ADTable getADTable1() {
        try {
            ADManager adManager = Framework.getService(ADManager.class);
            ADTable midTable = adManager.getADTable(Env.getOrgRrn(), "WIPScrapAction");
            return midTable;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
