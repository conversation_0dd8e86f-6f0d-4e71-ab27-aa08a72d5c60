package com.glory.mes.wip.lot.update.procedureversion;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.TreeItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;

public class LotFutureHoldDialog extends GlcBaseDialog { 

	public static final String FIELD_LOTLIST = "lotList";
	public static final String FIELD_FUTURE_HOLD_LIST = "lotFutureHoldList";
	public static final String FIELD_FUTUREHOLDINFO = "futureHoldInfo";
	public static final String FIELD_PROCEDUREINFO = "procedureInfo";
	public static final String FIELD_FUTUREHOLDSTEP = "futureHoldStep";
	public static final String FIELD_HOLDCODE = "holdCode";

	protected ListTableManagerField lotListField;
	protected ListTableManagerField futureHoldListField;
	protected GlcFormField futureHoldInfoField;
	protected CustomField procedureInfoField;
	protected EntityFormField futureHoldInfoField1;
	protected TextField futureHoldStepField;
	protected TextField holdCodeField;

	protected FlowCustomComposite procedureFlowCustomComposite;
	
	private List<Lot> lots;
	
	public static final String TREE_BUTTON_NAME_FUTURE_HOLD = "futureHold";
	protected TreeItem currentSelectedItem;
	protected StepState futureStepState = null;
	
	public LotFutureHoldDialog(String adFormName, String authority, IEventBroker eventBroker, List<Lot> lots) {
		super(adFormName, authority, eventBroker);
		this.lots = lots;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotListField = form.getFieldByControlId(FIELD_LOTLIST, ListTableManagerField.class);
		futureHoldListField = form.getFieldByControlId(FIELD_FUTURE_HOLD_LIST, ListTableManagerField.class);
		futureHoldInfoField = form.getFieldByControlId(FIELD_FUTUREHOLDINFO, GlcFormField.class);
		procedureInfoField = futureHoldInfoField.getFieldByControlId(FIELD_PROCEDUREINFO, CustomField.class);
		procedureFlowCustomComposite = (FlowCustomComposite) procedureInfoField.getCustomComposite();
		
		// �һ��¼�
		subscribeAndExecute(eventBroker, procedureInfoField.getFullTopic(GlcEvent.EVENT_MENU_ACTION), this::rightClickAction);
				
		futureHoldInfoField1 = futureHoldInfoField.getFieldByControlId(FIELD_FUTUREHOLDINFO, EntityFormField.class);
		
				
		futureHoldStepField = futureHoldInfoField1.getFieldByControlId(FIELD_FUTUREHOLDSTEP, TextField.class);
		holdCodeField = futureHoldInfoField1.getFieldByControlId(FIELD_HOLDCODE, TextField.class);
		
		init();
	}

	protected void init() {
		try {
			lotListField.setValue(lots);
			lotListField.refresh();
			
			Set idSet = new HashSet();
			for (Lot lot : lots) {
				idSet.add(lot.getLotId());
			}
			Map<String, Object> fieldMap = new HashMap<String, Object>();
			fieldMap.put("lotIds", idSet);
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<FutureHold> futureHolds = adManager.getEntityList(Env.getOrgRrn(), FutureHold.class, Integer.MIN_VALUE, Integer.MAX_VALUE, " lotId in (:lotIds) ", " lotId, stepStateName ", fieldMap);
			futureHoldListField.setValue(futureHolds);
			futureHoldListField.refresh();
			
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Procedure procedure = new Procedure();
			procedure.setObjectRrn(lots.get(0).getProcedureRrn());
			procedure = (Procedure) prdManager.getProcessDefinition(procedure);
					
			procedureFlowCustomComposite.loadFlowTreeByProcedure(procedure, null);
			
			holdCodeField.setValue(LotHold.HOLD_CODE_UPDVER);
			holdCodeField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void rightClickAction(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			TreeItem treeItem = (org.eclipse.swt.widgets.TreeItem) event.getProperty(GlcEvent.PROPERTY_DATA);
			StepState stepState = (StepState) treeItem.getData();
			
			if (currentSelectedItem != null) {
				currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				currentSelectedItem = null;
			}
			
			treeItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_GREEN));
			currentSelectedItem = treeItem;
			
			if (TREE_BUTTON_NAME_FUTURE_HOLD.equals(event.getProperty(GlcEvent.PROPERTY_MENU_ACTION_NAME))) {
				futureStepState = stepState;
				futureHoldInfoField1.setFieldValue("futureHoldStep", stepState.getName());	
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {	
		try {
			((EntityForm)futureHoldInfoField1.getControls()[0]).getMessageManager().removeAllMessages();
			
			StepState futureHoldStepState = (StepState) procedureFlowCustomComposite.getCurrentSelectedItem().getData();
			if (futureHoldStepState == null) {
				UI.showError(Message.getString("wip-001403: wip.future_hold_step_not_allow"));
				return;
			}
			futureHoldStepState.setPath(lots.get(0).getProcedureName() + "/" + futureHoldStepState.getPath());
			
			boolean saveFlag = true;
			if (!futureHoldInfoField1.validate()) {
				saveFlag = false;
			}

			if (saveFlag) {
				LotHold lotHold = (LotHold)futureHoldInfoField1.getValue();		
				
				LotAction lotAction = new LotAction();
				lotAction.setActionCode(lotHold.getHoldCode());
				lotAction.setActionReason(lotHold.getHoldReason());
				lotAction.setActionComment(lotHold.getHoldComment());
				lotAction.setActionOwner(lotHold.getHoldOwner());
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.addLotFutureHold(lots, futureHoldStepState, FutureHold.STEPPLACEMENT_QUEUED, lotAction, Env.getSessionContext());			
			}
			
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			((EntityForm)futureHoldInfoField1.getControls()[0]).getMessageManager().setAutoUpdate(false);
		}	
	}

	public List<Lot> getLots() {
		return lots;
	}

	public void setLots(List<Lot> lots) {
		this.lots = lots;
	}

}