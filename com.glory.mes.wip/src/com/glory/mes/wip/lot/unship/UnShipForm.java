package com.glory.mes.wip.lot.unship;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;

public class UnShipForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(UnShipForm.class);
	private Lot lot;
	private IField fieldUnShipCode;
	private static final String UNSHIPCODE = "UnShipCode";
	private static final String UNSHIPCODE_ID = "unShipCode";
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";
	private String unShipCodeRefName = "UnShipCode";
	private LotAction lotAction = new LotAction();

	public UnShipForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public UnShipForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public UnShipForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public UnShipForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			fieldUnShipCode = createUserRefList(UNSHIPCODE_ID, Message.getString("wip.unship_code") + "*", UNSHIPCODE, true);
			
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);

			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			addField(UNSHIPCODE, fieldUnShipCode);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("UnShipForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField)
						&& !f.equals(fieldUnShipCode)
						&& !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			lot = (Lot) object;
			lotAction.setActionCode((String) fieldUnShipCode.getValue());
			lotAction.setActionComment((String) fieldComment.getValue());
			return true;
		}
		return false;
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			XCombo combo = (XCombo) ((RefTableField)fieldUnShipCode).getComboControl();
			RCPUtil.refreshUserRefListCombo(combo, unShipCodeRefName);
			
			fieldUnShipCode.setValue("");
			fieldUnShipCode.refresh();
			((TextField) fieldComment).setText("");
		} catch (Exception e) {
			logger.error("UnShipForm : refresh()", e);
		}
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	public Lot getLot() {
		return lot;
	}

	@Override
	public boolean validate() {
		boolean validFlag = super.validate();
		if (fieldUnShipCode.getValue() == null
				|| "".equals(String.valueOf(fieldUnShipCode.getValue()).trim())) {
			validFlag = false;
			mmng.addMessage(UNSHIPCODE_ID, String.format(Message
					.getString("common.ismandatry"), UNSHIPCODE_ID), null,
					IMessageProvider.ERROR, fieldUnShipCode.getControls()[1]);
		}
		return validFlag;
	}
}
