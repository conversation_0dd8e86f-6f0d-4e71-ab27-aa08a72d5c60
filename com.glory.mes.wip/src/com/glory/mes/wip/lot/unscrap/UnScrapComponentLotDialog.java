package com.glory.mes.wip.lot.unscrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.jface.viewers.ColumnWeightData;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.TableLayout;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;

@Deprecated
public class UnScrapComponentLotDialog extends BaseTitleDialog {
	private static final Logger logger = Logger.getLogger(UnScrapComponentLotDialog.class);

	protected IManagedForm form;
	protected CheckboxTableViewer viewer;
	protected Table table;
	protected XCombo combo;
	protected Button remove, qtyAdd;
	protected Lot lot;
	protected List<ProcessUnit> unScrapLots = new ArrayList<ProcessUnit>();
	protected List<LotUnScrapAction> unScrapActions = new ArrayList<LotUnScrapAction>();
	protected String bonusCode;
	protected BigDecimal inputMainQty, inputSubQty, addMainQty, addSubQty, splitedMainQty, splitedSubQty;
	protected Text commentText;
	protected Object commentQty;
	protected String comment, unScrapCode;
	protected static String UNSCRAP_CODE = "UnScrapCode";
	protected String actionCode;

	public UnScrapComponentLotDialog(Shell parent) {
		super(parent);
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(300, 100, 800, 500);
		getShell().setMinimumSize(800, 500);
	}

	public UnScrapComponentLotDialog(Shell parent, IManagedForm form, Lot lot) {
		this(parent);
		this.form = form;
		try {
			LotManager manager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			lot = manager.getLotWithComponent(lot.getObjectRrn());

			List<LotScrap> lotScraps = manager.getLotScrap(lot, Env.getSessionContext());
			for (LotScrap lotScrap : lotScraps) {
				ComponentUnit componentUnit = new ComponentUnit();
				componentUnit.setObjectRrn(lotScrap.getComponentRrn());
				componentUnit = (ComponentUnit) adManager.getEntity(componentUnit);
				lot.getSubProcessUnit().add(componentUnit);
			}
			this.lot = lot;
		} catch (Exception e) {
			logger.error("Constructor in ScrapIdentifiedLotDialog", e);
		}
	}

	@Override
	protected Control buildView(Composite parent) {
		Color gray = new Color(Display.getCurrent(), 236, 233, 216);
		FormToolkit toolkit = form.getToolkit();
		setTitleImage(SWTResourceCache.getImage("trackin-dialog"));
		setTitle(Message.getString("wip.unscrap_lot"));
		setMessage(Message.getString("wip.unscrapLot_Info"));

		Composite content = toolkit.createComposite(parent);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setLayout(new GridLayout(1, false));
		content.setBackground(gray);

		Composite tableContainer = toolkit.createComposite(content, SWT.NULL);
		tableContainer.setLayout(new GridLayout());
		tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
		createTableViewer(tableContainer, toolkit);
		viewer.setLabelProvider(new ScrapLabelProvider());
		viewer.setContentProvider(new ScrapContentProvider());
		viewer.setInput(getScrapWafer());

		Composite unScrapComp = toolkit.createComposite(content, SWT.NONE);
		unScrapComp.setLayout(new GridLayout(2, false));
		unScrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		unScrapComp.setBackground(gray);

		Label lab = new Label(unScrapComp, SWT.NULL);
		lab.setText(Message.getString("wip.unscrapcode_lot"));
		combo = RCPUtil.getUserRefListCombo(unScrapComp, getBonusCodeSrc(), Env.getOrgRrn());

		combo.addFocusListener(new FocusListener() {

			@Override
			public void focusLost(FocusEvent e) {
				// TODO Auto-generated method stub
				unScrapCode = combo.getText();
				comment = commentText.getText();
			}

			@Override
			public void focusGained(FocusEvent e) {
				// TODO Auto-generated method stub
				focusLost(e);
			}
		});

		GridData cGd = new GridData(GridData.FILL_BOTH);
		cGd.horizontalSpan = 4;
		combo.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
		Label lab2 = new Label(unScrapComp, SWT.NULL);
		lab2.setText(Message.getString("wip.comment"));
		commentText = toolkit.createText(unScrapComp, "", SWT.BORDER);
		commentText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
		return parent;
	}

	private ArrayList<ProcessUnit> getScrapWafer() {
		ArrayList<ProcessUnit> list = new ArrayList<ProcessUnit>();
		for (ProcessUnit childUnit : lot.getSubProcessUnit()) {
			if (childUnit instanceof ComponentUnit) {
				ComponentUnit unit = (ComponentUnit) childUnit;
				if (ComponentUnit.STATE_SCRAP.equalsIgnoreCase(unit.getState())) {
					list.add(unit);
				}
			}
		}
		return list;
	}

	private String getBonusCodeSrc() {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			if (lot != null && lot.getObjectRrn() != null && lot.getStepRrn() != null) {
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) prdManager.getSimpleProcessDefinition(step);
				bonusCode = step.getBonusCodeSrc();
			} else {
				bonusCode = UNSCRAP_CODE;
			}
			if (bonusCode == null || bonusCode.trim().length() == 0) {
				bonusCode = UNSCRAP_CODE;
			}
		} catch (Exception e) {
			logger.error("UnScrapDialog : initComoContent() ", e);
		}
		return bonusCode;
	}

	private SelectionListener getQtyAddListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				TableItem ti = (TableItem) e.item;
				if (unScrapCode == null || "".equals(unScrapCode)) {
					ti.setChecked(false);
					UI.showWarning(Message.getString("wip.unscrap_select_unscrap_code_first"));
					return;
				} else {
					ProcessUnit unit = (ProcessUnit) ti.getData();
					if (unit instanceof ComponentUnit) {
						if (ti.getChecked() == true) {
							ti.setText(3, unScrapCode);
							if (unScrapLots.contains(unit)) {
								return;
							}
							ComponentUnit tempUnit = (ComponentUnit) unit;
							tempUnit.setActionCode(unScrapCode);
							unScrapLots.add(tempUnit);

						} else {
							ti.setText(3, "");
							unScrapLots.remove(ti.getData());
						}
					}
				}
			}
		};
	}

	@Override
	protected void okPressed() {
		/*try {
			LotManager lotManager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			lot.setOperator1(Env.getUserName());
			LotAction action = new LotAction();
			action.setActionComment(commentText.getText());
			action.setActionCode(getActionCode());
			for (ProcessUnit unit : unScrapLots) {
				ComponentUnit q = (ComponentUnit) unit;
				LotUnScrapAction unAction = new LotUnScrapAction();
				unAction.setActionCode(q.getActionCode());
				unAction.setUnMainQty(q.getMainQty());
				unAction.setUnSubQty(q.getSubQty());
				List<LotScrap> lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class, 1,
						"componentRrn = " + unit.getObjectRrn(), null);
				unAction.setLotScrap(lotScraps.get(0));
				unScrapActions.add(unAction);
			}
			lotManager.unScrapLot(lot, unScrapActions, action, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.unscrapLot_success"));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}*/
		super.okPressed();
	}

	protected String getActionCode() {
		if (unScrapLots != null) {
			for (ProcessUnit punit : unScrapLots) {
				ComponentUnit q = (ComponentUnit) punit;
				actionCode = q.getActionCode();
			}
		}
		return actionCode;
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == IDialogConstants.OK_ID) {
			if (unScrapCode == null || unScrapCode.trim() == "" || unScrapLots == null || unScrapLots.size() == 0) {
				UI.showError(Message.getString("wip.unscrap_not_select"));
				return;
			}
		}
		super.buttonPressed(buttonId);
	}

	protected void createTableViewer(Composite tc, FormToolkit toolkit) {
		String[] columnsHeaders = new String[] { Message.getString("wip.unscrap_compomentid"),
				Message.getString("wip.component_position"), Message.getString("wip.unscrap_state"),
				Message.getString("wip.unscrap_scrapcode"), Message.getString("wip.unscrap_unscrapcode") };
		Table table = toolkit.createTable(tc, SWT.CHECK | SWT.FULL_SELECTION | SWT.BORDER);
		table.setHeaderVisible(true);
		table.setLinesVisible(true);
		TableLayout tlayout = new TableLayout();
		table.setLayout(tlayout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = table.getItemHeight() * 13;
		table.setLayoutData(gd);
		table.addSelectionListener(getQtyAddListener());
		viewer = new CheckboxTableViewer(table);
		if (columnsHeaders != null) {
			for (int i = 0; i < columnsHeaders.length; i++) {
				TableColumn column;
				column = new TableColumn(table, SWT.NONE);
				column.setText(columnsHeaders[i]);
				column.setWidth(180);
				column.setResizable(true);
				tlayout.addColumnData(new ColumnWeightData(30));
			}
		}
	}

	private SelectionListener getDeleteListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				Object[] os = viewer.getCheckedElements();
				removeSelected(os);
			}
		};
	}

	public void removeSelected(Object[] os) {
		if (os.length != 0) {
			for (Object o : os) {
				ProcessUnit pe = (ProcessUnit) o;
				unScrapLots.remove(pe);
			}
			refresh();
		}
	}

	public void refresh() {
		if (getScrapLots() != null) {
			viewer.setInput(getScrapLots());
			viewer.refresh();
		}
	}

	public List<ProcessUnit> getScrapLots() {
		return unScrapLots;
	}

	public void decorateButton(Button button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData();
		data.horizontalAlignment = GridData.END;
		data.widthHint = 93;
		int widthHint = 92;
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}

	public class ScrapLabelProvider extends LabelProvider implements ITableLabelProvider {
		@Override
		public Image getColumnImage(Object element, int columnIndex) {
			return null;
		}

		@Override
		public String getColumnText(Object element, int columnIndex) {
			if (element instanceof ComponentUnit) {
				ComponentUnit component = (ComponentUnit) element;
				switch (columnIndex) {
				case 0:
					return component.getComponentId();
				case 1:
					if (component.getPosition() == null) {
						return "";
					}
					return component.getPosition().toString();
				case 2:
					if (component.getState() == null) {
						return "";
					}
					return component.getState();
				case 3:
					return component.getActionCode() != null ? component.getActionCode() : "";
				}
			}
			return "";
		}
	}

	private class ScrapContentProvider implements IStructuredContentProvider {
		@Override
		public Object[] getElements(Object inputElement) {
			if (inputElement instanceof List) {
				return ((List) inputElement).toArray();
			}
			return new Object[0];
		}

		@Override
		public void dispose() {
		}

		@Override
		public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		}
	}

}
