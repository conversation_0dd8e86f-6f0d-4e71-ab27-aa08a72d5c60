package com.glory.mes.wip.lot.unscrap;

import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.unscrapnew.UnScrapNewContext;
import com.glory.mes.wip.lot.unscrapnew.UnScrapNewDialog;
import com.glory.mes.wip.lot.unscrapnew.UnScrapNewWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.LotStateMachine;

public class UnScrapSection extends LotSection {

	protected ToolItem itemUnScrapByQty;
	protected ToolItem itemUnScrapByScrap;
	protected ToolItem itemUnScrapNew;
	protected EntityForm entityForm;

	public UnScrapSection() {
		super();
	}

	public UnScrapSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.unscrap_sectiontitle"));
		initAdObject();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemUnScrapByScrap(tBar);
		//createToolItemUnScrapByQty(tBar);
		// new ToolItem(tBar, SWT.SEPARATOR);
		// createToolItemUnScrapNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemUnScrapNew(ToolBar tBar) {
		itemUnScrapNew = new ToolItem(tBar, SWT.PUSH);
		itemUnScrapNew.setText(Message.getString("unscrap.unscrapnew_new"));
		itemUnScrapNew.setImage(SWTResourceCache.getImage("unscrap-lot"));
		itemUnScrapNew.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unscrapNewAdapter();
			}
		});
	}
	
	protected void unscrapNewAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			UnScrapNewContext context = new UnScrapNewContext();
	
			Lot lot = (Lot)getAdObject();
			if (!Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
				UI.showError(Message.getString("unscrap.unscrapnew_only_component_type"));
				return;
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			lot = lotManager.getLotWithComponent(lot.getObjectRrn());
			List<LotScrap> lotScraps = lotManager.getLotScrap(lot, Env.getSessionContext());
			for (LotScrap lotScrap : lotScraps) {
				ComponentUnit componentUnit = new ComponentUnit();
				componentUnit.setObjectRrn(lotScrap.getComponentRrn());
				componentUnit = (ComponentUnit) adManager.getEntity(componentUnit);
				lot.getSubProcessUnit().add(componentUnit);
			}
			context.setLot(lot);
	
			UnScrapNewWizard wizard = new UnScrapNewWizard(context);
			UnScrapNewDialog dialog = new UnScrapNewDialog(Display.getCurrent().getActiveShell(), wizard);
			if (dialog.open() == Dialog.OK) {
				refreshAdapter();
				UI.showInfo(Message.getString("unscrap.unscrapnew_operate_success"));
			}
			txtLot.selectAll();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	protected void createToolItemUnScrapByScrap(ToolBar tBar) {
		itemUnScrapByScrap = new ToolItem(tBar, SWT.PUSH);
		itemUnScrapByScrap.setText(Message.getString("wip.unscrap_lot_by_scrap"));
		itemUnScrapByScrap.setImage(SWTResourceCache.getImage("unscrap"));
		itemUnScrapByScrap.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				byScrapUnscrapAdapter(event);
			}
		});
	}
	
	protected void createToolItemUnScrapByQty(ToolBar tBar) {
		itemUnScrapByQty = new ToolItem(tBar, SWT.PUSH);
		itemUnScrapByQty.setText(Message.getString("wip.unscrap_lot_by_qty"));
		itemUnScrapByQty.setImage(SWTResourceCache.getImage("unscrap"));
		itemUnScrapByQty.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				byQtyUnscrapAdapter(event);
			}
		});
	}
	
	protected void byScrapUnscrapAdapter(SelectionEvent event) {
		try {
			Lot lot = (Lot)getAdObject();
			if(lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.noLotToSplit"));
			} else {
				if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())){
					UnScrapListTableComponentLotDialog sld = new UnScrapListTableComponentLotDialog(event.widget.getDisplay().getActiveShell(), form, lot);
					if(sld.open() == Dialog.OK) {
						refreshAdapter();
					}
				} else {
					UnScrapByScrapDialog sld = new UnScrapByScrapDialog(event.widget.getDisplay().getActiveShell(), form, lot);
					if(sld.open() == Dialog.OK) {
						refreshAdapter();
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}	
	}

	protected void byQtyUnscrapAdapter(SelectionEvent event) {
		try {
			Lot lot = (Lot)getAdObject();
			if(lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.noLotToSplit"));
			} else {
				if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())){
					UnScrapListTableComponentLotDialog sld = new UnScrapListTableComponentLotDialog(event.widget.getDisplay().getActiveShell(), form, lot);
					if(sld.open() == Dialog.OK) {
						refreshAdapter();
					}
				} else {
					UnScrapByQtyDialog sld = new UnScrapByQtyDialog(event.widget.getDisplay().getActiveShell(), form, lot);
					if(sld.open() == Dialog.OK) {
						refreshAdapter();
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}	
	}

	@Override
	public void statusChanged(String newStatus) {
		Lot lot =(Lot)getAdObject();
		statusChanged(lot);
		
	}
	public void statusChanged(Lot lot){
		if (lot != null && lot.getState() != null) {
			if (LotStateMachine.STATE_SCRAP.equals(lot.getState())
					|| LotStateMachine.STATE_FIN.equals(lot.getState())
					|| LotStateMachine.STATE_WAIT.equals(lot.getState())
					|| LotStateMachine.STATE_RUN.equalsIgnoreCase(lot.getState())) {
//				itemUnScrapByQty.setEnabled(true);
				itemUnScrapByScrap.setEnabled(true);
			} else {
//				itemUnScrapByQty.setEnabled(false);
				itemUnScrapByScrap.setEnabled(false);
			}
		//	itemUnScrapNew.setEnabled(true);
		} else {
//			itemUnScrapByQty.setEnabled(false);
			itemUnScrapByScrap.setEnabled(false);
		}
	}

}
