package com.glory.mes.wip.lot.unscrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.framework.core.exception.ExceptionBundle;

@Deprecated
public class UnScrapByScrapDialog extends BaseTitleDialog {
	private static final Logger logger = Logger.getLogger(UnScrapByScrapDialog.class);
	protected IManagedForm form;
	LotScrapTableManager viewer;
	protected Lot lot;
	protected static String UNSCRAP_CODE = "UnScrapCode";
	protected String actionCode;
	protected static String TABLE_NAME = "WIPLotScrapListToUnScrap";
	
	public UnScrapByScrapDialog(Shell parent) {
        super(parent);
    }
	
	public UnScrapByScrapDialog(Shell parent, IManagedForm form, Lot lot){
		this(parent);
		this.form = form;
		this.lot = lot;
	}
	
	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(300, 100, 800, 500);
		getShell().setMinimumSize(800, 600);
	}
	
	@Override
    protected Control buildView(Composite parent) {
		FormToolkit toolkit = form.getToolkit();
        setTitleImage(SWTResourceCache.getImage("trackin-dialog"));
        setTitle(Message.getString("wip.unscrap_lot"));
        setMessage(Message.getString("wip.unscrapLot_Info"));
        
        Composite content = toolkit.createComposite(parent);
        content.setLayoutData(new GridData(GridData.FILL_BOTH));
        content.setLayout(new GridLayout(1, false));
        
        Composite tableContainer = toolkit.createComposite(content, SWT.NULL);
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
        createTableViewer(tableContainer, toolkit);
        
        return parent;
	}
	

	public boolean validate(){
		IMessageManager mmng = form.getMessageManager();
		mmng.removeAllMessages();
		List<LotScrap> tempList = new ArrayList<LotScrap>();
		List<LotScrap> scrapLots = (List<LotScrap>) viewer.getInput();
		for ( LotScrap scrap : scrapLots ) {
			if ( scrap.getAttribute1() == null || "".equals(scrap.getAttribute1())) {
				if ( scrap.getAttribute2() == null || "".equals(scrap.getAttribute2())) {
					continue;
				}else{
					UI.showError(Message.getString("wip.step_id") + ": " + scrap.getStepName() + "(" + scrap.getActionCode() + ")" +
				                 "     " + UNSCRAP_CODE + Message.getString(ExceptionBundle.bundle.CommonIsNull()));
					return false;
				}
			}
			if ( scrap.getAttribute2() == null ) {
				UI.showError(Message.getString("wip.step_id") + ": " + scrap.getStepName() + "(" + scrap.getActionCode() + ")" +
						     "     " +Message.getString("wip.unscrap_mainqty") + Message.getString(ExceptionBundle.bundle.CommonIsNull()));
				return false;
			}
			BigDecimal num = new BigDecimal(scrap.getAttribute2().toString());
			if ( scrap.getMainQty().compareTo(num) < 0 ) {
				UI.showError( Message.getString("wip.unmainqty_larger_real") );
				return false;
			}
			tempList.add(scrap);
		}
		if ( tempList.size() == 0 ) {
			UI.showError(Message.getString("wip.unscrap_is_null"));
			return false;
		}
		viewer.setInput(tempList);
		return true;
	}
	
	@Override
    protected void okPressed() {
		try {
			if (validate()) {
				LotManager lotManager = Framework.getService(LotManager.class);
				lot.setOperator1(Env.getUserName());
				LotAction lotAction = new LotAction();
				lotAction.setActionCode(getActionCode());
				lotManager.unScrapLot(lot, getUSList(), lotAction, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.unscrapLot_success"));
			}else{
				return;
			}
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
        super.okPressed();
    }
	
	/*
	 * ��ñ��ϵĶ�����
	 */
	protected String getActionCode(){
		List<LotScrap> scrapLots = (List<LotScrap>) viewer.getInput();
		if(scrapLots!=null){
			for(LotScrap  scrap : scrapLots){
				actionCode=scrap.getAttribute1().toString();
			}
		}
		return actionCode;
	}
	
	
	protected void createTableViewer(Composite tc, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<LotScrap> scrapLots = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class, Integer.MAX_VALUE,
                    " lotRrn =" + lot.getObjectRrn(), "");
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			viewer = new LotScrapTableManager(adTable);
			viewer.newViewer(tc);
			viewer.setAutoSizeFlag(false);
			viewer.setInput(scrapLots);
			viewer.setIndexFlag(false);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	public List<LotUnScrapAction> getUSList () {
		/*List<LotUnScrapAction> unScrapActions = new ArrayList<LotUnScrapAction>();
		List<LotScrap> scrapLots = (List<LotScrap>) viewer.getInput();
		for ( LotScrap scrap : scrapLots ) {
			LotUnScrapAction action = new LotUnScrapAction();
			action.setActionCode((String) scrap.getAttribute1());
			BigDecimal main = new BigDecimal(scrap.getAttribute2().toString());
			action.setUnMainQty(main);
			if (scrap.getAttribute5() != null && !"".equals(scrap.getAttribute5())) {
				BigDecimal sub = new BigDecimal(scrap.getAttribute5().toString());
				action.setUnSubQty(sub);
			}
			action.setLotScrap(scrap);
			unScrapActions.add(action);
		}
		return unScrapActions;*/
		return null;
		
	}
}
