package com.glory.mes.wip.lot.history;


import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotHistoryManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.his.ComponentUnitHis;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.his.LotHisSMC;
import com.glory.mes.wip.model.LotStateMachine;

public class WIPSplitLotInfoDialog extends GlcBaseDialog { 

	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 300;
	public static final String FIELD_SPLITLOTHIS = "splitLotHis";

	protected ListTableManagerField splitLotHisField;
	protected ListTableManager listTableManager;
	
	protected LotHis lotHis;
	protected String whereClause;
	protected List<LotHisSMC> lotHisSMCList;

	public WIPSplitLotInfoDialog(String adFormName, String authority, IEventBroker eventBroker, LotHis lotHis) {
		super(adFormName, authority, eventBroker);
		this.setLotHis(lotHis);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		splitLotHisField = form.getFieldByControlId(FIELD_SPLITLOTHIS, ListTableManagerField.class);
		listTableManager = splitLotHisField.getListTableManager();
		
		if (LotStateMachine.TRANS_SPLITLOT.equals(getLotHis().getTransType()) ||
				LotStateMachine.TRANS_SPLITOUT.equals(getLotHis().getTransType())) {
			setTitleImage(SWTResourceCache.getImage("operation-dialog"));
	        setTitle(Message.getString(WipExceptionBundle.bundle.LotHisSplitDetail()));
	        setMessage(Message.getString(WipExceptionBundle.bundle.LotHisSplitDetail()));
		} else {
			setTitleImage(SWTResourceCache.getImage("operation-dialog"));
	        setTitle(Message.getString(WipExceptionBundle.bundle.LotHisMergeDetail()));
	        setMessage(Message.getString(WipExceptionBundle.bundle.LotHisMergeDetail()));
		}
		
		init();
	}
	
	protected void init() {
		try {
			LotHistoryManager lotHistoryManager = Framework.getService(LotHistoryManager.class);
			ADManager manager = Framework.getService(ADManager.class);
			if (LotStateMachine.TRANS_SPLITOUT.equals(getLotHis().getTransType())) {
				List<LotHis> his = lotHistoryManager.getLotHisByHistorySeq(Env.getOrgRrn(), lotHis.getHisSeq(), " hisSeqNo = " + lotHis.getHisSeqNo() + " and transType = '" + LotStateMachine.TRANS_SPLITLOT + "'");
				whereClause = " hisSeq = '" + his.get(0).getHisSeq() + "' AND transType = 'SPLITLOT'";
			} else if (LotStateMachine.TRANS_MERGEIN.equals(getLotHis().getTransType())) {
				List<LotHis> his = lotHistoryManager.getLotHisByHistorySeq(Env.getOrgRrn(), lotHis.getHisSeq(), " hisSeqNo = " + lotHis.getHisSeqNo() + " and transType = '" + LotStateMachine.TRANS_MERGELOT + "'");
				whereClause = " hisSeq = '" + his.get(0).getHisSeq() + "' AND transType = 'MERGELOT'";
			} else if (LotStateMachine.TRANS_MERGELOT.equals(getLotHis().getTransType())) {
				List<LotHis> his = lotHistoryManager.getLotHisByHistorySeq(Env.getOrgRrn(), lotHis.getHisSeq(), " hisSeqNo = " + lotHis.getHisSeqNo() + " and transType = '" + LotStateMachine.TRANS_MERGELOT + "'");
				whereClause = " hisSeq = '" + his.get(0).getHisSeq() + "' AND transType = 'MERGELOT'";
			} else {
				whereClause = " hisSeq = '" + getLotHis().getHisSeq() + "'";
			}	

			lotHisSMCList = manager.getEntityList(Env.getOrgRrn(), LotHisSMC.class, Integer.MAX_VALUE, whereClause, " ");

			for (LotHisSMC lotHisSMC : lotHisSMCList) {
				BigDecimal mainQty = null;
				String lotId = null;
				LotHis lotHis = getLotHis();
				String transType = lotHis.getTransType();
				if (LotStateMachine.TRANS_SPLITLOT.equals(transType)) {
					lotId = lotHisSMC.getToLotId();
					if (null == lotHisSMC.getToMainQty()) {
						mainQty = BigDecimal.ZERO;
					} else {
						mainQty = lotHisSMC.getToMainQty();
					}
				} else if (LotStateMachine.TRANS_SPLITOUT.equals(transType)) {
					lotId = lotHisSMC.getFromLotId();
					if (null == lotHisSMC.getToMainQty()) {
						mainQty = BigDecimal.ZERO;
					} else {
						mainQty = lotHisSMC.getToMainQty();
					}
				} else if (LotStateMachine.TRANS_MERGELOT.equals(transType)) {
					lotId = lotHisSMC.getFromLotId();
					if (null == lotHisSMC.getFromMainQty()) {
						mainQty = BigDecimal.ZERO;
					} else {
						mainQty = lotHisSMC.getFromMainQty();
					}
				} else if (LotStateMachine.TRANS_MERGEIN.equals(transType)) {
					lotId = lotHisSMC.getToLotId();
					if (null == lotHisSMC.getFromMainQty()) {
						mainQty = BigDecimal.ZERO;
					} else {
						mainQty = lotHisSMC.getFromMainQty();
					}
				} else {
					lotId = "";
				}
				
				String compStr = "";
				String compPositionStr = "";
				try {
					List<ComponentUnitHis> hiss = lotHistoryManager.getComponentHisByHistorySeq(Env.getOrgRrn(), lotHisSMC.getHisSeq(), " hisSeqNo = " + lotHisSMC.getHisSeqNo());
					Collections.sort(hiss, new Comparator<ComponentUnitHis>() {  						  
						@Override  
				        public int compare(ComponentUnitHis o1, ComponentUnitHis o2) { 
							return o2.getComponentId().compareTo(o1.getComponentId());
				        }  
				    });
			
					int i = 0;
					for (ComponentUnitHis his : hiss) {
						i ++;
						if (i < hiss.size()) {
							compStr = compStr + his.getComponentId() + ";";
							compPositionStr = compPositionStr + his.getPosition() + ";";
						} else {
							compStr = compStr + his.getComponentId();
							compPositionStr = compPositionStr + his.getPosition();
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				lotHisSMC.setLotId(lotId);
				lotHisSMC.setToMainQty(mainQty);
//				lotHisSMC.setToLotId(compStr); // ����� ����ţ� ��չʾ
				lotHisSMC.setHisSeq(compStr);// ����� ����ţ� ��չʾ
			}
			if(LotStateMachine.TRANS_MERGEIN.equals(getLotHis().getTransType()) || LotStateMachine.TRANS_SPLITLOT.equals(getLotHis().getTransType())) {
				lotHisSMCList = lotHisSMCList.stream().filter(s -> s.getFromLotId().equals(getLotHis().getLotId())).collect(Collectors.toList());
			}else if (LotStateMachine.TRANS_SPLITOUT.equals(getLotHis().getTransType())) {
				lotHisSMCList = lotHisSMCList.stream().filter(s -> s.getToLotRrn().equals(getLotHis().getLotRrn())).collect(Collectors.toList());
			}
			listTableManager.setInput(lotHisSMCList);
			listTableManager.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}

	public LotHis getLotHis() {
		return lotHis;
	}

	public void setLotHis(LotHis lotHis) {
		this.lotHis = lotHis;
	}
	
	
}