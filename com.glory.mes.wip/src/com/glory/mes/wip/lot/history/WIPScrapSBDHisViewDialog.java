package com.glory.mes.wip.lot.history;


import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.his.LotSBDHis;
import com.glory.mes.wip.model.LotStateMachine;

public class WIPScrapSBDHisViewDialog extends GlcBaseDialog { 

	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 300;
	public static final String FIELD_SCRAPSBDHIS = "scrapSBDHis";

	protected ListTableManagerField scrapSBDHisField;
	private LotHis lotHis;
	private String whereClause;
	protected List<LotSBDHis> lotHisSBDList;
	protected ListTableManager listTableManager;

	public WIPScrapSBDHisViewDialog(String adFormName, String authority, IEventBroker eventBroker, LotHis lotHis) {
		super(adFormName, authority, eventBroker);
		this.setLotHis(lotHis);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		scrapSBDHisField = form.getFieldByControlId(FIELD_SCRAPSBDHIS, ListTableManagerField.class);
		listTableManager = scrapSBDHisField.getListTableManager();
		
		if(LotStateMachine.TRANS_SCRAPLOT.equals(getLotHis().getTransType())){
			setTitleImage(SWTResourceCache.getImage("operation-dialog"));
	        setTitle(Message.getString(WipExceptionBundle.bundle.LotHisScrapDetail()));
	        setMessage(Message.getString(WipExceptionBundle.bundle.LotHisScrapDetail()));
		} else {
			setTitleImage(SWTResourceCache.getImage("operation-dialog"));
	        setTitle(Message.getString(WipExceptionBundle.bundle.LotHisUnScrapDetail()));
	        setMessage(Message.getString(WipExceptionBundle.bundle.LotHisUnScrapDetail()));
		}
		init();
	}
	
	protected void init() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			whereClause = " hisSeq = '" + getLotHis().getHisSeq() + "' AND lotRrn = " + getLotHis().getLotRrn();
			lotHisSBDList = adManager.getEntityList(Env.getOrgRrn(), LotSBDHis.class,
					Integer.MAX_VALUE, whereClause, " ");
			listTableManager.setInput(lotHisSBDList);
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}

	public LotHis getLotHis() {
		return lotHis;
	}

	public void setLotHis(LotHis lotHis) {
		this.lotHis = lotHis;
	}
	
}