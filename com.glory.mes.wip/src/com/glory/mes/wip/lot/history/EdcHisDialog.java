package com.glory.mes.wip.lot.history;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.DirectoryDialog;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcDialog;
import com.glory.edc.collection.ResultDialog;
import com.glory.edc.collection.query.adapter.EdcGeneralDataAdapter;
import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcResult;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.his.LotHis;
import com.glory.framework.core.exception.ExceptionBundle;

public class EdcHisDialog extends EdcDialog {

	private ToolItem itemSpc;
	private ToolItem itemExport;
	private LotHis lotHis;
	private static final String TEMPLATE_FILE_NAME = "edcdata_template.xlsx";

	public EdcHisDialog(Shell parentShell) {
		super(parentShell);
	}

	public EdcHisDialog(Shell parent, EdcSetCurrent edc,
			EdcEvent event, LotHis lothis) {
		super(parent,edc, null, event);
		setEdcSet();
		this.lotHis = lothis;
	}

	@Override
	protected void setButtonEnable() {
	    itemSave.setEnabled(false);
	    itemTempSave.setEnabled(false);
	    itemClear.setEnabled(false);
	    itemSave.setEnabled(false);
	}

	@Override
	protected void createToolBar(Composite parent) {
		ToolBar tBar = new ToolBar(parent, SWT.HORIZONTAL | SWT.FLAT);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemGetSpc(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTempSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClear(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClose(tBar);
		
		section.setTextClient(tBar);
	}
	
	private void createToolItemGetSpc(ToolBar tBar) {
		itemSpc = new ToolItem(tBar, SWT.PUSH);
		itemSpc.setText(Message.getString("wip.history_Spc_Result"));
		itemSpc.setImage(SWTResourceCache.getImage("search"));
		itemSpc.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				getSpcResiult();		
			}
		});
	}

	protected void getSpcResiult() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			String condition=" hisSeq = '"+lotHis.getHisSeq()+"'";
			EdcResult edcResult=adManager.getEntityList(Env.getOrgRrn(), 
					EdcResult.class, Env.getMaxResult(), condition, "").get(0);
			ResultDialog reDio = new ResultDialog(getParentShell(), EdcData.EDCFROM_LOT, edcSet, dcDatas, edcResult);
			if (reDio.open() == Dialog.OK) {
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * ��Ϊ����ʷ������뱾���棬���Ҳ���ΪSetCurrent��edcSet��ȻΪ�գ����Ա�����������EdcSet
	 */
	public void setEdcSet(){
		try{
			EDCManager edcManager = Framework.getService(EDCManager.class);
			edcSet = edcManager.getActualEdcSet(edcCurrent.getItemSetRrn(), null, null);
		}catch(Exception e){
			
		}
	}
	
	protected void createToolItemExport(ToolBar tBar) {
		  this.itemExport = new ToolItem(tBar, 8);
		  this.itemExport.setText(Message.getString(ExceptionBundle.bundle.CommonExport()));
		  this.itemExport.setImage(SWTResourceCache.getImage("export"));
		  this.itemExport.addSelectionListener(new SelectionAdapter()
		      {
		        public void widgetSelected(SelectionEvent event) {
		        	exportExcel();
		        	UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonExportSuccessed()));
		        }
		      });
		}
	
	// ����Excel
	public void exportExcel() {
		try {
			List<EdcData> edcDatas = getLastDcDatas();
			List<Map<String, String>> excelDatas = new ArrayList<Map<String, String>>();

			List<String> titles = new ArrayList<String>();
			titles.add("EDC_SET");
			titles.add("EDC_ITEM");
			titles.add("TIME");
			titles.add("LOT_ID");
			titles.add("WAFER");
			titles.add("EQUIPMENT_ID");
			
			boolean first = false;
			for (EdcData edcData : edcDatas) {
				EDCManager edcManager = Framework.getService(EDCManager.class);
				
				AbstractEdcSet tempEdcSet = edcManager.getActualEdcSet(edcData.getEdcSetRrn(), null, null);
				if (tempEdcSet != null) {
					Map<String, String> edcDataMap = new  HashMap<String, String>();
					edcDataMap.put("EDC_SET", edcData.getEdcSetName());
					edcDataMap.put("EDC_ITEM", edcData.getItemName());
					edcDataMap.put("TIME", edcData.getCreated().toString());
					edcDataMap.put("LOT_ID", edcData.getLotId());
					edcDataMap.put("WAFER", edcData.getComponentList());
					edcDataMap.put("EQUIPMENT_ID", edcData.getMeasureEqp());
					if (EdcItem.DATATYPE_VARIABLE.equals(edcData.getDataType())) {
						String[] rawDataString = edcData.getRawDataString();
						for (int i = 1; i <= rawDataString.length; i++) {
							edcDataMap.put(EdcGeneralDataAdapter.DATA_PREFIX + i, rawDataString[i-1]);
							if (!first) {
								titles.add(EdcGeneralDataAdapter.DATA_PREFIX + i);
							}
						}
					}
					excelDatas.add(edcDataMap);
				}
				first = true;
			}
			
			if (excelDatas.isEmpty()) {
				return;
			}
			DirectoryDialog dialog = new DirectoryDialog(UI.getActiveShell(), 4096);
			dialog.setFilterPath("C:/");
			String dirUrl = dialog.open();
			writeFile(dirUrl, titles, excelDatas);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}
	
	protected void writeFile(String exportDir, List<String> titles, List<Map<String, String>> excelDatas) {
		try {
			//����Excel�ļ���
	        XSSFWorkbook workbook=new XSSFWorkbook();
	        //����������sheeet
	        Sheet sheet=workbook.createSheet();
	        //������һ��
	        Row row=sheet.createRow(0);
	        Cell cell=null;
	        int i = 0;
	        for(String title : titles) {
	        	cell=row.createCell(i);
	            cell.setCellValue(title);
	            i ++;
	        }
	       
	        //׷������
	        for (int j=1;j<excelDatas.size() + 1;j++){
	            Row nextrow=sheet.createRow(j);
	            int colum = 0;
	            for (String title : titles) {
	            	Cell detailCell = nextrow.createCell(colum);
	            	detailCell.setCellValue(excelDatas.get(j-1).get(title));
	            	colum++;
	            	if (colum == excelDatas.get(0).keySet().size())  {
	            		break;
	            	}
	            }
	        }
	        //����һ���ļ�
	        File file=new File(exportDir+"\\"+ TEMPLATE_FILE_NAME + ".xlsx");
	        file.createNewFile();
	        FileOutputStream stream= FileUtils.openOutputStream(file);
	        workbook.write(stream);
	        stream.close();	
			
		} catch (Exception e) {
			e.printStackTrace();

		}
	}

}
