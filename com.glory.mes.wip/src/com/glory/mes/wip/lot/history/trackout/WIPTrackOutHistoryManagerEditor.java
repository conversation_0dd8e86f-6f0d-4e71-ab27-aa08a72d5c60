package com.glory.mes.wip.lot.history.trackout;


import java.util.Map;

import javax.inject.Inject;

import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.jface.action.MenuManager;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.lot.contextmenu.LotContextAction;
import com.google.common.collect.Lists;

public class WIPTrackOutHistoryManagerEditor extends GlcEditor implements IRefresh{ 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.history.trackout.WIPTrackOutHistoryManagerEditor";

	public static final String FIELD_LOTQUERY = "lotQuery";

	protected CustomField lotQueryField;
	protected MenuManager menuManager;
	
	@Inject
	protected EModelService modelService;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotQueryField = form.getFieldByControlId(FIELD_LOTQUERY, CustomField.class);
		
		LotListComposite lotQueryLotListComposite = (LotListComposite) lotQueryField.getCustomComposite();
		menuManager = lotQueryLotListComposite.getTableManager().getTableManager().getMenuManager();
		if (menuManager != null) {
    		LotContextAction.addRightClickMenu(lotQueryLotListComposite.getTableManager(), menuManager, partService, modelService, Lists.newArrayList("Wip.LotHistory"));
		} else {
			menuManager = new MenuManager();
    		LotContextAction.initRightClickMenu(lotQueryLotListComposite.getTableManager(), menuManager, partService, modelService, Lists.newArrayList("Wip.LotHistory"));
		}
		
	}

	@Override
	public Map<String, Object> getParameterMap() {
		return null;
	}

	@Override
	public String getWhereClause() {
		return null;
	}

	@Override
	public boolean isUseParam() {
		return false;
	}

	@Override
	public void refresh() {
		
	}

	@Override
	public void setWhereClause(String var1) {
	}
}