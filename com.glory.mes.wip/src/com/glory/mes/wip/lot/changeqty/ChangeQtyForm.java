package com.glory.mes.wip.lot.changeqty;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.core.util.PropertyUtil;

public class ChangeQtyForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(ChangeQtyForm.class);

	private IField fieldMainQty;
	private static final String MAINQTY = "MainQty";
	public static final String MAINQTY_ID = "mainQty";
	private IField fieldSubQty;
	private static final String SUBQTY = "SubQty";
	public static final String SUBQTY_ID = "subQty";

	public ChangeQtyForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {

			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);

			fieldMainQty = createText(MAINQTY_ID, Message.getString("wip.mainqty"), "", 32);

			fieldSubQty = createText(SUBQTY_ID, Message.getString("wip.subqty"), "", 32);

			addField(MAINQTY, fieldMainQty);
			addField(SUBQTY, fieldSubQty);

		} catch (Exception e) {
			logger.error("LotProForm : Init listItem", e);
		}

	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.equals(fieldMainQty) && !f.equals(fieldSubQty)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			refresh();
		}
	}

	@Override
	public void refresh() {
		super.refresh();
		((TextField) fieldMainQty).setText("");
		((TextField) fieldSubQty).setText("");
	}

	@Override
	public boolean validate() {
		mmng.removeAllMessages();
		boolean mainIsNull = GenericValidator.isBlankOrNull((String) fieldMainQty.getValue());
		boolean subIsNull = GenericValidator.isBlankOrNull((String) fieldSubQty.getValue());
		boolean validFlag = true;

		if (mainIsNull && subIsNull) {
			validFlag = validFlag && false;
		}

		return validFlag;
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !GenericValidator.isBlankOrNull(String.valueOf(f.getValue()))) {
					PropertyUtil.setProperty(object, f.getId(), f.getValue());
				}
			}
			return true;
		}
		return false;
	}

}
