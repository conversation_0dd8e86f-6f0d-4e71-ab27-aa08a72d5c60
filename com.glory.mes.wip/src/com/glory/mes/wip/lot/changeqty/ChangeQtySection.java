package com.glory.mes.wip.lot.changeqty;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeQtySection extends LotSection {
	private static final Logger logger = Logger.getLogger(EntitySection.class);

	private ChangeQtyForm itemForm;
	protected ToolItem itemChange;

	public ChangeQtySection() {
		super();
	}

	public ChangeQtySection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.sectiontitle_changeqty"));
		initAdObject();
	}

	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChange(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemChange(ToolBar tBar) {
		itemChange = new ToolItem(tBar, SWT.PUSH);
		itemChange.setText(Message.getString("wip.change"));
		itemChange.setImage(SWTResourceCache.getImage("hold"));
		itemChange.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeAdapter(event);
			}
		});
	}

	protected void changeAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					LotManager lotManager = Framework.getService(LotManager.class);
					Lot lot = (Lot) this.getAdObject();
					lot.setOperator1(Env.getUserName());
					lotManager.changeLotInfo(lot.getLotId(), getPropertiesMap(lot), Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
					refresh();
				}
			}
			txtLot.setFocus();
		} catch (Exception e) {
			logger.error("ChangeQtySection : changeAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public Map<String, String> getPropertiesMap(Lot lot){
		try {
			String mainQtyNow = String.valueOf(lot.getMainQty());
			String subQtyNow = lot.getSubQty() == null ? "" : String.valueOf(lot.getSubQty());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot hislot = lotManager.getLot(lot.getObjectRrn());
			
			String mainQtyLast = String.valueOf(hislot.getMainQty());
			String subQtyLast = hislot.getSubQty() == null ? "" : String.valueOf(hislot.getSubQty());
			
			Map<String, String> map = new HashMap<String, String>();
			if(!mainQtyNow.equals(mainQtyLast)){
				map.put(ChangeQtyForm.MAINQTY_ID, mainQtyNow);
			}
			
			if(!subQtyNow.equals(subQtyLast)){
				map.put(ChangeQtyForm.SUBQTY_ID, subQtyNow);
			}
			return map;
		} catch (Exception e) {
			logger.error("ChangeQtySection : getPropertiesMap()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}

	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Lot newBase = (Lot) this.getAdObject();
		if (newBase != null) {
			stateChange(newBase);
		}
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		itemForm = new ChangeQtyForm(composite, SWT.NONE, tab, mmng);
		return itemForm;
	}

	public void stateChange(Lot lot) {
		if (lot != null && lot.getState() != null) {
			if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState())) {
				itemChange.setEnabled(true);
			} else {
				itemChange.setEnabled(false);
			}
		} else {
			itemChange.setEnabled(false);
		}
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));	
				itemForm.setObject((Lot)getAdObject());
				itemForm.refresh();
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
	}
}
