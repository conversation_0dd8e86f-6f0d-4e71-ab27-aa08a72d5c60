package com.glory.mes.wip.lot.flowcdi;

import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.lang.ObjectUtils;
import org.apache.log4j.Logger;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.cdi.LotFlowCdiPoint;
import com.glory.mes.wip.cdi.client.FlowCdiActionManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class StepFlowCdiEditor extends GlcEditor {

	private static final Logger logger = Logger.getLogger(StepFlowCdiEditor.class);

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.flowcdi.StepFlowCdiEditor";
	
	public static final String CONTROL_STEP_FLOW_CDI_QUERY_INFO = "stepFlowCdiQueryInfo";
	public static final String CONTROL_STEP_FLOW_CDI_EDIT_INFO = "stepFlowCdiEditInfo";
	public static final String CONTROL_CDI_DOWN_LEFT_QUERY_INFO = "leftQueryInfo";
	public static final String CONTROL_CDI_DOWN_RIGHT_EDIT_INFO = "rightEditInfo";
	public static final String CONTROL_CDI_POINT_NAME = "cdiPointName";
	public static final String CONTROL_STEP_CATEGORY = "stepCategory";
	
	//���沿��
	public QueryFormField stepFlowCdiQueryInfoField;
	public RefTableField flowCdiPointsQueryInfo;
	
	//���沿��
	public GlcFormField stepFlowCdiEditInfoField;
	public EntityFormField downLeftEntityFormField; 
	public ListTableManagerField downRightListTableManagerField;
	public RefTableField downLeftFlowCdiPointsQueryInfo;
	public RefTableField downLeftStepCategoryQueryInfo; 
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		//��ȡ����Ĳ�ѯ�ؼ�
		stepFlowCdiQueryInfoField = form.getFieldByControlId(CONTROL_STEP_FLOW_CDI_QUERY_INFO, QueryFormField.class);
		
		//��ȡע���Ĳ�ѯ�ؼ�
		flowCdiPointsQueryInfo = stepFlowCdiQueryInfoField.getQueryForm().getFieldByControlId(CONTROL_CDI_POINT_NAME, RefTableField.class);
		
		//��ȡ����ı༭�ؼ�
		stepFlowCdiEditInfoField = form.getFieldByControlId(CONTROL_STEP_FLOW_CDI_EDIT_INFO, GlcFormField.class);
	
		//��ȡ���²�ѯ�ؼ�
		downLeftEntityFormField = stepFlowCdiEditInfoField.getFieldByControlId(CONTROL_CDI_DOWN_LEFT_QUERY_INFO, EntityFormField.class);
		
		//��ȡע���Ĳ�ѯ�ؼ�
		downLeftFlowCdiPointsQueryInfo = downLeftEntityFormField.getFieldByControlId(CONTROL_CDI_POINT_NAME, RefTableField.class);
		
		//��ȡ�������Ĳ�ѯ�ؼ�
		downLeftStepCategoryQueryInfo = downLeftEntityFormField.getFieldByControlId(CONTROL_STEP_CATEGORY, RefTableField.class);
		
		//��ȡ���±༭�ؼ�
		downRightListTableManagerField = stepFlowCdiEditInfoField.getFieldByControlId(CONTROL_CDI_DOWN_RIGHT_EDIT_INFO, ListTableManagerField.class);	
		
		//��ѯ���ѡ���¼�
		subscribeAndExecute(eventBroker, stepFlowCdiQueryInfoField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);
		
		//��ѯ����ѯ�¼�
		subscribeAndExecute(eventBroker, stepFlowCdiQueryInfoField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryFormAdapter);
		
		//���²�ѯ�¼�
		subscribeAndExecute(eventBroker, stepFlowCdiEditInfoField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		
		//�����¼�
		subscribeAndExecute(eventBroker, stepFlowCdiEditInfoField.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);			
		
		//ˢ���¼�
		subscribeAndExecute(eventBroker, stepFlowCdiEditInfoField.getFullTopic(ADButtonDefault.BUTTON_NAME_ENTITYREFRESH), this::queryAdapter);
		
		init();
	}
	
	protected void selectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotFlowCdiPoint == null) {
				return;
			}
			
			//ˢ������ı༭�б�
			downLeftEntityFormField.setValue(lotFlowCdiPoint);
			downLeftEntityFormField.refresh();
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getCdiPointName())) {
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
						lotFlowCdiPoint.getCdiPointName(), null, lotFlowCdiPoint.getStepCategory(), null);
				downRightListTableManagerField.setValue(lotFlowCdiPoints);
				downRightListTableManagerField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void queryFormAdapter(Object object) {
		LinkedHashMap<String, IField> fields = stepFlowCdiQueryInfoField.getQueryForm().getFields();
		boolean queryFlag = false;
        for(IField f : fields.values()) {
        	Object value = f.getValue();
        	if (CONTROL_STEP_CATEGORY.equals(f.getId())) {
        		if (value != null && !StringUtil.isEmpty(value.toString())) {
        			queryFlag = true;
	        		break;
	        	}
        	}
        }
//		if (!queryFlag) {
//			UI.showError(Message.getString("wip.step_name_and_step_category_is_all_empty"));
//			stepFlowCdiQueryInfoField.getQueryForm().getTableManager().getInput().clear();
//		}
	}
	
	protected void queryAdapter(Object object) {
		try {
			downLeftEntityFormField.getFormControl().getMessageManager().setAutoUpdate(true); //����Ϊtrueʱ�Ż�ˢ�½������icon
			downLeftEntityFormField.getFormControl().getMessageManager().removeAllMessages();

			LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint)downLeftEntityFormField.getValue();
			if (lotFlowCdiPoint == null || StringUtil.isEmpty(lotFlowCdiPoint.getCdiPointName())) {
				return;
			}
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getStepCategory())) {
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
						lotFlowCdiPoint.getCdiPointName(), null, lotFlowCdiPoint.getStepCategory(), null);
				downRightListTableManagerField.setValue(lotFlowCdiPoints);
				downRightListTableManagerField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			downLeftEntityFormField.getFormControl().getMessageManager().setAutoUpdate(false);
		}
	}
	
	protected void saveAdapter(Object object) {
		try {	
			if (downRightListTableManagerField.getValue() != null) {
				List<LotFlowCdiPoint> cdiPoints = (List<LotFlowCdiPoint> ) downRightListTableManagerField.getValue();
				LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint)downLeftEntityFormField.getValue();
				if (cdiPoints.isEmpty() || lotFlowCdiPoint == null) {
					return;
				}
				if (!ObjectUtils.equals(lotFlowCdiPoint.getCdiPointName(), cdiPoints.get(0).getCdiPointName())) {
					UI.showError(String.format(Message.getString("wip.flowcdi_is_different"), ((ADField)downLeftFlowCdiPointsQueryInfo.getADField()).getDescription()));
					return;
				}
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				flowCdiActionManager.saveStepFlowCdiPoint(lotFlowCdiPoint.getCdiPointName(), lotFlowCdiPoint.getStepCategory(), null, cdiPoints, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
				queryAdapter(object);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void init() {
		try {
			//ˢ������ע����б�
			FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
			List<LotFlowCdiPoint> cdiPoints = flowCdiActionManager.getFlowCdiPoints(false);
			cdiPoints.add(new LotFlowCdiPoint());
			flowCdiPointsQueryInfo.setInput(cdiPoints);
			flowCdiPointsQueryInfo.refresh();
			
			//ˢ�����²�������
			downLeftFlowCdiPointsQueryInfo.setInput(cdiPoints);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
