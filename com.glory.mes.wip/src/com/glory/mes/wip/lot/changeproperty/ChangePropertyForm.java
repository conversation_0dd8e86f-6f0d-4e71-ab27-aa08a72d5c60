package com.glory.mes.wip.lot.changeproperty;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.CalendarField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.model.Customer;
import com.glory.mes.wip.model.Lot;

@Deprecated
public class ChangePropertyForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(ChangePropertyForm.class);

	private IField fieldLotType;
	private static final String LOTTYPE = "LotType";
	public static final String LOTTYPE_ID = "lotType";
	private IField fieldLotCustomerCode;
	private static final String CUSTOMER_CODE = "CustomerCode";
	public static final String CUSTOMER_CODE_ID = "customerCode";
	private IField fieldLotCustomerOrder;
	private static final String CUSTOMER_ORDER = "CustomerOrder";
	public static final String CUSTOMER_ORDER_ID = "customerOrder";
	private IField fieldLotCustomerPartId;
	private static final String CUSTOMER_PART = "CustomerPartId";
	public static final String CUSTOMER_PART_ID = "customerPartId";
	private IField fieldLotCustomerLotId;
	private static final String CUSTOMER_LOT = "CustomerLotId";
	public static final String CUSTOMER_LOT_ID = "customerLotId";
	private IField fieldLotPlanStartDate;
	private static final String PlanStartDate = "PlanStartDate";
	public static final String PLAN_START_DATE_ID = "planStartDate";
	private IField fieldLotPlanEndDate;
	private static final String PlanEndDate = "PlanEndDate";
	public static final String PLAN_END_DATE_ID = "planEndDate";
	private static final String TABLE_NAME = "BASCustomer";	
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	public static final String COMMENT_ID = "lotComment";
	public String comment;
	
	public ChangePropertyForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public ChangePropertyForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public ChangePropertyForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public ChangePropertyForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}	
	
	@Override
	public void addFields() {
		super.addFields();
		try {	
			
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ADRefTable refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setTextField("name");
			refTable.setKeyField("name");
//			FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());
			ListTableManager tableManager = new ListTableManager(adTable);
			if (refTable.getWhereClause() == null || "".equalsIgnoreCase(refTable.getWhereClause().trim())
					|| StringUtil.parseClauseParam(refTable.getWhereClause()).size() == 0){
				
				List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), 
						adTable.getObjectRrn(), Env.getMaxResult(), refTable.getWhereClause(), refTable.getOrderByClause());
				tableManager.setInput(list);					
			}
			fieldLotCustomerCode = createRefTableFieldComboReadOnly(CUSTOMER_CODE_ID, Message.getString("wip.editproperty_customercode"), tableManager, refTable, false, 32);
			fieldLotCustomerOrder = createText(CUSTOMER_ORDER_ID,Message.getString("wip.editproperty_customerorder"),"",32);
			fieldLotCustomerPartId = createText(CUSTOMER_PART_ID,Message.getString("wip.editproperty_customerpart"),"",32);
			fieldLotCustomerLotId = createText(CUSTOMER_LOT_ID,Message.getString("wip.editproperty_customerlot"),"",32);
			fieldLotPlanStartDate = createCalendarField(PLAN_START_DATE_ID, Message.getString("wip.editproperty_planstartdate"));
			fieldLotPlanEndDate = createCalendarField(PLAN_END_DATE_ID, Message.getString("wip.editproperty_planenddate"));
			
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 1024);
			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			
			fieldLotType = createUserRefList(LOTTYPE_ID, Message.getString("wip.lot_type"), "LotType", false);
			
			addField(LOTTYPE, fieldLotType);
			addField(CUSTOMER_CODE, fieldLotCustomerCode);
			addField(CUSTOMER_ORDER, fieldLotCustomerOrder);
			addField(CUSTOMER_PART, fieldLotCustomerPartId);
			addField(CUSTOMER_LOT, fieldLotCustomerLotId);
			addField(PlanStartDate, fieldLotPlanStartDate);
			addField(PlanEndDate, fieldLotPlanEndDate);
			addField(COMMENT, fieldComment);
			
		} catch (Exception e) {
			logger.error("LotProForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) 
						&& !f.equals(fieldLotCustomerCode)
						&& !f.equals(fieldLotCustomerOrder)
						&& !f.equals(fieldLotCustomerPartId)
						&&!f.equals(fieldLotCustomerLotId)
						&&!f.equals(fieldLotPlanStartDate)
						&&!f.equals(fieldLotPlanEndDate)
							&&!f.equals(fieldComment)
							&&!f.equals(fieldLotType)
					) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
		}
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			Lot lot = (Lot) object;
			RefTableField trfcode =(RefTableField) fieldLotCustomerCode;
			trfcode.setData(null);
//			trfcode.setSelectionIndex(-1);
			List list2 = trfcode.getInput();
			if (lot != null && lot.getObjectRrn() != null) {
				((RefTableField) fieldLotType).setValue(lot.getLotType());
				((RefTableField) fieldLotCustomerCode).setValue(lot.getCustomerCode());
				((TextField) fieldLotCustomerOrder).setText(lot.getCustomerOrder());
				((TextField) fieldLotCustomerPartId).setText(lot.getCustomerPartId());
				((TextField) fieldLotCustomerLotId).setText(lot.getCustomerLotId());
				((CalendarField)fieldLotPlanStartDate).setValue(lot.getPlanStartDate());
				((CalendarField)fieldLotPlanEndDate).setValue(lot.getPlanEndDate());
				((TextField) fieldComment).setText(lot.getLotComment());
				for (int i = 0; i < list2.size(); i++) {
					if (((Customer)list2.get(i)).getName().equals(lot.getCustomerCode())) {
						((RefTableField)fieldLotCustomerCode).setData(list2.get(i));
//						((RefTableField)fieldLotCustomerCode).setSelectionIndex(i);
						break;
					}
				}
			} else {
				((TextField) fieldLotCustomerOrder).setText("");
				((TextField) fieldLotCustomerPartId).setText("");
				((TextField) fieldLotCustomerLotId).setText("");
				((TextField) fieldComment).setText("");
			}
		} catch (Exception e) {
			logger.error("LotPorForm : refresh()", e);
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			Lot lot = (Lot)object;	
			if (fieldLotCustomerCode.getData() != null) {
				PropertyUtil.setProperty(lot, fieldLotCustomerCode.getId(),
						((Customer)fieldLotCustomerCode.getData()).getName());
			}					
			PropertyUtil.setProperty(lot, fieldLotCustomerOrder.getId(),
					fieldLotCustomerOrder.getValue());
			PropertyUtil.setProperty(lot, fieldLotCustomerPartId.getId(),
					fieldLotCustomerPartId.getValue());
			PropertyUtil.setProperty(lot, fieldLotCustomerLotId.getId(),
					fieldLotCustomerLotId.getValue());
			PropertyUtil.setProperty(lot, fieldLotPlanStartDate.getId(),
					fieldLotPlanStartDate.getValue());
			PropertyUtil.setProperty(lot, fieldLotPlanEndDate.getId(),
					fieldLotPlanEndDate.getValue());
			PropertyUtil.setProperty(lot, fieldLotType.getId(),
					fieldLotType.getValue());
			PropertyUtil.setProperty(lot, fieldComment.getId(),
					fieldComment.getValue());
			return true;
		}
		return false;
	}


}

