package com.glory.mes.wip.lot.changeproperty;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangePropertySection extends LotSection {
	private static final Logger logger = Logger.getLogger(EntitySection.class);

	public static final String KEY_CHANGE_LOT = "change";
	
	protected AuthorityToolItem itemHold;
	protected EntityForm itemForm;
	protected EntityForm changeForm;
	
	public ChangePropertySection() {
		super();
	}

	public ChangePropertySection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.changeproperty_section"));
		initAdObject();
	}

	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemSave(ToolBar tBar) {
		itemHold = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_CHANGE_LOT);
		itemHold.setAuthEventAdaptor(this::changeAdapter);
		itemHold.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
		itemHold.setImage(SWTResourceCache.getImage("save"));
//		itemHold.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				changeAdapter(event);
//			}
//		});
	}

	protected void changeAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (!changeForm.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					Lot lot = (Lot) changeForm.getObject();
					lot.setOperator1(Env.getUserName());
					LotManager lotManager = Framework.getService(LotManager.class);
					SessionContext sc = Env.getSessionContext();
					if (itemHold.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
						sc.setUserName((String) itemHold.getData(LotAction.ACTION_TYPE_OPERATOR));
					}
					
					//���ú�̨������������
					lotManager.changeLotInfo(lot.getLotId(), getPropertiesMap(lot), sc);
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
					refresh();
				}
			}
			txtLot.setFocus();
		} catch (Exception e) {
			UI.showInfo(Message.getString("common.save_failure"));// ������ʾ��
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public Map<String, String> getPropertiesMap(Lot lot){
		try {
			ADTable adTable = getADTable1();
			Map<String, String> map = new HashMap<String, String>();
			if (CollectionUtils.isNotEmpty(adTable.getFields())) {
				for (ADField adField : adTable.getFields()) {
					Object o = PropertyUtil.getProperty(lot, adField.getName());
					String newValue = DBUtil.toString(o);
					map.put(adField.getName(), newValue);
				}
			}
			return map;
		} catch (Exception e) {
			logger.error("ChangeQtySection : getPropertiesMap()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
		
		changeForm = new EntityForm(itemForm, SWT.NONE, new Lot(), getADTable1(), form.getMessageManager());
		changeForm.setLayout(new GridLayout());
		GridData gd = new GridData(GridData.FILL_BOTH);
		changeForm.setLayoutData(gd);
		return itemForm;
	}
	
	@Override
	public void statusChanged(String newStatus){
		if (newStatus != null) {
			if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus)
					|| LotStateMachine.STATE_RUN.equalsIgnoreCase(newStatus)) {
				itemHold.setEnabled(true);
			} else {
				itemHold.setEnabled(false);
			}
		} else {
			itemHold.setEnabled(false);
		}
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));				
				itemForm.setObject((Lot)getAdObject());
				itemForm.refresh();
			}
			form.getMessageManager().removeAllMessages();
			if(changeForm != null) {
				changeForm.setObject((Lot)getAdObject());
				changeForm.loadFromObject();
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
	}

	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPChangeProperty");
		return midTable;
	}
}
