package com.glory.mes.wip.lot.contextmenu;

import java.io.Serializable;

public class LotContextActionModel implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private String id;
	private int seqNo;
	private String authority;
	private LotContextAction action;
	private String label;
	private String image;
	
	public String getId() {
		return id;
	}
	
	public void setId(String id) {
		this.id = id;
	}
	
	public int getSeqNo() {
		return seqNo;
	}
	
	public void setSeqNo(int seqNo) {
		this.seqNo = seqNo;
	}
	
	public String getAuthority() {
		return authority;
	}
	
	public void setAuthority(String authority) {
		this.authority = authority;
	}
	
	public void setAction(LotContextAction action) {
		this.action = action;
	}

	public LotContextAction getAction() {
		return action;
	}
	
	public String getLabel() {
		return label;
	}
	
	public void setLabel(String label) {
		this.label = label;
	}
	
	public String getImage() {
		return image;
	}
	
	public void setImage(String image) {
		this.image = image;
	}

	
}
