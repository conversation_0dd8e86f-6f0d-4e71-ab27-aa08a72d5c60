package com.glory.mes.wip.lot.contextmenu;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.ActionContributionItem;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.widgets.Menu;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.OpenDesignCommand;
import com.glory.framework.base.application.command.OpenDialogCommand;
import com.glory.framework.base.application.command.OpenEditorCommand;
import com.glory.framework.base.application.command.OpenFEditorCommand;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.model.Lot;

public class LotContextAction extends Action {
		
	private final static Logger logger = Logger.getLogger(LotContextAction.class);

	private ListTableManager tableManager;
	private ADAuthority authority;
	private EPartService partService;
	private EModelService modelService;
		
	public LotContextAction(ListTableManager tableManager, ADAuthority authority, EPartService partService, EModelService modelService) {
		this.tableManager = tableManager;
		this.authority = authority;
		this.partService = partService;
		this.modelService = modelService;

		String lable = (String)I18nUtil.getI18nMessage(authority, "label");
		this.setText(lable);
		ImageDescriptor image = ImageDescriptor.createFromImage(SWTResourceCache.getImage(authority.getImage()));
		this.setImageDescriptor(image);
		
		//����Ȩ��
		setEnabled(false);
		for(String authorityKey : Env.getAuthority()){
			if(authorityKey.equalsIgnoreCase(authority.getName())){
				setEnabled(true);
			}
		}
	}
	
	@Override
	public void run() {
		try {
			if (tableManager != null && tableManager.getSelectedObject() != null && (tableManager.getSelectedObject() instanceof Lot || tableManager.getSelectedObject() instanceof LotHis)) {
				String lotId = null;
				if (tableManager.getSelectedObject() instanceof Lot) {
					lotId = ((Lot)(tableManager.getSelectedObject())).getLotId();
				} else if (tableManager.getSelectedObject() instanceof LotHis) {
					lotId = ((LotHis)(tableManager.getSelectedObject())).getLotId();
				}
				authority.setAttribute1(lotId);
				
				if (ADAuthority.ACTION_TYPE_EDITOR.equals(authority.getAction())) {
					OpenEditorCommand.open(authority, partService, modelService);
				} else if ("F".equals(authority.getAction())) {
					OpenFEditorCommand.open(authority, partService);
				} else if (ADAuthority.ACTION_TYPE_DIALOG.equals(authority.getAction())) {
					OpenDialogCommand.open(authority, partService);
				} else if ("C".equals(authority.getAction())) {
					OpenDesignCommand.open(authority, partService, modelService);
				}
			}
		} catch (Exception e) {
			logger.error("LotContextAction : open", e);
		}
	}
	
	public static Menu initRightClickMenu(ListTableManager tableManager, MenuManager menuManager, EPartService partService, EModelService modelService){
		return initRightClickMenu(tableManager, menuManager, partService, modelService, null);
	}
	
	public static Menu initRightClickMenu(ListTableManager tableManager, MenuManager menuManager, EPartService partService, EModelService modelService, List<String> authoritys){
 		List<LotContextActionModel> actionModels = LotContextMenuExtensionPoint.getActionModels();
		for (LotContextActionModel actionModel : actionModels) {
			try {
    			String authority = actionModel.getAuthority();
    			if (authority != null && authority.trim().length() > 0) {
    				if (CollectionUtils.isNotEmpty(authoritys) && !authoritys.contains(authority)) {
    					continue;
    				}
    				ADManager manager = Framework.getService(ADManager.class);
    				List<ADAuthority> adAuthorities = manager.getEntityList(Env.getOrgRrn(), ADAuthority.class, 1, " name='" + authority + "'", "");
    				if (adAuthorities.size() > 0) {
    					ADAuthority auth = adAuthorities.get(0);
    					ADEditor editor = new ADEditor();
    					editor.setObjectRrn(auth.getEditorRrn());
    					editor = (ADEditor)manager.getEntity((ADBase)editor);
    					if (editor != null) {
    						LotContextAction action = new LotContextAction(tableManager, auth, partService, modelService);
    						menuManager.add(action);
    					}
    				}
    			} else {
    				//TODO �����������͵�Action,��ʱ����
    			}
			} catch (Exception e) {
			}
		}
		Menu contextMenu = menuManager.createContextMenu(tableManager.getNatTable().getParent());
		LotContextMenuConfiguration config = new LotContextMenuConfiguration(tableManager.getNatTable(), contextMenu);
		 
		tableManager.getNatTable().addConfiguration(config); 
		tableManager.getNatTable().configure();
		
		return contextMenu;
	}

	public static Menu addRightClickMenu(ListTableManager tableManager, MenuManager menuManager, EPartService partService, EModelService modelService){
		return addRightClickMenu(tableManager, menuManager, partService, modelService, null);
	}
	
	public static Menu addRightClickMenu(ListTableManager tableManager, MenuManager menuManager, EPartService partService, EModelService modelService, List<String> authoritys){
 		List<LotContextActionModel> actionModels = LotContextMenuExtensionPoint.getActionModels();
 		int i = 0;
		for (LotContextActionModel actionModel : actionModels) {
			try {
    			String authority = actionModel.getAuthority();
    			if (authority != null && authority.trim().length() > 0) {
    				if (CollectionUtils.isNotEmpty(authoritys) && !authoritys.contains(authority)) {
    					continue;
    				}
    				ADManager manager = Framework.getService(ADManager.class);
    				List<ADAuthority> adAuthorities = manager.getEntityList(Env.getOrgRrn(), ADAuthority.class, 1, " name='" + authority + "'", "");
    				if (adAuthorities.size() > 0) {
    					ADAuthority auth = adAuthorities.get(0);
    					ADEditor editor = new ADEditor();
    					editor.setObjectRrn(auth.getEditorRrn());
    					editor = (ADEditor)manager.getEntity((ADBase)editor);
    					if (editor != null) {
    						LotContextAction action = new LotContextAction(tableManager, auth, partService, modelService);
    						menuManager.insert(i, new ActionContributionItem(action));
    					}
    					
    					i++;
    				}
    			} else {
    				//TODO �����������͵�Action,��ʱ����
    			}
    			
			} catch (Exception e) {
			}
		}
		
		Separator sep2 = new Separator();
		menuManager.insert(i, sep2);
		
		return menuManager.getMenu();
	}

}
