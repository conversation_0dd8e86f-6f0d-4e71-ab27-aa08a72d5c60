package com.glory.mes.wip.lot.contextmenu;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.config.AbstractUiBindingConfiguration;
import org.eclipse.nebula.widgets.nattable.ui.binding.UiBindingRegistry;
import org.eclipse.nebula.widgets.nattable.ui.matcher.MouseEventMatcher;
import org.eclipse.nebula.widgets.nattable.ui.menu.PopupMenuAction;
import org.eclipse.nebula.widgets.nattable.ui.menu.PopupMenuBuilder;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Menu;

public class LotContextMenuConfiguration extends AbstractUiBindingConfiguration {

	private final Menu actionMenu;
	
	public LotContextMenuConfiguration(NatTable natTable, Menu actionMenu) {
		this.actionMenu =
                new PopupMenuBuilder(natTable, actionMenu)
                        .build();
	 }

	@Override
	public void configureUiBindings(UiBindingRegistry uiBindingRegistry) {
        uiBindingRegistry.registerMouseDownBinding(
                new MouseEventMatcher(SWT.NONE, null, 3),
                new PopupMenuAction(this.actionMenu));
	}

}
