package com.glory.mes.wip.lot.carrier.glc.merge;

import java.util.List;
import java.util.function.Consumer;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;

public class WIPLotSelectDialog extends LotActionDialog{

    private static final Logger logger = Logger.getLogger(WIPLotSelectDialog.class);

    private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String AD_FROM_NAME = "WIPLotSelectDialog";
	
	private static final String FIELD_LOTINFO = "lotInfo";

	protected ListTableManagerField lotListField;
	private Consumer<WIPLotSelectDialog> closeAdaptor;
    
    public WIPLotSelectDialog(String authority, IEventBroker eventBroker, List<Lot> lots){
		super(AD_FROM_NAME, authority, eventBroker);
		this.setLotList(lots);;
        this.setBlockOnOpen(true);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		lotListField = form.getFieldByControlId(FIELD_LOTINFO,
				ListTableManagerField.class);
        if (CollectionUtils.isNotEmpty(getLotList())) {
            lotListField.getListTableManager().setInput(getLotList());
		}
	}
	
	@Override
	protected void okPressed() {	
		try {
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public Lot getSelectLot() {
		return (Lot) lotListField.getListTableManager().getSelectedObject();
	}


	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<WIPLotSelectDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<WIPLotSelectDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}

	@Override
	public void initLot() {
		
	}

	@Override
	public boolean isSupportMulitLot() {
        return true;
	}
	
}
