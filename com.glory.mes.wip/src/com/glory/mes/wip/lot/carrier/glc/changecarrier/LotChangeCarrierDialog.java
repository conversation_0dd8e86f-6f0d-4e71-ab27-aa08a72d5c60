package com.glory.mes.wip.lot.carrier.glc.changecarrier;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.AuthoritySquareButton;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialogByEditor;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class LotChangeCarrierDialog extends GlcBaseDialogByEditor {

	private static int DIALOG_WIDTH = 200;
	private static int DIALOG_HEIGHT = 300;

    public LotChangeCarrierDialog(String authority, IEventBroker eventBroker){
        super(new LotChangeCarrierManagerEditor(), "LotChangeCarrierManager", "Wip.ChangeCarrier", eventBroker);
    }

    @Override
    protected Control buildView(Composite parent) {
        try {
            editor.postConstruct(parent);
            editor.subscribeAndExecute(editor.getEventBroker(), editor.getForm().getFullTopic(GlcEvent.EVENT_CLOSE_DIALOG), this::closeDialog);

            AuthoritySquareButton itemChange = (AuthoritySquareButton) editor.getForm().getButtonByControl(null, LotChangeCarrierManagerEditor.BUTTON_NAME);
            itemChange.setVisible(false);
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
        return parent;
    }

    @Override
    protected void okPressed() {
        LotChangeCarrierManagerEditor changeCarrierManagerEditor = (LotChangeCarrierManagerEditor) editor;
        changeCarrierManagerEditor.changeCarrierAdapter(null);

        super.okPressed();
    }

    @Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x), Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT), shellSize.y));
    }
}
