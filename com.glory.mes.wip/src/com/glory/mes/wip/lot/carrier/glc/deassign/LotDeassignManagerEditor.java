package com.glory.mes.wip.lot.carrier.glc.deassign;


import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.widgets.AuthoritySquareButton;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class LotDeassignManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.deassign.LotDeassignManagerEditor";

	public static final String FIELD_DEASSIGNINFO = "deassignInfo";

	public static final String BUTTON_NAME = "lotDeassign";

	protected CustomField deassignInfoField;
	protected CarrierLotCustomComposite carrierDeassignCustomComposite;
	protected AuthoritySquareButton itemDeassign;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		deassignInfoField = form.getFieldByControlId(FIELD_DEASSIGNINFO, CustomField.class);
        
		carrierDeassignCustomComposite = (CarrierLotCustomComposite) deassignInfoField.getCustomComposite();
        
		itemDeassign = (AuthoritySquareButton) form.getButtonByControl(null, BUTTON_NAME);
        
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME), this::deassignAdapter);
	}


	protected void deassignAdapter(Object object) {
		try {
			// ����ؾ�
			Object selectedObject = carrierDeassignCustomComposite.getLotTableManager().getSelectedObject();
			if (selectedObject != null) {
				Lot lot = (Lot) selectedObject;
				if (LotStateMachine.STATE_SORT.equals(lot.getState())) {
					UI.showInfo(Message.formatString("wip.deassign_state_not_allow" + "#" + lot.getLotId()));
					return;
				} else if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
					UI.showInfo(Message.formatString("wip.deassign_state_run_not_allow" + "#" + lot.getLotId()));
					return;
				}

				DurableManager durableManager = Framework.getService(DurableManager.class);
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), lot.getDurable());
				if (carrier != null) {
					CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
					carrierLotManager.deassignCarrierLot(carrier, lot, false, true, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.deassign_success"));
				} else {
					UI.showError(Message.getString("mm.durable_is_not_found"));
					return;
				}
			} else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			}
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void refresh() {
		String carrierId = carrierDeassignCustomComposite.getTxtCarrierId().getText();
        if (StringUtils.isNotEmpty(carrierId)) {
			carrierDeassignCustomComposite.getLotsByCarrierId(carrierId);
		} else {
			carrierDeassignCustomComposite.getLotsByCarrierId(null);
		}
	}
}