package com.glory.mes.wip.lot.carrier.glc.split;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.AuthoritySquareButton;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;

import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialogByEditor;

public class CarrierLotSplitDialog extends GlcBaseDialogByEditor {
	
	private static int DIALOG_WIDTH = 200;
	private static int DIALOG_HEIGHT = 300;
	private LotSortingSplitContext context;

	public CarrierLotSplitDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(new CarrierLotSplitManagerEditor(), adFormName, authority, eventBroker);
	}
	
	@Override
	protected Control buildView(Composite parent) {
		super.buildView(parent);
		
		AuthoritySquareButton itemSplit = (AuthoritySquareButton) editor.getForm().getButtonByControl(null, CarrierLotSplitManagerEditor.BUTTON_SPLIT);
        itemSplit.setVisible(false);
		return parent;
	}

    @Override
    protected void okPressed() {
    	CarrierLotSplitManagerEditor carrierLotSplitManagerEditor = (CarrierLotSplitManagerEditor) editor;
    	context = carrierLotSplitManagerEditor.splitAdapter(null);
    	if (context != null) {
    		super.okPressed();
    	}
    }

    @Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x), Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT), shellSize.y));
    }

	public LotSortingSplitContext getContext() {
		return context;
	}
    
}
