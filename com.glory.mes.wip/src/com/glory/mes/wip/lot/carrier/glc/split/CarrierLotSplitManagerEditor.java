package com.glory.mes.wip.lot.carrier.glc.split;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.AuthoritySquareButton;
import org.eclipse.swt.widgets.SquareButton;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.exception.MMExceptionBundle;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentComposite.DiffType;
import com.glory.mes.wip.custom.CarrierAssignCustomComposite;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.custom.ComponentCustomComposite;
import com.glory.mes.wip.custom.SortingCustomComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.sorting.SortModel;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class CarrierLotSplitManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitManagerEditor";

	public static final String FIELD_ASSIGNINFO = "assignInfo";
	public static final String FIELD_CARRIERLOTSPLITINFO = "carrierLotSplitInfo";
	public static final String FIELD_CARRIERLOTINFO = "carrierLotInfo";
	public static final String FIELD_FUTUREMERGESTEP = "futureMergeStep";

	public static final String BUTTON_SPLIT = "split";
	public static final String BUTTON_SELECT = "select";
	
	public static final String AUTHORITY_NAME = "Wip.SortingSplit";
	public static final String ADFORM_NAME = "CarrierLotSplitManager";
	public static final String IS_SORT = "isSort";
	public static final String TARGET_TAB_SIZE = "targetTabSize";
	public static final String IS_SHOW_MERGE_STEP = "isShowMergeStep";
	public static final String TARGET_COMPONENT_LIST = "targetComponentList";
	public static final String IS_AUTO_TRANSFER = "isAutoTransfer";

	protected GlcFormField assignInfoField;
	protected CustomField carrierLotSplitInfoField;
	protected CustomField carrierLotInfoField;
	protected TextField futureMergeStepField;
	protected CarrierAssignCustomComposite carrierAssignCustomComposite;
	protected ComponentAssignCustomComposite componentAssignCustomComposite;
	protected AuthoritySquareButton itemSplit;
	
	private Carrier sourceCarrier;
	private List<Carrier> targetCarriers;
	private Lot sourceLot;
	private boolean ignoreStandardMethod = false;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		assignInfoField = form.getFieldByControlId(FIELD_ASSIGNINFO, GlcFormField.class);
		carrierLotInfoField = assignInfoField.getFieldByControlId(FIELD_CARRIERLOTINFO, CustomField.class);
		futureMergeStepField = assignInfoField.getFieldByControlId(FIELD_FUTUREMERGESTEP, TextField.class);
		carrierLotSplitInfoField = form.getFieldByControlId(FIELD_CARRIERLOTSPLITINFO, CustomField.class);
		carrierAssignCustomComposite = (CarrierAssignCustomComposite)carrierLotInfoField.getCustomComposite();
		componentAssignCustomComposite = (ComponentAssignCustomComposite)carrierLotSplitInfoField.getCustomComposite();
		itemSplit = (AuthoritySquareButton) form.getButtonByControl(null, BUTTON_SPLIT);
		itemSplit.setEnabled(false);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SPLIT), this::splitAdapter);
		subscribeAndExecute(eventBroker, futureMergeStepField.getFullTopic(BUTTON_SELECT), this::selectAdapter);
		
		subscribeAndExecute(eventBroker, carrierLotInfoField.getFullTopic(CarrierAssignCustomComposite.ID_CARRIER_LOT + 
				GlcEvent.NAMESPACE_SEPERATOR + "lotId"  + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::lotEnterPressedAdapter);
		subscribeAndExecute(eventBroker, carrierLotInfoField.getFullTopic(CarrierAssignCustomComposite.ID_CARRIER_LOT + 
				GlcEvent.NAMESPACE_SEPERATOR + "carrierId"  + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::carrierEnterPressedAdapter);
	
		//�����ڷֵĲ���ҪĿ���ؾ߿�����
		RadioField radioField =  componentAssignCustomComposite.getSortingCustomComposite().getRadio();
		if(radioField != null) {
			radioField.addValueChangeListener( new IValueChangeListener() {
				@Override
				public void valueChanged(Object obj, Object obj1) {
					if (SortingCustomComposite.ACTIONTYPE_INTERNAL.equals(obj1)) {
						componentAssignCustomComposite.btnGo.setEnabled(false);
						for (ComponentCustomComposite targetComponentComposite : componentAssignCustomComposite.getMultiTargetComponentComposite()) {
							targetComponentComposite.txtCarrierId.setEnabled(false);
							targetComponentComposite.txtCarrierId.setText("");
						}
					} else {
						componentAssignCustomComposite.btnGo.setEnabled(true);
						for (ComponentCustomComposite targetComponentComposite : componentAssignCustomComposite.getMultiTargetComponentComposite()) {
							targetComponentComposite.txtCarrierId.setEnabled(true);
						}
					}
				}
			});
		}
	}

	protected LotSortingSplitContext splitAdapter(Object object) {
		try {
			componentAssignCustomComposite.getSortingCustomComposite().getForm().saveToObject();
			if (validate()) {
				// Sort��Ϣ
				SortModel sort = (SortModel) componentAssignCustomComposite.getSortingCustomComposite().getForm().getObject();
				Equipment sortEquipment = null;
				if (!StringUtil.isEmpty(sort.getEquipmentId())) {
					RASManager rasManager = Framework.getService(RASManager.class);
					RefTableField reftableField = (RefTableField)componentAssignCustomComposite.getSortingCustomComposite().getForm().getFields().get(SortingCustomComposite.FIELD_EQUIPMENTID);
					sortEquipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), reftableField.getComboControl().getText());
				}
				
				List<List<ComponentUnit>> childUnitsList = Lists.newArrayList();
				List<LotSortingAction> lotActions = Lists.newArrayList();
				int i = 0;
				for (Carrier targetCar : targetCarriers) {
					// ���Ŀ���ؾ�
					List<ComponentUnit> addComponents = componentAssignCustomComposite.getMultiTargetComponentComposite().get(i).getComponentComposite().getComponentsDiff().get(DiffType.ADD);
					for (int j = 0; j < addComponents.size(); j++) {
						addComponents.get(j).setDurable(targetCar.getDurableId());
					}
					childUnitsList.add(addComponents);
					
					// �Ƿ�sort
					LotSortingAction lotSortingAction = new LotSortingAction();
					lotSortingAction.setActionComment(sort.getActionComment());
					StepState futureMergeStepState = (StepState)futureMergeStepField.getData();
					if (sort.isSort()) {
						lotSortingAction.setToDurableId(targetCar.getDurableId());
						lotSortingAction.setSort(true);
						lotSortingAction.setSortingMode(LotSortingJob.SORTING_MODE_SPLIT_BYSORT);
						if (sortEquipment != null) {
							lotSortingAction.setEquipmentId(sortEquipment.getEquipmentId());
							lotSortingAction.setFromPortId(componentAssignCustomComposite.getMultiTargetPort().get(i).getText());
							lotSortingAction.setToPortId(componentAssignCustomComposite.getMultiSourcePort().get(0).getText());
						}
						if (futureMergeStepState != null) {
							lotSortingAction.setSortingMode(LotSortingJob.SORTING_MODE_SPLIT_BYLOGIC);
						}
					} else {
						// Internal������������ҪĿ��,����ͨ�����߼�
						if(!SortingCustomComposite.ACTIONTYPE_INTERNAL.equals(sort.getActionType())) {
							lotSortingAction.setToDurableId(targetCar.getDurableId());
						}
					}
					if (futureMergeStepState != null) {
						if (StringUtil.isEmpty(futureMergeStepState.getPath())) {
							futureMergeStepState.setPath(sourceLot.getProcedureName() + "/" + futureMergeStepState.getName() + "/");
						}
						lotSortingAction.setFutureStepState(futureMergeStepState);
					}
					Map<String, String> toPositionMap = addComponents.stream()
							.collect(Collectors.toMap(ComponentUnit::getComponentId, ComponentUnit::getPosition));
					lotSortingAction.setToPositionMap(toPositionMap);
					lotActions.add(lotSortingAction);
					i++;
				}
					
				if ((Equipment.COMMUNICATION_STATE_OFFLINE.equals(sort.getCommunicationState()) || StringUtil.isEmpty(sort.getCommunicationState())) && sort.isSort()) {
					if (UI.showConfirm(Message.getString(WipExceptionBundle.bundle.SorterJobAutoComplete()))) {
						lotActions.forEach(lotAction -> {((LotSortingAction)lotAction).setAutoComplete(true);});
					}
				}
				
				LotSortingSplitContext splitContext = new LotSortingSplitContext();
				LotManager lotManager = Framework.getService(LotManager.class);
				if (!ignoreStandardMethod) {
					if (sort.isSort()) {
						sourceLot.setOperator1(Env.getUserName());
						sourceLot = lotManager.splitLotComponentList(sourceLot, childUnitsList, true, null, (List<LotAction>)(List)lotActions, Env.getSessionContext());
						if (lotActions.get(0).isAutoComplete()) {
		        			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
						} else {
							UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()) + Message.getString(WipExceptionBundle.bundle.SorterJobTaskWarning()));
						}
						componentAssignCustomComposite.clear();
					} else {
						if (itemSplit.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
							Env.setUserName((String) itemSplit.getData(LotAction.ACTION_TYPE_OPERATOR));
						}
						sourceLot.setOperator1(Env.getUserName());
						sourceLot = lotManager.splitLotComponentList(sourceLot, childUnitsList, true, null, (List<LotAction>)(List)lotActions, Env.getSessionContext());
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
						refresh();
					}
				} else {
					splitContext.setIgnoreStandardMethod(true);
					splitContext.setChildUnitList(childUnitsList);
					splitContext.setSourceLot(sourceLot);
					splitContext.setLotActions((List<LotAction>)(List)lotActions);
				}
				return splitContext;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	protected boolean validate() {
		try {
			SortModel sort = (SortModel) componentAssignCustomComposite.getSortingCustomComposite().getForm().getObject();
			if(sort.getActionType() == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.WipSplitTypeIsNull()));
				return false;
			}
			
			String carrierId = componentAssignCustomComposite.getSourceTabFolder().getItem(0).getText();
			DurableManager durableManager = Framework.getService(DurableManager.class);
			sourceCarrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, false);
			
			//Internal�������ؾ��ڲ𣩣�Ŀ���ؾߺ�Դ�ؾ߲���
			targetCarriers = Lists.newArrayList();
			List<ComponentCustomComposite> targetComponentCustomCompositeList = componentAssignCustomComposite.getMultiTargetComponentComposite();
			if(SortingCustomComposite.ACTIONTYPE_INTERNAL.equals(sort.getActionType())) {
				targetCarriers.clear();
				//�ڲ�֧�ֶ������ȫĿ���ؾ�list
				targetComponentCustomCompositeList.forEach(t -> {
					Carrier cloneCarrier = sourceCarrier;
					targetCarriers.add(cloneCarrier);
				});
			} else {
				for (ComponentCustomComposite targetComponentCustomComposite : targetComponentCustomCompositeList) {
					Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), targetComponentCustomComposite.txtCarrierId.getText(), true);
					targetCarriers.add(targetCarrier);
				}
			}
			
			//Ŀ���ؾ߻���Դ�ؾ߲���Ϊ��
			if (CollectionUtils.isEmpty(targetCarriers) || sourceCarrier == null) {
				UI.showError(Message.getString(MMExceptionBundle.bundle.MMDurableNotFound()));
				return false;
			}
			
			//Ŀ���ؾ߲����ظ�
			List<String> duplicateCar = targetCarriers.stream().map(Carrier :: getDurableId).collect(Collectors
					.collectingAndThen(Collectors.groupingBy(Function.identity(), Collectors.counting()),map->{
						map.values().removeIf(size -> size == 1);
						return Lists.newArrayList(map.keySet());
			}));
			
			if (CollectionUtils.isNotEmpty(duplicateCar) && !SortingCustomComposite.ACTIONTYPE_INTERNAL.equals(sort.getActionType())) {
				String targetCar = duplicateCar.stream().collect(Collectors.joining(";"));
				UI.showError(Message.getString(WipExceptionBundle.bundle.WipTargetDurableRepeat()) + targetCar);
				return false;
			}
			
			if (!(LotStateMachine.STATE_WAIT.equals(sourceLot.getState()) 
					|| LotStateMachine.STATE_FIN.equals(sourceLot.getState())
					|| LotStateMachine.STATE_SCRAP.equals(sourceLot.getState())
					|| LotStateMachine.STATE_TRACKOUT.equals(sourceLot.getState()))) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.LotStatusNotAllow));
				return false;
			}
			
			if (sort.isSort()) {
				List<String> targetDurableIds = targetCarriers.stream().map(Carrier :: getDurableId).collect(Collectors.toList());
				if(targetDurableIds.contains(sourceCarrier.getDurableId())) {
					UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipSortIsNotSupported()));
					return false;
				}
			}
			
			int i = 0;
			Map<String, String> positionMap = new HashMap<>();
			for (Carrier targetCar : targetCarriers) {
				List<ComponentUnit> addComponents = componentAssignCustomComposite.getMultiTargetComponentComposite().get(i).getComponentComposite().getComponentsDiff().get(DiffType.ADD);
				if (CollectionUtils.isEmpty(addComponents)) {
					UI.showError(Message.getString(WipExceptionBundle.bundle.WipRequireAssignComponent()));
					return false;
				}
				
				if (!Objects.equal(sourceCarrier.getMainMatType(), targetCar.getMainMatType())) {
					UI.showError(MMExceptionBundle.bundle.MMCarrierMainMatTypeDifferent());
					return false;
				}
				
				//ͬһ��Ŀ���ؾߵ�ͬһ��λ�ò�������waferռ��
				// �����������Լ��������ͬʱ��   
				Map<Long, Integer> map = new HashMap<Long, Integer>(); 
				Long parentRrn = addComponents.get(0).getParentUnitRrn();
				for (ComponentUnit componentUnit : addComponents) {
					String car = targetCar.getDurableId() + ":" + componentUnit.getPosition();						
					if (positionMap.containsKey(car)) {
						UI.showError(String.format(Message.getString(WipExceptionBundle.bundle.WipUnscrapPositionRepeat()), positionMap.get(car), componentUnit.getComponentId()));
						return false;
					} else {
						positionMap.put(car, componentUnit.getComponentId());
					}
					map.put(componentUnit.getParentUnitRrn(), 1);
					
					// �ж��������Ƿ�Ϊͬһ��������
					if (parentRrn.longValue() != componentUnit.getParentUnitRrn().longValue()) {
						UI.showError(Message.getString(WipExceptionBundle.bundle.WipSelectCompFromMultiLot()));
						return false;
					}
				}
				
				if (map.keySet().size() > 1) {
					UI.showInfo(WipExceptionBundle.bundle.WipSelectCompFromMultiLot());
					return false;
				}
				
				for (Long objectRrn : map.keySet()) {
					if (!sourceLot.getObjectRrn().equals(objectRrn)) {
						UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipSourceComponentNotMatch()));
						return false;
					}
				}
				
				if (sort.isSort()) {
					String targetPortId = componentAssignCustomComposite.getMultiTargetPort().get(i).getText();
					String sourcePortId = componentAssignCustomComposite.getMultiSourcePort().get(0).getText();
					// port��Ҫ������Ҫ����û��
					if((!StringUtil.isEmpty(sourcePortId) && StringUtil.isEmpty(targetPortId))
							||(StringUtil.isEmpty(sourcePortId) && !StringUtil.isEmpty(targetPortId))) {
						UI.showError(Message.getString(WipExceptionBundle.bundle.SorterPortIncomplete()));
						return false;
					}
					
					// У��port���Ƿ�һ��
					if (!StringUtil.isEmpty(sourcePortId) && !StringUtil.isEmpty(targetPortId)) {
						if ((sourcePortId).equals(targetPortId)) {
							UI.showError(Message.getString(WipExceptionBundle.bundle.SorterPortDiverse()));
							return false;
						}
					}
				}
				i++;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return true;
	}

	private void selectAdapter(Object object) {
		try {
			Lot lot = (Lot) carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().getSelectedObject();
			List<Lot> lots = (List<Lot>) carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().getInput();
			if (lot == null) {
				if (CollectionUtils.isNotEmpty(lots)) {
					lot = lots.get(0);
				}
			}
			
			if (lot != null) {
				SelectFutureMergeStepDialog dialog = new SelectFutureMergeStepDialog("SelectFutureMergeStepDialog", "Wip.SplitLot", eventBroker, lot);			
				if (dialog.open() == Dialog.OK) {
					futureMergeStepField.setText(dialog.getStepState().getStepName());
					futureMergeStepField.setData(dialog.getStepState());
				}
			} else {
				UI.showWarning(Message.getString(WipExceptionBundle.bundle.WipNotSelectLot()));
				return;
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void lotEnterPressedAdapter(Object object) {
		try {
			itemSplit.setEnabled(false);
			List<Lot> lots = (List<Lot>) carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().getInput();
			if (CollectionUtils.isEmpty(lots) || StringUtil.isEmpty(lots.get(0).getDurable())) {
				carrierAssignCustomComposite.getCarrierLotComposite().getTxtCarrierId().setText("");
				componentAssignCustomComposite.getSourceTabFolder().getItem(0).setText(" Tab0 ");
				componentAssignCustomComposite.getSourceComponentComposite().setCarrier(null);
				if (CollectionUtils.isNotEmpty(lots) && StringUtil.isEmpty(lots.get(0).getDurable())) {
					UI.showError(Message.getString(WipExceptionBundle.bundle.WipLotNotAssignedCarrier()));
					return;
				}
			} else {
				Event event = (Event) object;
				String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
				componentAssignCustomComposite.getSourceComponentComposite().setLot(lotId);
				sourceLot = componentAssignCustomComposite.getSourceComponentComposite().getComponentsByLot(lotId, true);
				
				componentAssignCustomComposite.getSourceComponentComposite().setCarrier(sourceLot.getDurable());
				String carrierId = componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId.getText();
				if (!StringUtil.isEmpty(carrierId)) {
					carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().setSelection(new StructuredSelection(new Object[] {sourceLot}));
					carrierAssignCustomComposite.getCarrierLotComposite().getTxtCarrierId().setText(sourceLot.getDurable());
					componentAssignCustomComposite.getSourceTabFolder().getItem(0).setText(sourceLot.getDurable());
					componentAssignCustomComposite.getSourceComponentComposite().getComponentsByCarrier(sourceLot.getDurable(), true);
					if (Lot.UNIT_TYPE_COMPONENT.equals(sourceLot.getSubUnitType())) {
						componentAssignCustomComposite.getSortingCustomComposite().setValue(new SortModel());
						componentAssignCustomComposite.getSortingCustomComposite().refresh();
						itemSplit.setEnabled(true);
					}
				}
			}
			futureMergeStepField.setValue(null);
			futureMergeStepField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void carrierEnterPressedAdapter(Object object) {
		try {
			itemSplit.setEnabled(false);
			List<Lot> lots = (List<Lot>) carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().getInput();
			if (CollectionUtils.isEmpty(lots)) {
				carrierAssignCustomComposite.getCarrierLotComposite().getTxtLotId().setText("");
				componentAssignCustomComposite.getSourceTabFolder().getItem(0).setText("Tab0");
				componentAssignCustomComposite.getSourceComponentComposite().setCarrier(null);
			} else {
				Event event = (Event) object;
				String carrierId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
				componentAssignCustomComposite.getSourceComponentComposite().setCarrier(carrierId);
				carrierId = componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId.getText();
				if (!StringUtil.isEmpty(carrierId)) {
					sourceLot = lots.get(0);
					carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().setSelection(new StructuredSelection(new Object[] {sourceLot}));
					carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().setCheckedObject(sourceLot);
					carrierAssignCustomComposite.getCarrierLotComposite().getTxtLotId().setText(sourceLot.getLotId());
					componentAssignCustomComposite.getSourceTabFolder().getItem(0).setText(sourceLot.getDurable());
					componentAssignCustomComposite.getSourceComponentComposite().getComponentsByCarrier(sourceLot.getDurable(), true);
					if (Lot.UNIT_TYPE_COMPONENT.equals(sourceLot.getSubUnitType())) {
						componentAssignCustomComposite.getSortingCustomComposite().setValue(new SortModel());
						componentAssignCustomComposite.getSortingCustomComposite().refresh();
						itemSplit.setEnabled(true);
					}
				}
			}
			futureMergeStepField.setValue(null);
			futureMergeStepField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh() {
		// ˢ���ұ߷���ҳ����Ϣ
		componentAssignCustomComposite.getSourceComponentComposite().setLot(null);
		componentAssignCustomComposite.getSourceComponentComposite().getTableManager().getInput().clear();
		List<ComponentCustomComposite> targetComponentCustomCompositeList = componentAssignCustomComposite.getMultiTargetComponentComposite();
		for (ComponentCustomComposite targetComponentCustomComposite : targetComponentCustomCompositeList) {
			targetComponentCustomComposite.setCarrier(targetComponentCustomComposite.txtCarrierId.getText());
		}
		componentAssignCustomComposite.getSortingCustomComposite().setValue(new SortModel());
		componentAssignCustomComposite.getSortingCustomComposite().refresh();
		
		// ˢ�����������Ϣ
		componentAssignCustomComposite.getSourceComponentComposite().setCarrier(sourceLot.getDurable());
		carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().setSelection(new StructuredSelection(new Object[] {sourceLot}));
		carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().setCheckedObject(sourceLot);
		carrierAssignCustomComposite.getCarrierLotComposite().getTxtLotId().setText(sourceLot.getLotId());
		itemSplit.setEnabled(false);
	}
	
	@Override
    public void initializeDatas(Map<String, Object> initDatas) {
        try {
            // ����г�ʼ����
            String lotId = DBUtil.toString(initDatas.get(MesGlcEvent.PROPERTY_LOT_ID));

            CarrierLotCustomComposite carrierLotComposite = carrierAssignCustomComposite.getCarrierLotComposite();
            carrierLotComposite.getTxtCarrierId().setEnabled(false);
            carrierLotComposite.getTxtLotId().setEnabled(false);
            if (!StringUtil.isEmpty(lotId)) {
                Lot lot = LotProviderEntry.getLot(lotId);
                if (lot != null) {
                    carrierLotComposite.getTxtLotId().setText(lotId);
                    carrierLotComposite.getLotTableManager().setInput(Lists.newArrayList(lot));
                    carrierLotComposite.getLotTableManager().refresh();
                    carrierLotComposite.getLotTableManager().setCheckedObject(lot);
                    carrierLotComposite.getLotTableManager().setSelection(new StructuredSelection(new Object[] {lot}));
                    Map<String, String> propertyData = Maps.newHashMap();
					propertyData.put(GlcEvent.PROPERTY_DATA, lotId);
					Event event = new Event(ADFORM_NAME, propertyData);
                    lotEnterPressedAdapter(event);
                }
            }

            String carrierId = componentAssignCustomComposite.getSourceComponentComposite().txtCarrierId.getText();
			if (!StringUtil.isEmpty(carrierId)) {
				componentAssignCustomComposite.btnTargetAdd.setEnabled(false);
				componentAssignCustomComposite.btnTargetDelete.setEnabled(false);
			}
				
			String targetTabSize = DBUtil.toString(initDatas.get(TARGET_TAB_SIZE));
			if (!StringUtil.isEmpty(targetTabSize) && Integer.valueOf(targetTabSize) > 1) {
				for (int i = 0; i < Integer.valueOf(targetTabSize) - 1; i++) {
					componentAssignCustomComposite.addTabAdapter(false);
				}
			}
			
			List<List<ComponentUnit>> componentUnitLists = (List<List<ComponentUnit>>) initDatas.get(TARGET_COMPONENT_LIST);
			if (CollectionUtils.isNotEmpty(componentUnitLists)) {
				boolean isAutoTransfer = true;
				if (initDatas.containsKey(IS_AUTO_TRANSFER)) {
					isAutoTransfer = DBUtil.toBoolean(initDatas.get(IS_AUTO_TRANSFER));
				}
				if (isAutoTransfer) {
					List<String> targetCarrierIds = (List<String>) initDatas.get(MesGlcEvent.PROPERTY_TARGET_CARRIER_ID);
					if (CollectionUtils.isNotEmpty(targetCarrierIds)) {
						for (int i = 0; i < targetCarrierIds.size(); i++) {
							componentAssignCustomComposite.getTargetTabFolder().getItem(i).setText(targetCarrierIds.get(i));
				            componentAssignCustomComposite.getTargetComponentComposite().setCarrier(targetCarrierIds.get(i));
						}	
					}	
					ListTableManager tableManager = componentAssignCustomComposite.getSourceComponentComposite().getTableManager();
					List<ComponentUnit> allUnits = (List<ComponentUnit>) tableManager.getInput();
					List<String> componentUnitIdLists = Lists.newArrayList();
					for (int i = 0; i < componentUnitLists.size(); i++) {
						List<String> componentUnitIds = componentUnitLists.get(i).stream().map(ComponentUnit::getComponentId).collect(Collectors.toList());
						componentUnitIdLists.addAll(componentUnitIds);
						allUnits.forEach(x ->{
							if (componentUnitIds.contains(x.getComponentId())) {
								tableManager.setCheckedObject(x);
							}
						});
						if (i >= 1) {
							componentAssignCustomComposite.getTargetTabFolder().setSelection(i);
						}
					}
					
					List<Object> disableUnits = Lists.newArrayList();
					allUnits.forEach(x ->{
						if (!componentUnitIdLists.contains(x.getComponentId())) {
							disableUnits.add(x);
						}
					});
					((CheckBoxTableViewerManager)tableManager.getTableManager()).setDisableObject(disableUnits);
				} else {
					ListTableManager tableManager = componentAssignCustomComposite.getSourceComponentComposite().getTableManager();
					for (List<ComponentUnit> componentUnits : componentUnitLists) {
						List<String> componentUnitIds = componentUnits.stream().map(ComponentUnit::getComponentId).collect(Collectors.toList());
						for (Object obj : tableManager.getInput()) {
							ComponentUnit unit = (ComponentUnit) obj;
							if (componentUnitIds.contains(unit.getComponentId())) {
								tableManager.setCheckedObject(obj);
							}
						}
					}
				}
			}
            
            boolean isSort = false;
			if (initDatas.containsKey(IS_SORT)) {
				isSort = DBUtil.toBoolean(initDatas.get(IS_SORT));
			}
            SortModel sort = new SortModel();
			sort.setSort(isSort);
			sort.setActionType(isSort ? SortingCustomComposite.ACTIONTYPE_EXTERNAL: SortingCustomComposite.ACTIONTYPE_INTERNAL);
			EntityForm sorterForm = componentAssignCustomComposite.getSortingCustomComposite().getForm();
			sorterForm.setObject(sort);
			sorterForm.loadFromObject();
            
            boolean isShowMergeStep = DBUtil.toBoolean(initDatas.get(IS_SHOW_MERGE_STEP));
            if (!isShowMergeStep) {
            	futureMergeStepField.getLabelControl().setVisible(false);
            	futureMergeStepField.getTextControl().setVisible(false);
            	((SquareButton) futureMergeStepField.getButtonByControl(BUTTON_SELECT)).setVisible(false);
			} else {
				String futureMergeProcedureName =  DBUtil.toString(initDatas.get(MesGlcEvent.PROPERTY_PROCEDURE_NAME));
				String futureMergeStep = DBUtil.toString(initDatas.get(MesGlcEvent.PROPERTY_STEP_NAME));
				if (!StringUtil.isEmpty(futureMergeStep)) {
					futureMergeStepField.setText(futureMergeStep);
					PrdManager prdManager = Framework.getService(PrdManager.class);
					Procedure superProcedure = new Procedure();
					superProcedure.setOrgRrn(Env.getOrgRrn());
					superProcedure.setName(futureMergeProcedureName);
					superProcedure = (Procedure) prdManager.getSimpleProcessDefinition(superProcedure);
					futureMergeStepField.setData(prdManager.getNodeByName(Env.getOrgRrn(), superProcedure, futureMergeStep));
				}
			}
            
            if (carrierLotComposite.getTxtLotId() != null) {
            	carrierLotComposite.getTxtLotId().setEnabled(false);
			}
            carrierLotComposite.getTxtCarrierId().setEnabled(false);
            form.getFieldByControlId(FIELD_CARRIERLOTSPLITINFO, CustomField.class);
            
            BooleanField booleanField =  (BooleanField) sorterForm.getFields().get(SortingCustomComposite.FIELD_SORT);
            if (booleanField != null) {
				booleanField.setEnabled(false);
			}
           	RadioField radioField =  (RadioField) sorterForm.getFields().get(SortingCustomComposite.FIELD_ACTIONTYPE);
			if (radioField != null) {
				radioField.setEnabled(false);
			}
			carrierLotComposite.getLotTableManager().getNatTable().setEnabled(false);
			ignoreStandardMethod = true;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

}