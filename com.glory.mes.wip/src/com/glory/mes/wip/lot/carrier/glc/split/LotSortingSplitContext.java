package com.glory.mes.wip.lot.carrier.glc.split;

import java.util.List;
import java.util.Map;

import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class LotSortingSplitContext {

	private boolean ignoreStandardMethod = false;
	
	private Map<String, Object> datas;
	
	private Lot sourceLot;
	
	private List<List<ComponentUnit>> childUnitList;
	
	private List<Lot> splitOutLots;
	
	private List<LotAction> lotActions;
	
	private Carrier targetCarrier;
	
	public boolean isIgnoreStandardMethod() {
		return ignoreStandardMethod;
	}

	public void setIgnoreStandardMethod(boolean ignoreStandardMethod) {
		this.ignoreStandardMethod = ignoreStandardMethod;
	}

	public Map<String, Object> getDatas() {
		return datas;
	}

	public void setDatas(Map<String, Object> datas) {
		this.datas = datas;
	}

	public Lot getSourceLot() {
		return sourceLot;
	}

	public void setSourceLot(Lot sourceLot) {
		this.sourceLot = sourceLot;
	}

	public List<List<ComponentUnit>> getChildUnitList() {
		return childUnitList;
	}

	public void setChildUnitList(List<List<ComponentUnit>> childUnitList) {
		this.childUnitList = childUnitList;
	}

	public List<Lot> getSplitOutLots() {
		return splitOutLots;
	}

	public void setSplitOutLots(List<Lot> splitOutLots) {
		this.splitOutLots = splitOutLots;
	}

	public List<LotAction> getLotActions() {
		return lotActions;
	}

	public void setLotActions(List<LotAction> lotActions) {
		this.lotActions = lotActions;
	}

	public Carrier getTargetCarrier() {
		return targetCarrier;
	}

	public void setTargetCarrier(Carrier targetCarrier) {
		this.targetCarrier = targetCarrier;
	}

}
