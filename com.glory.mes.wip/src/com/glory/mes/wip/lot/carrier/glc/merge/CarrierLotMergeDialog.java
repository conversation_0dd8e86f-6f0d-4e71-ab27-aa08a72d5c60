package com.glory.mes.wip.lot.carrier.glc.merge;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.AuthoritySquareButton;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialogByEditor;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class CarrierLotMergeDialog extends GlcBaseDialogByEditor {

	private static int DIALOG_WIDTH = 200;
	private static int DIALOG_HEIGHT = 300;

    public CarrierLotMergeDialog(String authority, IEventBroker eventBroker){
        super(new CarrierLotMergeManagerEditor(), "CarrierLotMergeManager", "Wip.LotSortingMerge", eventBroker);
    }

    @Override
    protected Control buildView(Composite parent) {
        try {
            editor.postConstruct(parent);
            editor.subscribeAndExecute(editor.getEventBroker(), editor.getForm().getFullTopic(GlcEvent.EVENT_CLOSE_DIALOG), this::closeDialog);

            AuthoritySquareButton itemMerge = (AuthoritySquareButton) editor.getForm().getButtonByControl(null, CarrierLotMergeManagerEditor.BUTTON_MERGE);
            SquareButton itemRefresh = (SquareButton) editor.getForm().getButtonByControl(null, CarrierLotMergeManagerEditor.BUTTON_REFRESH);
            itemMerge.setVisible(false);
            itemRefresh.setVisible(false);
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
        return parent;
    }

    @Override
    protected void okPressed() {
        CarrierLotMergeManagerEditor carrierLotMergeManagerEditor = (CarrierLotMergeManagerEditor) editor;
        carrierLotMergeManagerEditor.mergeAdapter(null);

        super.okPressed();
    }

    @Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x), Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT), shellSize.y));
    }
}
