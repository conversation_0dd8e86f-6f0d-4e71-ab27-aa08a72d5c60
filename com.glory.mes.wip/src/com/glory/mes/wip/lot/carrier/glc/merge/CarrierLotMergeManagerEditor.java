package com.glory.mes.wip.lot.carrier.glc.merge;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.custom.CTabItem;
import org.eclipse.swt.widgets.AuthoritySquareButton;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.SquareButton;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentComposite.DiffType;
import com.glory.mes.wip.custom.CarrierAssignCustomComposite;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.custom.ComponentCustomComposite;
import com.glory.mes.wip.custom.SortingCustomComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.sorting.SortModel;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.collect.Lists;

public class CarrierLotMergeManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.merge.CarrierLotMergeManagerEditor";

	public static final String FIELD_CARRIERLOTMERGEINFO = "carrierLotMergeInfo";
	public static final String FIELD_CARRIERLOTINFO = "carrierLotInfo";
	public static final String FIELD_FUTUREMERGESTEP = "futureMergeStep";
    private static final String FORM_MERGEFORM = "WIPLotActionMergeDialog";


	public static final String BUTTON_MERGE = "merge";
	public static final String BUTTON_REFRESH = "refresh";

	protected GlcFormField assignInfoField;
	protected CustomField carrierLotMergeInfoField;
	protected CustomField carrierLotInfoField;
	protected TextField futureMergeStepField;
	protected CarrierAssignCustomComposite carrierAssignCustomComposite;
	protected ComponentAssignCustomComposite componentAssignCustomComposite;
	protected AuthoritySquareButton itemMerge;
    protected SquareButton itemRefresh;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		carrierLotInfoField = form.getFieldByControlId(FIELD_CARRIERLOTINFO, CustomField.class);
		carrierLotMergeInfoField = form.getFieldByControlId(FIELD_CARRIERLOTMERGEINFO, CustomField.class);
		carrierAssignCustomComposite = (CarrierAssignCustomComposite) carrierLotInfoField.getCustomComposite();
		componentAssignCustomComposite = (ComponentAssignCustomComposite) carrierLotMergeInfoField.getCustomComposite();
		componentAssignCustomComposite.btnSourceAdd.setVisible(false);
		componentAssignCustomComposite.getTargetComponentComposite().txtCarrierId.setEnabled(false);

		itemMerge = (AuthoritySquareButton) form.getButtonByControl(null, BUTTON_MERGE);
        itemRefresh = (SquareButton) form.getButtonByControl(null, BUTTON_REFRESH);
        itemMerge.setEnabled(false);


		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MERGE), this::mergeAdapter);
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);

		carrierLotInfoField.unsubscribeDefaultEvent(CarrierAssignCustomComposite.ID_CARRIER_LOT + GlcEvent.NAMESPACE_SEPERATOR + "lotId"
				+ GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED);

		carrierLotInfoField.unsubscribeDefaultEvent(CarrierAssignCustomComposite.ID_CARRIER_LOT + GlcEvent.NAMESPACE_SEPERATOR + "carrierId"
				+ GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED);

		subscribeAndExecute(eventBroker, carrierLotInfoField.getFullTopic(CarrierAssignCustomComposite.ID_CARRIER_LOT + GlcEvent.NAMESPACE_SEPERATOR + "lotId"
                + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::sourceLotEnterPressedAdapter);

		subscribeAndExecute(eventBroker, carrierLotInfoField.getFullTopic(CarrierAssignCustomComposite.ID_CARRIER_LOT + GlcEvent.NAMESPACE_SEPERATOR
                + "carrierId" + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::sourceCarrierEnterPressedAdapter);

		subscribeAndExecute(eventBroker, carrierLotInfoField.getFullTopic(CarrierAssignCustomComposite.ID_CARRIER_LOT + GlcEvent.NAMESPACE_SEPERATOR
                + "lotTable" + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::sourceLotSelectChangeAdapter);

		subscribeAndExecute(eventBroker, carrierLotInfoField.getFullTopic("carrierId" + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED),
				this::targetCarrierEnterPressedAdapter);
	}

    @Override
    public void initializeDatas(Map<String, Object> initDatas) {
        try {
            // ����г�ʼ����
            String lotId = (String) initDatas.get(MesGlcEvent.PROPERTY_LOT_ID);
            String carrierId = (String) initDatas.get(MesGlcEvent.PROPERTY_CARRIER_ID);
            String targetCarrierId = (String) initDatas.get(MesGlcEvent.PROPERTY_TARGET_CARRIER_ID);

            CarrierLotCustomComposite carrierLotComposite = carrierAssignCustomComposite.getCarrierLotComposite();
            carrierLotComposite.getTxtCarrierId().setEnabled(false);
            carrierLotComposite.getTxtLotId().setEnabled(false);
            if (!StringUtil.isEmpty(carrierId)) {
                try {
                    DurableManager durableManager = Framework.getService(DurableManager.class);
                    Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);

                    if (carrier != null) {
                        CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
                        List<Lot> carrierLots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
                        carrierLotComposite.getTxtCarrierId().setText(carrierId);
                        carrierLotComposite.getLotTableManager().setInput(carrierLots);
                        carrierLotComposite.getLotTableManager().refresh();

                        createSourceComposite(carrierLots);
                    }
                } catch (Exception e) {
                    ExceptionHandlerManager.asyncHandleException(e);
                }
            } else if (!StringUtil.isEmpty(lotId)) {
                Lot lot = LotProviderEntry.getLot(lotId);
                if (lot != null) {
                    carrierLotComposite.getTxtLotId().setText(lotId);
                    carrierLotComposite.getLotTableManager().setInput(Lists.newArrayList(lot));
                    carrierLotComposite.getLotTableManager().refresh();
                    createSourceComposite(Lists.newArrayList(lot));
                }
            }

            if (!StringUtil.isEmpty(targetCarrierId)) {
                CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
                List<Lot> targetCarrierLots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), targetCarrierId);

                carrierAssignCustomComposite.getTargetLotTableManager().setInput(targetCarrierLots);
                carrierAssignCustomComposite.getTargetLotTableManager().refresh();
                carrierAssignCustomComposite.getTxtCarrierId().setEnabled(false);
                carrierAssignCustomComposite.getTxtCarrierId().setText(targetCarrierId);

                componentAssignCustomComposite.getTargetTabFolder().getItem(0).setText(carrierId);
                componentAssignCustomComposite.getTargetComponentComposite().setCarrier(carrierId);
                carrierAssignCustomComposite.getTxtCarrierId().setEnabled(false);
                itemMerge.setEnabled(true);
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    public void mergeAdapter(Object object) {
		try {
            Object[] objs = getSortingMergeComponentObjects();
            if (!DBUtil.toBoolean(objs[0])) {
                return;
            }
            List<LotAction> lotActions = (List<LotAction>) objs[1];
            List<Lot> childLots = (List<Lot>) objs[2];
            Lot targetLot = (Lot) objs[3];
            LotManager lotManager = Framework.getService(LotManager.class);
            lotManager.mergeLotList(targetLot, childLots, (List<LotSortingAction>) (List) lotActions, Env.getSessionContext());

            LotSortingAction lotSortingAction = (LotSortingAction) lotActions.get(0);
            if (lotSortingAction.isSort() && !lotSortingAction.isAutoComplete()) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed())
                        + Message.getString(WipExceptionBundle.bundle.SorterJobTaskWarning()));
            } else {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
            }
            
            refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

    protected void refreshAdapter(Object object) {
        try {
            refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    protected void sourceCarrierEnterPressedAdapter(Object object) {
		try {
			Event event = (Event) object;
			String carrierId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);

			CarrierLotCustomComposite carrierLotComposite = carrierAssignCustomComposite.getCarrierLotComposite();

			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);

			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> carrierLots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				List<Lot> lots = (List<Lot>) carrierLotComposite.getLotTableManager().getInput();
				lots.removeAll(carrierLots);
				lots = Lists.newArrayList(lots);
				lots.addAll(carrierLots);

				carrierLotComposite.getLotTableManager().setInput(lots);
				carrierLotComposite.getTxtCarrierId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				carrierLotComposite.getLotTableManager().refresh();

				createSourceComposite(carrierLots);

			} else {
				carrierLotComposite.getTxtCarrierId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

    protected void sourceLotEnterPressedAdapter(Object object) {
		try {
			Event event = (Event) object;
			String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);

			CarrierLotCustomComposite carrierLotComposite = carrierAssignCustomComposite.getCarrierLotComposite();

			Lot lot = LotProviderEntry.getLot(lotId);
			if (lot != null) {
				List<Lot> lots = (List<Lot>) carrierLotComposite.getLotTableManager().getInput();
				lots = Lists.newArrayList(lots);
				if (!lots.contains(lot)) {
					lots.add(lot);
				}

				carrierLotComposite.getLotTableManager().setInput(lots);
				carrierLotComposite.getTxtLotId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				carrierLotComposite.getLotTableManager().refresh();

				createSourceComposite(Lists.newArrayList(lot));
			} else {
				carrierLotComposite.getTxtLotId().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

    protected void sourceLotSelectChangeAdapter(Object object) {
		try {
			Event event = (Event) object;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);

            if (lot != null) {
                CTabFolder sourceTabFolder = componentAssignCustomComposite.getSourceTabFolder();
                for (int i = 0; i < sourceTabFolder.getItemCount(); i++) {
                    CTabItem item = sourceTabFolder.getItem(i);
                    if (StringUtils.equals(item.getText(), lot.getLotId())) {
                        sourceTabFolder.setSelection(i);
                        break;
                    }
                }
            }

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void targetCarrierEnterPressedAdapter(Object object) {
		try {
			Event event = (Event) object;
			String carrierId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);

			componentAssignCustomComposite.getTargetTabFolder().getItem(0).setText(carrierId);
			componentAssignCustomComposite.getTargetComponentComposite().setCarrier(carrierId);
			itemMerge.setEnabled(true);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createSourceComposite(List<Lot> lots) {
		for (Lot lot : lots) {
			// �Ѿ���tab�����ظ�����
			CTabItem[] items = componentAssignCustomComposite.getSourceTabFolder().getItems();
			boolean isExist = false;
			for (CTabItem cTabItem : items) {
				if (StringUtils.equals(lot.getLotId(), cTabItem.getText())) {
					isExist = true;
					ComponentCustomComposite componentCustomComposite = (ComponentCustomComposite) cTabItem.getControl().getData();
					componentCustomComposite.setLot(lot.getLotId());
					break;
				}
			}

			if (isExist) {
				continue;
			}

			// �����µ�Source Tabҳ
			List<ComponentCustomComposite> multiSourceComponentComposite = componentAssignCustomComposite.getMultiSourceComponentComposite();
			if (multiSourceComponentComposite.size() > 1 || StringUtils.isNotEmpty(multiSourceComponentComposite.get(0).txtCarrierId.getText())) {
				componentAssignCustomComposite.addTabAdapter(true);
			}
			ComponentCustomComposite componentCustomComposite = componentAssignCustomComposite.getSourceComponentComposite();
			componentAssignCustomComposite.getSourceTabFolder().getItem(componentAssignCustomComposite.getSourceTabFolder().getItemCount() - 1)
					.setText(lot.getLotId());
			componentCustomComposite.setLot(lot.getLotId());

		}
	}

	/**
     * У�鲢��װ�������� ���ض��� Object[] 0���Ƿ�У��ɹ� 1��LotActions 2:childLots 3.targetLot
     */
	private Object[] getSortingMergeComponentObjects() {
        Object[] objs = new Object[4];
		objs[0] = false;
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
            RASManager rasManager = Framework.getService(RASManager.class);

            // ��ȡĿ���ؾߺ�Ŀ������
			String targetCarrierId = componentAssignCustomComposite.getTargetCarrierId();
            if (StringUtils.isEmpty(targetCarrierId)) {
                UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInputError()));
                return objs;
            }

            List<Lot> targetLots = (List<Lot>) carrierAssignCustomComposite.getTargetLotTableManager().getInput();
            Lot selectedTargetLot = (Lot) carrierAssignCustomComposite.getTargetLotTableManager().getSelectedObject();
            
            Lot targetLot = null;
            if (selectedTargetLot != null) {
                targetLot = selectedTargetLot;
            } else if (targetLots.size() == 1) {
                targetLot = targetLots.get(0);
            } else if (CollectionUtils.isNotEmpty(targetLots)) {
                // TargetCarrier����Mulit Lot���������ѡ��Ŀ������
                WIPLotSelectDialog baseDialog = new WIPLotSelectDialog(null, eventBroker, targetLots);
                if (Dialog.OK == baseDialog.open()) {
                    targetLot = baseDialog.getSelectLot();
                }
            }
            
            if (targetLot == null) {
                UI.showError(Message.getString(WipExceptionBundle.bundle.WipMergeLotIsNull()));
                return objs;
            }
            targetLot.setOperator1(Env.getUserName());
            
            // Sort����
            componentAssignCustomComposite.getSortingCustomComposite().getForm().saveToObject();
            SortModel sort = (SortModel) componentAssignCustomComposite.getSortingCustomComposite().getForm().getObject();
            if (sort.getActionType() == null) {
                UI.showError(Message.getString(WipExceptionBundle.bundle.WipMergeTypeIsNull()));
                return objs;
            }

            List<Lot> childLots = Lists.newArrayList();
            List<LotAction> lotActions = Lists.newArrayList();
            if (SortingCustomComposite.ACTIONTYPE_INTERNAL.equals(sort.getActionType())) {
                // �ڷ�û��Componentλ�ñ仯��ͨ����ѡ��ѡ�в�����ָ��������ĸ��
                List<Object> sourceLots = (List<Object>) carrierAssignCustomComposite.getCarrierLotComposite().getLotTableManager().getCheckedObject();
                if (CollectionUtils.isEmpty(sourceLots)) {
                    UI.showError(Message.getString(ExceptionBundle.bundle.CommonCheckedObject()));
                    return objs;
                }

                for (Object sourceLot : sourceLots) {
                    Lot childLot = (Lot) sourceLot;
                    // Դ�ؾߺ�Ŀ���ؾ���ͬ
                    if (!StringUtils.equals(childLot.getDurable(), targetCarrierId)) {
                        UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipMergeCarrierDifferent()));
                        return objs;
                    }
                    if (childLot.getLotId().equals(targetLot.getLotId())) {
                        UI.showError(Message.getString(WipExceptionBundle.bundle.WipMergeLotRepeat()));
                        return objs;
                    }

                    if (!(LotStateMachine.STATE_WAIT.equals(childLot.getState()) || LotStateMachine.STATE_FIN.equals(childLot.getState())
                            || LotStateMachine.STATE_TRACKOUT.equals(childLot.getState()))) {
                        UI.showError(Message.getString(WipExceptionBundle.bundle.LotStatusNotAllow));
                        return objs;
                    }
                    LotSortingAction lotSortingAction = new LotSortingAction();
                    lotSortingAction.setActionType(LotSortingJob.ACTION_TYPE_MERGE);
                    lotSortingAction.setSort(false);
                    lotActions.add(lotSortingAction);
                    childLots.add(childLot);
                }
            } else {
                Equipment sortEquipment = null;
                if (!StringUtil.isEmpty(sort.getEquipmentId())) {
                    RefTableField reftableField = (RefTableField) componentAssignCustomComposite.getSortingCustomComposite().getForm().getFields().get(SortingCustomComposite.FIELD_EQUIPMENTID);
                    sortEquipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), reftableField.getComboControl().getText());
                }

                List<ComponentUnit> addComponents = componentAssignCustomComposite.getTargetComponentComposite().getComponentComposite().getComponentsDiff().get(DiffType.ADD);
                if (CollectionUtils.isEmpty(addComponents)) {
                    UI.showError(Message.getString(WipExceptionBundle.bundle.WipMergeAssignComponentIsNull()));
                    return objs;
                }

                Map<Long, List<ComponentUnit>> childUnitMap = addComponents.stream().collect(Collectors.groupingBy(ComponentUnit::getParentUnitRrn));
                List<Combo> sourcePorts = componentAssignCustomComposite.getMultiSourcePort();
                String targetPortId = componentAssignCustomComposite.getMultiTargetPort().get(0).getText();

                for (int i = 0; i < componentAssignCustomComposite.getMultiSourceComponentComposite().size(); i++) {
                    ComponentCustomComposite componentCustomComposite = componentAssignCustomComposite.getMultiSourceComponentComposite().get(i);
                    String sourcePortId = sourcePorts.get(i).getText();

                    String lotId = componentAssignCustomComposite.getSourceTabFolder().getItem(i).getText();
                    Lot childLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
                    childLot = lotManager.getLotWithComponent(childLot.getObjectRrn());

                    LotSortingAction lotSortingAction = new LotSortingAction();
                    lotSortingAction.setActionType(LotSortingJob.ACTION_TYPE_MERGE);

                    lotSortingAction.setToDurableId(targetCarrierId);
                    if (childUnitMap.get(childLot.getObjectRrn()) == null) {
                        continue;
                    }

                    // Դ�ؾߺ�Ŀ���ؾ߲�����ͬ
                    if (StringUtils.equals(childLot.getDurable(), targetCarrierId)) {
                        UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipMergeCarrierRepeat()));
                        return objs;
                    }

                    // Դ�ؾ�������������ȫ���ŵ�Ŀ���ؾ���
                    if (childUnitMap.get(childLot.getObjectRrn()).size() != childLot.getSubProcessUnit().size()) {
                        UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipComponentIncomplete()));
                        return objs;
                    }

                    childLot.setSubProcessUnit((List) childUnitMap.get(childLot.getObjectRrn()));
                    Map<String, String> toPositionMap = childUnitMap.get(childLot.getObjectRrn()).stream().collect(Collectors.toMap(ComponentUnit::getComponentId,
                                                                                                                                    ComponentUnit::getPosition));
                    lotSortingAction.setToPositionMap(toPositionMap);
                    if (sort.isSort()) {
                        // port��Ҫ������Ҫ����û��
                        if ((!StringUtil.isEmpty(sourcePortId) && StringUtil.isEmpty(targetPortId))
                                || (StringUtil.isEmpty(sourcePortId) && !StringUtil.isEmpty(targetPortId))) {
                            UI.showError(Message.getString(WipExceptionBundle.bundle.SorterPortIncomplete()));
                            return objs;
                        }
                        // У��port��ʱ��һ��
                        if (!StringUtil.isEmpty(sourcePortId) && !StringUtil.isEmpty(targetPortId)) {
                            if ((sourcePortId).equals(targetPortId)) {
                                UI.showError(Message.getString(WipExceptionBundle.bundle.SorterPortDiverse()));
                                return objs;
                            }
                        }

                        lotSortingAction.setSort(true);
                        if (sortEquipment != null) {
                            lotSortingAction.setEquipmentId(sortEquipment.getEquipmentId());
                        }
                        lotSortingAction.setFromPortId(sourcePortId);
                        lotSortingAction.setToPortId(targetPortId);
                    } else {
                        // ����sort��ҪУ������״̬
                        if (!(LotStateMachine.STATE_WAIT.equals(childLot.getState()) || LotStateMachine.STATE_FIN.equals(childLot.getState())
                                || LotStateMachine.STATE_TRACKOUT.equals(childLot.getState()))) {
                            UI.showError(Message.getString(WipExceptionBundle.bundle.LotStatusNotAllow));
                            return objs;
                        }
                        lotSortingAction.setSort(false);
                    }

                    lotActions.add(lotSortingAction);
                    childLot.setOperator1(Env.getUserName());
                    childLots.add(childLot);
                }

                if ((Equipment.COMMUNICATION_STATE_OFFLINE.equals(sort.getCommunicationState()) || StringUtil.isEmpty(sort.getCommunicationState()))
                        && sort.isSort()) {
                    if (UI.showConfirm(Message.getString(WipExceptionBundle.bundle.SorterJobAutoComplete()))) {
                        lotActions.forEach(lotAction -> {
                            ((LotSortingAction) lotAction).setAutoComplete(true);
                        });
                    }
                }
            }

			objs[0] = true;
			objs[1] = lotActions;
			objs[2] = childLots;
			objs[3] = targetLot;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return objs;
	}

    public void refresh() {
        // ˢ���ұ߷���ҳ����Ϣ
    	componentAssignCustomComposite.clear();

        // ˢ�����������Ϣ
        CarrierLotCustomComposite carrierLotComposite = carrierAssignCustomComposite.getCarrierLotComposite();
        carrierLotComposite.getLotTableManager().setInput(new ArrayList<Lot>());
        carrierLotComposite.getLotTableManager().refresh();

        String carrierId = carrierAssignCustomComposite.getCarrierLotComposite().getTxtCarrierId().getText();
        String lotId = carrierAssignCustomComposite.getCarrierLotComposite().getTxtLotId().getText();
        if (StringUtils.isNotEmpty(lotId)) {
            carrierLotComposite.getLotByLotId(lotId);
        } else if (StringUtils.isNotEmpty(carrierId)) {
            carrierLotComposite.getLotsByCarrierId(carrierId);
        } else {
            carrierLotComposite.getLotsByCarrierId(null);
        }

        String targetCarrierId = carrierAssignCustomComposite.getTxtCarrierId().getText();
        carrierAssignCustomComposite.getTargetLotTableManager().setInput(new ArrayList<Lot>());
        carrierAssignCustomComposite.getTargetLotTableManager().refresh();
        carrierAssignCustomComposite.getLotsByCarrierId(targetCarrierId);
        componentAssignCustomComposite.getTargetComponentComposite().setCarrier(null);

    }
}