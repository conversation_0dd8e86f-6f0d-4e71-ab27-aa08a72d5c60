package com.glory.mes.wip.lot.release;

import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Table;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.mes.wip.model.Lot;

public class HoldLotTableField extends AbstractField {

	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
    protected ListTableManager viewer;
    protected TableViewer viewer1;
    protected Table table;
	private Lot lot;
    
    public HoldLotTableField(String id, String label, int style) {
        super(id);
        this.label = label;
        mStyle = style;
    }
    
    public HoldLotTableField(String id, String label, ListTableManager viewer) {
        super(id);
        this.viewer = viewer;
        this.label = label;
    }
    
    public HoldLotTableField(String id) {
        super(id);
    }
    
    public void clear() {
    	viewer.getTableManager().removeList(valueChangeListeners);
	}
    
	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		int i = 0;
		String labelStr = getLabel();
        if (labelStr != null) {
        	mControls = new Control[2];
        	Label label = toolkit.createLabel(composite, labelStr);
            mControls[0] = label;
            i = 1;
        } else {
        	mControls = new Control[1];
        }    
        
        Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
        GridData gd = new GridData(GridData.FILL_BOTH);
        gd.grabExcessHorizontalSpace = true;
        gd.horizontalAlignment = SWT.FILL;
        gd.heightHint = 120;
		gd.widthHint = 420;
        tableContainer.setLayoutData(gd);
        tableContainer.setLayout(new GridLayout(1,false));  
        
        viewer.newViewer(tableContainer);
        viewer.refresh();
        
        //table = viewer1.getTable(); 
    	//table.setParent(tableContainer);
    	//table.setLayoutData(new GridData(GridData.FILL_BOTH));
    	//createButtons(toolkit, tableContainer);
        //add.addSelectionListener(getAddListener());
 		//remove.addSelectionListener(getDeleteListener());
    	//gd.heightHint = table.getItemHeight () * 10;
    	//tableContainer.setLayoutData(gd);
        mControls[i] = tableContainer;
        
	}

    public Label getLabelControl() {
        Control[] ctrl = getControls();
        if(ctrl.length >  1) {
            return (Label)ctrl[0];
        } else {
            return null;
        }
    }

    
	@Override
	public void refresh() {

	}
	
	public String getFieldType() {
		return "tablelist";
	}

	public void setLot(Lot lot) {
		this.lot = lot;
		
	}
}

