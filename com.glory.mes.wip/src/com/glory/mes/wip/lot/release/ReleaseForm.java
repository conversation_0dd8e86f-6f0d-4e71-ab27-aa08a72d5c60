package com.glory.mes.wip.lot.release;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;

public class ReleaseForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(ReleaseForm.class);

	List<LotHold> lotHolds = new ArrayList<LotHold>();
	
	private static final String AD_TABLE_RELEASE = "WIPLotRelease";
	private IField field;
	private ListTableManager listTableManager;
	
	private Lot lot;

	public ReleaseForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		getMergeField();
	}
	
	public IField getMergeField(){
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE_RELEASE);
			listTableManager = new ListTableManager(adTable);
			field = new HoldLotTableField(AD_TABLE_RELEASE, null, listTableManager);
			ADField adField = new ADField();
			adField.setIsMandatory(true); //����
			adField.setIsSameline(true);
			field.setADField(adField);
			addField(AD_TABLE_RELEASE, field);
			
		} catch (Exception e) {
			logger.error("SplitLotForm : Init tablelist", e);
		}
		return field;
	}

	@Override
	public void refresh() {
		super.refresh();
		this.getAllLots();
		listTableManager.setInput(this.getLotHolds());
	}

	void getAllLots() {
		try {
			ADManager lotManager = Framework.getService(ADManager.class);
			List<LotHold> lotHolds = lotManager.getEntityList(Env.getOrgRrn(), LotHold.class, Integer.MAX_VALUE, " lotRrn = " + lot.getObjectRrn() + " and lotRrn is not null", " holdTime ");
			this.setLotHolds(lotHolds);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}
	
	public List<LotHold> getLotHolds() {
		return lotHolds;
	}

	public void setLotHolds(List<LotHold> lotHolds) {
		this.lotHolds = lotHolds;
	}

	public LotHold getSelectedLotHold() {
		if (listTableManager.getSelectedObject() != null) {
			LotHold ss = (LotHold)listTableManager.getSelectedObject();
			return ss;
		}
		return null;
	}
	
}
