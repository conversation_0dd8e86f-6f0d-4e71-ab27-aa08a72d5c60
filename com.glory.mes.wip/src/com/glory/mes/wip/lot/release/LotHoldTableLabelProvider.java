package com.glory.mes.wip.lot.release;

import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Image;

import com.glory.framework.core.util.DateUtil;
import com.glory.mes.wip.model.LotHold;

public class LotHoldTableLabelProvider extends LabelProvider implements ITableLabelProvider { 
	public Image getColumnImage( Object element, int index) { 
		return null; 
	} 
	public String getColumnText(Object element, int index) { 
		LotHold lotHold = (LotHold) element; 
		switch (index) { 
			case 0 : return lotHold.getHoldCode(); 
			case 1 : return lotHold.getHoldReason(); 
			case 2 : return lotHold.getHoldOwner(); 
			case 3 : return lotHold.getHoldUserName(); 
			case 4 : return DateUtil.formatDateTime(lotHold.getHoldTime());
			case 5 : return lotHold.getHoldComment();
			default : return "unknown " + index; 
		} 
	} 
} 
