package com.glory.mes.wip.lot.release;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class ReleaseSection extends LotSection {

	protected AuthorityToolItem itemRelease;
	protected ToolItem itemSuperRelease;
	protected ReleaseForm releaseForm;
	
	protected EntityForm batchReleaseForm;
	protected LotAction lotAction = new LotAction();
	
	public static final String KEY_SUPERRELEASE = "SuperRelease";
	public static final String KEY_RELEASE = "Relase";

	public ReleaseSection() {
		super();
	}

	public ReleaseSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.release_lot"));
		initAdObject();
		Composite client = (Composite) section.getClient();

		batchReleaseForm = new EntityForm(client, SWT.NONE, new LotAction(), getADTable1(), form.getMessageManager());
		batchReleaseForm.setLayout(new GridLayout());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.heightHint = 50;
		batchReleaseForm.setLayoutData(gd);
	}

	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemRelease(tBar);		
		new ToolItem(tBar, SWT.SEPARATOR);
//      createToolItemSuperRelease(tBar);
//      new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemRelease(ToolBar tBar) {
		itemRelease = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() +"."+ KEY_RELEASE);
		itemRelease.setAuthEventAdaptor(this::releaseAdapter);
		itemRelease.setText(Message.getString("wip.release"));
		itemRelease.setImage(SWTResourceCache.getImage("release-lot"));
//		itemRelease.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				releaseAdapter(event);
//			}
//		});
	}
	protected void createToolItemSuperRelease(ToolBar tBar){
		itemSuperRelease = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_SUPERRELEASE);
		itemSuperRelease.setText(Message.getString("wip.superrelease"));
		itemSuperRelease.setImage(SWTResourceCache.getImage("release-lot"));
		itemSuperRelease.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				superRseleaseAdapter(event);				
			}
		});
	}

	protected void superRseleaseAdapter(SelectionEvent event){
		itemSuperRelease.setData(true);
		releaseAdapter(event);
	}
	
	protected void releaseAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null && null != getAdObject().getObjectRrn()) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					if (batchReleaseForm.validate()) {
						batchReleaseForm.saveToObject();
						lotAction = (LotAction) batchReleaseForm.getObject();
						for (IForm detailForm : getDetailForms()) {
							LotManager lotManager = Framework.getService(LotManager.class);
							if (null == ((ReleaseForm) detailForm).getSelectedLotHold()) {
								UI.showInfo(Message.getString("wip.release_no_exist_lot"));
								return;
							}
							long lotRrn = ((ReleaseForm) detailForm).getSelectedLotHold().getLotRrn();
							long lotHoldRrn = ((ReleaseForm) detailForm).getSelectedLotHold().getObjectRrn();
//						if (null != itemSuperRelease.getData()) {
//							lotAction.setActionPwd(((ReleaseForm) detailForm).getSelectedLotHold().getHoldPwd());
//							itemSuperRelease.setData(null);
//						}
							Lot lot = lotManager.getLot(lotRrn);
							String operator = Env.getUserName();
							if (itemRelease.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
								operator = (String) itemRelease.getData(LotAction.ACTION_TYPE_OPERATOR);
							}
							lot.setOperator1(operator);
							SessionContext sc = Env.getSessionContext();
							sc.setUserName(operator);
							lotManager.releaseLot(lot, lotHoldRrn, lotAction, sc);
						}
						UI.showInfo(Message.getString("wip.release_successed"));// ������ʾ��
						refresh();
					} else {
						UI.showWarning(Message.getString("warn.required_entry"));
						return;
					}
				} 
			} else {
				UI.showInfo(Message.getString("wip.release_not_null"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}

	@Override
	protected void createSectionContent(Composite client) {
		super.createSectionContent(client);
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		releaseForm = new ReleaseForm(composite, SWT.NONE, tab, mmng);
		return releaseForm;
	}

	@Override
	public void refresh() {		
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));				
			}
//			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		Lot lot = (Lot)getAdObject();
		releaseForm.setLot(lot);
		super.refresh();
		if(batchReleaseForm != null) {
			lotAction = new LotAction();
			batchReleaseForm.setObject(lotAction);
			batchReleaseForm.loadFromObject();
		}
	}
	
	public void statusChanged(String newStatus){
		if (null == newStatus || "".equals(newStatus)) {
			itemRelease.setEnabled(false);
			//itemSuperRelease.setEnabled(false);
		} else {
			itemRelease.setEnabled(true);
			//itemSuperRelease.setEnabled(true);
		}
	}

	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPBatchReleaseAction");
		return midTable;
	}
}
