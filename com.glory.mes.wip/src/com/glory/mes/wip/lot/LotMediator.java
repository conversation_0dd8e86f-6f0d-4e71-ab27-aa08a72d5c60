package com.glory.mes.wip.lot;

import org.apache.log4j.Logger;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.mes.wip.lot.flow.LotFlowSection;
import com.glory.mes.wip.lot.flow.LotFlowTreeField;
import com.glory.mes.wip.model.Lot;

public class LotMediator {
	private static final Logger logger = Logger.getLogger(LotMediator.class);
	
	protected LotSection lotSection;
	protected LotFlowSection flowSection;
	protected LotFlowTreeField treeField;
	
	public LotSection getLotSection() {
		return lotSection;
	}
	
	public LotMediator() {}
	
	public LotMediator(LotSection lotSection) {
		this.lotSection = lotSection;
	}
																								
	public LotMediator(LotSection lotSection, LotFlowTreeField treeField) {
		this(lotSection);
		this.treeField = treeField;
	}
	
	public void notifyLotFlowTreeField(String key, ADBase adBase) {
		notifyLotFlowTreeFieldAdapter(key, adBase);
	}
	
	public void notifyLotFlowTreeField(String key, ADBase parent, ADBase child) {
		
	}
	
	public void notifySection(ADBase adBase) {
		if(adBase instanceof Lot) {
			lotSection.setAdObject(adBase);
			notifySectionAdapter(adBase);
		}
	}
	
	public void addTreeViewerSelectionChangedListener() {
		try {
			if(treeField != null) {
				treeField.addTreeViewerSelectionChangedListener(
						new SelectionListener() {
							public void widgetSelected(SelectionEvent e) {
								selectionChangedAdapter(e);
							}
							public void widgetDefaultSelected(SelectionEvent e) {
								widgetSelected(e);
							}
						}
				);
			}
		} catch(Exception e) {
			logger.error("Error at LotMediator : addTreeViewerSelectionChangedListener"
					+ e.getStackTrace());
		}
	}
	
	public void addTreeViewerMouseListener(MouseListener listener) {
		if(treeField != null) {
			treeField.addTreeViewerMouseListener(listener);
		}
	}
	
	protected void notifyLotFlowTreeFieldAdapter(String key, ADBase adBase) {
	}
	
	protected void notifySectionAdapter(ADBase adBase) {
	}
	
	protected void selectionChangedAdapter(SelectionEvent event) {
	}
	
	public void setLotSection(LotSection lotSection) {
		this.lotSection = lotSection;
	}
	
	public LotFlowTreeField getLotFlowTreeField() {
		return treeField;
	}
	
	public void setLotFlowTreeField(LotFlowTreeField treeField) {
		this.treeField = treeField;
	}

	public LotFlowSection getFlowSection() {
		return flowSection;
	}

	public void setFlowSection(LotFlowSection flowSection) {
		this.flowSection = flowSection;
	}

}
