package com.glory.mes.wip.lot.batch;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class BatchShipSection extends QueryEntityListSection {

	public static final String KEY_BATCHSHIP = "batchShip";
	
	public static final String TABLE_NAME_SHIP_ACTION = "WIPShipAction";
	
	public CheckBoxFixEditorTableManager manager;
	protected AuthorityToolItem itemMultiRelease;
	protected LotAction Action = new LotAction();

	protected EntityForm BatchForm;
	protected ListTableManager tableManager;

	public BatchShipSection(ListTableManager tableManager) {
		super(tableManager);
		this.tableManager = tableManager;

	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemBatchShip(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemBatchShip(ToolBar tBar) {
		itemMultiRelease = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_BATCHSHIP);
		itemMultiRelease.setText(Message.getString("wip.batchship"));
		itemMultiRelease.setImage(SWTResourceCache.getImage("ship-lot"));
		itemMultiRelease.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				BathchShipAdapter(event);
			}
		});
	}

	public void createContents(final IManagedForm form, Composite parent, int sectionStyle) {
		super.createContents(form, parent, sectionStyle);

		Composite client = (Composite) section.getClient();

		BatchForm = new EntityForm(client, SWT.NONE, new LotAction(), getADTable1(), form.getMessageManager());
		BatchForm.setLayout(new GridLayout());
		BatchForm.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	protected void BathchShipAdapter(SelectionEvent event) {
		try {
			managedForm.getMessageManager().removeAllMessages();
			LinkedHashMap<String, IField> fields = BatchForm.getFields();

			for (IField f : fields.values()) {
				if (f.getId().equals("actionCode")) {
					String hCode = (String) f.getValue();
					if (hCode != null) {
						Action.setActionCode(hCode);
						continue;
					} else {
						UI.showInfo(Message.getString("wip.select_ship_code"));
						return;
					}
				}

				if (f.getId().equals("actionComment")) {
					String hComment = (String) f.getValue();
					Action.setActionComment(hComment);
					continue;
				}
			}

			List<Object> batchObject = tableManager.getCheckedObject();
			List<Lot> batchLots = new ArrayList<Lot>();

			if (batchObject.size() > 0) {
				LotManager lotManager = Framework.getService(LotManager.class);
				for (int i = 0; i < batchObject.size(); i++) {
					batchLots.add((Lot) batchObject.get(i));
				}
				lotManager.shipLot(batchLots, Action, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.ship_success"));

				refresh();
			} else {
				UI.showError(Message.getString("wip.ship_error_nolot_toship"));
			}
		} catch (Exception e) {

			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), TABLE_NAME_SHIP_ACTION);
		return midTable;
	}

	@Override
	public void refresh() {
		super.refresh();
		Action = new LotAction();
		BatchForm.setObject(Action);
		BatchForm.loadFromObject();
	}

}
