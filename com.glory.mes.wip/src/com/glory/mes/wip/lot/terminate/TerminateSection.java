package com.glory.mes.wip.lot.terminate;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class TerminateSection extends LotSection {
	
	public static final String KEY_TERMINATE = "terminate";

	protected AuthorityToolItem itemTerminate;

	public TerminateSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.terminate_sectiontitle"));
		initAdObject();
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		if (adObject != null && adObject.getObjectRrn() != null) {
			for(IForm detailForm : this.getDetailForms()){
				((TerminateForm)detailForm).addLot((Lot)adObject);
			}
		}
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemTerminate(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemTerminate(ToolBar tBar) {
		itemTerminate = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() +"." + KEY_TERMINATE);
		itemTerminate.setAuthEventAdaptor(this::terminateAdapter);
		itemTerminate.setText(Message.getString("wip.terminate"));
		itemTerminate.setImage(SWTResourceCache.getImage("terminate-lot"));
//		itemTerminate.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				terminateAdapter(event);
//			}
//		});
	}

	protected void terminateAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (((TerminateForm) detailForm).getLots().size() != 0) {
					if (!detailForm.saveToObject()) {
						UI.showWarning(Message.getString("warn.required_entry"));
						saveFlag = false;
					}
				} else {
					UI.showError(Message.getString("wip.terminate_error_nolot"));
					saveFlag = false;
				}
			}
			
			String operator = Env.getUserName();
			if (itemTerminate.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
				operator = (String) itemTerminate.getData(LotAction.ACTION_TYPE_OPERATOR);
			}
			if (saveFlag) {
				for (IForm detailForm : getDetailForms()) {
					LotManager lotManager = Framework.getService(LotManager.class);
					LotAction lotAction = ((TerminateForm) detailForm)
							.getLotAction();
					List<Lot> terminateLots = new ArrayList<Lot>();
					
					for(Lot lot : ((TerminateForm) detailForm).getLots()) {
						lot.setOperator1(operator);
						terminateLots.add(lot);
					}
					lotManager.terminate(terminateLots
							, lotAction,
							Env.getSessionContext());
				}
				UI.showInfo(Message.getString("wip.terminate_success"));// ������ʾ��
				for (IForm detailForm : getDetailForms()) {
					((TerminateForm) detailForm).clearTerminateLot();
				}
				setAdObject(new Lot());
				refresh();
				setFocus();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		TerminateForm itemForm = new TerminateForm(composite, SWT.NONE,
				tab, mmng);
		return itemForm;
	}

}
