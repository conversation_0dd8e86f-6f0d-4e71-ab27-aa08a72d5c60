package com.glory.mes.wip.lot.terminate;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TableListField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class TerminateForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(TerminateForm.class);
	
	private List<Lot> lots = new ArrayList<Lot>();
	
	private static String TABLE_NAME = "WIPLotTerminate";
	
	private IField fieldTerminateLot;
	private static final String TERMINATELOT = "TerminateLot";
	private static final String TERMINATELOT_ID = "TerminateLot";
	private IField fieldTerminateCode;
	private static final String TERMINATECODE = "TerminateCode";
	private static final String TERMINATECODE_ID = "TerminateCode";
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";
	
	private LotAction lotAction = new LotAction();

	public TerminateForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public TerminateForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}
	public TerminateForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public TerminateForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ListTableManager tableManager = new ListTableManager(adTable);
			fieldTerminateLot = createTableListField(TERMINATELOT_ID, Message.getString("wip.terminate_lot"), tableManager);
			
			fieldTerminateCode = createUserRefList(TERMINATECODE_ID, Message.getString("wip.terminate_code") + "*", TERMINATECODE, true);			

			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);
			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			fieldTerminateLot.setADField(adField);
			
			addField(TERMINATELOT, fieldTerminateLot);
			addField(TERMINATECODE, fieldTerminateCode);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("TerminateForm : Init listItem", e);
		}
	}
	
	public void addLot(Lot lot) {
		if (!lots.contains(lot)) {
			if(LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState())
					|| LotStateMachine.STATE_SCHD.equalsIgnoreCase(lot.getState())
					|| LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState())) {
				lots.add(lot);	
				fieldTerminateLot.setValue(lots);
			} else {
				UI.showError(Message.getString("error.state_not_allow"));
			}
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) 
						&& !f.equals(fieldTerminateLot)
						&& !f.equals(fieldTerminateCode)
						&& !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			lotAction.setActionCode((String) fieldTerminateCode.getValue());
			lotAction.setActionComment((String) fieldComment.getValue());
			return true;
		}
		return false;
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			fieldTerminateCode.setValue("");
			fieldTerminateCode.refresh();
			((TextField) fieldComment).setText("");
		} catch (Exception e) {
			logger.error("TerminateForm : refresh()", e);
		}
	}
	
	public void clearTerminateLot() {
		((TableListField)fieldTerminateLot).setValue(null);
		lots.clear();
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	public List<Lot> getLots() {
		return lots;
	}

	@Override
	public boolean validate() {
		boolean validFlag = super.validate();
		if (fieldTerminateCode.getValue() == null
				|| "".equals(String.valueOf(fieldTerminateCode.getValue()).trim())) {
			validFlag = false;
			mmng.addMessage(TERMINATECODE_ID + "common.ismandatory", 
					String.format(Message.getString("common.ismandatry"), TERMINATECODE_ID), null,
					IMessageProvider.ERROR, fieldTerminateCode.getControls()[1]);
		}	
		return validFlag;
	}
}

