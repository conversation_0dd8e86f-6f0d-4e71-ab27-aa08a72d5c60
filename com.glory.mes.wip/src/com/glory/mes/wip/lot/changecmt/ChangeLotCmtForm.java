package com.glory.mes.wip.lot.changecmt;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.core.util.PropertyUtil;

public class ChangeLotCmtForm extends EntityForm {

	private IField fieldComment;
	private static final String COMMENT = "Comment";
	public static final String COMMENT_ID = "lotComment";

	public ChangeLotCmtForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		fieldComment = createText(COMMENT_ID,
				Message.getString("wip.comment"), "", 32);
		ADField adField = new ADField();
		adField.setIsSameline(true);
		fieldComment.setADField(adField);
		addField(COMMENT, fieldComment);
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
		}
	}

	@Override
	public void refresh() {
		super.refresh();
		((TextField) fieldComment).setText("");
	}

	@Override
	public boolean validate() {
		mmng.removeAllMessages();
		boolean mainIsNull = GenericValidator.isBlankOrNull((String) fieldComment.getValue());
		
		return !mainIsNull;
	}

	@Override
	public boolean saveToObject() {
		if (object != null){
			if (!validate()){
				return false;
			}
			for (IField f : fields.values()){
				if (!(f instanceof SeparatorField) && !GenericValidator.isBlankOrNull(String.valueOf(f.getValue()))){
					PropertyUtil.setProperty(object, f.getId(), f.getValue());
				}
			}
			return true;
		}
		return false;
    }
	
}
