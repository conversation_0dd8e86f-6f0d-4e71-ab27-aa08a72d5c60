package com.glory.mes.wip.lot.flow;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.adapter.PrdItemAdapter;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.EndState;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.StartState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.model.Lot;

public class LotItemAdapter extends PrdItemAdapter {
	private static final Logger logger = Logger.getLogger(LotItemAdapter.class);
	protected static final Object[] EMPTY = new Object[0];
	
	protected long processInstanceRrn;
	protected String currentPath;
	protected List<Node> nodeList;
	protected Lot lot;
	
	@Override
	public Object[] getElements(Object object) {
		try {
			Lot lot = (Lot)object;
			if (lot != null && lot.getObjectRrn() != null){
				processInstanceRrn = lot.getProcessInstanceRrn();
			
				PrdManager prdManager = Framework.getService(PrdManager.class);
				ProcessDefinition pf = prdManager.getProcessInstance(processInstanceRrn).getProcessDefinition();
			
				return new Object[]{pf};
			}
		} catch (Exception e) {
			logger.error("LotItemAdapter : getElements()", e);
		}
		return EMPTY;
	}
	
	@Override
	public Object[] getChildren(Object object) {
		try {
			List<Node> children = new ArrayList<Node>();
			Node node = null;
			if (nodeList.size() > 0) {
				node = nodeList.get(0);
			} 
			
			Object[] objects = getProcessDefinitionChildern(object);
			
			if (objects != null) {
				for (Object childNode : objects) {
					Node currentNode; 
					if (node != null && node.getObjectRrn().equals(((Node)childNode).getObjectRrn())) {
						currentNode = node;
					} else {
						currentNode = (Node)childNode;
					}
					currentNode.setProcessInstanceRrn(processInstanceRrn);
					//Ϊ׼ȷ��λ���,��ҪΪ��ǰLot����Node����Path
					if (currentNode instanceof ProcedureState) {
						ProcedureState state = (ProcedureState)currentNode;
						state.setPath(state.getName() + "/");
					} else if (currentNode instanceof StepState) {
						StepState state = (StepState)currentNode;
						state.setPath(state.getName() + "/");
					}
					children.add(currentNode);
				}
			}
			return children.toArray();
			
		} catch (Exception e) {
			logger.error("LotItemAdapter : getElements()", e);
		}
		return EMPTY;
	}
	
	public Object[] getProcessDefinitionChildern(Object object) {
		if (object instanceof ProcessDefinition){
			ProcessDefinition pf = (ProcessDefinition)object;
			try {
				if (pf != null) {
					List<Node> nodes;
					if (pf.getObjectRrn() != null) {
						PrdManager prdManager = Framework.getService(PrdManager.class);
						PrdQueryAction queryAction = PrdQueryAction.newIntance();
						queryAction.setCopyNode(true);
						nodes = prdManager.getProcessDefinitionChildern(lot.getObjectRrn(), pf, PrdQueryAction.newIntance());
					} else {
						nodes = pf.getChildren();
					}
					if (nodes != null) {
						List<Node> list = new ArrayList<Node>();
						for (Node node : nodes) {
							if (node instanceof StartState) {
								continue;
							} else if (node instanceof EndState) {
								continue;
							} else {
								node.setPath(node.getName() + "/");
								list.add(node);
							}
						}
						return list.toArray();
					}
				}
	            
	        } catch (Exception e) {
	        	logger.error(e.getMessage(), e);
	        }
		} else {
			logger.error("Expect ProcessDefinition, but found " + object.toString());
		}
        return EMPTY;
	}
	
	@Override
	public boolean hasChildren(Object object) {
		return true;
	}
	
	public void refresh(Lot lot, String currentPath, List<Node> nodeList) {
		this.lot = lot;
		this.currentPath = currentPath;
		this.nodeList = nodeList;
	}
}
