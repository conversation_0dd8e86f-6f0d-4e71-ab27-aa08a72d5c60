package com.glory.mes.wip.lot.flow;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.jface.viewers.IElementComparer;
import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.TreeItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.mes.prd.FlowTreeField;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.model.Lot;

public class LotFlowTreeField extends FlowTreeField {
	
	protected List<ADBase> flowList;
	private LotFlowForm lotFlowForm;
	
	public LotFlowTreeField(String id, String label, TreeViewerManager manager) {
		super(id, label, manager);
	}
	
	public LotFlowTreeField(String id, String label,
			TreeViewerManager manager, LotFlowForm lotFlowForm) {
		super(id, label, manager);
		this.lotFlowForm = lotFlowForm;
	}
	
	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		super.createContent(composite, toolkit);
		lotFlowForm.getLotMediator().setLotFlowTreeField(this);
		lotFlowForm.getLotMediator().addTreeViewerSelectionChangedListener();
		
		IElementComparer flowComparer = new IElementComparer() {
			public boolean equals(Object a, Object b) {
				if (a instanceof List && b instanceof List) {
					return a.equals(b);
				} else if (a instanceof Lot && b instanceof Lot) {
					return a.equals(b);
				} else if (a instanceof Node && b instanceof Node) {
					Node af = (Node) a;
					Node bf = (Node) b;
					return af.getObjectRrn().equals(bf.getObjectRrn());
				} else if(a instanceof ProcessDefinition && b instanceof ProcessDefinition) {
					ProcessDefinition ap = (ProcessDefinition)a;
					ProcessDefinition bp = (ProcessDefinition)b;
					return ap.getObjectRrn().equals(bp.getObjectRrn());
				} else if(a instanceof FutureHold && b instanceof FutureHold) {
					FutureHold fa = (FutureHold)a;
					FutureHold fb = (FutureHold)b;
					return fa.getObjectRrn().equals(fb.getObjectRrn());
				}
				return false;
			}

			public int hashCode(Object element) {
				if (element instanceof Node) {
					Node af = (Node) element;
					if (af.getObjectRrn() == null) {  //af.getNodeId()
						return af.getObjectRrn().intValue();
					} else {
						return af.getObjectRrn().intValue();
					}
				}
				return element.hashCode();
			}
		};
		viewer.setComparer(flowComparer);
	}

	@Override
	public void refresh() {
		if (getValue() != null) { 
			Lot lot = (Lot) getValue();
			if (lot.getObjectRrn() == null) {
				viewer.setInput(null);
				return;
			}
			((LotFlowTreeManager)manager).refresh(lot);
			viewer.collapseAll();
			
			if (flowList != null && flowList.size() > 0) {
				TreeSelection section = new TreeSelection(new TreePath(flowList.toArray()));
				viewer.setSelection(section);

				TreeItem[] items = tree.getSelection();
				for (TreeItem item : items) {
					item.setImage(SWTResourceCache.getImage("currentstep"));
					while (item.getParentItem() != null) {
						item.setForeground(SWTResourceCache.getColor("Red"));
						item = item.getParentItem();
					}
					item.setForeground(SWTResourceCache.getColor("Red"));
				}
			}
		} else {
			viewer.setInput(null);
		}
	}


	public void setFlowList(List<ADBase> flowList) {
		this.flowList = flowList;
	}

	public List<ADBase> getFlowList() {
		return flowList;
	}
	
	public TreeViewer getTreeViwer() {
		return viewer;
	}
	
	public void refreshViewerNode(ADBase adBase) {
		if(adBase != null) {
			viewer.refresh(adBase);
		}
	}
	
	public void removeLeafNode(ADBase parentNode, ADBase leafNode) {
		if(leafNode != null) {
			viewer.remove(leafNode);
			if(parentNode != null) {
				viewer.refresh(parentNode, true);
			}
		}
	}
	
	public void addTreeViewerSelectionChangedListener(SelectionListener listener) {
		viewer.getTree().addSelectionListener(listener);
	}
	
	public void addTreeViewerMouseListener(MouseListener listener) {
		viewer.getTree().addMouseListener(listener);
	}

	/**
	 * �ж�ѡ�нڵ��Ƿ�Ϊ��ǰ���̽ڵ�
	 */
	public boolean isCurrentProcedure(TreeItem selectedItem) {
		if (flowList != null && selectedItem != null) {
			Object[] nodes = flowList.toArray();
			//�����ڶ���ĵ�ǰ���̽ڵ�
			Node node1 = (Node) nodes[nodes.length-2];
			if (selectedItem.getParentItem() != null) {
				Node selectedParentNode = (Node) selectedItem.getParentItem().getData();
				if (node1.equals(selectedParentNode)) {
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * �ж�ѡ�нڵ��Ƿ�Ϊ�������̽ڵ�(�Ƿ���������)
	 */
	public boolean isNormalNode(TreeItem selectedItem) {
		if (selectedItem != null && selectedItem.getParentItem() != null) {
			Node selectedParentNode = (Node) selectedItem.getParentItem().getData();
			if (!(selectedParentNode instanceof ReworkState)) {
				return true;
			}
		}
		return false;
	}

}
