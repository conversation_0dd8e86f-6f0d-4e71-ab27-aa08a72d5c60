package com.glory.mes.wip.lot.flow;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.mes.prd.adapter.ProcedureStateItemAdapter;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.exe.Token;
import com.glory.mes.prd.workflow.graph.node.EndState;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StartState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class LotProcedureStateItemAdapter extends ProcedureStateItemAdapter {
	
	private static final Logger logger = Logger.getLogger(LotProcedureStateItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	protected String currentPath;
	protected List<Node> nodeList;
	protected Lot lot ;
	
	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof ProcedureState){
			ProcedureState procedureState = (ProcedureState)object;
			procedureState.setProcessInstanceRrn(lot.getProcessInstanceRrn());
			try {
				if(procedureState != null){
					PrdManager prdManager = Framework.getService(PrdManager.class);
					List<Node> nodes = prdManager.getProcedureStateChildern(lot.getObjectRrn(), procedureState, false);
					List<Node> list = new ArrayList<Node>();
					for (Node node : nodes) {
						if (node instanceof StartState) {
							continue;
						} else if (node instanceof EndState) {
							continue;
						} else {
							list.add(node);
						}
					}
					
					String statePath = procedureState.getPath();
					if (currentPath.startsWith(statePath)) {
						//��������Ƿ���Rework��
						String[] paths = statePath.split("/");
						Node currentNode = nodeList.get(paths.length);
						if (currentNode instanceof ReworkState) {
							Token currentToken = currentNode.getCurrentToken();
							if (currentToken != null) {
								for (Node node : list) {
									if (node instanceof StepState && 
											node.getObjectRrn().equals(currentToken.getLastNodeRrn())) {
										((StepState)node).setReworkState((ReworkState)currentNode);
										break;
									}
								}
							}
						}
					}
					
					return list.toArray();
				}
	            
	        } catch (Exception e) {
	        	logger.error(e.getMessage(), e);
	        	ExceptionHandlerManager.asyncHandleException(e);
	        }
		} else {
			logger.error("Expect ProcedureState, but found " + object.toString());
		}
        return EMPTY;
	}
	
	public void refresh(Lot lot, String currentPath, List<Node> nodeList) {
		this.lot = lot;
		this.currentPath = currentPath;
		this.nodeList = nodeList;
	}
}
