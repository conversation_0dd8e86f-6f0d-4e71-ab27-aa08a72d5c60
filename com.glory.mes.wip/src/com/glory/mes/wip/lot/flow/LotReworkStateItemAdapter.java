package com.glory.mes.wip.lot.flow;

import org.apache.log4j.Logger;
import org.eclipse.jface.resource.ImageDescriptor;

import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.LotProcedure;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;

public class LotReworkStateItemAdapter extends LotProcedureStateItemAdapter {
	
	private static final Logger logger = Logger.getLogger(LotReworkStateItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	
	private Lot lot;
	
	@Override
	public String getText(Object element) {
		if(element instanceof ReworkState){
			ReworkState state = (ReworkState)element;
			String path = state.getPath();
			state = getReworkState(state);
			ProcessDefinition procedure = (ProcessDefinition) state.getUsedProcedure();
			if (procedure == null && !StringUtil.isEmpty(state.getProcedureName())) {
				try {
					PrdManager prdManager = Framework.getService(PrdManager.class);
					try {
						procedure = (LotProcedure)prdManager.getLotProcedure(lot.getObjectRrn(), path, false);
					} catch(Exception e) {
						logger.error(e.getMessage(), e);
					}
					if(procedure == null) {
						procedure = new Procedure();
						procedure.setName(state.getProcedureName());
						procedure.setOrgRrn(state.getOrgRrn());
						procedure.setVersion(state.getProcedureVersion());
						procedure = (Procedure)prdManager.getSimpleProcessDefinition(procedure, false);
					}
				} catch (Exception e) {
		        	logger.error(e.getMessage(), e);
		        }
			} else {
				return "TempRework";
			}
			
			if (procedure != null && procedure.getObjectRrn() != null) {
				String description = (procedure.getDescription() == null || "".equals(procedure.getDescription().trim())
						 ? "" : " <" + procedure.getDescription() + ">");
				return procedure.getId() + description;
			}
		}
		return "";
	}
	
	@Override
	public ImageDescriptor getImageDescriptor(Object object, String id) {
		if (object instanceof ProcedureState){
			return SWTResourceCache.getImageDescriptor("rework");
		}
		return null;
	}
	
	protected ReworkState getReworkState(ReworkState state) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			state = (ReworkState)adManager.getEntity(state);
			return state;

		} catch (Exception e) {
        	logger.error(e.getMessage(), e);
        }
		return null;
	}
	
	public void setLot(Lot lot) {
		this.lot = lot;
	}

}
