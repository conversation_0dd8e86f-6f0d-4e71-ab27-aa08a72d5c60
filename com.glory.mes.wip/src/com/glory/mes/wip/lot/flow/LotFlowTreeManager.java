package com.glory.mes.wip.lot.flow;

import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapter;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ElseState;
import com.glory.mes.prd.workflow.graph.node.EndIfState;
import com.glory.mes.prd.workflow.graph.node.IfState;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.prd.adapter.ElseStateItemAdapter;
import com.glory.mes.prd.adapter.EndIfStateItemAdapter;
import com.glory.mes.prd.adapter.IfStateItemAdapter;
import com.glory.mes.prd.adapter.MoveToStateItemAdapter;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.designer.model.MoveToState;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class LotFlowTreeManager extends TreeViewerManager {
	private static final Logger logger = Logger.getLogger(LotFlowTreeManager.class);
	
	@Override
	protected ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        ItemAdapter itemAdapter = new LotItemAdapter();
        try {
        	factory.registerAdapter(Lot.class, itemAdapter);
        	factory.registerAdapter(Process.class, itemAdapter);
        	factory.registerAdapter(Procedure.class, new LotProcedureAdapter());
        	factory.registerAdapter(ReworkState.class, new LotReworkStateItemAdapter());
        	factory.registerAdapter(ProcedureState.class, new LotProcedureStateItemAdapter());
	        factory.registerAdapter(StepState.class, new LotStepStateItemAdapter());
	        factory.registerAdapter(MoveToState.class, new MoveToStateItemAdapter());
	        factory.registerAdapter(IfState.class, new IfStateItemAdapter());
	        factory.registerAdapter(ElseState.class, new ElseStateItemAdapter());
	        factory.registerAdapter(EndIfState.class, new EndIfStateItemAdapter());
        } catch (Exception e){
        	logger.error(e.getMessage(), e);
        }
        return factory;
    }
	
	public void refresh(Lot lot) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			List<Node> nodeList = prdManager.getProcessFlowList(lot.getProcessInstanceRrn());
			String currentPath = "";
			for (Node node : nodeList) {
				if (node instanceof ProcedureState || node instanceof StepState) {
					currentPath += node.getName() + "/";
				}
			}
			
			LotItemAdapter lotAdapter = (LotItemAdapter)adapterFactory.getAdapter(Lot.class);
			ItemAdapter adapter = adapterFactory.getAdapter(Procedure.class);
			LotProcedureAdapter lotProcedureAdapter = null;
			if(adapter instanceof LotProcedureAdapter) {
				lotProcedureAdapter = (LotProcedureAdapter)adapterFactory.getAdapter(Procedure.class);
			}
			LotReworkStateItemAdapter lotReworkStateAdapter = (LotReworkStateItemAdapter)adapterFactory.getAdapter(ReworkState.class);
			lotReworkStateAdapter.setLot(lot);
			LotProcedureStateItemAdapter lotProcedureStateAdapter = (LotProcedureStateItemAdapter)adapterFactory.getAdapter(ProcedureState.class);
			LotStepStateItemAdapter lotStepStateAdapter = (LotStepStateItemAdapter)adapterFactory.getAdapter(StepState.class);
			lotAdapter.refresh(lot, currentPath, nodeList);
			if (lotProcedureAdapter != null) {
				lotProcedureAdapter.refresh(lot, currentPath, nodeList);
			}
			lotReworkStateAdapter.refresh(lot, currentPath, nodeList);
			lotProcedureStateAdapter.refresh(lot, currentPath, nodeList);
			lotStepStateAdapter.refresh(lot, currentPath, nodeList);
		} catch (Exception e) {
			logger.error("LotFlowTreeManager : refresh()", e);
		}
		if (!LotStateMachine.STATE_FIN.equals(lot.getState())) {
			super.setInput(lot);
		} else {
			super.setInput(null);
		}
	}
}
