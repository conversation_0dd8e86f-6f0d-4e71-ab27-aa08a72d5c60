package com.glory.mes.wip.lot.schedule;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class ScheduleLotEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.schedule.ScheduleLotEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new ScheduleLotBlock(new ListTableManager(adTable));
	}
	
}
