package com.glory.mes.wip.lot.newpart;

import java.util.List;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;

public class NewPartForm extends EntityForm {
	
	private List<Node> flowList;
	private LotAction lotAction;
	protected IManagedForm managerForm;
	protected NewPartSection newPartSection;
	protected IField fieldSashForm;
	protected static final String SASHFORM = "SashForm";
	protected static final String SASHFORM_ID = "sashForm";
	protected IField fieldComment;
	protected static final String COMMENT = "Comment";
	protected static final String COMMENT_ID = "comment";

	public NewPartForm(Composite parent, int style, ADTable table, 
			NewPartSection newPartSection, IMessageManager mmng, 
			IManagedForm managerForm) {
		super(parent, style, table, mmng);
		this.managerForm = managerForm;
		this.newPartSection = newPartSection;
		super.createForm();
	}
	
	
	@Override
	public void createForm() {
		// DO NOTHING but it's important
	}



	@Override
	public void addFields() {
		super.addFields();
		fieldSashForm = new SashFormField(SASHFORM_ID, null, table, newPartSection, managerForm);
		fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 256);
		addField(SASHFORM, fieldSashForm);
		addField(COMMENT, fieldComment);
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.equals(fieldSashForm)
						&& !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		flowList = ((SashFormField)fieldSashForm).getFlowList();
		lotAction = new LotAction();
		String comment = (String) fieldComment.getValue();
		lotAction.setActionComment(comment == null || "".equals(comment.trim()) ? "" : comment);
		if(validate()){
			return true;
		}	
		return false;
	}
	
	public Part getNewPart(){
		return ((SashFormField)fieldSashForm).getNewPart();
	}

	@Override
	public void refresh() {
		super.refresh();
	}

	public Lot getLot() {
		return ((SashFormField)fieldSashForm).getLot();
	}

	@Override
	public boolean validate() {
		boolean validFlag = true;
		String message = "";
		if (getLot() != null){
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			message = Message.getString("wip.newpart_to_nolot") + "\n";
		}
		
		if(flowList != null && flowList.size() != 0){
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			message = message + "" + Message.getString("wip.noflow_be_selected");
		}
		if(!validFlag){
			UI.showError(message);
		}
		return validFlag;
	}

	public List<Node> getFlowList() {
		return flowList;
	}

	public LotAction getLotAction() {		
		return lotAction;
	}

	@Override
	public boolean setFocus() {
		((SashFormField) fieldSashForm).setFocus();
		return true;
	}
}
