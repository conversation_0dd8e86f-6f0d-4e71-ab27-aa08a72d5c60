package com.glory.mes.wip.lot.newpart;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.lot.flow.LotFlowSection;
import com.glory.mes.wip.model.Lot;

public class SashFormField extends AbstractField {
	
	protected SashForm sashForm;
	protected ADTable table;
	protected IManagedForm form;
	protected NewPartSection newPartSection;
	protected LotFlowSection lotFlowSection;
	protected NewPartFlowSection newPartFlowSection;
	
	public SashFormField(String id, String label, ADTable table, NewPartSection newPartSection, IManagedForm form) {
		super(id);
		this.label = label;
		this.table = table;
		this.newPartSection = newPartSection;
		this.form = form;
	}
	
	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		int i = 0;
		String labelStr = getLabel();
        if (labelStr != null) {
        	mControls = new Control[2];
        	Label label = toolkit.createLabel(composite, labelStr);
            mControls[0] = label;
            i = 1;
        } else {
        	mControls = new Control[1];
        }
        GridData gd = new GridData(GridData.FILL_BOTH);
        ScrolledForm sForm = toolkit.createScrolledForm(composite);
        ManagedForm managedForm = new ManagedForm(toolkit, sForm);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		sForm.setLayoutData(gd);
		sForm.getBody().setLayout(layout);
		sashForm = new MDSashForm(sForm.getBody(), SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		sashForm.setMenu(sForm.getBody().getMenu());
		sashForm.setLayoutData(gd);
		sForm.getBody().setLayoutData(gd);
		sashForm.setLayout(layout);
		
 		lotFlowSection = new LotFlowSection(table,
 				new LotMediator(newPartSection));
		lotFlowSection.createContents(managedForm, sashForm);
		
		newPartFlowSection = new NewPartFlowSection(table);
		newPartFlowSection.createContents(managedForm, sashForm);
		mControls[i] = sashForm;

	}

	@Override
	public void refresh() {
		lotFlowSection.excuteSearch();
	}
	
	public void setFocus(){
		lotFlowSection.setFocus();
	}
	
	public Lot getLot(){
		return (Lot)lotFlowSection.getAdObject();		
	}
	
	public Part getNewPart() {
		return (Part)newPartFlowSection.getAdObject();
	}

	public List<Node> getFlowList(){
		return newPartFlowSection.getFlowList();
	}
}
