package com.glory.mes.wip.lot.newpart;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.future.FutureAction;

public class LotFutureActionListDialog extends BaseTitleDialog {
	
	public static final String TABLE_NAME1 = "WIPADVProcedureFutureActionQuery";
	public static final String TABLE_NAME2 = "WIPADVTimerStartedQuery";
	
	public ListTableManager futureActionTableManager;
	public ListTableManager futureTimerInstanceTableManager;
	
	public List<Object> selectObjects = new ArrayList<Object>();
	private List<Object> list;
	
	protected LotFutureActionListDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public LotFutureActionListDialog(Shell parentShell, List<Object> list) {
		this(parentShell);
		this.list = list;
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setBounds(200, 90, 300, 595);
		getShell().setMinimumSize(1100, 600);
		getShell().setBackground(new Color(Display.getCurrent(), 188, 201, 228));
	}
	
	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(Message.getString("wip.lot_future_action_list"));
        createTableContent(parent);
		return parent;
	}
	
	protected Control createTableContent(Composite parent) {
		Composite content = new Composite(parent, SWT.NONE);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setBackground(new Color(Display.getCurrent(), 255, 255, 255));

		try {
			ADManager entityManager = (ADManager)Framework.getService(ADManager.class);	
			ADTable futureActionAdTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME1);
			futureActionTableManager = new ListTableManager(futureActionAdTable, true);
			futureActionTableManager.newViewer(content);
			
			ADTable qtimerAdTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME2);
			futureTimerInstanceTableManager = new ListTableManager(qtimerAdTable, true);
			futureTimerInstanceTableManager.newViewer(content);
			
			List<FutureAction> futureActions = new ArrayList<FutureAction>();
			List<FutureTimerInstance> futureTimerInstances = new ArrayList<FutureTimerInstance>();
			for (Object object : list) {
				if (object instanceof FutureAction) {
					futureActions.add((FutureAction) object);
				} 
				if (object instanceof FutureTimerInstance) {
					futureTimerInstances.add((FutureTimerInstance) object);
				}	
			}
			
			futureActionTableManager.setInput(futureActions);
			futureTimerInstanceTableManager.setInput(futureTimerInstances);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return content;
	}
	
	@Override
	public void okPressed() {
		try {
			List<Object> os = futureActionTableManager.getCheckedObject();
			if (os.size() > 0) {
				for (Object o : os) {
					selectObjects.add(o);	
				}
			}	
			os = futureTimerInstanceTableManager.getCheckedObject();
			if (os.size() > 0) {
				for (Object o : os) {
					selectObjects.add(o);	
				}
			}	
			
			if (selectObjects.size() > 0) {
				if (!UI.showConfirm(Message.getString("wip.newpart_select_future_action_is_clear"))) {
					selectObjects.clear();
					return;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		close();
	}

	
	public List<Object> getList() {
		return list;
	}

	public void setList(List<Object> list) {
		this.list = list;
	}

	public List<Object> getSelectObjects() {
		return selectObjects;
	}

	public void setSelectObjects(List<Object> selectObjects) {
		this.selectObjects = selectObjects;
	}
	
}
