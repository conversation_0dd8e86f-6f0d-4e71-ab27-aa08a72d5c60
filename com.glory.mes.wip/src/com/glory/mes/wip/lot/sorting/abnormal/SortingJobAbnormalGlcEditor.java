package com.glory.mes.wip.lot.sorting.abnormal;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.PreDestroy;
import javax.inject.Inject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.e4.ui.workbench.modeling.IPartListener;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.glory.mes.wip.sorting.LotSortingJobDetail;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.glory.framework.core.exception.ExceptionBundle;

public class SortingJobAbnormalGlcEditor extends GlcEditor implements IPartListener{

    public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.sorting.abnormal.SortingJobAbnormalGlcEditor";
    
    @Inject
	protected EPartService partService;
	
	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected IEventBroker eventBroker;

	@Inject	
	protected MPart mPart;
    
	private static final Logger logger = Logger.getLogger(SortingJobAbnormalGlcEditor.class);

    private static final String CONTROL_BUTTON_CANCEL = "CancelJob";
    private static final String CONTROL_BUTTON_RELEASE = "ReleaseJob";
    private static final String CONTROL_BUTTON_REFRESH = "Refresh";

	// glcForm
    private static final String CONTROL_FIELD_FORM_JOB = "job";
    private static final String CONTROL_FIELD_FORM_LOT = "lot";

    // Job
    private static final String CONTROL_FIELD_JOBLIST = "jobList";
    private static final String CONTROL_FIELD_JOBDETAIL = "jobDetail";

    // Lot
    private static final String CONTROL_FIELD_LOTLIST = "lotList";
    private static final String CONTROL_FIELD_RELEASELOT = "releaseLot";
    private static final String CONTROL_FIELD_LOTACTION = "lotAction";

    protected GlcFormField jobGlcFormField, lotGlcFormField;

    protected ListTableManagerField jobTableManagerField, jobDetailTableManagerField, lotTableManagerField;

    protected BooleanField releaseLotField;

    protected EntityFormField lotActionField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		try {
            subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_BUTTON_CANCEL), this::cancelJobAdapter);
            subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_BUTTON_RELEASE), this::releaseJobAdapter);
            subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_BUTTON_REFRESH), this::refreshAdapter);

			// glcForm
            jobGlcFormField = form.getFieldByControlId(CONTROL_FIELD_FORM_JOB, GlcFormField.class);
			lotGlcFormField = form.getFieldByControlId(CONTROL_FIELD_FORM_LOT, GlcFormField.class);

            // Jobģ��
            jobTableManagerField = jobGlcFormField.getFieldByControlId(CONTROL_FIELD_JOBLIST, ListTableManagerField.class);
            jobDetailTableManagerField = jobGlcFormField.getFieldByControlId(CONTROL_FIELD_JOBDETAIL, ListTableManagerField.class);

            subscribeAndExecute(eventBroker, jobTableManagerField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::jobSelectionChanged);

			// Lotģ��
            lotTableManagerField = lotGlcFormField.getFieldByControlId(CONTROL_FIELD_LOTLIST, ListTableManagerField.class);
            releaseLotField = lotGlcFormField.getFieldByControlId(CONTROL_FIELD_RELEASELOT, BooleanField.class);
            lotActionField = lotGlcFormField.getFieldByControlId(CONTROL_FIELD_LOTACTION, EntityFormField.class);

            if (partService != null) {
				partService.addPartListener((IPartListener) this);
			}
            
            refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

    private void refreshAdapter(Object obj) {
        try {
            ADManager adManager = Framework.getService(ADManager.class);
            
            String whereCluase = " holdState = 'On' and jobState not in('CANCEL','DONE')";
            String orderBy = "batchId , jobSeqNo";
            
            List<LotSortingJob> sortingJobs = adManager.getEntityList(Env.getOrgRrn(), LotSortingJob.class, Integer.MAX_VALUE, whereCluase, orderBy);
            jobTableManagerField.getListTableManager().setInput(sortingJobs);

            jobDetailTableManagerField.getListTableManager().setInput(Lists.newArrayList());
            jobDetailTableManagerField.refresh();
            lotTableManagerField.getListTableManager().setInput(Lists.newArrayList());
            lotTableManagerField.refresh();

            releaseLotField.setValue(false);
            releaseLotField.refresh();

            lotActionField.setValue(new LotAction());
            lotActionField.refresh();

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    private void jobSelectionChanged(Object obj) {
		try {
            Object selectedObject = jobTableManagerField.getListTableManager().getSelectedObject();

            if (selectedObject != null) {
                LotSortingJob sortingJob = (LotSortingJob) selectedObject;

                ADManager adManager = Framework.getService(ADManager.class);
                LotManager lotManager = Framework.getService(LotManager.class);

                List<LotSortingJobDetail> jobDetails = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Integer.MAX_VALUE, " jobRrn = '" + sortingJob.getObjectRrn() + "'", null);

                jobDetailTableManagerField.getListTableManager().setInput(jobDetails);

                Set<Long> allLotRrns = Sets.newHashSet();
                for (LotSortingJobDetail detail : jobDetails) {
                    if (LotSortingJob.ACTION_TYPE_STARTASSIGN.equals(sortingJob.getActionType())) {
                        if (detail.getToLotRrn() != null) {
                            allLotRrns.add(detail.getToLotRrn());
                        }
                    } else {
                        allLotRrns.add(detail.getFromLotRrn());
                        if (detail.getToLotRrn() != null) {
                            allLotRrns.add(detail.getToLotRrn());
                        }
                    }
                }

                List<Lot> lots = Lists.newArrayList();
                allLotRrns.forEach(lotRrn -> {
                    Lot lot = lotManager.getLot(lotRrn);
                    lots.add(lot);
                });

                lotTableManagerField.getListTableManager().setInput(lots);

            }
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
		}
	}

    private void cancelJobAdapter(Object object) {
        try {
            Object selectedObject = jobTableManagerField.getListTableManager().getSelectedObject();

            if (selectedObject == null) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }

            LotSortingJob sortingJob = (LotSortingJob) selectedObject;
            
            SortingManager sortingManager = Framework.getService(SortingManager.class);
            
            boolean isReleaseLot = (boolean) releaseLotField.getValue();
            
            LotAction lotAction = (LotAction) lotActionField.getValue();

            if (isReleaseLot && StringUtils.isEmpty(lotAction.getActionCode())) {
                UI.showInfo(Message.getString("wip.release_code_empty"));
                return;
            }

            if (UI.showConfirm(Message.getString("wip.confirm.cancel_batch_job"))) {
                sortingManager.cancelSortingJob(sortingJob, true, isReleaseLot, lotAction, Env.getSessionContext());

                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
                refreshAdapter(null);
            }

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }

    }

    private void releaseJobAdapter(Object object) {
        try {
             Object selectedObject = jobTableManagerField.getListTableManager().getSelectedObject();
            
             if (selectedObject == null) {
                 UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                 return;
             }
            
             LotSortingJob sortingJob = (LotSortingJob) selectedObject;
             
             SortingManager sortingManager = Framework.getService(SortingManager.class);
             
             List<LotSortingJob> sortingJobs = sortingManager.getSortingJobByBatch(Env.getOrgRrn(), sortingJob.getBatchId());
             
            Optional<LotSortingJob> optional = sortingJobs.stream().filter(job -> LotSortingJob.STATE_START.equals(job.getJobState())).findAny();
            if (optional.isPresent()) {
                UI.showInfo(Message.getString("wip.start_job_cannot_release"));
                return;
            }

            boolean isReleaseLot = (boolean) releaseLotField.getValue();

            LotAction lotAction = (LotAction) lotActionField.getValue();

            if (isReleaseLot && StringUtils.isEmpty(lotAction.getActionCode())) {
                UI.showInfo(Message.getString("wip.release_code_empty"));
                return;
            }

            if (UI.showConfirm(Message.getString("wip.confirm.release_batch_job"))) {
                sortingManager.releaseSortingJob(sortingJob, true, isReleaseLot, lotAction, Env.getSessionContext());

                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
                refreshAdapter(null);
            }

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }
    
    /**
	 * ������ת
	 */
	@Override
	public void partActivated(MPart part) {
		if (part.equals(mPart)) {
			try {
				//���������ǵ�ǰMPart
				ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);	
				//����ͨ���Ҽ��˵���ʾҳ��,Attribute1��¼���κ�
				if (!StringUtil.isEmpty(adEditor.getAttribute1())) {
					SortingManager manager = Framework.getService(SortingManager.class);
		            String batchId = adEditor.getAttribute1();
		            
		            List<LotSortingJob> sortingJobs = manager.getSortingJobByBatch(Env.getOrgRrn(), batchId);
		            jobTableManagerField.getListTableManager().setInput(sortingJobs);

		            jobDetailTableManagerField.getListTableManager().setInput(Lists.newArrayList());
		            jobDetailTableManagerField.refresh();
		            lotTableManagerField.getListTableManager().setInput(Lists.newArrayList());
		            lotTableManagerField.refresh();

		            releaseLotField.setValue(false);
		            releaseLotField.refresh();

		            lotActionField.setValue(new LotAction());
		            lotActionField.refresh();
				}
	        } catch (Exception e) {
	            ExceptionHandlerManager.asyncHandleException(e);
	        }
		}
	}

	@Override
	public void partBroughtToTop(MPart part) {
	}

	@Override
	public void partDeactivated(MPart part) {
	}

	@Override
	public void partHidden(MPart part) {
	}

	@Override
	public void partVisible(MPart part) {
	}
	
	@PreDestroy
	public void preDestroy() {
		if (partService != null) {
			partService.removePartListener(this);
		}
	}

}
