package com.glory.mes.wip.lot.sorting.job;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.glory.mes.wip.sorting.LotSortingJobDetail;
import com.glory.framework.core.exception.ExceptionBundle;

public class SortingJobChangeDurableGlcDialog extends GlcBaseDialog {

	protected LotSortingJob lotSortingJob;
	
	protected SortingJobGlcEditor sortingJobGlcEditor;
	protected Boolean isSelectChange = true;
	
	public static final String CONTROL_FIELD_LEFT_GLCFORM = "changeCarrier";
	public static final String CONTROL_FIELD_LOTSORTINGJOB_LIST = "lotSortingJobList";
	public static final String CONTROL_FIELD_CARRIER_QUERY_LIST = "carrierList";
	public static final String CONTROL_FIELD_CARRIER_POSITION_LIST = "carrierPositionList";
	
	public static final String CONTROL_BUTTON_CHANGEDURABLE = "changeDurable";
	public static final String CONTROL_BUTTON_COPYSOURCEPOSITION = "copySourcePosition";
	public static final String CONTROL_BUTTON_CHANGEPOSITION = "changePosition";
	
	public ListTableManagerField lotSortingJobList;
	public QueryFormField carrierQueryList;
	public ListTableManagerField carrierPositionList;
	
	public SortingJobChangeDurableGlcDialog(String adFormName, String authority, IEventBroker eventBroker, LotSortingJob lotSortingJob, SortingJobGlcEditor sortingJobGlcEditor) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
		this.lotSortingJob = lotSortingJob;	
		this.sortingJobGlcEditor = sortingJobGlcEditor;
	}
	
	protected void createButtonsForButtonBar(Composite parent) {		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
	}
	
	protected void createFormAction(GlcForm form) {		
		GlcFormField leftGlcForm = form.getFieldByControlId(CONTROL_FIELD_LEFT_GLCFORM, GlcFormField.class);
		lotSortingJobList = leftGlcForm.getFieldByControlId(CONTROL_FIELD_LOTSORTINGJOB_LIST, ListTableManagerField.class);
		carrierQueryList = leftGlcForm.getFieldByControlId(CONTROL_FIELD_CARRIER_QUERY_LIST, QueryFormField.class);	
		carrierPositionList = form.getFieldByControlId(CONTROL_FIELD_CARRIER_POSITION_LIST, ListTableManagerField.class);
		
//		subscribeAndExecute(eventBroker, carrierQueryList.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		subscribeAndExecute(eventBroker, leftGlcForm.getFullTopic(CONTROL_FIELD_LOTSORTINGJOB_LIST + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		subscribeAndExecute(eventBroker, leftGlcForm.getFullTopic(CONTROL_BUTTON_CHANGEDURABLE), this::changeDurableAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_BUTTON_COPYSOURCEPOSITION), this::copySourcePositionAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_BUTTON_CHANGEPOSITION), this::changePositionAdapter);
		
		init();
	}

	private void init() {
		 try {
			 List<LotSortingJob> lotSortingJobs = new ArrayList<LotSortingJob>();
			 if (!StringUtil.isEmpty(lotSortingJob.getBatchId())) {
				 SortingManager sortingManager = Framework.getService(SortingManager.class);
				 List<LotSortingJob> batchLotSortingJobs = sortingManager.getSortingJobByBatch(Env.getOrgRrn(), lotSortingJob.getBatchId());
				 if (batchLotSortingJobs == null || batchLotSortingJobs.size() == 0) {
					 lotSortingJobs.add(lotSortingJob);
				 } else {
					 lotSortingJobs.addAll(batchLotSortingJobs);
				 }
			 } else {
				 lotSortingJobs.add(lotSortingJob);
			 }
			 lotSortingJobList.setValue(lotSortingJobs);
			 lotSortingJobList.refresh();	 			
		 } catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
			 return;
	    }
	}
	
	private void selectionChanged(Object object) {
		try {
			if(!isSelectChange) {
				isSelectChange = true;
				return;
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			
			Object o = lotSortingJobList.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString("wip.select_sorting_job"));
                return;
        	}
        	LotSortingJob sortingJob = (LotSortingJob) o;
        	
        	List<LotSortingJobDetail> allLotSortingJobs = new ArrayList<LotSortingJobDetail>(); 	
        	
        	if (!StringUtil.isEmpty(sortingJob.getBatchId())) {
        		if (LotSortingJob.ACTION_TYPE_STARTASSIGN.equals(sortingJob.getActionType()) 
   					 || LotSortingJob.ACTION_TYPE_EXCHANGE.equals(sortingJob.getActionType())) {
           		
        			SortingManager sortingManager = Framework.getService(SortingManager.class);
    				List<LotSortingJob> batchLotSortingJobs = sortingManager.getSortingJobByBatch(Env.getOrgRrn(), sortingJob.getBatchId());				
   				 	for (LotSortingJob batchLotSortingJob : batchLotSortingJobs) {
	   				 	List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Env.getMaxResult(), " jobRrn = " + batchLotSortingJob.getObjectRrn(), "");
	                	if (details != null && details.size() > 0) {
	                		allLotSortingJobs.addAll(details);
	                	}
   				 	}
   				} else {
   					List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Env.getMaxResult(), " jobRrn = " + sortingJob.getObjectRrn(), "");
                	if (details != null && details.size() > 0) {
                		allLotSortingJobs.addAll(details);
                	}
   				}
   			} else {
   				List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Env.getMaxResult(), " jobRrn = " + sortingJob.getObjectRrn(), "");
            	if (details != null && details.size() > 0) {
            		allLotSortingJobs.addAll(details);
            	}
   			}
			
        	Collections.sort(allLotSortingJobs, new Comparator<LotSortingJobDetail>() {
                 @Override
                 public int compare(LotSortingJobDetail unit1, LotSortingJobDetail unit2) {
                	 if (StringUtil.isEmpty(unit1.getFromPosition()) || StringUtil.isEmpty(unit2.getFromPosition())) {
                		 return unit1.getComponentId().compareTo(unit2.getComponentId());
                	 }
                	 if (Long.valueOf(unit1.getFromPosition()) >= Long.valueOf(unit2.getFromPosition())) {
                		 return 1;        
                	 } else {
                		 return 0;
                	 }           	       
                 }
             });
        	 
			carrierPositionList.setValue(allLotSortingJobs);
			carrierPositionList.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void changeDurableAdapter(Object object) {
        try {
        	Object o = carrierQueryList.getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString("common.select_durable"));
                return;
        	}
        	Object obj = lotSortingJobList.getListTableManager().getSelectedObject();
        	if (obj == null) {
        		UI.showInfo(Message.getString("wip.select_sorting_job"));
                return;
        	}
        	
        	Carrier toCarrier = (Carrier) o;
        	LotSortingJob sortingJob = (LotSortingJob) obj;
        	
        	if (LotSortingJob.ACTION_TYPE_MERGE.equals(sortingJob.getActionType())) {
        		UI.showError(Message.getString("mm.unsupport_this_batchtype"));
        		return;
        	}
        	
        	if (LotSortingJob.ACTION_TYPE_STARTASSIGN.equals(sortingJob.getActionType()) ||
        			LotSortingJob.ACTION_TYPE_EXCHANGE.equals(sortingJob.getActionType())) {
	        	if (!UI.showConfirm(Message.getString("wip.sorting_job_batch_assign_durable"))) {
	        		return;
	        	}
        	}
        	SortingManager sortingManager = Framework.getService(SortingManager.class);
    		sortingManager.assignSortingJobTargetDurable(sortingJob, toCarrier.getDurableId(), true, Env.getSessionContext());
    		UI.showInfo(Message.getString("common.modify_success"));
    		
    		init();
    		carrierQueryList.refresh();	
			carrierPositionList.setValue(new ArrayList<ComponentUnit>());
        	carrierPositionList.refresh();
        	sortingJobGlcEditor.refreshAdapter(object);
        	isSelectChange = false;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void copySourcePositionAdapter(Object object) {
        try {
        	Object o = lotSortingJobList.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString("wip.select_sorting_job"));
                return;
        	}
        	LotSortingJob sortingJob = (LotSortingJob) o;
        	
        	Map<String, String> positionMap = new HashMap<String, String>();
        	ADManager adManager = Framework.getService(ADManager.class);
        	if (!StringUtil.isEmpty(sortingJob.getBatchId())) {
        		if (LotSortingJob.ACTION_TYPE_STARTASSIGN.equals(sortingJob.getActionType()) 
   					 || LotSortingJob.ACTION_TYPE_EXCHANGE.equals(sortingJob.getActionType())) {
        			SortingManager sortingManager = Framework.getService(SortingManager.class);
    				List<LotSortingJob> batchLotSortingJobs = sortingManager.getSortingJobByBatch(Env.getOrgRrn(), sortingJob.getBatchId());				
   				 	for (LotSortingJob batchLotSortingJob : batchLotSortingJobs) {
	        			List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Env.getMaxResult(), " jobRrn = " + batchLotSortingJob.getObjectRrn(), "");
	                	if (details != null && details.size() > 0) {
	                		for (LotSortingJobDetail detail : details) {
	                			positionMap.put(detail.getComponentId(), detail.getFromPosition());
	                		}
	                	}
   				 	}
        		} else {
        			List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Env.getMaxResult(), " jobRrn = " + sortingJob.getObjectRrn(), "");
                	if (details != null && details.size() > 0) {
                		for (LotSortingJobDetail detail : details) {
                			positionMap.put(detail.getComponentId(), detail.getFromPosition());
                		}
                	}
        		}
        	} else {
        		List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Env.getMaxResult(), " jobRrn = " + sortingJob.getObjectRrn(), "");
            	if (details != null && details.size() > 0) {
            		for (LotSortingJobDetail detail : details) {
            			positionMap.put(detail.getComponentId(), detail.getFromPosition());
            		}
            	}
        	}
        	
        	List<LotSortingJobDetail> lotSortingJobDetails = (List<LotSortingJobDetail>) carrierPositionList.getValue();
        	if (lotSortingJobDetails != null && lotSortingJobDetails.size() > 0) {
        		for (LotSortingJobDetail lotSortingJobDetail : lotSortingJobDetails) {
        			lotSortingJobDetail.setToPosition(positionMap.get(lotSortingJobDetail.getComponentId()));
        		}
        	}
        	carrierPositionList.setValue(lotSortingJobDetails);
        	carrierPositionList.refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void changePositionAdapter(Object object) {
        try {
        	Object o = lotSortingJobList.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString("wip.select_sorting_job"));
                return;
        	}
        	LotSortingJob sortingJob = (LotSortingJob) o;
        	
        	DurableManager durableManager = Framework.getService(DurableManager.class);
        	Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), sortingJob.getToDurableId(), true);
        	
        	Map<String, String> positionMap = new HashMap<String, String>();
        	List<LotSortingJobDetail> lotSortingJobDetails = (List<LotSortingJobDetail>) carrierPositionList.getValue();
        	if (lotSortingJobDetails != null && lotSortingJobDetails.size() > 0) {
        		for (LotSortingJobDetail lotSortingJobDetail : lotSortingJobDetails) {
        			if (StringUtil.isEmpty(lotSortingJobDetail.getToPosition())) {
        				UI.showError(Message.getString("wip.unscrap_position_null"));
        				return;
        			}        			
        			Long toPosition = null;
        			try {
        				toPosition = Long.valueOf(lotSortingJobDetail.getToPosition());
        			} catch (Exception e) {
        				UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInvalidNumber()));
        	            return;
        	        }
        			if (toPosition < 1) {
    					UI.showError(Message.getString("wip.component_position_is_not_exist"));
        				return;
    				}       			
        			if (targetCarrier.getCapacity() != null) {
        				if (toPosition > targetCarrier.getCapacity().longValue()) {
        					UI.showError(Message.getString("wip.component_position_is_not_exist"));
            				return;
        				}
        			}
        			if (positionMap.containsKey(lotSortingJobDetail.getComponentId())) {
        				UI.showError(Message.getString("wip.unscrap_multiple_position"));
        				return;
        			} 
        			if (positionMap.containsValue(lotSortingJobDetail.getToPosition())) {
        				UI.showError(Message.getString("wip.component_position_is_repeate"));
        				return;
        			}
        			positionMap.put(lotSortingJobDetail.getComponentId(), lotSortingJobDetail.getToPosition());
        		}
        	}
        	
        	SortingManager sortingManager = Framework.getService(SortingManager.class);
        	sortingManager.changeSortingJobTargetPosition(sortingJob, positionMap, Env.getSessionContext());
        	UI.showInfo(Message.getString("common.modify_success"));
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
}
