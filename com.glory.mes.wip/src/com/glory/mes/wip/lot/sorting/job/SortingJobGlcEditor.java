package com.glory.mes.wip.lot.sorting.job;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.AuthoritySquareButton;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.glory.mes.wip.sorting.LotSortingJobDetail;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class SortingJobGlcEditor extends GlcEditor {

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.sorting.job.SortingJobGlcEditor";
	
	public static final String DIALOG_GLCFORM_CHANGEDURABLE = "WIPLotSortingJobChangeManageForm";
	
	public static final String CONTROL_FIELD_SORTINGJOB_LEFT_INFO = "sortingJobLeftInfo";
	public static final String CONTROL_FIELD_SORTINGJOB_RIGHT_INFO = "sortingJobRightInfo";
	public static final String CONTROL_FIELD_EQUIPMENT_INFO = "equipmentInfo";
	public static final String CONTROL_FIELD_EQUIPMENT_ID = "equipmentId";
	public static final String CONTROL_FIELD_PORT_INFO = "portInfo";
	public static final String CONTROL_FIELD_CURRENT_SORTINGJOB = "currentSortingJob";
	public static final String CONTROL_FIELD_WAIT_SORTINGJOB = "waitSortingJob";
	public static final String CONTROL_FIELD_FROMPORT = "fromPort";
	public static final String CONTROL_FIELD_TOPORT = "toPort";
	public static final String CONTROL_FIELD_CURRENT_FROMPORT = "currentFromPort";
	public static final String CONTROL_FIELD_CURRENT_TOPORT = "currentToPort";
	
	public static final String CONTROL_BUTTON_UNRESERVE = "unReserve";
	public static final String CONTROL_BUTTON_MANUALCOMPLETE = "manualComplete";
	public static final String CONTROL_BUTTON_RESERVE = "reserve";
	public static final String CONTROL_BUTTON_CANCELJOB = "cancelJob";
	public static final String CONTROL_BUTTON_REFRESH = "refresh";
	public static final String CONTROL_BUTTON_SETUPDURABLE = "setupDurable";
	public static final String CONTROL_BUTTON_SETUPWAITDURABLE = "setupWaitDurable";
	public static final String CONTROL_BUTTON_SETTHEPORT = "setThePort";
		
	public static final String BUTTON_MANUALCOMPLETE = "manualComplete";
	public static final String BUTTON_SETTHEPORT = "setThePort";
	public static final String BUTTON_SETUPDURABLE = "setupDurable";
	public static final String BUTTON_UNRESERVE = "unReserve";
	
	public GlcFormField sortingJobLeftInfoField; 
	public ListTableManagerField sortingJobDetailField;
	public EntityFormField equipmentInfoField; 
	public RefTableField equipmentIdField; 
	public ListTableManagerField protInfoField;
	public ListTableManagerField currentSortingJobField;
	public ListTableManagerField waitSortingJobField;
	public RefTableField fromPortField, currentFromPortField;
	public RefTableField toPortField, currentToPortField;
	
	public ADManager adManager;
	public RASManager rasManager;
	public SortingManager sortingManager;
	public DurableManager durableManager;
	public CarrierLotManager carrierLotManager;
	
	private AuthoritySquareButton itemManualComplete;
	private AuthoritySquareButton itemSetThePort;
	private AuthoritySquareButton itemSetupDurable;
	private AuthoritySquareButton itemUnReserve;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);	
		
		try {
			adManager = Framework.getService(ADManager.class);
			rasManager = Framework.getService(RASManager.class);
	    	sortingManager = Framework.getService(SortingManager.class);
	    	durableManager = Framework.getService(DurableManager.class);
	    	carrierLotManager = Framework.getService(CarrierLotManager.class);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
    	
		//��ȡ��ߵ�GlcForm�ؼ�
		sortingJobLeftInfoField = form.getFieldByControlId(CONTROL_FIELD_SORTINGJOB_LEFT_INFO, GlcFormField.class);
		//��ȡ�ұߵ�ListTableManager�ؼ�
		sortingJobDetailField = form.getFieldByControlId(CONTROL_FIELD_SORTINGJOB_RIGHT_INFO, ListTableManagerField.class);
				
		equipmentInfoField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_EQUIPMENT_INFO, EntityFormField.class);		
		protInfoField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_PORT_INFO, ListTableManagerField.class);
		currentSortingJobField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_CURRENT_SORTINGJOB, ListTableManagerField.class);
		currentSortingJobField.getListTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection)event.getSelection();
				if (selection != null) {
					LotSortingJob selectLotSortingJob = (LotSortingJob) selection.getFirstElement();
					if (selectLotSortingJob != null) {
						if(LotSortingJob.STATE_START.equals(selectLotSortingJob.getJobState())) {
							statusChanged(1);
						} else if(LotSortingJob.STATE_DOWNLOAD.equals(selectLotSortingJob.getJobState())) {
							statusChanged(2);
						} else {
							statusChanged(0);
						}
						
						loadLotSortingJobDetail(selectLotSortingJob.getObjectRrn());	
					}
				}
						
			}			
		});
		waitSortingJobField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_WAIT_SORTINGJOB, ListTableManagerField.class);
		waitSortingJobField.getListTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection)event.getSelection();
				if (selection != null) {
					LotSortingJob selectLotSortingJob = (LotSortingJob) selection.getFirstElement();
					if (selectLotSortingJob != null) {
						loadLotSortingJobDetail(selectLotSortingJob.getObjectRrn());	
					}
				}							
			}			
		});
		fromPortField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_FROMPORT, RefTableField.class);
		toPortField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_TOPORT, RefTableField.class);
		currentFromPortField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_CURRENT_FROMPORT, RefTableField.class);
		currentToPortField = sortingJobLeftInfoField.getFieldByControlId(CONTROL_FIELD_CURRENT_TOPORT, RefTableField.class);
		
		equipmentIdField = equipmentInfoField.getFieldByControlId(CONTROL_FIELD_EQUIPMENT_ID, RefTableField.class);		
		equipmentIdField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object sender, Object newValue) {	
				if (!StringUtil.isEmpty((String)newValue)) {
					load((String)newValue);
				}				
			}		
		});	
		
		//ȡ��Ԥ���¼�
		subscribeAndExecute(eventBroker, sortingJobLeftInfoField.getFullTopic(CONTROL_BUTTON_UNRESERVE), this::unReserveAdapter);
				
		//Ԥ���豸�¼�
		subscribeAndExecute(eventBroker, sortingJobLeftInfoField.getFullTopic(CONTROL_BUTTON_RESERVE), this::reserveAdapter);			
				
		//ȡ�������¼�
		subscribeAndExecute(eventBroker, sortingJobLeftInfoField.getFullTopic(CONTROL_BUTTON_CANCELJOB), this::cancelJobAdapter);	
		
		//���õ�ǰsort�����Ŀ���ؾ��¼�
		subscribeAndExecute(eventBroker, sortingJobLeftInfoField.getFullTopic(CONTROL_BUTTON_SETUPDURABLE), this::setupCurrentDurableAdapter);
		
		//���õȴ�sort�����Ŀ���ؾ��¼�
		subscribeAndExecute(eventBroker, sortingJobLeftInfoField.getFullTopic(CONTROL_BUTTON_SETUPWAITDURABLE), this::setupWaitDurableAdapter);

		//����Port���¼�
		subscribeAndExecute(eventBroker, sortingJobLeftInfoField.getFullTopic(CONTROL_BUTTON_SETTHEPORT), this::setPortAdapter);
				
		//�ֶ�����¼�
		subscribeAndExecute(eventBroker, sortingJobLeftInfoField.getFullTopic(CONTROL_BUTTON_MANUALCOMPLETE), this::manualCompleteAdapter);
		
		//ˢ���¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_BUTTON_REFRESH), this::refreshAdapter);		
		
		init();
	}
	
	private void init() {
		itemManualComplete = (AuthoritySquareButton) sortingJobLeftInfoField.getButtonByControl(BUTTON_MANUALCOMPLETE);
		itemSetThePort = (AuthoritySquareButton) sortingJobLeftInfoField.getButtonByControl(BUTTON_SETTHEPORT);
		itemSetupDurable = (AuthoritySquareButton) sortingJobLeftInfoField.getButtonByControl(BUTTON_SETUPDURABLE);
		itemUnReserve = (AuthoritySquareButton) sortingJobLeftInfoField.getButtonByControl(BUTTON_UNRESERVE);
		
		statusChanged(0);
	}
	
	private void statusChanged(int number) {
		//1
		if(1 == number) {
			itemManualComplete.setEnabled(false);
			itemSetThePort.setEnabled(false);
			itemSetupDurable.setEnabled(false);
			itemUnReserve.setEnabled(false);
		} else if (2 == number) {
			itemManualComplete.setEnabled(false);
			itemSetThePort.setEnabled(false);
			itemSetupDurable.setEnabled(false);
			itemUnReserve.setEnabled(true);
		} else {
			itemManualComplete.setEnabled(true);
			itemSetThePort.setEnabled(true);
			itemSetupDurable.setEnabled(true);
			itemUnReserve.setEnabled(true);
		}
	}

	//�����豸�����Ϣ
	protected void load(String equipmentId) {
		loadEquipment(equipmentId);
		loadCurrentSortingJob(equipmentId);
		loadWaitSortingJob(equipmentId);
		loadLotSortingJobDetail(null);
	}
	
	//�����豸��Ϣ������Port
	protected void loadEquipment(String equipmentId) {
        try {   	
        	//��ȡ�豸��Ϣ
        	if (!StringUtil.isEmpty(equipmentId)) {
	        	Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentId);
	        	equipmentInfoField.setValue(equipment);
	        	equipmentInfoField.refresh();
	        	//��ȡ�豸�˿���Ϣ
	        	List<Port> ports = rasManager.getPortsByEquipment(Env.getOrgRrn(), equipmentId);
	        	if (ports != null && ports.size() > 0) {
	        		for (Port port : ports) {
	        			Carrier carrier = durableManager.getCarrierByPortId(Env.getOrgRrn(), equipmentId, port.getPortId());
	        			if (carrier != null) {
	        				port.setCurrentDurable(carrier.getDurableId());
	        				Lot lot = carrierLotManager.getLotByCarrierId(Env.getOrgRrn(), carrier.getDurableId());
	        				if (lot != null) {
	        					port.setCurrentLotId(lot.getLotId());
	        				}
	        			}
	            	}
	        	}   	
	        	protInfoField.setValue(ports);   
	        	loadEquipmentPort(ports);
        	} else {
        		protInfoField.setValue(new ArrayList<Port>());	 
        		loadEquipmentPort(new ArrayList<Port>());
        	}
        	protInfoField.refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            protInfoField.setValue(new ArrayList<Port>());	 
    		loadEquipmentPort(new ArrayList<Port>());
    		protInfoField.refresh();
            return;
        }
    }
	
	protected void setupCurrentDurableAdapter(Object object) {
        try {
        	Object o = currentSortingJobField.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	LotSortingJob lotSortingJob = (LotSortingJob) o;
        	if (lotSortingJob.getObjectRrn() != null) {
        		SortingJobChangeDurableGlcDialog glcDialog = new SortingJobChangeDurableGlcDialog(DIALOG_GLCFORM_CHANGEDURABLE, null, eventBroker, lotSortingJob, this);
        		if (glcDialog.open() == Dialog.OK) {
        			refreshAdapter(object);
        		}
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void setupWaitDurableAdapter(Object object) {
        try {
        	Object obj = waitSortingJobField.getListTableManager().getSelectedObject();
        	if (obj == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	LotSortingJob lotSortingJob = (LotSortingJob) obj;
        	if (lotSortingJob.getObjectRrn() != null) {
        		SortingJobChangeDurableGlcDialog glcDialog = new SortingJobChangeDurableGlcDialog(DIALOG_GLCFORM_CHANGEDURABLE, null, eventBroker, lotSortingJob, this);
        		if (glcDialog.open() == Dialog.OK) {
        			refreshAdapter(object);
        		}
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	//���ص�ǰSortingJob
	protected void loadCurrentSortingJob(String equipmentId) {
        try {
        	if (!StringUtil.isEmpty(equipmentId)) {
	        	//��ȡ���Sorting���͹�������,��ʱ��δ����SortingJob����ҲҪ�Ž���
	        	/*List<LotSortingJob> currentLotSortingJobBySortingSteps = new ArrayList<LotSortingJob>();        	    	
	        	List<LotSortingJob> lotSortingJobBySortingSteps = sortingManager.getSortingJobBySortingStep(Env.getOrgRrn(), equipmentId, Env.getSessionContext());
	        	for (LotSortingJob lotSortingJobBySortingStep : lotSortingJobBySortingSteps) {
	        		if (!StringUtil.isEmpty(lotSortingJobBySortingStep.getEquipmentId())) {
	        			if (equipmentId.equals(lotSortingJobBySortingStep.getEquipmentId())) {
	        				currentLotSortingJobBySortingSteps.add(lotSortingJobBySortingStep);
	        			}
	        		}
	        	}*/
	        	
	        	//��ȡ�豸��ǰSorting����
	        	// List<LotSortingJob> allCurrentLotSortingJobs = new ArrayList<LotSortingJob>();
	        	List<String> jobStates = new ArrayList<String>();
	        	jobStates.add(LotSortingJob.STATE_START); 
	        	jobStates.add(LotSortingJob.STATE_READY);
	        	jobStates.add(LotSortingJob.STATE_PREPARE);     	
	        	jobStates.add(LotSortingJob.STATE_DOWNLOAD);     	
	        	List<LotSortingJob> currentLotSortingJobs = sortingManager.getSortingJobs(Env.getOrgRrn(), equipmentId, null, jobStates, " priority, created ");      	
	        	
	        	/*if (currentLotSortingJobBySortingSteps.size() > 0) {
	        		for (LotSortingJob currentLotSortingJobBySortingStep : currentLotSortingJobBySortingSteps) {
	        			if (currentLotSortingJobs != null && currentLotSortingJobs.size() > 0) {
	        				boolean flag = true;
	        				for (LotSortingJob currentLotSortingJob : currentLotSortingJobs) {
	                    		if (currentLotSortingJobBySortingStep.getLotRrn().equals(currentLotSortingJob.getLotRrn())) {
	                    			flag = false;
	                    			break;
	                    		}
	                    	}
	        				if (flag) {
	        					allCurrentLotSortingJobs.add(currentLotSortingJobBySortingStep);
	        				}
	        			} else {
	        				allCurrentLotSortingJobs.add(currentLotSortingJobBySortingStep);
	        			}
	            	}
	        	}*/
	        	currentSortingJobField.setValue(currentLotSortingJobs);
        	} else {
        		currentSortingJobField.setValue(new ArrayList<LotSortingJob>());
        	}
        	currentSortingJobField.refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	//���صȴ�SortingJob
	protected void loadWaitSortingJob(String equipmentId) {
        try {
        	if (!StringUtil.isEmpty(equipmentId)) {
	        	//��ȡ���Sorting���͹�������,��ʱ��δ����SortingJob����ҲҪ�Ž���      
	        	/*List<LotSortingJob> waitLotSortingJobBySortingSteps = new ArrayList<LotSortingJob>();       	
	        	List<LotSortingJob> lotSortingJobBySortingSteps = sortingManager.getSortingJobBySortingStep(Env.getOrgRrn(), equipmentId, Env.getSessionContext());
	        	for (LotSortingJob lotSortingJobBySortingStep : lotSortingJobBySortingSteps) {
	        		if (StringUtil.isEmpty(lotSortingJobBySortingStep.getEquipmentId())) {
	        			waitLotSortingJobBySortingSteps.add(lotSortingJobBySortingStep);
	        		} 
	        	}
	        	
	        	//��ȡ�ȴ�Sorting����
	        	List<LotSortingJob> allWaitLotSortingJobs = new ArrayList<LotSortingJob>();*/
	        	List<LotSortingJob> waitLotSortingJobs = adManager.getEntityList(Env.getOrgRrn(), 
	        			LotSortingJob.class, Integer.MAX_VALUE, " (jobState = '" + LotSortingJob.STATE_PREPARE + "' OR jobState = '" + LotSortingJob.STATE_READY + "') AND (equipmentId is null or equipmentId = '')", "priority, created");
	        	
	        	/*if (waitLotSortingJobBySortingSteps.size() > 0) {
	        		for (LotSortingJob waitLotSortingJobBySortingStep : waitLotSortingJobBySortingSteps) {
	        			if (waitLotSortingJobs != null && waitLotSortingJobs.size() > 0) {
	        				boolean flag = true;
	        				for (LotSortingJob waitLotSortingJob : waitLotSortingJobs) {
	                    		if (waitLotSortingJobBySortingStep.getLotRrn().equals(waitLotSortingJob.getLotRrn())) {
	                    			flag = false;
	                    			break;
	                    		}
	                    	}
	        				if (flag) {
	        					allWaitLotSortingJobs.add(waitLotSortingJobBySortingStep);
	        				}
	        			} else {
	        				allWaitLotSortingJobs.add(waitLotSortingJobBySortingStep);
	        			}
	            	}
	        	}*/
        		waitSortingJobField.setValue(waitLotSortingJobs);
        	} else {
        		waitSortingJobField.setValue(new ArrayList<LotSortingJob>());
        	}
        	waitSortingJobField.refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	//����Port
	protected void loadEquipmentPort(List<Port> ports) {
        try {
        	List<Port> plbs = new ArrayList<Port>();
        	List<Port> pubs = new ArrayList<Port>();
        	if (ports != null && ports.size() > 0) {
        		Port nullPort = new Port();
        		nullPort.setTransferState(null);
        		plbs.add(nullPort);
        		pubs.add(nullPort);
        		for (Port port : ports) {
        			if (Port.PORTTYPE_PL.equals(port.getPortType())) {
        				plbs.add(port);
        			} else if (Port.PORTTYPE_PU.equals(port.getPortType())) {
        				pubs.add(port);
        			} else if (Port.PORTTYPE_PB.equals(port.getPortType())) {
        				plbs.add(port);
        				pubs.add(port);
        			}
            	}
        	}
        	fromPortField.getComboControl().setInput(plbs);	
	        toPortField.getComboControl().setInput(pubs);
	        currentFromPortField.getComboControl().setInput(plbs);	
	        currentToPortField.getComboControl().setInput(pubs);
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }	
	
	protected void loadLotSortingJobDetail(Long jobRrn) {
        try {
        	if (jobRrn != null) {
        		ADManager adManager = Framework.getService(ADManager.class);
            	List<LotSortingJobDetail> details = adManager.getEntityList(Env.getOrgRrn(), LotSortingJobDetail.class, Integer.MAX_VALUE, " jobRrn = " + jobRrn, "");
            	orderbySortingJobDetail(details);
            	sortingJobDetailField.setValue(details);
            	sortingJobDetailField.refresh();
        	} else {
        		sortingJobDetailField.setValue(new ArrayList<LotSortingJobDetail>());
            	sortingJobDetailField.refresh();
        	} 	
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	/**
	 * ȡ��Ԥ��
	 * @param object
	 */
	protected void unReserveAdapter(Object object) {
        try {
        	Object o = currentSortingJobField.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	LotSortingJob lotSortingJob = (LotSortingJob) o;
        	if (lotSortingJob.getObjectRrn() != null) {
        		sortingManager.unReserveSortingJob(lotSortingJob, Env.getSessionContext());
        		UI.showInfo(Message.getString("common.unreserve_success"));
            	loadCurrentSortingJob((String)equipmentIdField.getValue());
            	loadWaitSortingJob((String)equipmentIdField.getValue()); 
            	loadLotSortingJobDetail(null);
        	} else {
        		UI.showInfo(Message.getString("wip.lot_sorting_job_not_create"));
        	}     	
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	/**
	 * Ԥ���豸
	 * @param object
	 */
	protected void reserveAdapter(Object object) {
        try {
        	Object o = waitSortingJobField.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	String fromPort = (String) fromPortField.getValue();
        	String toPort = (String) toPortField.getValue();
        	if (StringUtil.isEmpty(fromPort) || StringUtil.isEmpty(toPort)) {
        		UI.showError(Message.getString("wip.lot_sorting_job_select_port"));
                return;
        	}
        	
        	LotSortingJob lotSortingJob = (LotSortingJob) o;
        	if (lotSortingJob.getObjectRrn() != null) {	
        		sortingManager.reserveSortingJob(lotSortingJob, (String)equipmentIdField.getValue(), fromPort, toPort, lotSortingJob.getToDurableId(), Env.getSessionContext());
        		UI.showInfo(Message.getString("common.reserve_success"));
        		loadCurrentSortingJob((String)equipmentIdField.getValue());
        		loadWaitSortingJob((String)equipmentIdField.getValue());  
        		loadLotSortingJobDetail(lotSortingJob.getObjectRrn());
        	} else {
        		UI.showInfo(Message.getString("wip.lot_sorting_job_not_create"));
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	/**
	 * ����Port��
	 * @param object
	 */
	protected void setPortAdapter(Object object) {
        try {
        	Object o = currentSortingJobField.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	String fromPort = (String) currentFromPortField.getValue();
        	String toPort = (String) currentToPortField.getValue();
        	if (StringUtil.isEmpty(fromPort) || StringUtil.isEmpty(toPort)) {
        		UI.showError(Message.getString("wip.lot_sorting_job_select_port"));
                return;
        	}
        	
        	LotSortingJob lotSortingJob = (LotSortingJob) o;
        	if (lotSortingJob.getObjectRrn() != null) {	
        		sortingManager.reserveSortingJob(lotSortingJob, lotSortingJob.getEquipmentId(), fromPort, toPort, lotSortingJob.getToDurableId(), Env.getSessionContext());
        		UI.showInfo(Message.getString("common.setport_success"));
        		loadCurrentSortingJob((String)equipmentIdField.getValue());
        		loadWaitSortingJob((String)equipmentIdField.getValue());  
        		loadLotSortingJobDetail(lotSortingJob.getObjectRrn());
        	} else {
        		UI.showInfo(Message.getString("wip.lot_sorting_job_not_create"));
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	/**
	 * ȡ������
	 * @param object
	 */
	protected void cancelJobAdapter(Object object) {
        try {
        	Object o = waitSortingJobField.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	LotSortingJob lotSortingJob = (LotSortingJob) o;
        	if (lotSortingJob.getObjectRrn() != null) {
                if (StringUtils.equals(LotSortingJob.SORTING_MODE_EXCHNAGE_DOWNGRADE, lotSortingJob.getSortingMode())) {
                    UI.showInfo(Message.getString("wip.downgrade_lot_sorting_job_cannot_cancel"));
                    return;
                }

	        	if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmCancel()))) {
	        		sortingManager.cancelSortingJob(lotSortingJob, Env.getSessionContext());      		
	        		UI.showInfo(Message.getString("common.cancel_success"));
	        		loadWaitSortingJob((String)equipmentIdField.getValue());
	        		loadLotSortingJobDetail(lotSortingJob.getObjectRrn());
	        	} 	
        	} else {
        		UI.showInfo(Message.getString("wip.lot_sorting_job_not_create"));
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	/**
	 * �ֶ����
	 * @param object
	 */
	protected void manualCompleteAdapter(Object object) {
        try {
        	Object o = currentSortingJobField.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	LotSortingJob lotSortingJob = (LotSortingJob) o;
        	if (lotSortingJob.getObjectRrn() != null) {
	        	if (UI.showConfirm(Message.getString("common.submit_confirm"))) {
	        		List<LotSortingJob> lotSortingJobs = Lists.newArrayList();
                    // if (!StringUtil.isEmpty(lotSortingJob.getBatchId())) {
                    // List<LotSortingJob> batchLotSortingJobs = sortingManager.getSortingJobByBatch(Env.getOrgRrn(),
                    // lotSortingJob.getBatchId());
                    // lotSortingJobs.addAll(batchLotSortingJobs);
                    // } else {
                    lotSortingJobs.add(lotSortingJob);
                    // }
            		durableManager.checkCarrierDirty(Env.getSessionContext().getOrgRrn(), Lists.newArrayList(lotSortingJob.getToDurableId()), true);
                    
	        		List<LotSortingJob> sortingJobs = sortingManager.completeLotSortingJobByJobType(lotSortingJobs, Env.getSessionContext());  
	        		if(LotSortingJob.ACTION_TYPE_SPLIT.equals(lotSortingJob.getActionType())) {
	        			for(LotSortingJob job : sortingJobs) {
	        				job.setLotId("SubLot :" + job.getLotId());
	        			}
	        			String lots = sortingJobs.stream().map(LotSortingJob :: getLotId).collect(Collectors.joining(";"));
	        			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()) + lots);
	        		}else {
	        			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
	        		}
	        		loadCurrentSortingJob((String)equipmentIdField.getValue());
	            	loadWaitSortingJob((String)equipmentIdField.getValue());
	            	loadLotSortingJobDetail(null);
	        	}
        	} else {
        		UI.showInfo(Message.getString("wip.lot_sorting_job_not_create"));
        	}    
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	
	/**
	 * JAVA��������ComponentUnit����
	 * @ComponentUnit
	 */
   private void orderbySortingJobDetail(List<LotSortingJobDetail> list) {
		Collections.sort(list ,new Comparator<LotSortingJobDetail>() {
			public int compare(LotSortingJobDetail o1,LotSortingJobDetail o2) {
				if(o2.getComponentId().compareTo(o1.getComponentId()) < 0) {
					return 1;
				}else if (o2.getComponentId().compareTo(o1.getComponentId()) == 0) {
					return 0;
				}else {
					return -1;
				}
			}
		});
	}
	
	/**
	 * ˢ�°�ť
	 * @param object
	 */
	protected void refreshAdapter(Object object) {
        try {
        	String equipmentId = (String) equipmentIdField.getValue();
        	loadEquipment(equipmentId);
    		loadCurrentSortingJob(equipmentId);
    		loadWaitSortingJob(equipmentId);
    		loadLotSortingJobDetail(null);
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
}
