package com.glory.mes.wip.lot.assign;

import java.math.BigDecimal;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.lot.AbstractAssignComposite;
import com.glory.mes.wip.model.Lot;

@Deprecated
public class LotAssignQtyComposite extends AbstractAssignComposite {

	private Label label;

	public LotAssignQtyComposite(Composite parent, int style, Carrier targetCarrier, Lot sourceLot) {
		super(parent, style);
		setTargetCarrier(targetCarrier);
		setSourceLot(sourceLot);
		createForm();
	}

	@Override
	protected void createAssignComposite() {
		Lot lot = getSourceLot();
		Carrier carrier = getTargetCarrier();
		if (lot != null && carrier != null) {
			label = new Label(this, SWT.NONE);
			BigDecimal carrierLeftQty = carrier.getCapacity().subtract(carrier.getCurrentQty());
			BigDecimal lotQty = lot.getMainQty();
			label.setText(String.format(Message.getString("wip.carrier_assign_qty_lot_info"), 
					carrierLeftQty, lotQty));
			
			if (carrierLeftQty.compareTo(lotQty) < 0) {
				label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
		}
	}
	
	public void afterAssign() {
		setSourceLot(null);
		Carrier carrier = searchCarrier(getTargetCarrier().getDurableId(), true, true);
		if (label != null) {
			BigDecimal carrierLeftQty = carrier.getCapacity().subtract(carrier.getCurrentQty());
			label.setText(String.format(Message.getString("wip.carrier_assign_qty_lot_info1"), 
					carrierLeftQty));
		}
		
		setTargetCarrier(carrier);
	}
	
}
