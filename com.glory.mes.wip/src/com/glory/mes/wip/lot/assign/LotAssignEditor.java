package com.glory.mes.wip.lot.assign;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.carrier.CarrierLotComposite;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

@Deprecated
public class LotAssignEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.assign.LotAssignEditor";

	public static final String TABLE_NAME = "WIPLotByCarrier";
	
	private LotAssignSection section;
	private EntityForm lotDetailsForm;
		
	private String carrierId;
	
	@Inject
	protected ESelectionService selectionService;
	
	@Inject
	protected MPart mPart;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		try {
			parent.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
			parent.setBackgroundMode(SWT.INHERIT_FORCE);
			
			DurableManager durableManager = Framework.getService(DurableManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			
			ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);

			configureBody(parent);
			
			FormToolkit toolkit = new FormToolkit(UI.getActiveShell().getDisplay());
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_BOTH)); // fill_both �Ż��й�����
			ManagedForm mform = new ManagedForm(toolkit, form);

			Composite body = mform.getForm().getBody();
	        GridLayout layout = new GridLayout(2, false);
	        body.setLayout(layout);
	        body.setLayoutData(new GridData(GridData.FILL_VERTICAL));
//	        body.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));
	        
	        Composite left = toolkit.createComposite(body, SWT.NONE);
			GridData gdLeft = new GridData(GridData.FILL_VERTICAL);
//			gdLeft.widthHint = (int) ((Toolkit.getDefaultToolkit().getScreenSize().width)/2.5);;
			left.setLayout(new GridLayout(1, false));
			left.setLayoutData(gdLeft);
			
			// left carrierLot
			CarrierLotComposite carrierLotComposite = new CarrierLotComposite(left, SWT.BORDER, false);
			carrierLotComposite.createPartControl();

			HeaderText txtCarrierId = carrierLotComposite.getTxtCarrierId();
			txtCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// �س��¼�
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						carrierId = ((Text) event.widget).getText();
					}
				}
			});
			
			// left lotInfo
			Composite lotInfoComposite = toolkit.createComposite(left, SWT.BORDER);
			lotInfoComposite.setLayout(new GridLayout(1, false));
			lotInfoComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
//			lotInfoComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));
			
			Composite lotComposite = toolkit.createComposite(lotInfoComposite, SWT.NONE);
			lotComposite.setLayout(new GridLayout(2, false));
//			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));

			Label lblLotId = new Label(lotComposite, SWT.NONE);
			lblLotId.setText(Message.getString("wip.lot_id"));
			lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

			HeaderText txtLotId = new HeaderText(lotComposite, SWTResourceCache.getImage("header-text-lot"));
			txtLotId.setTextLimit(64);
			txtLotId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						if (StringUtil.isEmpty(carrierId)) {
							UI.showInfo(Message.getString("wip.carrier_scan_carrierid_first"));
							return;
						}
						
						try {
							Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, true);
							
							String lotId = ((Text) event.widget).getText();
							Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, false);
							if (lot == null) {
								txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
								txtLotId.warning();
								return;
							}							
							if (!StringUtil.isEmpty(lot.getDurable())) {
								lotDetailsForm.setObject(new Lot());
								lotDetailsForm.loadFromObject();
								txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
								txtLotId.warning();
								UI.showError(Message.getString("wip.lot_assigned_carrier"));
								return;
							}
							
							section.setTargetCarrier(targetCarrier);
							section.setSourceLot(lot);
							section.reflow();
							
							// Load LotInfo
							lotDetailsForm.setObject(lot);
							txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
							txtLotId.focusing();
							lotDetailsForm.loadFromObject();
						} catch (Exception e) {
							e.printStackTrace();
							ExceptionHandlerManager.asyncHandleException(e);
						}
						
					}
				}
			});
			
			Composite lotDetailsComposite = toolkit.createComposite(lotInfoComposite, SWT.NONE);
			lotDetailsComposite.setLayout(new GridLayout(1, false));
//			lotDetailsComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));

			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotDetailsForm = new EntityForm(lotDetailsComposite, SWT.NONE, lotTable, null);
			
			Composite right = toolkit.createComposite(body, SWT.NONE);
			right.setLayout(new GridLayout(1, false));
			right.setLayoutData(new GridData(GridData.FILL_BOTH));
//			right.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));

			createSection(adTable);
			section.createContents(mform, right);
			section.carrierLotComposite = carrierLotComposite;
			section.lotDetailsForm = lotDetailsForm;
//			mPart.setLabel(Message.getString("wip.assign_carrier"));
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createSection(ADTable adTable) {
		section = new LotAssignSection(adTable);
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
}
