package com.glory.mes.wip.lot.bonding;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotBondingRightComposite extends Composite {

	private static final Logger logger = Logger.getLogger(LotBondingLeftComposite.class);
	
	public static final String UNIT_TYPE_LOT = "Lot";
	public static final String UNIT_TYPE_COMPONENT = "Component";
	
	protected String bondingInputType = "Lot";
	
	private static final String TABLE_NAME = "WIPLotComponentBonding";	
	//Table���Ƿ���ʾcheckBox
	protected boolean checkFlag;
	protected ListTableManager lotComponentTableManager;
	
	protected HeaderText txtCarrierId;
	protected SearchField combo;
	protected HeaderText txtCompoentId;
	
	public SquareButton up, down, delete;
	
//	protected int tableHeigthHint = Toolkit.getDefaultToolkit().getScreenSize().height / 3;
	protected int tableHeigthHint = 550;
	
	public LotBondingRightComposite(Composite parent, int style, boolean checkFlag) {
		super(parent, style);
		this.checkFlag = checkFlag;
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite inputComposite = new Composite(this, SWT.NONE);	
			inputComposite.setLayout(new GridLayout(6, false));
			inputComposite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
			if (UNIT_TYPE_LOT.equals(bondingInputType)) {
				Label lblLotId = new Label(inputComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.bonding_lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
				
//				txtLotId = new Text(inputComposite, SWT.BORDER);
//				txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
//				txtLotId.setLayoutData(gText);
//				txtLotId.setTextLimit(64);				
//				txtLotId.addKeyListener(new KeyAdapter() {
//					@Override
//					public void keyPressed(KeyEvent event) {
//						// �س��¼�
//						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
//							String lotId = ((Text) event.widget).getText();
//							if (!StringUtil.isEmpty(lotId)) {
//								getLotByLotId(lotId);
//							}
//						}
//					}
//				});		
										
				try {
					FormToolkit toolkit = new FormToolkit(Display.getCurrent());
					
					ADManager adManager = Framework.getService(ADManager.class);
					ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "WIPBondingLotQuery");
					ADRefTable refTable = new ADRefTable();
					refTable.setTableRrn(adTable.getObjectRrn());
					refTable.setTextField("lotId");
					refTable.setKeyField("lotId");// getValue()��ֵ

					combo = new SearchField("lotId", adTable, refTable, "", SWT.NULL);
					combo.createContent(inputComposite, toolkit);
					GridData gText = new GridData();
					gText.widthHint = DPIUtil.autoScaleDown(DPIUtil.autoScaleUpUsingNativeDPI(216));
					gText.heightHint = SWTResourceCache.getHeight(FieldType.SEARCH) + 1;
					combo.getControls()[0].setLayoutData(gText);
					combo.getControls()[combo.getControls().length - 1]
							.setEnabled(true);		
					combo.addValueChangeListener(lotRefTableListener);
//					combo.getControls()[0].addKeyListener(new KeyListener() {
//						@Override
//						public void keyPressed(KeyEvent event) {
//							// �س��¼�
//							if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
//								String lotId = combo.getText();
//								if (!StringUtil.isEmpty(lotId)) {
//									getLotByLotId(lotId);
//								}
//							}
//						}	
//						
//						@Override
//						public void keyReleased(KeyEvent e) {}
//					});
					
				} catch (Exception e) {
					e.printStackTrace();
				}
				
		        Label lblCarrierId = new Label(inputComposite, SWT.NONE);
		        lblCarrierId.setText(Message.getString("wip.bonding_carrier_id"));
				lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
				txtCarrierId = new HeaderText(inputComposite, SWTResourceCache.getImage("header-text-carrier"));
				txtCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));	
				txtCarrierId.setTextLimit(32);
				txtCarrierId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// �س��¼�
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String carrierId = ((Text) event.widget).getText();
							if (!StringUtil.isEmpty(carrierId)) {
								getLotsByCarrierId(carrierId);
							}
						}
					}
				});
			} else {
				Label lblComponentId = new Label(inputComposite, SWT.NONE);
				lblComponentId.setText(Message.getString("wip.bonding_component_id"));
				lblComponentId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));		
				txtCompoentId = new HeaderText(inputComposite);
				txtCompoentId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtCompoentId.setTextLimit(32);				
				txtCompoentId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// �س��¼�
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String componentId = ((Text) event.widget).getText();
							if (!StringUtil.isEmpty(componentId)) {
								getComponentByComponentId(componentId);
							}
						}
					}
				});		
			}
			
																
			Composite componentComposite = new Composite(this, SWT.NONE);
			componentComposite.setLayout(new GridLayout(1, false));
			
			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
			gridData.heightHint = tableHeigthHint;
			componentComposite.setLayoutData(gridData);
			componentComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotComponentTableManager = new ListTableManager(lotTable, checkFlag);
			lotComponentTableManager.setAutoSizeFlag(true);
			lotComponentTableManager.newViewer(componentComposite);
			
			Composite btnComposite = new Composite(this, SWT.NONE);
			GridLayout layoutBtn = new GridLayout(3, false);
			btnComposite.setLayout(layoutBtn);					
			GridData gd1 = new GridData(GridData.END);	
			gd1.horizontalAlignment = SWT.RIGHT;		
			btnComposite.setLayoutData(gd1);
			delete = UIControlsFactory.createButton(btnComposite, "  " + Message.getString(ExceptionBundle.bundle.CommonDelete()) + "  ", null);
			delete.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						delAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			//����
			up = UIControlsFactory.createButton(btnComposite, Message.getString("mm.move_up"), null);
			up.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						upAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			//����
			down = UIControlsFactory.createButton(btnComposite, Message.getString("mm.move_down"), null);
			down.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						downAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
		} catch (Exception e) {
			logger.error("LotBondingLeftComposite createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	IValueChangeListener lotRefTableListener = new IValueChangeListener() {
		@Override
		public void valueChanged(Object sender, Object newValue) {
			try {
				if (!(newValue instanceof String)){
					return;
				}
				String value = (String)newValue;
				if (!StringUtil.isEmpty(value)) {
					getLotByLotId(value);
				}
			} catch (Exception e) {
				logger.error("LogEventSection : IValueChangeListener", e);
			}
		}
	};
	
	public void getComponentByComponentId(String componentId) {
		try {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			ComponentUnit componentUnit = componentManager.getComponentByComponentId(Env.getOrgRrn(), componentId);					
			if (componentUnit != null) {
				List<ComponentUnit> componentUnits = new ArrayList<ComponentUnit>();												
				
				List<ComponentUnit> tableComponentUnits = (List<ComponentUnit>) lotComponentTableManager.getInput();
				if (tableComponentUnits != null && tableComponentUnits.size() > 0) {
					for (ComponentUnit tableComponentUnit : tableComponentUnits) {					
						if (componentUnit.getComponentId().equals(tableComponentUnit.getComponentId())) {
							UI.showError("wip.component_is_exist");
							return;
						}										
					}				
				} 
			
				componentUnits.add(componentUnit);
				componentUnits.addAll(tableComponentUnits);								
				lotComponentTableManager.setInput(componentUnits);
				lotComponentTableManager.refresh();		
				txtCompoentId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));										
			} else {
				txtCompoentId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotByLotId(String lotId) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
			if (lot != null) {
				//���Bonding������״̬
	    		if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
	    			UI.showError("error.lot_holdstate_not_allow");
	    			return;
				}
	    		if (!LotStateMachine.COMCLASS_FIN.equals(lot.getComClass())) {
	    			UI.showError("error.lot_state_not_allow");
	    			return;
				}
	    		
				List<ComponentUnit> componentUnits = new ArrayList<ComponentUnit>();
				
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				List<ComponentUnit> lotComponentUnits = componentManager.getComponentsByParentUnitRrn(lot.getObjectRrn());
				if (lotComponentUnits == null) {
					return;
				}
				for (ComponentUnit lotComponentUnit : lotComponentUnits) {
					lotComponentUnit.setLotId(lot.getLotId());
				}
				
				List<ComponentUnit> tableComponentUnits = (List<ComponentUnit>) lotComponentTableManager.getInput();
				if (tableComponentUnits != null && tableComponentUnits.size() > 0) {
					for (ComponentUnit tableComponentUnit : tableComponentUnits) {
						for (ComponentUnit lotComponentUnit : lotComponentUnits) {
							if (lotComponentUnit.getParentUnitRrn().equals(tableComponentUnit.getParentUnitRrn())) {
								UI.showError("wip.component_is_exist");
								return;
							}
						}					
					}				
				} 
		
				componentUnits.addAll(tableComponentUnits);	
				componentUnits.addAll(lotComponentUnits);
					
//				int i = 1;
//				for (ComponentUnit componentUnit : componentUnits) {
//					componentUnit.setPosition(String.valueOf(i));
//					i++;
//				}
				
				lotComponentTableManager.setInput(componentUnits);
				lotComponentTableManager.refresh();														
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (lots != null && lots.size() > 0) {
					for (Lot lot : lots) {
						getLotByLotId(lot.getLotId());
					}
				} else {							
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				}							
			} else {					
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@SuppressWarnings("unchecked")
	protected void upAdapter() {
		try {
			List<ComponentUnit> componentUnits = (List<ComponentUnit>)(List<? extends Object>)lotComponentTableManager.getInput();
			List<Object> os = lotComponentTableManager.getCheckedObject();
			List<ComponentUnit> sortList = new ArrayList<ComponentUnit>();
			for(Object o : os) {
				sortList.add((ComponentUnit) o);
			}
			//���ݵ�ǰλ�ý�����������1 --> n
			int tmpIndex = 1;
			if (sortList.size() != 0) {
				for(ComponentUnit o : sortList) {
					ComponentUnit checkComponentUnit = o;
				    tmpIndex = componentUnits.indexOf(checkComponentUnit); 
					if (tmpIndex == 0) {
						break;
					}
				}
				if(tmpIndex != 0) {
					for (ComponentUnit o : sortList) {
						ComponentUnit componentUnit = o;
						if (componentUnits.contains(componentUnit)) {
							int index = componentUnits.indexOf(componentUnit);						
							if (index > 0) {
								componentUnits.remove(componentUnit);
								componentUnits.add(index - 1, componentUnit);
							}
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	@SuppressWarnings("unchecked")
	protected void downAdapter() {
		try {
			List<ComponentUnit> componentUnits = (List<ComponentUnit>)(List<? extends Object>)lotComponentTableManager.getInput();
			List<Object> os = lotComponentTableManager.getCheckedObject();
			List<ComponentUnit> sortList = new ArrayList<ComponentUnit>();
			for(Object o : os) {
				sortList.add((ComponentUnit) o);
			}
			//���ݵ�ǰλ�ý�����������1 --> n
			if (sortList.size() != 0) {
				for (int i = sortList.size() - 1;i >= 0;i--) {
					ComponentUnit componentUnit = sortList.get(i);
					if (componentUnits.contains(componentUnit)) {
						int index = componentUnits.indexOf(componentUnit);	
						if(index == componentUnits.size() - 1) {
							break;
						}
						if (index < componentUnits.size() - 1) {
							componentUnits.remove(componentUnit);
							componentUnits.add(index + 1, componentUnit);
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	@SuppressWarnings("unchecked")
	protected void delAdapter() {
		try {
			List<ComponentUnit> ComponentUnits = (List<ComponentUnit>)(List<? extends Object>)lotComponentTableManager.getInput();
			List<ComponentUnit> lines = new ArrayList<ComponentUnit>();
			for(ComponentUnit ComponentUnit : ComponentUnits){
				lines.add(ComponentUnit);
			}				
			List<Object> os = lotComponentTableManager.getCheckedObject();
			if (os.size() != 0) {
				for (Object o : os) {
					ComponentUnit line = (ComponentUnit) o;
					lines.remove(line);
				}
			}
			lotComponentTableManager.setInput(lines);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	public HeaderText getTxtCarrierId() {
		return txtCarrierId;
	}

	public void setTxtCarrierId(HeaderText txtCarrierId) {
		this.txtCarrierId = txtCarrierId;
	}

	public SearchField getCombo() {
		return combo;
	}

	public void setCombo(SearchField combo) {
		this.combo = combo;
	}

	public HeaderText getTxtCompoentId() {
		return txtCompoentId;
	}

	public void setTxtCompoentId(HeaderText txtCompoentId) {
		this.txtCompoentId = txtCompoentId;
	}

	public ListTableManager getLotComponentTableManager() {
		return lotComponentTableManager;
	}

	public void setLotComponentTableManager(ListTableManager lotComponentTableManager) {
		this.lotComponentTableManager = lotComponentTableManager;
	}

	
}
