package com.glory.mes.wip.lot.bonding;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class LotBondingComposite extends Composite {

	private static final Logger logger = Logger.getLogger(LotBondingComposite.class);

	protected LotBondingLeftComposite lotBondingLeftComposite;
	protected LotBondingRightComposite lotBondingRightComposite;
	protected LotBondingSection section;
	
	public LotBondingComposite(Composite parent, int style, LotBondingSection section) {
		super(parent, style);
		this.section = section;
	}

	public void createForm() {
		try {
			GridLayout layout = new GridLayout(2, true);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
						
			createBondingComposite();
		} catch (Exception e) {
			logger.error("MLotCarrierAssignComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createBondingComposite() {
		createLeftComponent(this);
		createRightComponent(this);
	}
	
	protected void createLeftComponent(final Composite parent) {
		Composite leftComposite = new Composite(parent, SWT.NONE);
		leftComposite.setLayout(new GridLayout());
		leftComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		lotBondingLeftComposite = new LotBondingLeftComposite(leftComposite, SWT.NONE, false, section);
		lotBondingLeftComposite.createPartControl();
	}
	
	protected void createRightComponent(final Composite parent) {        
		Composite rightComposite = new Composite(parent, SWT.NONE);
		rightComposite.setLayout(new GridLayout());
		rightComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		lotBondingRightComposite = new LotBondingRightComposite(rightComposite, SWT.NONE, true);
		lotBondingRightComposite.createPartControl();
	}

	public LotBondingLeftComposite getLotBondingLeftComposite() {
		return lotBondingLeftComposite;
	}

	public void setLotBondingLeftComposite(LotBondingLeftComposite lotBondingLeftComposite) {
		this.lotBondingLeftComposite = lotBondingLeftComposite;
	}

	public LotBondingRightComposite getLotBondingRightComposite() {
		return lotBondingRightComposite;
	}

	public void setLotBondingRightComposite(LotBondingRightComposite lotBondingRightComposite) {
		this.lotBondingRightComposite = lotBondingRightComposite;
	}
	
} 
