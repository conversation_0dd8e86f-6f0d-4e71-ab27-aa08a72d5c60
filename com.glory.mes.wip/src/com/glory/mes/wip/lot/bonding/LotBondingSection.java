package com.glory.mes.wip.lot.bonding;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.abort.AbortDialog;
import com.glory.mes.wip.lot.run.bylot.ByLotSection;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.ComponentUnitBonding;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;

public class LotBondingSection extends ByLotSection {

	private static final Logger logger = Logger.getLogger(LotBondingSection.class);
	
	protected boolean isTracking = true;
	
	protected ToolItem itemBonding;
	protected ToolItem itemUnBonding;
	
	protected LotBondingComposite bondingComposite;
	
	public LotBondingSection(ADTable table) {
		super(table);
	}
	
	protected void createSectionTitle(Composite client) {
	}
	
	@Override
    protected void createSectionContent(Composite client) {
        try {
        	section.setText(Message.getString("wip.lot_bonding_section_title"));
            final FormToolkit toolkit = form.getToolkit();
            mmng = form.getMessageManager();

            Composite bodyComposite = toolkit.createComposite(client, SWT.NONE);
            bodyComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
            GridLayout layoutBody = new GridLayout(1, false);
            bodyComposite.setLayout(layoutBody);

            bondingComposite = new LotBondingComposite(bodyComposite, SWT.NONE, this);
            bondingComposite.createForm();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		if (isTracking) {
			createToolItemTrackIn(tBar);
			new ToolItem(tBar, SWT.SEPARATOR);
		}
		createToolItemBonding(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnBonding(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		if (isTracking) {
			createToolItemTrackOut(tBar);
			new ToolItem(tBar, SWT.SEPARATOR);		
			createToolItemAbort(tBar);
			new ToolItem(tBar, SWT.SEPARATOR);
		}
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemBonding(ToolBar tBar) {
		itemBonding = new ToolItem(tBar, SWT.PUSH);
		itemBonding.setText(Message.getString("wip.combine"));
		itemBonding.setImage(SWTResourceCache.getImage("bonding"));
		itemBonding.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				bondingAdapter();
			}
		});
	}
	
	protected void createToolItemUnBonding(ToolBar tBar) {
		itemUnBonding = new ToolItem(tBar, SWT.PUSH);
		itemUnBonding.setText(Message.getString("wip.uncombine"));
		itemUnBonding.setImage(SWTResourceCache.getImage("unbonding"));
		itemUnBonding.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unBondingAdapter();
			}
		});
	}
	
	protected void bondingAdapter() {
        try {
        	ListTableManager componentTableManager = bondingComposite.getLotBondingLeftComposite().getLotComponentTableManager();
        	List<ComponentUnit> componentUnits = (List<ComponentUnit>)(List)componentTableManager.getInput();
        	if (componentUnits == null || componentUnits.size() == 0) {
				UI.showError(Message.getString("wip.component_is_null"));
				return;
			}
        	
        	Set<String> positionSet = new HashSet<String>();
        	for (ComponentUnit componentUnit : componentUnits) {
        		if (StringUtil.isEmpty(componentUnit.getPosition())) {
        			UI.showError(Message.getString("wip.component_position_is_null"));
    				return;
        		}
        		positionSet.add(componentUnit.getPosition());
        	}
        	if (positionSet.size() != componentUnits.size()) {
        		UI.showError(Message.getString("wip.component_position_is_exist_same"));
				return;	
        	}
        	
        	ADManager adManager = Framework.getService(ADManager.class);
        	Lot parentLot = new Lot();
        	parentLot.setObjectRrn(componentUnits.get(0).getParentUnitRrn());
        	parentLot = (Lot) adManager.getEntity(parentLot);
        	    	
        	ListTableManager bondingComponentTableManager = bondingComposite.getLotBondingRightComposite().getLotComponentTableManager();
        	List<ComponentUnit> bondingComponentUnits = (List<ComponentUnit>)(List)bondingComponentTableManager.getInput();
        	if (bondingComponentUnits == null || bondingComponentUnits.size() == 0) {
				UI.showError(Message.getString("wip.component_position_is_null"));
				return;
			}
        	
        	positionSet = new HashSet<String>();
        	for (ComponentUnit bondingComponentUnit : bondingComponentUnits) {
        		if (StringUtil.isEmpty(bondingComponentUnit.getPosition())) {
        			UI.showError(Message.getString("wip.bonding_component_position_is_null"));
    				return;
        		}
        		positionSet.add(bondingComponentUnit.getPosition());
        	}
        	if (positionSet.size() != bondingComponentUnits.size()) {
        		UI.showError(Message.getString("wip.component_position_is_exist_same"));
				return;	
        	}
        	
        	if (componentUnits.size() != bondingComponentUnits.size()) {
        		UI.showError(Message.getString("wip.bonding_component_qty_unequal"));
				return;
        	}
        	
        	List<ComponentUnitBonding> componentBondings = new ArrayList<ComponentUnitBonding>();
        	
        	for (int i = 0; i < componentUnits.size(); i++) {  		
        		ComponentUnitBonding componentBonding = new ComponentUnitBonding();
        		componentBonding.setOrgRrn(Env.getOrgRrn());
        		componentBonding.setIsActive(true);
        		componentBonding.setCreatedBy(Env.getUserName());
        		componentBonding.setUpdatedBy(Env.getUserName());
        		componentBonding.setLotId(parentLot.getLotId());
        		componentBonding.setStepRrn(parentLot.getStepRrn());
        		componentBonding.setStepName(parentLot.getStepName());
        		componentBonding.setStepVersion(parentLot.getStepVersion());
        		componentBonding.setEquipmentId(parentLot.getEquipmentId());
        		componentBonding.setDurable(componentUnits.get(i).getDurable());
        		componentBonding.setComponentRrn(componentUnits.get(i).getObjectRrn());
        		componentBonding.setComponentId(componentUnits.get(i).getComponentId());
        		componentBonding.setPosition(componentUnits.get(i).getPosition());    		
        		componentBonding.setBondingLotId(bondingComponentUnits.get(i).getLotId());
        		componentBonding.setBondingDurable(bondingComponentUnits.get(i).getDurable());
        		componentBonding.setBondingComponentRrn(bondingComponentUnits.get(i).getObjectRrn());
        		componentBonding.setBondingComponentId(bondingComponentUnits.get(i).getComponentId());
        		componentBonding.setBondingPosition(bondingComponentUnits.get(i).getPosition());
        		componentBonding.setBondingState(ComponentUnitBonding.BONDING_STATE_DONE);
        		componentBondings.add(componentBonding);
        	}
        		       	
        	ComponentManager componentManager = Framework.getService(ComponentManager.class);
        	componentManager.bondingComponent(parentLot, componentBondings, false, true, null, Env.getSessionContext());     	
        	
 
        	bondingComponentTableManager.setInput(new ArrayList<ComponentUnit>());
        	bondingComponentTableManager.refresh();
        	
        	if (bondingComposite.getLotBondingRightComposite().getTxtCarrierId() != null) {
        		bondingComposite.getLotBondingRightComposite().getTxtCarrierId().setText("");
        	}    	
        	if (bondingComposite.getLotBondingRightComposite().getCombo() != null) {
        		bondingComposite.getLotBondingRightComposite().getCombo().setValue(null);
        	}
        	if (bondingComposite.getLotBondingRightComposite().getTxtCompoentId() != null) {
        		bondingComposite.getLotBondingRightComposite().getTxtCompoentId().setText("");
        	}
        	
        	parentLot = (Lot) adManager.getEntity(parentLot);
			setAdObject(parentLot);
			refreshAdapter();
        	bondingComposite.getLotBondingLeftComposite().refresh();
        	UI.showInfo(Message.getString("wip.bonding_success"));
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    protected void unBondingAdapter() {
    	try {      
    		ListTableManager componentTableManager = bondingComposite.getLotBondingLeftComposite().getLotComponentTableManager();
        	List<ComponentUnit> componentUnits = (List<ComponentUnit>)(List)componentTableManager.getInput();
        	if (componentUnits == null || componentUnits.size() == 0) {
				UI.showError(Message.getString("wip.component_is_null"));
				return;
			}
        	
        	ADManager adManager = Framework.getService(ADManager.class);
        	Lot parentLot = new Lot();
        	parentLot.setObjectRrn(componentUnits.get(0).getParentUnitRrn());
        	parentLot = (Lot) adManager.getEntity(parentLot);
        	    	 
        	List<ComponentUnitBonding> componentBondings = adManager.getEntityList(Env.getOrgRrn(), ComponentUnitBonding.class,
					Integer.MAX_VALUE, " lotId = '" + parentLot.getLotId() + "'", ""); 
        	if (componentBondings != null && componentBondings.size() > 0) {
	        	Set<String> childLotIds = new HashSet<String>();
				for (ComponentUnitBonding componentBonding : componentBondings) {
					childLotIds.add(componentBonding.getBondingLotId());
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				List<Lot> childLots = new ArrayList<Lot>();
				for (String childLotId : childLotIds) {
					Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), childLotId, true);
					childLots.add(lot);
				}
				
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
	        	componentManager.unBondingComponent(parentLot, childLots, Env.getSessionContext());     	
	        	
	        	parentLot = (Lot) adManager.getEntity(parentLot);
				setAdObject(parentLot);
				refreshAdapter();
	        	bondingComposite.getLotBondingLeftComposite().refresh();
	        	UI.showInfo(Message.getString("wip.unbonding_success"));
        	} else {
        		UI.showError(Message.getString("wip.lot_component_not_bonding"));
				return;
        	}
        	
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }
    
	public void statusChanged(String newStatus) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			Lot lot = (Lot)getAdObject();
			if (isTracking) {
				if (LotStateMachine.STATE_RUN.equals(newStatus)) {
					if (itemTrackIn != null) {
						itemTrackIn.setEnabled(false);
					}
					
					//������ݲɼ��Ƿ���ڡ��Ƿ��Ѿ����
					boolean edcCompFlag = true;
					List<EdcSetCurrent> currents = getEdcSetCurrent();
					if (currents != null && currents.size() > 0){
						if (itemDcop != null) {
							itemDcop.setEnabled(true);
							//itemAutoDcop.setEnabled(true);
						}
						for (EdcSetCurrent current : currents) {
							if (!EdcSetCurrent.FLAG_DONE.equals(current.getEdcFlag())
									&& !EdcSetCurrent.FLAG_PASS.equals(current.getEdcFlag())) {
								//ֻҪ��һ�����ݲɼ�δ���
								edcCompFlag = false;
								break;
							}
						}
					} else {
						if (itemDcop != null) {
							itemDcop.setEnabled(false);
						}
					}
					
					List<ComponentUnitBonding> componentBondings = adManager.getEntityList(Env.getOrgRrn(), ComponentUnitBonding.class,
							Integer.MAX_VALUE, " lotId = '" + lot.getLotId() + "'", ""); 
		        	if (componentBondings != null && componentBondings.size() > 0) {
		        		itemBonding.setEnabled(false);
		        		itemUnBonding.setEnabled(true);
		        		if (itemTrackOut != null) {
		        			if (edcCompFlag) {
		        				itemTrackOut.setEnabled(true);
		        			} else {
		        				itemTrackOut.setEnabled(false);
		        			}
		    			}
		        		if (itemAbort != null) {
		    				itemAbort.setEnabled(false);
		    			}
		        	} else {
		        		itemBonding.setEnabled(true);
		        		itemUnBonding.setEnabled(false);
		        		if (itemTrackOut != null) {
		    				itemTrackOut.setEnabled(false);
		    			}
		        		if (itemAbort != null) {
		    				itemAbort.setEnabled(true);
		    			}
		        	}
		        	
					if (itemTrackMove != null) {
						itemTrackMove.setEnabled(false);
					}
				} else if (LotStateMachine.STATE_WAIT.equals(newStatus)) {
					if (trackMoveFlag) {
						if (itemTrackMove != null) {
							itemTrackMove.setEnabled(true);
						}
						if (itemTrackIn != null) {
							itemTrackIn.setEnabled(false);
						}
					} else {
						if (itemTrackMove != null) {
							itemTrackMove.setEnabled(false);
						}
						if (itemTrackIn != null) {
							itemTrackIn.setEnabled(true);
						}
					}
					if (itemTrackOut != null) {
						itemTrackOut.setEnabled(false);
					}
					if (itemDcop != null) {
						itemDcop.setEnabled(false);
					}
					if (itemAbort != null) {
						itemAbort.setEnabled(false);
					}
					itemBonding.setEnabled(false);
					itemUnBonding.setEnabled(false);
				} else {
					if (itemTrackIn != null) {
						itemTrackIn.setEnabled(false);
					}
					if (itemTrackOut != null) {
						itemTrackOut.setEnabled(false);
					}
					if (itemDcop != null) {
						itemDcop.setEnabled(false);
					}
					if (itemTrackOut != null) {
						itemTrackOut.setEnabled(false);
					}
					if (itemTrackMove != null) {
						itemTrackMove.setEnabled(false);
					}
					if (itemAbort != null) {
						itemAbort.setEnabled(false);
					}
					itemBonding.setEnabled(false);
					itemUnBonding.setEnabled(false);
				}
			
				if (lot != null && Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
					//run Hold״̬���Գ�վ
					if(itemTrackIn != null) {
						itemTrackIn.setEnabled(false);
					}
				}
			} else {	
				if (!StringUtil.isEmpty(newStatus)) {
					List<ComponentUnitBonding> componentBondings = adManager.getEntityList(Env.getOrgRrn(), ComponentUnitBonding.class,
							Integer.MAX_VALUE, " lotId = '" + lot.getLotId() + "'", ""); 
		        	if (componentBondings != null && componentBondings.size() > 0) {
		        		itemBonding.setEnabled(false);
		        		itemUnBonding.setEnabled(true);
		        	} else {
		        		itemBonding.setEnabled(true);
		        		itemUnBonding.setEnabled(false);
		        	}
				} else {
					itemBonding.setEnabled(false);
	        		itemUnBonding.setEnabled(false);
				}
			}
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
	}
	
	protected void trackInAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYLOT);
			context.setUseCategory(Step.USE_CATEGORY_BONDING);
			List<Lot> lots = new ArrayList<Lot>();
			Lot lot = (Lot) getAdObject();
			lots.add(lot);
			context.setLots(lots);
			
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			FlowWizard wizard = getTrackInWizard(context, step.getTrackInFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);			
			ChainContext wipContext = lotManager.checkTrackInConstraint(inContext
					, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			TrackInDialog dialog = new TrackInDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if ((result == Dialog.OK || result == TrackInDialog.FIN) 
					&& context.getReturnCode() == TrackInContext.OK_ID) {
				Lot trackInLot = ((TrackInWizard) wizard).getContext().getTrackInLot();
				if (trackInLot != null) {
					setAdObject(trackInLot);
				}
				refreshAdapter();
				bondingComposite.getLotBondingLeftComposite().refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void trackOutAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackOutContext context = new TrackOutContext();
			context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);
			context.setUseCategory(Step.USE_CATEGORY_BONDING);
			
			Lot lot = (Lot)getAdObject();
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);

			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			FlowWizard wizard = getTrackOutWizard(context, step.getTrackOutFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			TrackOutDialog dialog = new TrackOutDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackOutDialog.FIN) {
				if (((TrackOutWizard) wizard).getContext().getOutLots().size() > 0) {
					Lot trackOutLot = ((TrackOutWizard) wizard).getContext().getOutLots().get(0);
					if (trackOutLot != null){			
						setAdObject(trackOutLot);
					}
//					bondingComposite.getLotBondingLeftComposite().refresh();
				}
				refreshAdapter();
				UI.showInfo(Message.getString("wip.trackout_success"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void abortAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			InContext context = new InContext();
			context.setUseCategory(Step.USE_CATEGORY_BONDING);

			Lot lot = (Lot)getAdObject();
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);

			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setCurrentStep(step);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkAbortConstraint(
					context, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			FlowWizard wizard = getAbortWizard(context, step.getAbortFlow());

			AbortDialog dialog = new AbortDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == AbortDialog.FIN) {
				refreshAdapter();
				bondingComposite.getLotBondingLeftComposite().refresh();
			}  	
		} catch (Exception e) {
			logger.error("Error at LotBondingSection : abortAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
}
