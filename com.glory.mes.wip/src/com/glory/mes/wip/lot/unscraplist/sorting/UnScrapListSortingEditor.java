package com.glory.mes.wip.lot.unscraplist.sorting;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADQuery;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.MesGlcEvent;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitDialog;
import com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitManagerEditor;
import com.glory.mes.wip.lot.carrier.glc.split.LotSortingSplitContext;
import com.glory.mes.wip.lot.unscraplist.UnScrapListDialog;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

public class UnScrapListSortingEditor extends GlcEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.unscraplist.sorting.UnScrapListSortingEditor";
	
	public static final String CONTROL_QUERY_FORM = "queryInfo";
	public static final String CONTROL_PART_NAME = "partName";
	public static final String BUTTON_NAME_UNSCRAP = "unscrap";
	public static final String BUTTON_NAME_UNSCRALOT = "unscraLot";
	public static final String DURABLE_ID = "attribute1";
	protected QueryFormField queryFormField;
	
	private boolean isUnScrapQty = false;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		queryFormField = form.getFieldByControlId(CONTROL_QUERY_FORM, QueryFormField.class);
		
		// ��ѯ���ѡ���¼�
		form.unsubscribeDefaultEvent(queryFormField.getFullTopic(GlcEvent.EVENT_QUERY));
		subscribeAndExecute(eventBroker, queryFormField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		
		//ȡ�������¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_UNSCRAP), this::unScrapAdapter);
				
		//ˢ���¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_REFRESH), this::refreshAdapter);	
				
	}
	
	public void queryAdapter(Object object) {
		try {
			// ��ѯ��ť��д
			QueryTableForm queryTableForm = queryFormField.getQueryForm();
			queryTableForm.getQueryForm().removeAllMessages();
			if (!queryTableForm.getQueryForm().validate()){
				return;
			}
			
			StringBuffer sb = new StringBuffer("");
			sb.append(" 1=1 ");
			sb.append(queryTableForm.getQueryForm().createWhereClause());
			List<ADQuery> querys = getADManger().getEntityList(0L, ADQuery.class, 1, " name = 'GetLotScrap'", "");
			if (querys.size() != 0) {
				ADQuery query = (ADQuery) querys.get(0);
				String queryText = query.getQueryText();
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("orgRrn", Env.getOrgRrn());
				paramMap.putAll(queryTableForm.getQueryForm().getParmaterMap());
				
				String whereCluase = StringUtil.relpaceWildcardCondition(sb.toString());
				whereCluase = whereCluase.replaceAll(queryTableForm.getTableManager().getADTable().getModelName() + ".", "");
				
				List<Map> currentList = getADManger().getEntityMapListByQueryText(queryText, paramMap, 0, Env.getMaxResult(), whereCluase, "");
				// ������
				queryFormField.getQueryForm().getTableManager().setInput(currentList);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void unScrapAdapter(Object object) {
		try {
			List<Object> objects = queryFormField.getQueryForm().getCheckedObject();
			ADManager adManager = Framework.getService(ADManager.class);
			if (CollectionUtils.isNotEmpty(objects)) {
				List<LotScrap> lotScraps = Lists.newArrayList();
				for (Object obj: objects) {
					Map<String, Object> map = (Map<String, Object>) obj;
					if (map != null && map.get("OBJECTRRN") != null) {
						LotScrap lotScrap = new LotScrap();
						lotScrap.setObjectRrn(DBUtil.toLong(map.get("OBJECTRRN")));
						lotScrap = (LotScrap) adManager.getEntity(lotScrap);
						lotScraps.add(lotScrap);
					}
				}
				
				if (validate(lotScraps)) {
					for (LotScrap lotScrap : lotScraps) {
						ComponentUnit componentUnit = new ComponentUnit();
						componentUnit.setObjectRrn(lotScrap.getComponentRrn());
						if (lotScrap.getComponentRrn() == null) {
							UI.showInfo(Message.getString("wip.un_scrap_sorter_un_apply_qty"));
							return;
						}
						componentUnit = (ComponentUnit) getADManger().getEntity(componentUnit);
						if (!LotStateMachine.STATE_SCRAP.equals(componentUnit.getState())) {
							UI.showError(String.format(Message.getString("wip.unscrap_use_unscrap"), componentUnit.getComponentId()));
							return;
						}
						// Ƭ��ǰ����RRN
						lotScrap.setAttribute1(componentUnit.getParentUnitRrn());
						// ����ԭposition
						lotScrap.setAttribute2(componentUnit.getPosition());
						lotScrap.setAttribute3(componentUnit);
						lotScrap.setSubQty(lotScrap.getSubQty() == null ? BigDecimal.ZERO : lotScrap.getSubQty());
						// ����������
						lotScrap.setUnScrapMainQty(lotScrap.getMainQty());
						lotScrap.setUnScrapSubQty(lotScrap.getSubQty());
						// �����ϴ���
						lotScrap.setUnScrapCode("");
						// ��ע
						lotScrap.setUnScrapComment("");
					}
					
					LotManager lotManager = Framework.getService(LotManager.class);
					Lot lot = lotManager.getLotWithComponent(Long.valueOf(lotScraps.get(0).getLotRrn()));
					if(LotStateMachine.STATE_RUN.equals(lot.getState()) || LotStateMachine.STATE_TERM.equals(lot.getState())) {
						UI.showError(Message.getString("wip.lot_state_is_run_cannot_unscarp"));
						return;
					} else {
						// ��ȫ���ϵ�����
						if (LotStateMachine.COMCLASS_COM.equals(lot.getComClass())
								&& LotStateMachine.STATE_SCRAP.equals(lot.getState())) {
							for(LotScrap lotScarp : lotScraps) {
								if(!lotScarp.getStepName().equals(lot.getStepName())) {
									UI.showError(Message.getString("wip.component_state_or_stepname_inconsistent_with_lot"));
									return;
								}
							}
						} else {
							for(LotScrap lotScarp : lotScraps) {
								String state = lotScarp.getState();
								// ����sort������״̬ΪSCRAP
								if((!LotStateMachine.STATE_SORT.equals(state))|| (!lotScarp.getStepName().equals(lot.getStepName()))) {
									UI.showError(Message.getString("wip.component_state_or_stepname_inconsistent_with_lot"));
									return;
								}
							}
						}
					}
					
					UnScrapListDialog dialog = new UnScrapListDialog(UI.getActiveShell(), lot, lotScraps, true, getADManger(), true);
					Event event = (Event) object;
					if (Dialog.OK == dialog.open()) {
						if (dialog.getExternalSplit().isEnabled()) {
							partialUnScrap(dialog.getLotScraps(), event);
						} else {
							allUnScrap(dialog.getLotScraps(), event);
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void allUnScrap(List<LotScrap> lotScraps, Event event) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			List<ComponentUnit> unScrapUnits = Lists.newArrayList();
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			if (isUnScrapQty) {
				Map<String, List<LotUnScrapAction>> unScrapActionsMap = Maps.newHashMap();
				// ��scrapת��action
				for (LotScrap scrap : lotScraps) {
					LotUnScrapAction action = new LotUnScrapAction();
					action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
					action.setActionCode(scrap.getUnScrapCode());
					action.setActionComment(scrap.getUnScrapComment());
					action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
					action.setLotScrap(scrap);

					// �����ϲ���
					ComponentUnit unit = componentManager.getComponentByComponentId(Env.getOrgRrn(), scrap.getComponentId());
					if (!unScrapUnits.contains(unit)) {
						unit.setPosition(DBUtil.toString(scrap.getAttribute2()));
						unScrapUnits.add(unit);
					}
					if (!unScrapActionsMap.containsKey(scrap.getComponentId())) {
						unScrapActionsMap.put(scrap.getComponentId(), Lists.newArrayList());
					}
					unScrapActionsMap.get(scrap.getComponentId()).add(action);
				}
				componentManager.unScrapComponentQty(unScrapUnits, unScrapActionsMap, true, false, Env.getSessionContext());
			} else {
				List<LotUnScrapAction> unScrapActions = Lists.newArrayList();
				Lot lastParentLot = null;
				// ��scrapת��action
				for (LotScrap scrap : lotScraps) {
					// ���������
					scrap.setSubQty(null);
					scrap.setUnScrapSubQty(null);

					LotUnScrapAction action = new LotUnScrapAction();
					action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
					action.setActionCode(scrap.getUnScrapCode());
					action.setActionComment(scrap.getUnScrapComment());
					action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
					action.setLotScrap(scrap);

					// �����ϲ���
					ComponentUnit unit = componentManager.getComponentByComponentId(Env.getOrgRrn(), scrap.getComponentId());
					if (!unScrapUnits.contains(unit)) {
						unit.setPosition(DBUtil.toString(scrap.getAttribute2()));
						unScrapUnits.add(unit);
						unScrapActions.add(action);
					}
					if (lastParentLot == null) {
						lastParentLot = lotManager.getLotByLotId(Env.getOrgRrn(), scrap.getLotId(), true);
					}
				}
				SessionContext sc = Env.getSessionContext();
				String operator = Env.getUserName();
				if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
					operator = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
				}
				lastParentLot.setOperator1(operator);
				sc.setUserName(operator);
				componentManager.unScrapComponent(lastParentLot, unScrapUnits, unScrapActions, null, false, sc);
			}
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void partialUnScrap(List<LotScrap> lotScraps, Event event) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			List<ComponentUnit> componentUnits = Lists.newArrayList();
			for (LotScrap scrap : lotScraps) {
				ComponentUnit unit = (ComponentUnit) scrap.getAttribute3();
				if (!componentUnits.contains(unit)) {
					componentUnits.add(unit);
				}
			}
			
			Map<String, Object> initDatas = Maps.newHashMap();
        	initDatas.put(MesGlcEvent.PROPERTY_LOT_ID, lotScraps.get(0).getLotId());
        	initDatas.put(CarrierLotSplitManagerEditor.IS_SORT, true);
        	initDatas.put(CarrierLotSplitManagerEditor.TARGET_TAB_SIZE, 1);
        	initDatas.put(CarrierLotSplitManagerEditor.IS_SHOW_MERGE_STEP, false);
			CarrierLotSplitDialog dialog = new CarrierLotSplitDialog(CarrierLotSplitManagerEditor.ADFORM_NAME, CarrierLotSplitManagerEditor.AUTHORITY_NAME, getEventBroker());
			if (dialog.open(initDatas) == Dialog.OK) {
				LotSortingSplitContext splitContext = dialog.getContext();
				List<List<LotUnScrapAction>> sorterUnitActionsList = Lists.newArrayList();
				List<ComponentUnit> sorterComponentUnits = Lists.newArrayList();
				for (List<ComponentUnit> componentUnitList : splitContext.getChildUnitList()) {
					for (ComponentUnit componentUnit : componentUnitList) {
						sorterComponentUnits.add(componentUnit);
						List<LotUnScrapAction> unitActions = Lists.newArrayList();
						for (LotScrap scrap : lotScraps) {
							if (scrap.getComponentId().equals(componentUnit.getComponentId())) {
								LotUnScrapAction action = new LotUnScrapAction();
								action.setActionCode(scrap.getUnScrapCode());
								action.setActionComment(scrap.getUnScrapComment());
								action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
								action.setLotScrap(scrap);
								unitActions.add(action);
							}
						}
						sorterUnitActionsList.add(unitActions);
					}
				}
				
				Lot lot = splitContext.getSourceLot();
				lot.setOperator1(Env.getUserName());
				
				// û����������Ĭ������Ƭ����
				boolean isQty = false;
				if (lot.getSubQty() != null) {
					isQty = true;
				}
				
				LotSortingAction lotSortingAction = (LotSortingAction) dialog.getContext().getLotActions().get(0);
            	lotSortingAction.setActionComment(lotScraps.get(0).getActionComment());
            	lotSortingAction.setSortingMode(LotSortingJob.SORTING_MODE_SPLIT_BYLOGIC);
				
            	SessionContext sc = Env.getSessionContext();
				String operator = Env.getUserName();
				if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
					operator = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
				}
				lot.setOperator1(operator);
				sc.setUserName(operator);
            	
				lot = componentManager.unScrapComponentNewLot(lot, null, 
						sorterComponentUnits, sorterUnitActionsList, isQty, true, false, lotSortingAction, sc);
				UI.showInfo(Message.getString("unscrap.unscrapnew_operate_success") + "LotId:<" + lot.getLotId() + ">");
				refreshAdapter(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void refreshAdapter(Object object) {
		try {
			queryAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private boolean validate(List<LotScrap> lotScraps) {
		// ������Ƭ��������ͬʱ����
		boolean isUnScrapSubQty = false;
		boolean isUnScrapMainQty = false;
		Set<String> lotIds = Sets.newHashSet();
		for (LotScrap lotScrap : lotScraps) {
			if (lotScrap.getSubQty() == null || BigDecimal.ZERO.compareTo(lotScrap.getSubQty()) == 0) {
				isUnScrapMainQty = true;
			} else {
				isUnScrapSubQty = true;
			}
			
			lotIds.add(lotScrap.getLotId());
		}

		if (isUnScrapMainQty && isUnScrapSubQty) {
			UI.showError(Message.getString("wip.unscrap_main_sub_not_allow"));
			return false;
		}

		if (lotIds.size() > 1) {
			UI.showError(Message.getString("wip.unscrap_comp_multiple_lot"));
			return false;
		}
		
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			if (MesCfMod.isScrapSorter(Env.getOrgRrn(), sysParamManager)) {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotScraps.get(0).getLotId());
				//����Ƿ���ؾ�
				if (StringUtil.isEmpty(lot.getDurable())) {
					UI.showError(Message.getString("wip.lot_not_assign_durable"));
					return false;
				}
				if (!LotStateMachine.STATE_SCRAP.equals(lot.getState())) {
					if (LotStateMachine.STATE_SORT.equals(lot.getState())) {
						UI.showError(Message.getString("wip.sorting_job_is_exist_cannot_unscrap"));
					} else {
						UI.showError(Message.getString("wip.lot_state_not_allow"));
					}
					return false;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		
		this.isUnScrapQty = isUnScrapSubQty;
		return true;
	}
	
}
