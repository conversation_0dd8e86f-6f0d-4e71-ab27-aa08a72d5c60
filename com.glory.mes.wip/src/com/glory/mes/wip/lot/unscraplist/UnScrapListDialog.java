package com.glory.mes.wip.lot.unscraplist;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Maps;

public class UnScrapListDialog extends BaseTitleDialog {
	
	public static final String TABLE_NAME_LOT = "WIPLotUnScrapEdit";
	public static final String TABLE_NAME_COMP = "WIPComponentUnScrapEdit";
	
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 420;

	private ADManager adManager;
	private List<LotScrap> lotScraps;
	private CheckBoxFixEditorTableManager tableManager;
	
	protected XCombo combo;
	protected Text commentText;
	protected Lot lot;
	protected boolean isUnScrapComponent;
	protected boolean isExternal;
	protected SquareButton externalSplit;
	protected SquareButton ok;
    
    public UnScrapListDialog(Shell parentShell, Lot lot, List<LotScrap> lotScraps, boolean isUnScrapComponent, ADManager adManager) {
		this(parentShell, lot, lotScraps, isUnScrapComponent, adManager, false);
	}

	public UnScrapListDialog(Shell parentShell, Lot lot, List<LotScrap> lotScraps, boolean isUnScrapComponent, ADManager adManager, boolean isExternal) {
		super(parentShell);
		this.lot = lot;
		this.lotScraps = lotScraps;
		this.adManager = adManager;
		this.isExternal = isExternal;
		this.isUnScrapComponent = isUnScrapComponent;
	}

	@Override
	protected Control buildView(Composite parent) {
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		setTitleImage(SWTResourceCache.getImage("common-dialog"));
		
		setTitle(Message.getString("wip.lotHis_UnScrap"));
		setMessage(Message.getString("wip.unscrap_qty_input"));
		

		Composite content = toolkit.createComposite(parent);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setLayout(new GridLayout(1, false));
		
		tableManager = new CheckBoxFixEditorTableManager(getTable());
		tableManager.newViewer(content);
		tableManager.setInput(lotScraps);
		
		tableManager.addICheckChangedListener(new ICheckChangedListener() {
			
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if (CollectionUtils.isEmpty(eventObjects)) {
					return;
				}
				
				boolean removeCheckFlag = false;
				boolean commentIsNull = false;
				for (Object object : eventObjects) {
					LotScrap unit = (LotScrap) object;
					if (unit == null) {
						return;
					}
					
					if (checked) {
						String unScrapCode = combo.getText();
						if (StringUtil.isEmpty(unScrapCode)) {
							removeCheckFlag = true;
							break;
						}
						String comments = commentText.getText();
						if (StringUtil.isEmpty(comments)) {
							removeCheckFlag = true;
							commentIsNull = true;
							break;
						}
						unit.setUnScrapCode(unScrapCode);
						unit.setUnScrapComment(comments);
					} else {
						unit.setUnScrapCode("");
						unit.setUnScrapComment("");
					}
				}
				
				if (removeCheckFlag) {
					tableManager.getCheckedObject().removeAll(eventObjects);
					if (commentIsNull) {
						UI.showWarning(Message.getString("spc.chart_addcomment_cannot_null"));
					} else {
						UI.showWarning(Message.getString("unscrap.unscrapnew_dialog_selectcodefirst"));
					}
				} else {
					if (isExternal) {
						List<Object> elements = tableManager.getCheckedObject();
						BigDecimal unScrapQty = BigDecimal.ZERO;
						for (Object element : elements) {
							LotScrap unit = (LotScrap) element;
							unScrapQty = unScrapQty.add(unit.getMainQty());
						}
					
						if (unScrapQty.compareTo(lot.getMainQty()) == 0) {
							externalSplit.setEnabled(false);
							ok.setEnabled(true);
						} else {
							externalSplit.setEnabled(true);
							ok.setEnabled(false);
						}
					}
				}
			}
		});
		
		Composite unScrapComp = toolkit.createComposite(parent, SWT.NONE);
		unScrapComp.setLayout(new GridLayout(2, false));
		unScrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		unScrapComp.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));

		Label lab1 = toolkit.createLabel(unScrapComp, Message
				.getString("wip.unscrapcode_lot"), SWT.NULL);
		lab1.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		combo = RCPUtil.getUserRefListCombo(unScrapComp, "UnScrapCode", Env.getOrgRrn());
		
		combo.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));

		Label lab2 = toolkit.createLabel(unScrapComp, Message
				.getString("common.comment") + "*", SWT.NULL);
		lab2.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		commentText = toolkit.createText(unScrapComp, "", SWT.BORDER);
		commentText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true,
				false));
		return parent;
	}
	
	@Override
	protected void okPressed() {
		// �������
		List<Object> objects = tableManager.getCheckedObject();
		if (CollectionUtils.isEmpty(objects)) {
			UI.showInfo(Message.getString("wip.unscrap_no_units_selected"));
			return;
		}
		
		List<LotScrap> lotScraps = objects.stream()
				.map(l -> ((LotScrap)l)).collect(Collectors.toList());
		
		// �����ϵ�������������������Ϊ��
		Optional<LotScrap> f = lotScraps.stream().filter(
				l -> l.getUnScrapMainQty() == null || l.getUnScrapSubQty() == null).findFirst();
		if (f.isPresent()) {
			UI.showInfo(Message.getString("wip.unscrap_qty_input"));
			return;
		}
		// �����Ϸ������������������ܶ�Ϊ0
		f = lotScraps.stream().filter(
				l -> {
					return BigDecimal.ZERO.compareTo(l.getUnScrapMainQty()) >= 0 &&
							BigDecimal.ZERO.compareTo(l.getUnScrapSubQty()) >= 0;
				}).findFirst();
		if (f.isPresent()) {
			UI.showInfo(Message.getString("wip.unscrap_main_sub_qty_zero"));
			return;
		}
		
		// �����ϵ������������������ܴ��ڱ�������
		f = lotScraps.stream().filter(
				l -> {
					return l.getMainQty().compareTo(l.getUnScrapMainQty()) < 0 ||
							l.getSubQty().compareTo(l.getUnScrapSubQty()) < 0;
				}).findFirst();
		if (f.isPresent()) {
			UI.showInfo(Message.getString("wip.unscrap_greater_than_scrapped"));
			return;
		}
		
		if (isUnScrapComponent) {
			// λ�ò���Ϊ��
			f = lotScraps.stream().filter(
					l -> {
						return l.getAttribute2() == null ||
								StringUtil.isEmpty(l.getAttribute2().toString());
					}).findFirst();
			if (f.isPresent()) {
				UI.showInfo(Message.getString("wip.unscrap_position_null"));
				return;
			}

			// ���һƬ�Ƿ������˶��λ��
			// ���һ��λ���Ƿ��Ӧ��Ƭ��
			Map<String, String> positionsMap = Maps.newHashMap();
			Map<String, String> unitsMap = Maps.newHashMap();
			for (LotScrap lotScrap : lotScraps) {
				String position = DBUtil.toString(lotScrap.getAttribute2());
				if (positionsMap.containsKey(lotScrap.getComponentId())) {
					if (!position.equals(positionsMap.get(lotScrap.getComponentId()))) {
						UI.showInfo(String.format(Message.getString("wip.unscrap_multiple_position"),
								lotScrap.getComponentId()));
						return;
					}
				}

				if (unitsMap.containsKey(position)) {
					if (!lotScrap.getComponentId().equals(unitsMap.get(position))) {
						UI.showInfo(String.format(Message.getString("wip.unscrap_position_repeat"),
								lotScrap.getComponentId(), unitsMap.get(position)));
						return;
					}
				}

				positionsMap.put(lotScrap.getComponentId(), position);
				unitsMap.put(position, lotScrap.getComponentId());
			}

			// ���Ƭλ���Ƿ��ѱ�ռ��
			List<ProcessUnit> processUnits = lot.getSubProcessUnit();
			if (!CollectionUtils.isEmpty(processUnits)) {
				Map<String, String> poistions = Maps.newHashMap();
				for (ProcessUnit unit : processUnits) {
					ComponentUnit componentUnit = (ComponentUnit) unit;
					poistions.put(componentUnit.getComponentId(), componentUnit.getPosition());
				}

				for (LotScrap lotScrap : lotScraps) {
					String position = DBUtil.toString(lotScrap.getAttribute2());
					if (poistions.containsKey(lotScrap.getComponentId())) {
						if (!position.equals(poistions.get(lotScrap.getComponentId()))) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"),
									lotScrap.getComponentId(), poistions.get(lotScrap.getComponentId())));
							return;
						}
					} else {
						Boolean judge = false;
						String repeatPosition = null;
						for (Map.Entry<String, String> entry : poistions.entrySet()) {
							if (position.equals(entry.getValue())) {
								repeatPosition = entry.getKey();
								judge = true;
								break;
							}
						}
						if (judge) {
							UI.showInfo(String.format(Message.getString("wip.unscrap_position_used"), repeatPosition,
									poistions.get(repeatPosition)));
							return;
						}
					}
				}
			}
		}
		
		setLotScraps(lotScraps);
		super.okPressed();
	}
	
	private ADTable getTable() {
		ADTable adTable = null;
    	try {
    		if (isUnScrapComponent) {
    			adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_COMP);
    		} else {
    			adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOT);
    		}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	return adTable;
    }

	public List<LotScrap> getLotScraps() {
		return lotScraps;
	}

	public void setLotScraps(List<LotScrap> lotScraps) {
		this.lotScraps = lotScraps;
	}
	
	@Override
	protected Point getInitialSize() {
		super.getShellStyle();
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.NONE);
	}

	public CheckBoxFixEditorTableManager getTableManager() {
		return tableManager;
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
    	ok = createSquareButton(parent, IDialogConstants.OK_ID,
				Message.getString(ExceptionBundle.bundle.CommonOk()), false, null);
		
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

		fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(cancel, -12, SWT.LEFT);
		fd.bottom = new FormAttachment(100, -15);
		ok.setLayoutData(fd);
		
		if (isExternal) {
			externalSplit = createSquareButton(parent, IDialogConstants.YES_ID,
					Message.getString("common.external_split"), false, UIControlsFactory.BUTTON_DEFAULT);
			
			fd = new FormData();
			fd.width = 90;
			fd.height = 35;
			fd.top = new FormAttachment(0, 15);
			fd.right = new FormAttachment(ok, -12, SWT.LEFT);
			fd.bottom = new FormAttachment(100, -15);
			externalSplit.setLayoutData(fd);
    	}
			
    }
	
	@Override
    protected void buttonPressed(int buttonId) {
		if (IDialogConstants.OK_ID == buttonId || IDialogConstants.YES_ID == buttonId) {
			okPressed();
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed();
		}
	}

	public SquareButton getExternalSplit() {
		return externalSplit;
	}
}
