package com.glory.mes.wip.lot.start;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.mes.wip.lot.schedule.ScheduleLotProperties;

public class StartLotBlock extends EntityBlock {
	private static final Logger logger = Logger.getLogger(StartLotBlock.class);
	StartLotProperties page;
	
	public StartLotBlock(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class klass = Class.forName(table.getModelClass());		
			page = new StartLotProperties();
			page.setMasterParent(this);
			page.setTable(table);
			detailsPart.registerPage(klass, page);
		} catch (Exception e){
			logger.error("StartLotBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((StartLotProperties)page).setFocus();
	}
}
