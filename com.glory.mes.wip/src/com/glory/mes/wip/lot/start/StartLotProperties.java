package com.glory.mes.wip.lot.start;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotParameterForm;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class StartLotProperties extends EntityProperties {
	
	private static final Logger logger = Logger.getLogger(StartLotProperties.class);
	
	protected LotParameterForm paramForm;
	
	protected AuthorityToolItem itemStart;
	
	public static final String KEY_START = "start";
	
	public StartLotProperties() {
		super();
    }	

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		EntityForm itemForm = null;
		String tabName = tab.getName();
		if(tabName.equalsIgnoreCase("Parameter")) {
			paramForm = new LotParameterForm(composite, SWT.NONE, tab, mmng);
			return paramForm;
		} else {
			itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
		}
		return itemForm;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolitemStart(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolitemStart(ToolBar tBar) {
		itemStart = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_START);
		itemStart.setText(Message.getString("common.start"));
		itemStart.setImage(SWTResourceCache.getImage("newlot_start"));
		itemStart.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event){
				startAdapter();
			}
		});
	}

	protected void startAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;						
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}	
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());	
					}
										
					LotManager lotManager = Framework.getService(LotManager.class);
					Lot lot = (Lot)getAdObject();
					lot.setOperator1(Env.getUserName());
					setAdObject(lotManager.newLotStart(lot, null, null, Env.getSessionContext()));						
					UI.showInfo(Message.getString("common.start_successed"));//������ʾ��
					getMasterParent().refresh();
					this.createAdObject();										
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Lot newBase = (Lot)this.getAdObject();
		if (newBase != null) {
			statusChanged(newBase.getState());
		} else {
			statusChanged("");
		}
	}
	
	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_SCHD.equals(newStatus)){
			itemStart.setEnabled(true);
		} else {
			itemStart.setEnabled(false);
		}
	}

}