package com.glory.mes.wip.lot.revert;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Table;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.lot.split.SplitForm;

public class RevertLotForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(SplitForm.class);
	protected static final String PROPERTY_ID = "scheduleSources";
	private TableViewer viewer;
	private RevertLotManager tableManager;
	private static String TABLE_NAME1 = "WIPStartSource";

	public RevertLotForm(Composite parent, int style, ADTab tab,
			IManagedForm form) {
		super(parent, style, tab, form.getMessageManager());
	}

	@Override
	public void createForm() {
		super.createForm();
		try{
			Composite body = form.getBody();
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME1);
			tableManager = new RevertLotManager(adTable);
			tableManager.setStyle(SWT.FULL_SELECTION | SWT.BORDER | SWT.MULTI);
			viewer = (TableViewer) tableManager.createViewer(body,
						new FormToolkit(getShell().getDisplay()),50);
			Table table = viewer.getTable();
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			gd.heightHint=160;
			gd.horizontalSpan=6;
			table.setLayoutData(gd);
			} catch (Exception e){
				logger.error("SplitForm : Init tablelist", e);				
			}
	}

	public TableViewer getViewer() {
		return viewer;
	}	
	
}
