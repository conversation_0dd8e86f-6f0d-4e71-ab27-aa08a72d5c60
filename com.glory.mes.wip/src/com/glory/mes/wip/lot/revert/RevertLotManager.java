package com.glory.mes.wip.lot.revert;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ICellModifier;
import org.eclipse.jface.viewers.StructuredViewer;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.viewers.TableViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;

public class RevertLotManager extends TableViewerManager {
	private static final Logger logger = Logger.getLogger(RevertLotManager.class);
	private CellEditor[] cellEditor;
	protected TableViewer tableViewer;
	protected String[] str;
	protected static final Rectangle firstRect = new Rectangle(1, 0, 200, 420);

	public RevertLotManager(ADTable adTable) {
		super(adTable);
		super.setStyle(SWT.BORDER | SWT.H_SCROLL | SWT.V_SCROLL | SWT.FULL_SELECTION | SWT.HIDE_SELECTION);
	}

	public RevertLotManager(ADTable adTable, int style) {
		this(adTable);
		super.addStyle(style);
	}

	@Override
	protected ItemAdapterFactory createAdapterFactory() {
		ItemAdapterFactory factory = new ItemAdapterFactory();
		try {
			factory.registerAdapter(Object.class, new ListItemAdapter<ADBase>());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return factory;
	}

	@Override
	protected StructuredViewer newViewer(Composite parent, FormToolkit toolkit, int h) {
		final TableViewer viewer = (TableViewer) super.newViewer(parent, toolkit, 400);
		setCellEditor(viewer);
		return viewer;
	}

	
	protected void setCellEditor(TableViewer tableViewer) {
		int size = this.getColumns().length;
		cellEditor = new CellEditor[size];
		String[] properties = new String[size];
		for (int i = 0; i < size; i++) {
			String column = (String) tableViewer.getTable().getColumn(i).getData(TableViewerManager.COLUMN_ID);
			if("partSpec5".equals(column)){																			
				cellEditor[i] = new TextCellEditor(tableViewer.getTable());
			}else {
				cellEditor[i] = null;
			}
			properties[i] = column;
		}
		tableViewer.setColumnProperties(properties);
		tableViewer.setCellEditors(cellEditor);
		StartCellModifier ccm = new StartCellModifier(tableViewer);
		tableViewer.setCellModifier(ccm);
	}
	
	
	private class StartCellModifier implements ICellModifier {
		TableViewer tableViewer;
//		StartSource ss;

		public StartCellModifier(TableViewer tableViewer) {
			this.tableViewer = tableViewer;
		}

		@Override
		public boolean canModify(Object element, String property) {
			if ("partSpec5".equals(property)) {
				TableItem it = tableViewer.getTable().getSelection()[0];
				it.setImage(1, null);
				return true;
			}
			return false;
		}

		@Override
		public Object getValue(Object element, String property) {
//			if (element instanceof StartSource) {
//				ss = (StartSource) element;
//				if ("partSpec5".equals(property)) {
//					return ss.getPartSpec5();
//				}
//			}
			return null;
		}
		@Override
		public void modify(Object element, String property, Object value) {
			try {
				TableItem it = ((TableItem)element);
//				StartSource sc = (StartSource)it.getData();
//				if(value != null) {
//					if("partSpec5".equals(property)){
//						String mainQty = (String)value;
//						sc.setPartSpec5(mainQty);//
//					}
//					
//				}								
				tableViewer.refresh();
			} catch(Exception e) {
				e.printStackTrace();
				logger.error("Error NewScheduleLotListManager : modify()" + e.getMessage());
			}
		}
	}

}

