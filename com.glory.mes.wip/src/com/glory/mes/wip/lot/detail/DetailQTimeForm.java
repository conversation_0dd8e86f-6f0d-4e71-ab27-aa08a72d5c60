package com.glory.mes.wip.lot.detail;

import java.util.List;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.future.FutureTimer;
import com.glory.mes.wip.model.Lot;

public class DetailQTimeForm extends EntityForm {
	private static String TABLE_NAME = "WIPLotNoteQtimeInstance";

	public DetailQTimeForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	private ListTableManager tableManager;
	
	@Override
	public void createForm() {
		super.createForm();
		try {
			Composite bodyComposite = form.getBody();

			final ADManager entiyManager = Framework.getService(ADManager.class);
			final ADTable adTable = entiyManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListTableManager(adTable);
			tableManager.newViewer(bodyComposite);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	public void refresh() {
		Lot lot = (Lot)getObject();
		if (lot != null&&lot.getObjectRrn()!=null) {
			try {
				ADManager adManager = Framework.getService(ADManager.class);

				String whereClause = " timerType = '" + FutureTimer.TIMERTYPE_MAXIMAL + "' and timerAction = '" + FutureTimer.ACTION_NOTE + "' and timerFlag ='" + FutureTimerInstance.FLAG_DONE +"' and lotRrn = " + lot.getObjectRrn() ;
				List<FutureTimerInstance> noteTimerInstances = adManager.getEntityList(Env.getOrgRrn(), FutureTimerInstance.class, Integer.MAX_VALUE, whereClause, "");
				
				tableManager.setInput(noteTimerInstances);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	}

}
