package com.glory.mes.wip.lot.detail;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Message;
import com.glory.mes.wip.lot.LotSection;

public class DetailLotSection extends LotSection {
	
	protected Text text;
	protected ToolItem itemMerge;

	public DetailLotSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.sectiontitle_lotdetail"));
		initAdObject();
	}

	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		if ("LotInfo".equals(tab.getName())) {
			return new EntityForm(composite, SWT.NONE, tab, form.getMessageManager());
		} else if("ComponentInfo".equals(tab.getName())){
			return new DetailComponentForm(composite, SWT.NONE, tab, form.getMessageManager());
		} else if("ParamInfo".equals(tab.getName())){
			return new DetailParameterForm(composite, SWT.NONE, tab, form.getMessageManager());
		} else if("ReworkInfo".equals(tab.getName())){
			return new DetailReworkInfoForm(composite, SWT.NONE, tab, form.getMessageManager());
		}
//		else if("FutureStepInfo".equals(tab.getName())){
//			return new DetailFutureStepForm(composite, SWT.NONE, tab, form.getMessageManager());
//		} 
		else if("QTimeInfo".equals(tab.getName())){
			return new DetailQTimeForm(composite, SWT.NONE, tab, form.getMessageManager());
		} 
		else{
			return new DetailAttributeForm(composite, SWT.NONE, tab, form.getMessageManager());
		}
	}
	
}
