package com.glory.mes.wip.lot.detail;

import java.util.List;
import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotRework;

public class DetailReworkInfoForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(DetailReworkInfoForm.class);
	private static String TABLE_NAME = "WipLotReworkInfo";

	private ListTableManager tableManager;
	
	public DetailReworkInfoForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
    }
	
	
	@Override
	public void createForm() {
		super.createForm();
		try {
			Composite bodyComposite = form.getBody();

			final ADManager entiyManager = Framework.getService(ADManager.class);
			final ADTable bomTable = entiyManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListTableManager(bomTable);
			tableManager.newViewer(bodyComposite);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	public void refresh() {
		Lot lot = (Lot)getObject();
		if (lot != null&&lot.getObjectRrn()!=null) {
			try {
				ADManager adManager = Framework.getService(ADManager.class);
				 List<LotRework> lotReworks = adManager.getEntityList(Env.getOrgRrn(), LotRework.class, Integer.MAX_VALUE,
                         " lotRrn=" + lot.getObjectRrn(),
                         null);
				 tableManager.setInput(lotReworks);
			} catch (Exception e) {
				logger.error("LotSection searchLotEntity(): Lot isn' t exsited!");
			}
			
		}
	}

}
