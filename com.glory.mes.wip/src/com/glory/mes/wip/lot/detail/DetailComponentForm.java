package com.glory.mes.wip.lot.detail;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class DetailComponentForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(DetailParameterForm.class);
	private static String TABLE_NAME = "WIPComponentUnitList";

	private ListTableManager tableManager;
	
	public DetailComponentForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
    }
	
	
	@Override
	public void createForm() {
		super.createForm();
		try {
			Composite bodyComposite = form.getBody();

			final ADManager entiyManager = Framework.getService(ADManager.class);
			final ADTable bomTable = entiyManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListTableManager(bomTable);
			tableManager.newViewer(bodyComposite);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	public void refresh() {
		Lot lot = (Lot)getObject();
		if (lot != null&&lot.getObjectRrn()!=null) {
			try {
				LotManager manager = Framework.getService(LotManager.class);
				lot = manager.getLotWithComponent(lot.getObjectRrn());
			} catch (Exception e) {
				logger.error("LotSection searchLotEntity(): Lot isn' t exsited!");
			}
			tableManager.setInput(lot.getSubProcessUnit());
		}
	}

}
