package com.glory.mes.wip.lot.detail;

import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.FutureQueryManager;
import com.glory.mes.wip.model.Lot;

public class DetailLotEquipmentDialog extends GlcBaseDialog{

	private static final String FIELD_ENTITYFORM = "eqpInfo";
	private static final String FIELD_LISTTABLE = "eqpListTable";
	
	protected EntityFormField entityFormField;
	protected ListTableManagerField tableManagerField;
	
	protected ListTableManager listTableManager;
	
	private Lot lot;

	
	public DetailLotEquipmentDialog(String adFormName, String authority, IEventBroker eventBroker,Lot lot) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		entityFormField = form.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		
		tableManagerField = form.getFieldByControlId(FIELD_LISTTABLE, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, tableManagerField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED),
				this::selectionChange);
		
		listTableManager = tableManagerField.getListTableManager();
		
		init();
	}
	
	private void init() {
		try {
			FutureQueryManager futureQueryManager = Framework.getService(FutureQueryManager.class);
			List<Equipment> equipments = futureQueryManager.getAvailableEquipmentsByFutureStep(lot, lot.getStepRrn(), lot.getStageId(), lot.getStepName(), null, Env.getSessionContext());
			if (equipments != null) {
				listTableManager.setInput(equipments);
				listTableManager.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void selectionChange(Object obj) {
		try {
			Equipment equipment = (Equipment) listTableManager.getSelectedObject();
			entityFormField.setValue(equipment);
			entityFormField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	
	
	protected void cancelPressed() {
		super.cancelPressed();
	}

	
	@Override
	 protected Point getInitialSize() {
	    return new Point(1000,900);
	 }
	
}
