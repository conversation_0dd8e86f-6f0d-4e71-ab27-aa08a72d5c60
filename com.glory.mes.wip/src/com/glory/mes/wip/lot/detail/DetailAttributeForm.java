package com.glory.mes.wip.lot.detail;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotAttributeValue;

public class DetailAttributeForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(DetailAttributeForm.class);
	TableViewer tableViewer;
	private ListTableManager listTableManager;
	private IField fieldAttribute;
	private static final String Attribute_ID = "attribute1";
	private static final String Attribute = "attribute1";

	public DetailAttributeForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		try {
			listTableManager = new ListTableManager(getAttributeADTable(), false);
			fieldAttribute = new AttributeTableField(Attribute_ID, null, listTableManager);
			ADField adField = new ADField();
			fieldAttribute.setADField(adField);
			addField(Attribute, fieldAttribute);

		} catch (Exception e) {
			logger.error("Error at DetailParameterForm : addFields() : " + e);
		}

	}

	private static ADTable getAttributeADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		ADField Attributename = new ADField();
		Attributename.setName("attributeName");
		Attributename.setIsMain(true);
		Attributename.setIsDisplay(true);
		Attributename.setDisplayLength((long) 200);
		Attributename.setIsTableShowRef(true);
		Attributename.setLabel(Message.getString("wip.attribute_name"));
		Attributename.setLabel_zh(Message.getString("wip.attribute_name"));
		adFields.add(Attributename);

		ADField Attributevalue = new ADField();
		Attributevalue.setName("attributeValue");
		Attributevalue.setIsMain(true);
		Attributevalue.setIsDisplay(true);
		Attributevalue.setDisplayLength((long) 200);
		Attributevalue.setIsTableShowRef(true);
		Attributevalue.setLabel(Message.getString("wip.attribute_value"));
		Attributevalue.setLabel_zh(Message.getString("wip.attribute_value"));
		adFields.add(Attributevalue);

		adTable.setFields(adFields);

		return adTable;

	}

	public String getColumnText(Object element, int columnIndex) {
		if (element instanceof LotAttributeValue) {
			LotAttributeValue attri = (LotAttributeValue) element;
			switch (columnIndex) {
			case 0:
				return attri.getAttributeName();
			case 1:
				return attri.getAttributeValue() != null ? attri.getAttributeValue() : "";
			}
		}
		return "";

	}

	public Object[] getElements(Object inputElement) {
		if (inputElement instanceof List) {
			return ((List) inputElement).toArray();
		}
		return new Object[0];
	}

	@Override
	public void refresh() {
		Lot lot = (Lot) getObject();
		List<LotAttributeValue> attributes = new ArrayList<LotAttributeValue>();
		if (lot != null && lot.getObjectRrn() != null) {
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				Map<String, LotAttributeValue> attributeValueMap = lotManager.getLotAttributeValues(lot.getObjectRrn());
				for (String name : attributeValueMap.keySet()) {
					attributes.add(attributeValueMap.get(name));
				}
				listTableManager.setInput(attributes);
				listTableManager.refresh();
			} catch (Exception e) {
				logger.error("LotSection searchLotEntity(): Lot isn' t exsited!");
			}
		}
	}

}

class AttributeTableField extends AbstractField {

	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
	protected ListTableManager viewer;

	public AttributeTableField(String id, String label, ListTableManager viewer) {
		super(id);
		this.viewer = viewer;
		this.label = label;
	}

	public AttributeTableField(String id, String label, ListTableManager viewer, int style) {
		super(id);
		this.viewer = viewer;
		mStyle = style;
		this.label = label;
	}

	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		int i = 0;
		String labelStr = getLabel();
		if (labelStr != null) {
			mControls = new Control[2];
			Label label = toolkit.createLabel(composite, labelStr);
			mControls[0] = label;
			i = 1;
		} else {
			mControls = new Control[1];
		}

		Composite tableContainer = toolkit.createComposite(composite, SWT.NONE);
		GridData gd = new GridData();
		gd.grabExcessHorizontalSpace = true;
		gd.horizontalAlignment = SWT.FILL;
		gd.heightHint = 450;
		gd.widthHint = 420;
		tableContainer.setLayoutData(gd);
		tableContainer.setLayout(new GridLayout());
		viewer.newViewer(tableContainer);
		viewer.refresh();

		mControls[i] = tableContainer;
	}

	public Label getLabelControl() {
		Control[] ctrl = getControls();
		if (ctrl.length > 1) {
			return (Label) ctrl[0];
		} else {
			return null;
		}
	}

	@Override
	public void refresh() {
		// DO Nothing
	}

	public String getFieldType() {
		return "tablelist";
	}
}