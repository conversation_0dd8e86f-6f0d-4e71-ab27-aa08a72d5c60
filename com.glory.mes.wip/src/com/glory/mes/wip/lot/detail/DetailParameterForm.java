package com.glory.mes.wip.lot.detail;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotParameter;

public class DetailParameterForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(DetailParameterForm.class);
	TableViewer tableViewer;
	private ListTableManager listTableManager;
	private IField fieldParameter;
	private static final String Parameter = "lotParameters";
	private static final String Parameter_ID = "lotParameters";

	public DetailParameterForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		try {
			listTableManager = new ListTableManager(getParameterADTable(), false);
			fieldParameter = new ParameterTableField(Parameter_ID, null, listTableManager);
			ADField adField = new ADField();
			fieldParameter.setADField(adField);
			addField(Parameter, fieldParameter);

		} catch (Exception e) {
			logger.error("Error at DetailParameterForm : addFields() : " + e);
		}

	}

	public static ADTable getParameterADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();

		ADField adFieldparamname = new ADField();
		adFieldparamname.setName("variableName");
		adFieldparamname.setIsMain(true);
		adFieldparamname.setIsDisplay(true);
		adFieldparamname.setDisplayLength((long) 200);
		adFieldparamname.setIsTableShowRef(true);
		adFieldparamname.setLabel(Message.getString("wip.param_name"));
		adFieldparamname.setLabel_zh(Message.getString("wip.param_name"));

		adFields.add(adFieldparamname);

		ADField adFieldparamvalue = new ADField();
		adFieldparamvalue.setName("defaultValue");
		adFieldparamvalue.setIsMain(true);
		adFieldparamvalue.setIsDisplay(true);
		adFieldparamvalue.setDisplayLength((long) 200);
		adFieldparamvalue.setIsTableShowRef(true);
		adFieldparamvalue.setLabel(Message.getString("wip.param_value"));
		adFieldparamvalue.setLabel_zh(Message.getString("wip.param_value"));
		adFields.add(adFieldparamvalue);
		adTable.setFields(adFields);

		return adTable;
	}

	public String getColumnText(Object element, int columnIndex) {

		if (element instanceof LotParameter) {
			LotParameter param = (LotParameter) element;
			switch (columnIndex) {
			case 0:
				return param.getVariableName();
			case 1:
				return param.getDefaultValue();
			}
		}
		return "";

	}

	public Object[] getElements(Object inputElement) {
		if (inputElement instanceof List) {
			return ((List) inputElement).toArray();
		}
		return new Object[0];
	}

	@Override
	public void refresh() {
		List<LotParameter> params = new ArrayList<LotParameter>();
		Lot lot = (Lot) getObject();
		if (lot != null && lot.getObjectRrn() != null) {
			try {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				long processInstanceRrn = lot.getProcessInstanceRrn();
				Map<String, Object> paramMap = prdManager.getCurrentParameter(processInstanceRrn);

				for (String key : paramMap.keySet()) {
					LotParameter param = new LotParameter();
					param.setVariableName(key);
					param.setDefaultValue(DBUtil.toString(paramMap.get(key)));
					params.add(param);
				}

			} catch (Exception e) {
				logger.error("LotSection searchLotEntity(): Lot isn' t exsited!");
			}

		}
		listTableManager.setInput(params);
		listTableManager.refresh();
	}

	class ParameterTableField extends AbstractField {

		protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
		protected ListTableManager viewer;

		public ParameterTableField(String id, String label, ListTableManager viewer) {
			super(id);
			this.viewer = viewer;
			this.label = label;
		}

		public ParameterTableField(String id, String label, ListTableManager viewer, int style) {
			super(id);
			this.viewer = viewer;
			mStyle = style;
			this.label = label;
		}

		@Override
		public void createContent(Composite composite, FormToolkit toolkit) {
			int i = 0;
			String labelStr = getLabel();
			if (labelStr != null) {
				mControls = new Control[2];
				Label label = toolkit.createLabel(composite, labelStr);
				mControls[0] = label;
				i = 1;
			} else {
				mControls = new Control[1];
			}

			Composite tableContainer = toolkit.createComposite(composite, SWT.NONE);
			GridData gd = new GridData();
			gd.grabExcessHorizontalSpace = true;
			gd.horizontalAlignment = SWT.FILL;
			gd.heightHint = 450;
			gd.widthHint = 420;
			tableContainer.setLayoutData(gd);
			tableContainer.setLayout(new GridLayout());
			viewer.newViewer(tableContainer);
			viewer.refresh();
			mControls[i] = tableContainer;
		}

		public Label getLabelControl() {
			Control[] ctrl = getControls();
			if (ctrl.length > 1) {
				return (Label) ctrl[0];
			} else {
				return null;
			}
		}

		@Override
		public void refresh() {
			// DO Nothing
		}

		public String getFieldType() {
			return "tablelist";
		}
	}

}
