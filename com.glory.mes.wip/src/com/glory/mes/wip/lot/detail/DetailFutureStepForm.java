package com.glory.mes.wip.lot.detail;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class DetailFutureStepForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(DetailFutureStepForm.class);
	TableViewer tableViewer;
	private ListTableManager listTableManager;
	private IField fields;
	private static final String FUTURESTEPS = "attribute5";
	private static final String AD_TABLE_NAME = "LotTrackFutureStep";
	
	protected IEventBroker iEventBroker;
	
	private List<String> actionTypes;
	
	public DetailFutureStepForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}
	
	public DetailFutureStepForm(Composite parent, int style, ADTab tab, IMessageManager mmng, IEventBroker eventBroker) {
		super(parent, style, tab, mmng);
		this.iEventBroker = eventBroker;
	}

	@Override
	public void addFields() {
		try {
			listTableManager = new ListTableManager(getParameterADTable(), false);
			fields = new ParameterTableField(FUTURESTEPS, null, listTableManager);
			ADField adField = new ADField();
			fields.setADField(adField);
			addField(FUTURESTEPS, fields);

		} catch (Exception e) {
			logger.error("Error at DetailFutureStepForm : addFields() : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	public static ADTable getParameterADTable() {
		ADTable remoteAdTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			remoteAdTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE_NAME);
			return remoteAdTable;
		} catch (Exception e) {
			logger.error("Error at DetailFutureStepForm : getParameterADTable() : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
		}

		return remoteAdTable;
	}

    @Override
    public void refresh() {

        Lot lot = (Lot) getObject();
        if (lot != null && lot.getObjectRrn() != null) {
            try {
                LotManager manager = Framework.getService(LotManager.class);
                PrdManager prdManager = Framework.getService(PrdManager.class);
                Display.getDefault().asyncExec(new Runnable() {

                    @Override
                    public void run() {
                    	if(LotStateMachine.COMCLASS_SCHD.equals(lot.getState())) {
        					UI.showError(lot.getLotId() + ":" + Message.getString("wip.lot_current_state_is_schd"));
        					return;
                    	}
                    	// ������ε�ǰ����
                        Map<String, Object> lotParamMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
                        List<Lot> currentList = manager.getLotFutureSteps(lot, lotParamMap, false, true, false, 10, true);
                        List<FutureAction> futureActions = manager.getLotFutureActions(
                        		lot, 10, getActionTypes());
                        List<Lot> adList = new ArrayList<Lot>();
                        for (int i = 0; i < currentList.size(); i++) {
                        	//��ʾδ������
                        	for (FutureAction futureAction : futureActions) {
                        		if (currentList.get(i).getStepName().equals(futureAction.getStepName())) {
                        			currentList.get(i).setAttribute3(currentList.get(i).getAttribute3() == null ? futureAction.getAction() : currentList.get(i).getAttribute3()+","+futureAction.getAction());
                        			currentList.get(i).setAttribute4(currentList.get(i).getAttribute4() == null ? futureAction.getDescription() : currentList.get(i).getAttribute4()+","+futureAction.getDescription());
								}
                        	}
                            if (i == 0) {
                                currentList.get(i).setAttribute1("Y");
                            }
                            currentList.get(i).setTrackInTime(null);
                            currentList.get(i).setTrackOutTime(null);
						
                            // �����ʾ�豸����
                            StepState stepState = (StepState) currentList.get(i).getData("STEP");
                            currentList.get(i).setAttribute2(stepState.getUsedStep().getCapabilityName());
					}

                        adList.addAll(currentList);
                        listTableManager.setInput(adList);
                        listTableManager.refresh();
				}
                });

            } catch (Exception e) {
                logger.error("Error at DetailFutureStepForm showAllRefresh ", e);
                ExceptionHandlerManager.asyncHandleException(e);
            } finally {
			}
		}
    }
	
	class ParameterTableField extends AbstractField {

		protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
		protected ListTableManager viewer;

		public ParameterTableField(String id, String label, ListTableManager viewer) {
			super(id);
			this.viewer = viewer;
			this.label = label;
		}

		public ParameterTableField(String id, String label, ListTableManager viewer, int style) {
			super(id);
			this.viewer = viewer;
			mStyle = style;
			this.label = label;
		}

		@Override
		public void createContent(Composite composite, FormToolkit toolkit) {
			int i = 0;
			String labelStr = getLabel();
			if (labelStr != null) {
				mControls = new Control[2];
				Label label = toolkit.createLabel(composite, labelStr);
				mControls[0] = label;
				i = 1;
			} else {
				mControls = new Control[1];
			}

			Composite tableContainer = toolkit.createComposite(composite, SWT.NONE);
			GridData gd = new GridData();
			gd.grabExcessHorizontalSpace = true;
			gd.horizontalAlignment = SWT.FILL;
			gd.heightHint = 450;
			gd.widthHint = 420;
			tableContainer.setLayoutData(gd);
			tableContainer.setLayout(new GridLayout());
			viewer.newViewer(tableContainer);
			viewer.refresh();
			
			viewer.addDoubleClickListener(new AbstractMouseSelectionAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					Lot lot = (Lot) listTableManager.getSelectedObject();
					DetailLotEquipmentDialog baseDialog = new DetailLotEquipmentDialog("WIPTrackInFutureSelEqpInfo", null, iEventBroker, lot);
					if (Dialog.OK == baseDialog.open()) {
						
					}
				}
			});
			mControls[i] = tableContainer;
			
		}

		public Label getLabelControl() {
			Control[] ctrl = getControls();
			if (ctrl.length > 1) {
				return (Label) ctrl[0];
			} else {
				return null;
			}
		}

		@Override
		public void refresh() {
			// DO Nothing
		}

		public String getFieldType() {
			return "tablelist";
		}
	}
	
	public List<String> getActionTypes() {
		actionTypes = new ArrayList<String>();
		this.actionTypes.add(FutureAction.ACTION_NOTE);
		this.actionTypes.add(FutureAction.ACTION_HOLD);
		this.actionTypes.add(FutureAction.ACTION_TIMER);
		this.actionTypes.add(FutureAction.ACTION_MERGE);	
		return actionTypes;
	}

}
