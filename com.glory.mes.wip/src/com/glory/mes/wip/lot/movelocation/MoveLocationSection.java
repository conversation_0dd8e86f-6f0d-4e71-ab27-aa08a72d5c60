package com.glory.mes.wip.lot.movelocation;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class MoveLocationSection extends LotSection {
	private static final Logger logger = Logger.getLogger(MoveLocationSection.class);
	
	public static final String KEY_MOVE = "move";
	
	protected AuthorityToolItem move;
	protected MoveLocationForm moveLocationForm;

	public MoveLocationSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.movelocation_location"));
		initAdObject();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemMove(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemMove(ToolBar tBar) {
		move = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_MOVE);
		move.setAuthEventAdaptor(this::moveAdapter);
		move.setText(Message.getString("wip.move"));
		move.setImage(SWTResourceCache.getImage("move_to_loc"));
//		move.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				moveAdapter(event);
//			}
//		});
	}

	protected void moveAdapter(SelectionEvent event) {
		try {
//			form.getMessageManager().removeAllMessages();
////			boolean saveFlag = true;
//			if (getAdObject() != null) {
//				boolean saveFlag = true;
//				for (Form detailForm : getDetailForms()) {
//					if (!detailForm.saveToObject()) {
//						saveFlag = false;
//					}
//				}
//				if (saveFlag) {
//					for (Form detailForm : getDetailForms()) {
//						PropertyUtil.copyProperties(getAdObject(), detailForm
//								.getObject(), detailForm.getFields());
//					}
//					
//				}  else {
//					return;
//				}
//			}
			Lot lot = (Lot)getAdObject();
			String move = ((RefTableField)getField("move")).getText();
			String location = lot.getLocation();
			if (move == null || move.trim().length() == 0) {
				UI.showError(Message.getString("wip.movelocation_location_not_null"));
				return;
			} else if (move.trim().equals(location)) {
				UI.showError(Message.getString("wip.movelocation_location_not_equal"));
				return;
			}
			
			String comment = ((TextField)getField("Comment")).getText();
			LotAction lotAction = new LotAction();
			lotAction.setActionCode(move);
			lotAction.setActionReason(comment);
			lotAction.setActionComment(comment);
			LotManager lotManager = Framework.getService(LotManager.class);
			String operator = Env.getUserName();
			if (this.move.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
				operator = (String) this.move.getData(LotAction.ACTION_TYPE_OPERATOR);
			}
			lot.setOperator1(operator);
			lotManager.moveToLocation(lot, move, lotAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.movelocation_move_successful"));
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		moveLocationForm = new MoveLocationForm(composite, SWT.NONE, tab, mmng);
		return moveLocationForm;
	}

	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Lot newBase = (Lot)this.getAdObject();
		if (newBase != null ){
			statusChanged(newBase.getState());
		} else {
			statusChanged("");
		}
	}
	
	@Override
	public void statusChanged(String newStatus) {
		if (newStatus == null || "".equals(newStatus.trim())) {
			move.setEnabled(false);
		} else if (LotStateMachine.STATE_FIN.equals(newStatus) ||
				LotStateMachine.STATE_WAIT.equals(newStatus)) {
			move.setEnabled(true);
		} else {
			move.setEnabled(false);
		}
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		super.refresh();
	}
}
