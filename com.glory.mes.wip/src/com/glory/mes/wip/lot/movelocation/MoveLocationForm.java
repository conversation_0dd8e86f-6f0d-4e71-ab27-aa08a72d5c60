package com.glory.mes.wip.lot.movelocation;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;

public class MoveLocationForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(MoveLocationForm.class);
	
	public static final String FIELD_MOVELOCATION = "move";
	private IField fieldLoaction;

	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";

	public MoveLocationForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public MoveLocationForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public MoveLocationForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public MoveLocationForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
			ADTable adTable;
			FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());
			ADManager adManager = Framework.getService(ADManager.class);
			adTable  = adManager.getADTable(Env.getOrgRrn(), "BASLocation");
			ADRefTable refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setTextField("name");
			
			ListTableManager tableManager = new ListTableManager(adTable);
			if (refTable.getWhereClause() == null || "".equalsIgnoreCase(refTable.getWhereClause().trim())
					|| StringUtil.parseClauseParam(refTable.getWhereClause()).size() == 0){
				
				List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), 
						adTable.getObjectRrn(), Env.getMaxResult(), refTable.getWhereClause(), refTable.getOrderByClause());
				tableManager.setInput(list);					
			}
			
			fieldLoaction = createRefTableFieldList(FIELD_MOVELOCATION, Message.getString("wip.movelocation_movetoLoaction"), tableManager, refTable, 32);			
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);
			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			addField(FIELD_MOVELOCATION, fieldLoaction);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("MoveLocationForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField)
						&& !f.equals(fieldLoaction)
						&& !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}
	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			return true;
		}
		return false;
	}
	
	@Override
	public boolean validate() {
		boolean validFlag = super.validate();
		if(fieldLoaction.getValue() == null
				|| "".equals(String.valueOf(fieldLoaction.getValue()).trim())){
			validFlag = false;
			mmng.addMessage(FIELD_MOVELOCATION, String.format(Message
					.getString("common.ismandatry"), FIELD_MOVELOCATION), null,
					IMessageProvider.ERROR, fieldLoaction.getControls()[1]);
		}
		return validFlag;
	}
}
