package com.glory.mes.wip.lot;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.AttributeForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotAttribute;
import com.glory.mes.wip.model.LotAttributeValue;

public class LotAttributeForm extends AttributeForm {

	private Lot lot;
	
	public LotAttributeForm(Composite parent, int style,
			String objectType, Object object, int gridY, IMessageManager mmng) {
		super(parent, style, null, mmng);
		this.setGridY(gridY);
		this.lot = (Lot)object; 
		createForm();
	}
	
	public void createForm() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<LotAttribute> attributes = adManager.getEntityList(Env.getOrgRrn(), LotAttribute.class, 
					Integer.MAX_VALUE, "", "seqNo");
			this.setAttributes(attributes);
			super.createForm();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
    public void setObject(Object object) {
		lot = (Lot)object;
        //������û��Attributeʱ,�����ݿ���ȡֵ
        if (lot.getObjectRrn() != null && lot.getAttributeValues() == null) {
        	try {
        		LotManager lotManager = Framework.getService(LotManager.class);
        		Map<String, LotAttributeValue> valueMap = lotManager.getLotAttributeValues(lot.getObjectRrn());
				super.setObject(valueMap.values());
        	} catch (Exception e) {
				e.printStackTrace();
			}
        } else {
        	super.setObject(new ArrayList<LotAttributeValue>());
        }
    }
	
}
