package com.glory.mes.wip.lot.unmerge;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class UnMergeForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(UnMergeForm.class);
	private Lot lot;
	private IField fieldUnMergeCode;
	private static final String UNMERGECODE = "UnMergeCode";
	private static final String UNMERGECODE_ID = "unMergeCode";
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";
	private String unMergeCodeRefName = "UnMergeCode";
	private LotAction lotAction = new LotAction();

	private static final String AD_TABLE = "WIPLotSplit";
	private IField fieldMergedLots;
	private static final String MERGEDLOTS = "MergedLots";
	private static final String MERGEDLOTS_ID = "mergedLots";
	
	public UnMergeForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public UnMergeForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public UnMergeForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public UnMergeForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			fieldUnMergeCode = createUserRefList(UNMERGECODE_ID, Message.getString("wip.unmerge_code") + "*", UNMERGECODE, true);
			
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE);
			ListTableManager listTableManager = new ListTableManager(adTable);
			fieldMergedLots = createTableListField(MERGEDLOTS_ID, Message.getString("wip.merged_lot_list"), listTableManager);
			
			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			addField(UNMERGECODE, fieldUnMergeCode);
			addField(COMMENT, fieldComment);
			
			fieldMergedLots.setADField(adField);
			addField(MERGEDLOTS, fieldMergedLots);
			
		} catch (Exception e) {
			logger.error("UnShipForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField)
						&& !f.equals(fieldUnMergeCode)
						&& !f.equals(fieldComment)) {
					if (f.equals(fieldMergedLots)) {
						try {
							Lot parentLot = (Lot) object;
							LotManager lotManager = Framework.getService(LotManager.class);
							List<Lot> childLots = lotManager.getCanUnMergeLots(parentLot, Env.getSessionContext());
							f.setValue(childLots);
						} catch (Exception e) {
							e.printStackTrace();
						}
					} else {
						Object o = PropertyUtil.getPropertyForIField(object, f
								.getId());
						f.setValue(o);
					}
					
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			lot = (Lot) object;
			lotAction.setActionCode((String) fieldUnMergeCode.getValue());
			lotAction.setActionComment((String) fieldComment.getValue());
			lot.setChildrenLots((List<Lot>) (List)fieldMergedLots.getValue());
			return true;
		}
		return false;
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			XCombo combo = (XCombo) ((RefTableField)fieldUnMergeCode).getComboControl();
			RCPUtil.refreshUserRefListCombo(combo, unMergeCodeRefName);
			
			fieldUnMergeCode.setValue("");
			fieldUnMergeCode.refresh();
			((TextField) fieldComment).setText("");
		} catch (Exception e) {
			logger.error("UnShipForm : refresh()", e);
		}
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	public Lot getLot() {
		return lot;
	}

	@Override
	public boolean validate() {
		boolean validFlag = super.validate();
		if (fieldUnMergeCode.getValue() == null
				|| "".equals(String.valueOf(fieldUnMergeCode.getValue()).trim())) {
			validFlag = false;
			mmng.addMessage(UNMERGECODE_ID, String.format(Message
					.getString("common.ismandatry"), UNMERGECODE_ID), null,
					IMessageProvider.ERROR, fieldUnMergeCode.getControls()[1]);
		}
		return validFlag;
	}
	
}
