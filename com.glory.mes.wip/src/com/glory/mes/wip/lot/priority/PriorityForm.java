package com.glory.mes.wip.lot.priority;


import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.wip.model.Lot;

@Deprecated
public class PriorityForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(PriorityForm.class);


	private IField fieldLotPro, fieldComment;
	private static final String LOTPRO = "LotPriority";
	private static final String LOTPRO_ID = "priority";
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "lotComment";
	public String comment;
	public PriorityForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public PriorityForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public PriorityForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public PriorityForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}	
	
	@Override
	public void addFields() {
		super.addFields();
		try {	
			
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
			fieldLotPro = createUserRefList(LOTPRO_ID, LOTPRO, LOTPRO, true);
			fieldComment = createTextArea(COMMENT_ID, Message.getString("wip.comment"), "");
		
			addField(LOTPRO,fieldLotPro);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("LotProForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) 
						&&!f.equals(fieldLotPro)
					) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
//			setEnabled();
		}
	}

//	@Override
//	public void setEnabled() {
//		if (object != null && object instanceof ADBase) {
//			ADBase base = (ADBase) object;
//			for (IField f : fields.values()) {
//				ADField adField = adFields.get(f.getId());
//				if (adField != null && !adField.getIsEditable()) {
//					if (base.getObjectRrn() == null || base.getObjectRrn() == 0) {
//						f.setEnabled(true);
//					} else {
//						f.setEnabled(false);
//					}
//				}
//			}
//			fieldComment.setEnabled(true);
//		}
//	}
	
	@Override
	public void refresh() {
		super.refresh();
		try {
			Lot lot = (Lot) object;
			// TODO �ص����
			RefTableField trflotType = (RefTableField) fieldLotPro;
			List list = trflotType.getInput();
			if (lot != null && lot.getObjectRrn() != null) {
				for(int i = 0; i < list.size(); i++){
					if(((ADURefList)list.get(i)).getText().equals(lot.getLotType())){
						((RefTableField)fieldLotPro).setData(lot);
					}
				}	
			} 
//			RefTableField trflotType = (RefTableField) fieldLotPro;
//			trflotType.setSelectionIndex(-1);
//			List list = trflotType.getInput();
//			if (lot != null && lot.getObjectRrn() != null) {
//				for(int i = 0; i < list.size(); i++){
//					if(((ADURefList)list.get(i)).getText().equals(lot.getLotType())){
//						((RefTableField)fieldLotPro).setSelectionIndex(i);
//					}
//				}	
//			} 
		} catch (Exception e) {
			logger.error("LotPorForm : refresh()", e);
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			Lot lot =(Lot)object;
			PropertyUtil.setProperty(lot, fieldLotPro.getId(),
					fieldLotPro.getValue());
			PropertyUtil.setProperty(lot, fieldComment.getId(),
					fieldComment.getValue());
			return true;
		}
		return false;
	}

	@Override
	public boolean validate() {
		boolean validFlag = true;
		return validFlag;
	}

}

