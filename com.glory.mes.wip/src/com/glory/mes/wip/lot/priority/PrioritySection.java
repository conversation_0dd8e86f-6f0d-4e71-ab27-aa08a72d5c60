package com.glory.mes.wip.lot.priority;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.framework.core.exception.ExceptionBundle;

public class PrioritySection extends LotSection {
	private static final Logger logger = Logger.getLogger(EntitySection.class);

	public static final String KEY_CHANGE = "change";
	
	protected EntityForm itemForm;
	protected EntityForm changeForm;

	private AuthorityToolItem itemChange;
	
	public PrioritySection() {
		super();
	}

	public PrioritySection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.priority_edit"));
		initAdObject();
	}

	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChangePriority(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	private void createToolItemChangePriority(ToolBar tBar) {
		itemChange = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_CHANGE);
		itemChange.setAuthEventAdaptor(this::saveAdapter);
		itemChange.setImage(SWTResourceCache.getImage("save"));
		itemChange.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
//		itemChange.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				saveAdapter(event);
//			}
//		});
		
	}

	protected void saveAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (!changeForm.saveToObject()) {
					saveFlag = false;
				}
				
				if (saveFlag) {
					Lot lot = (Lot) changeForm.getObject();
					lot.setOperator1(Env.getUserName());
					
					LotManager lotManager = Framework.getService(LotManager.class);
					SessionContext sc = Env.getSessionContext();
					if (itemChange.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
						sc.setUserName((String) itemChange.getData(LotAction.ACTION_TYPE_OPERATOR));
					}
					LotAction action = new LotAction();
					action.setActionComment(lot.getAttribute1().toString());
					//���ú�̨������������
					lotManager.changeLotPriority(lot.getLotId(), lot.getPriority(), action, sc);
					UI.showInfo(Message.getString("wip.priority_successed"));// ������ʾ��
					refresh();
				}
			}
			txtLot.setFocus();
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
		
		changeForm = new EntityForm(itemForm, SWT.NONE, new Lot(), getADTable1(), form.getMessageManager());
		changeForm.setLayout(new GridLayout());
		GridData gd = new GridData(GridData.FILL_BOTH);
		changeForm.setLayoutData(gd);
		return itemForm;
	}
	
	public void statusChanged(String newStatus){
		if(LotStateMachine.STATE_SHIP.equals(newStatus)) {
			itemChange.setEnabled(false);
		} else {
			itemChange.setEnabled(true);
		}
	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));				
				itemForm.setObject((Lot)getAdObject());
				itemForm.refresh();
			}
			form.getMessageManager().removeAllMessages();
			if(changeForm != null) {
				changeForm.setObject((Lot)getAdObject());
				changeForm.loadFromObject();
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
	}
	
	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPChangePriority");
		return midTable;
	}

}
