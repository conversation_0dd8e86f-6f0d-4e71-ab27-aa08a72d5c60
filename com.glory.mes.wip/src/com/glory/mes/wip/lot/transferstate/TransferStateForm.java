package com.glory.mes.wip.lot.transferstate;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.Lot;

public class TransferStateForm extends EntityForm {
    private static final Logger logger = Logger.getLogger(TransferStateForm.class);

    private IField fieldWarehouseId;
    private static final String WAREHOUSEID = "WarehouseId";
    private static final String WAREHOUSE_ID = "warehouseId";

    private IField fieldComment;
    private static final String COMMENT = "Comment";
    private static final String COMMENT_ID = "holdComment";

    public TransferStateForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
        super(parent, style, tab, mmng);
    }

    @Override
    public void addFields() {
        super.addFields();
        try {
            ADField separatorADField = new ADField();
            separatorADField.setIsSameline(true);
            SeparatorField separatorField = createSeparatorField("Separator", "");
            separatorField.setADField(separatorADField);
            this.addField("Separator", separatorField);

            ADManager adManager = (ADManager) Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "MMWarehouse");
            ADRefTable refTable = new ADRefTable();
            refTable.setTableRrn(adTable.getObjectRrn());
            refTable.setKeyField("warehouseId");
            refTable.setTextField("warehouseId");

            ListTableManager tableManager = new ListTableManager(adTable);
            List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn()
                    .longValue(), Env.getMaxResult(), "", null);
            tableManager.setInput(list);

            fieldWarehouseId = createRefTableFieldList(WAREHOUSE_ID, "Warehouse", tableManager, refTable,
                    32);

            fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);

            addField(WAREHOUSEID, fieldWarehouseId);
            //addField(COMMENT, fieldComment);
        } catch (Exception e) {
            logger.error("HoldLotForm : Init listItem", e);
        }
    }

    @Override
    public void loadFromObject() {
        if (object != null) {
            for (IField f : fields.values()) {
                if (!(f instanceof SeparatorField) && !f.equals(fieldWarehouseId)) {
                    Object o = PropertyUtil.getPropertyForIField(object, f.getId());
                    f.setValue(o);
                }
            }
            refresh();
            setEnabled();
        }
    }

    @Override
    public void setEnabled() {
        if (object != null && object instanceof ADBase) {
            ADBase base = (ADBase) object;
            for (IField f : fields.values()) {
                ADField adField = adFields.get(f.getId());
                if (adField != null && !adField.getIsEditable()) {
                    if (base.getObjectRrn() == null || base.getObjectRrn() == 0) {
                        f.setEnabled(true);
                    } else {
                        f.setEnabled(false);
                    }
                }
            }

            Lot lot = (Lot) object;
            if (lot.getWarehouseId() != null) {
                fieldWarehouseId.setValue(lot.getWarehouseId());
                fieldWarehouseId.setEnabled(false);
            } else {
                fieldWarehouseId.setEnabled(true);
            }
            fieldComment.setEnabled(true);
        }
    }

    @Override
    public void refresh() {
        super.refresh();
        try {

        } catch (Exception e) {
            logger.error("TransferStateForm : refresh()", e);
        }
    }

    @Override
    // ���浽���ݿ�
    public boolean saveToObject() {
        if (object != null) {
            if (!validate()) {
                return false;
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean validate() {
        boolean validFlag = true;
        if (fieldWarehouseId.getValue() != null
                && !"".equals(String.valueOf(fieldWarehouseId.getValue()).trim())) {
            validFlag = validFlag && true;
        } else {
            validFlag = validFlag && false;
            mmng.addMessage(WAREHOUSE_ID,
                    String.format(Message.getString("common.ismandatry"), WAREHOUSE_ID), null,
                    IMessageProvider.ERROR, fieldWarehouseId.getControls()[1]);
        }
        return validFlag;
    }

}
