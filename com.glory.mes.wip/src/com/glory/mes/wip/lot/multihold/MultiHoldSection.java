package com.glory.mes.wip.lot.multihold;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.idquery.IdQueryEntityQueryListSection;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;

public class MultiHoldSection extends IdQueryEntityQueryListSection {
	
	public static final String KEY_MULTIHOLD = "multiHold";

	protected AuthorityToolItem itemMultiHold;
	protected ADManager manager;
	protected ADTable muHold;
	protected EntityForm multiHoldForm;
	protected LotAction action;

	protected LotHold multihold = new LotHold();
	protected IField fieldHoldCode;
	protected Text comment, holdReasonText;
	protected String holdCode = new String("holdCode");
	protected String holdReason = new String("holdReason");
	protected String holdComment = new String("holdComment");
	protected String holdOwner = new String("holdOwner");
	TextField commentField;
	ADField reasonField, codeField;
	ADRefTable refTable;

	public MultiHoldSection(ListTableManager tableManager) {
		super(tableManager);

	}

	@Override
	public void refresh() {
		super.refresh();
		action = new LotAction();
		multiHoldForm.setObject(action);
		multiHoldForm.loadFromObject();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemMultiHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent,
			int sectionStyle) {
		super.createContents(form, parent, sectionStyle);
		section.setText(Message.getString("wip.multihold_title"));
		Composite client = (Composite) section.getClient();
		Composite holdComp = new Composite(client, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		holdComp.setLayout(layout);
		holdComp.setLayoutData(new GridData(GridData.FILL_BOTH));

		multiHoldForm = new EntityForm(holdComp, SWT.NONE, new LotAction(), getADTable1(), form.getMessageManager());
		multiHoldForm.setLayout(new GridLayout());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.heightHint = 100;
		multiHoldForm.setLayoutData(gd);
	}

	protected void createToolItemMultiHold(ToolBar tBar) {
		itemMultiHold = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_MULTIHOLD);
		itemMultiHold.setAuthEventAdaptor(this::multiHoldAdapter);
		itemMultiHold.setText(Message.getString("wip.multihold"));
		itemMultiHold.setImage(SWTResourceCache.getImage("hold-lot"));
//		itemMultiHold.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				multiHoldAdapter(event);
//			}
//		});
	}

	protected void multiHoldAdapter(SelectionEvent event) {
		if (multiHoldForm.validate()) {
			managedForm.getMessageManager().removeAllMessages();
			multiHoldForm.saveToObject();
			action = (LotAction) multiHoldForm.getObject();
			multihold.setHoldCode(action.getActionCode());
			multihold.setHoldReason(action.getActionReason());
			multihold.setHoldOwner(action.getActionOperator());
			multihold.setHoldComment(action.getActionComment());
		
			
			List<HoldError> errlogs = new ArrayList<HoldError>();
			List<Object> objects = tableManager.getCheckedObject();
			
			if (objects.size() != 0) {
				for (Object object : objects) {
					Lot lot = (Lot) object;
					try {
						LotManager lotManager = Framework.getService(LotManager.class);
						
						SessionContext sc = Env.getSessionContext();
						if (itemMultiHold.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
							sc.setUserName((String) itemMultiHold.getData(LotAction.ACTION_TYPE_OPERATOR));
						}
						
						lotManager.holdLot(lot, multihold, sc);
					} catch (Exception e1) {
						HoldError ho = new HoldError();
						ho.setLotId(lot.getLotId());
						ho.setState(lot.getState());
						ho.setErrMessage(e1.getMessage());
						errlogs.add(ho);
					}
				}
				
				if (errlogs.size() != 0) {
					MultiHoldErrorDialog ErrorDialog = new MultiHoldErrorDialog(event.widget.getDisplay().getActiveShell(), errlogs);
					ErrorDialog.open();
					errlogs.clear();
				} else {
					UI.showInfo(Message.getString("common.multihold_successed"));
				}
				refresh();
				queryAdapter();
			}
		} else {
			UI.showWarning(Message.getString("warn.required_entry"));
			return;
		}
	}

	@Override
	protected void queryAdapter() {
		managedForm.getMessageManager().removeAllMessages();
		if (!getQueryForm().validate()) {
			return;
		} else {
			String whereClause = (new StringBuilder(" 1 = 1 "))
					.append(getQueryForm().createWhereClause())
					.append(" and comClass in ('WIP', 'FIN')").toString();
			whereClause = StringUtil.relpaceWildcardCondition(whereClause);
			setWhereClause(whereClause);
			super.refresh();
			return;
		}
	}

	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPHoldAction");
		return midTable;
	}

	@Override
	public List<Object> getObjectsByInClause(List<String> ids) {
		try {
			Set idSet = new HashSet(ids);
			Map<String, Object> fieldMap = new HashMap<String, Object>();
			fieldMap.put("lotIds", idSet);
			ADManager adManager = Framework.getService(ADManager.class);
			List<Lot> lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, 
					Integer.MIN_VALUE, Integer.MAX_VALUE, " lotId in (:lotIds) ", "", fieldMap);
			return (List<Object>)(List)lots;
		} catch (Exception e) {
			logger.error("MultiHoldSection getObjectsByInClause error:", e);
		}
		return null;
	}
}
