package com.glory.mes.wip.lot.multihold;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.LotHold;

public class MultiHoldForm extends EntityForm {

    public MultiHoldForm(Composite parent, int style, ADTable table, IMessageManager mmng) {
        super(parent, style, table, mmng);
    }

    private static final Logger logger = Logger.getLogger(MultiHoldForm.class);

    private IField fieldHoldCode;
    private static final String HOLDCODE = "HoldCode";
    private static final String HOLDCODE_ID = "holdCode";
    private IField fieldHoldReason;
    private static final String HOLDREASON = "HoldReason";
    private static final String HOLDREASON_ID = "holdReason";
    private IField fieldComment;
    private static final String COMMENT = "Comment";
    private static final String COMMENT_ID = "holdComment";
    private IField fieldHoldOwner;
    // private IField fieldHoldPwd;
    // private static final String HOLDPWD = "HoldPwd";
    // private static final String HOLDPWD_ID = "holdPwd";
    private static final String HOLDOWNER = "HoldOwner";
    private static final String HOLDOWNER_ID = "holdOwner";
    protected LotHold lotHold = new LotHold();

    @Override
    public void addFields() {
        try {
            this.setGridY(2);
            fieldHoldCode = createUserRefList(HOLDCODE_ID, Message.getString("wip.holdcode"), "HoldCode", false);

            fieldHoldReason = createText(HOLDREASON_ID, Message.getString("wip.holdreason"), "", 256);
            ADManager adManager = (ADManager) Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADUserGroup");
            ADRefTable refTable = new ADRefTable();
            refTable.setTableRrn(adTable.getObjectRrn());
            refTable.setKeyField("name");
            refTable.setTextField("name");
            fieldHoldOwner = createSearchField(HOLDOWNER_ID, Message.getString("wip.holdowner"),
                    adTable, refTable, "", SWT.READ_ONLY);
            fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);

            addField(HOLDCODE, fieldHoldCode);
            addField(HOLDREASON, fieldHoldReason);
            addField(HOLDOWNER, fieldHoldOwner);
            // addField(HOLDPWD, fieldHoldPwd);
            addField(COMMENT, fieldComment);
        } catch (Exception e) {
            logger.error("HoldLotForm : Init listItem", e);
        }
    }

    @Override
    public void loadFromObject() {
        if (object != null) {
            for (IField f : fields.values()) {
                if (!(f instanceof SeparatorField) && !f.equals(fieldHoldCode)
                        && !f.equals(fieldHoldReason) && !f.equals(fieldHoldOwner)
                        && !f.equals(fieldComment)) {
                    Object o = PropertyUtil.getPropertyForIField(object, f.getId());
                    f.setValue(o);
                }
            }
            refresh();
            setEnabled();
        }
    }

    @Override
    public boolean validate() {
        mmng.removeAllMessages();
        boolean validFlag = true;
        if (fieldHoldCode.getValue() != null
                && !"".equals(String.valueOf(fieldHoldCode.getValue()).trim())) {
            validFlag = validFlag && true;
        } else {
            validFlag = validFlag && false;
            mmng.addMessage(HOLDCODE_ID,
                    String.format(Message.getString("common.ismandatry"), HOLDCODE_ID), null,
                    IMessageProvider.ERROR, fieldHoldCode.getControls()[1]);
        }

        if (fieldHoldOwner.getValue() != null
                && !"".equals(String.valueOf(fieldHoldOwner.getValue()).trim())) {
            validFlag = validFlag && true;
        } else {
            validFlag = validFlag && false;
            mmng.addMessage(HOLDOWNER_ID,
                    String.format(Message.getString("common.ismandatry"), HOLDOWNER_ID), null,
                    IMessageProvider.ERROR, fieldHoldOwner.getControls()[1]);
        }

        return validFlag;
    }

    @Override
    public void refresh() {
        //super.refresh();
        try {
            fieldHoldCode.setValue("");
            ((TextField) fieldHoldReason).setText("");
            fieldHoldOwner.setValue(null);
            ((TextField) fieldComment).setText("");
        } catch (Exception e) {
            logger.error("HoldLotForm : refresh()", e);
        }
    }

}
