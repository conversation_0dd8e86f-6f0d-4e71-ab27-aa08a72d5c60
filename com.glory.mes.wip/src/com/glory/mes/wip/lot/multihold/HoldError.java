package com.glory.mes.wip.lot.multihold;

import java.io.Serializable;

public class HoldError implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private String lotId;
	private String state;
	private String errMessage;
	
	public void setLotId(String lotId) {
		this.lotId = lotId;
	}
	public String getLotId() {
		return lotId;
	}
	
	public void setState(String state) {
		this.state = state;
	}
	public String getState() {
		return state;
	}
	
	public void setErrMessage(String errMessage) {
		this.errMessage = errMessage;
	}
	public String getErrMessage() {
		return errMessage;
	}
	
}
