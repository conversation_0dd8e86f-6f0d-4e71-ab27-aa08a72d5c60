package com.glory.mes.wip.lot.multihold;

import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.viewers.StructuredViewer;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.viewers.ListTableManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiHoldErrorDialog extends BaseTitleDialog {	
	
	IManagedForm form;
	List<HoldError> errlogs;
	HoldErrorTableManager tableManager;
	StructuredViewer viewer;
	
	
	public MultiHoldErrorDialog(Shell parentShell, List<HoldError> errlogs) {
		super(parentShell);
		this.errlogs = errlogs;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		String dialogTitle = String.format(Message.getString("wip.multihold_error_title"));
		setTitle(dialogTitle);
		Composite composite = (Composite) super.createDialogArea(parent);
		FormToolkit toolkit = new FormToolkit(composite.getDisplay());		
		ScrolledForm sForm = toolkit.createScrolledForm(composite);
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		Composite body = sForm.getForm().getBody();
		configureBody(body);		
		tableManager = new HoldErrorTableManager();
		viewer = tableManager.createViewer(body, toolkit, 
				tableManager.getColumns(), tableManager.getColumnsHeader(), new int[] {15, 15, 70}, 180);
		viewer.setInput(errlogs);
		tableManager.updateView(viewer);
		return composite;
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, true);
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(800), shellSize.x), Math.max(
				convertVerticalDLUsToPixels(350), shellSize.y));
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.CANCEL_ID, Message.getString(ExceptionBundle.bundle.CommonExit()), true);
	}
	
	class HoldErrorTableManager extends ListTableManager {
		public String LotID = "lotId";
		public String State = "state";
		public String ErrMessage = "errMessage";

		public String Cloumn_LotID = Message.getString("wip.lot_id");
		public String Cloumn_State = Message.getString("wip.state");
		public String Cloumn_ErrMessage = Message.getString("wip.multihold_errmessage");
		
		public HoldErrorTableManager() {
			super(null);
		}
		
	    @Override
	    protected String[] getColumns() {
	    	return new String[]{LotID, State, ErrMessage};
	    }
	    
	    @Override
	    protected String[] getColumnsHeader() {
	    	return new String[]{Cloumn_LotID, Cloumn_State, Cloumn_ErrMessage};
	    }
	    
	    protected Integer[] getColumnSize() {
	    	return new Integer[]{15, 15, 70};
	    }
	}

//	@Override
//	protected int getShellStyle() {
//		return super.getShellStyle() | SWT.RESIZE;
//	}

}
