package com.glory.mes.wip.lot.recovery;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.swt.widgets.TreeItem;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.EndState;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.future.FutureMultiProcedureTimer;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotRework;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.QtyUnit;
import com.glory.mes.wip.sorting.LotSortingAction;

public class RecoveryEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.recovery.glc.RecoveryEditor";

	private static final String DIALOG_SPLIT_FORM_NAME = "WIPLotComponentSplitRecovery";
	private static final String DIALOG_SPLIT_QTIME_FORM_NAME = "WIPVProcMuiltQtimerDialogForm";
	
	private static final String FIELD_LOTFLOWGLC = "lotFlowGlc";
	private static final String FIELD_RECOVERYINFOGLC = "recoveryInfoGlc";
	private static final String FIELD_LOTFLOW = "lotFlow";
	private static final String FIELD_REWORKPROCEDURE = "reworkProcedure";
	private static final String FIELD_LOTINFO = "lotInfo";
	private static final String FIELD_REWORKINFO = "reworkInfo";
	private static final String FIELD_SPLITINFO = "splitInfo";
	private static final String FIELD_SPLITREWORKQTY = "splitReworkQty";
	private static final String FIELD_ISAUTOHOLD = "isAutoHold";

	private static final String BUTTON_RECOVERY = "recovery";
	private static final String BUTTON_SPLITRECOVERY = "splitRecovery";
	private static final String BUTTON_REFRESH = "refresh";

	public static final java.lang.String TREE_BUTTON_NAME_RETURTO = "returnTo";
	public static final java.lang.String TREE_BUTTON_NAME_RETURNANDMERGE = "returnAndMerge";
	
	protected GlcFormField lotFlowGlcField;
	protected GlcFormField recoveryInfoGlcField;
	protected CustomField lotFlowField;
	protected CustomField reworkProcedureField;
	protected EntityFormField lotInfoField;
	protected EntityFormField reworkInfoField;
	protected EntityFormField splitInfoField;
	protected TextField splitReworkQtyField;
	protected BooleanField booleanField;
	
	private FlowCustomComposite lotFlowCustomComposite;
	private FlowCustomComposite procedureFlowCustomComposite;
	
	private ToolItem itemRecovery;
	private ToolItem itemSplitRecovery;
	
	protected Lot currentLot;
	protected Procedure reworkProcedure;
	protected StepState futureStepState = null;
	
	protected TreeItem currentSelectedItem;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotFlowGlcField = form.getFieldByControlId(FIELD_LOTFLOWGLC, GlcFormField.class);	
		lotFlowField = lotFlowGlcField.getFieldByControlId(FIELD_LOTFLOW, CustomField.class);
		reworkProcedureField = lotFlowGlcField.getFieldByControlId(FIELD_REWORKPROCEDURE, CustomField.class);
		lotFlowCustomComposite = (FlowCustomComposite) lotFlowField.getCustomComposite();
		procedureFlowCustomComposite = (FlowCustomComposite) reworkProcedureField.getCustomComposite();
		
		recoveryInfoGlcField = form.getFieldByControlId(FIELD_RECOVERYINFOGLC, GlcFormField.class);
		lotInfoField = recoveryInfoGlcField.getFieldByControlId(FIELD_LOTINFO, EntityFormField.class);
		reworkInfoField = recoveryInfoGlcField.getFieldByControlId(FIELD_REWORKINFO, EntityFormField.class);
		splitInfoField = recoveryInfoGlcField.getFieldByControlId(FIELD_SPLITINFO, EntityFormField.class);
		splitReworkQtyField = splitInfoField.getFieldByControlId(FIELD_SPLITREWORKQTY, TextField.class);
		booleanField = splitInfoField.getFieldByControlId(FIELD_ISAUTOHOLD, BooleanField.class);

		subscribeAndExecute(eventBroker, lotFlowGlcField.getFullTopic(FIELD_LOTFLOW + 
				GlcEvent.NAMESPACE_SEPERATOR + "lotId"  + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_ENTERPRESSED), this::lotEnterPressedAdapter);
		subscribeAndExecute(eventBroker, lotFlowGlcField.getFullTopic(FIELD_REWORKPROCEDURE + 
				GlcEvent.NAMESPACE_SEPERATOR + "procedureId"  + GlcEvent.NAMESPACE_SEPERATOR + GlcEvent.EVENT_SELECTION_CHANGED), this::procedureSelectionChangedAdapter);
		
		// �һ��¼�
		subscribeAndExecute(eventBroker, lotFlowField.getFullTopic(GlcEvent.EVENT_MENU_ACTION), this::lotFlowRightClickAction);
				
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RECOVERY), this::recoveryAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SPLITRECOVERY), this::splitRecoveryAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		itemRecovery = (ToolItem) form.getButtonByControl(null, BUTTON_RECOVERY);
		itemSplitRecovery = (ToolItem) form.getButtonByControl(null, BUTTON_SPLITRECOVERY);
		
		init();
		statusChanged(null, null);
	}
	
	private void init() {	
		lotInfoField.setValue(new Lot());
		lotInfoField.refresh();
		reworkInfoField.setValue(new LotRework());
		reworkInfoField.refresh();
		splitInfoField.setValue(new LotRework());
		splitInfoField.refresh();		
	}
	
	public void statusChanged(String state, String holdStatus) {
		if (LotStateMachine.STATE_WAIT.equals(state) || LotStateMachine.STATE_FIN.equals(state)) {
			itemRecovery.setEnabled(true);
			itemSplitRecovery.setEnabled(true);
		} else {
			itemRecovery.setEnabled(false);
			itemSplitRecovery.setEnabled(false);
		}
		 if (Lot.HOLDSTATE_ON.equals(holdStatus)) {
			 if (itemRecovery.getEnabled()) {
				 itemRecovery.setEnabled(false); 
			 }
			 if (itemSplitRecovery.getEnabled()) {
				 itemSplitRecovery.setEnabled(false); 
			 }
		 } 
	}
	
	private void lotEnterPressedAdapter(Object obj) {
		try {
			String lotId = lotFlowCustomComposite.getTxtId().getText();
			if (StringUtil.isEmpty(lotId)) {
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId, true);
			lotFlowCustomComposite.loadFlowTreeByLot(currentLot);	
			
			procedureFlowCustomComposite.loadFlowTreeByProcedure(null, null);
			procedureFlowCustomComposite.refresh();
			
			currentSelectedItem = null;	
			init();
			statusChanged(currentLot.getState(), currentLot.getHoldState());
			
			lotInfoField.setValue(currentLot);
			lotInfoField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}

	private void procedureSelectionChangedAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String procedureRrn = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (!StringUtil.isEmpty(procedureRrn)) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Procedure procedure = new Procedure();
				procedure.setObjectRrn(Long.valueOf(procedureRrn));
				procedure = (Procedure) prdManager.getProcessDefinition(procedure);
				procedureFlowCustomComposite.loadFlowTreeByProcedure(procedure, null);
				reworkProcedure = procedure;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}
	
	protected void lotFlowRightClickAction(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			TreeItem treeItem = (org.eclipse.swt.widgets.TreeItem) event.getProperty(GlcEvent.PROPERTY_DATA);
			StepState stepState = (StepState) treeItem.getData();
			
			if (currentSelectedItem != null) {
				currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				currentSelectedItem = null;
			}
			
			treeItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_GREEN));
			currentSelectedItem = treeItem;
			
			if (TREE_BUTTON_NAME_RETURNANDMERGE.equals(event.getProperty(GlcEvent.PROPERTY_MENU_ACTION_NAME))) {
				futureStepState = stepState;
				splitInfoField.setFieldValue("futureStepState", stepState.getName());	
			} else if (TREE_BUTTON_NAME_RETURTO.equals(event.getProperty(GlcEvent.PROPERTY_MENU_ACTION_NAME))) {
				futureStepState = null;
				splitInfoField.setFieldValue("futureStepState", null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	//����
	private void recoveryAdapter(Object object) {
		try {
			((EntityForm)reworkInfoField.getControls()[0]).getMessageManager().removeAllMessages();

			if (currentLot == null) {
				return;
			}
			if (reworkProcedure == null) {
				UI.showError(Message.getString("wip.lot_select_reworkProcedure"));
				return;
			}
			
			boolean saveFlag = true;
			if (!reworkInfoField.validate()) {
				saveFlag = false;
			}

			if (saveFlag) {
				TextField reworkQty = splitInfoField.getFieldByControlId("splitReworkQty", TextField.class);
				if (!StringUtil.isEmpty(reworkQty.getText())) {
					UI.showError(Message.getString("wip.please_use_split_recovery"));
					return;
				}			
                // ����Ƿ񳬹���󷵹�����
                if (!currentLot.getState().equals(LotStateMachine.STATE_FIN)
                    && !checkLotCanRework(currentLot, reworkProcedure)) {
                    return;
                }
                
                List<Lot> lots = (new ArrayList<Lot>());
				currentLot.setOperator1(Env.getUserName());
				lots.add(currentLot);
				
				LotRework lotRework = (LotRework) reworkInfoField.getValue();
				LotAction lotAction = new LotAction();
				lotAction.setActionCode(lotRework.getActionCode());
				lotAction.setActionReason(lotRework.getActionReason());
				lotAction.setActionComment(lotRework.getActionComment());
				lotAction.setRework(true);
				
                StepState startNode = null;
                StepState endNode = null;
                StepState returnTo = null;
                if (procedureFlowCustomComposite.getStartStepStateItem() != null 
                		&& !procedureFlowCustomComposite.getStartStepStateItem().isDisposed()) {
                	startNode = (StepState) procedureFlowCustomComposite.getStartStepStateItem().getData();
                }
                if (procedureFlowCustomComposite.getEndStepStateItem() != null
                		&& !procedureFlowCustomComposite.getEndStepStateItem().isDisposed()) {
                	endNode = (StepState) procedureFlowCustomComposite.getEndStepStateItem().getData();
                }
                if (currentSelectedItem != null
                		&& !currentSelectedItem.isDisposed()) {
                	returnTo = (StepState) currentSelectedItem.getData();
                }
               
                LotManager lotManager = Framework.getService(LotManager.class);
                if (currentLot.getState().equals(LotStateMachine.STATE_WAIT)) {
                	if (returnTo == null) {
                     	UI.showError(Message.getString("wipadv.future_flow_action_select_return"));
                     	return;
                     }  
                     
                     PrdManager prdManager = Framework.getService(PrdManager.class);
                     //�ж��Ƿ��ڵ�ǰ������
                     boolean isInCurrentProcedure = prdManager.isInProcedure(Env.getOrgRrn(), currentLot.getProcedureRrn(), returnTo);
					if (isInCurrentProcedure) {
						if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotRecoveryWhetherToContinue()))) {
							return;
						}
						lotManager.recoveryLot(lots, reworkProcedure.getObjectRrn(), startNode, endNode, returnTo, lotAction, Env.getSessionContext());
					} else {
						String hiSuperInstruction = EndState.SUPER_INSTRUCTION_PARENT + ":" + returnTo.getPath();
						List<Node> currentNodeList = prdManager.getProcessFlowList(currentLot.getProcessInstanceRrn());
						if (!((ProcedureState)currentNodeList.get(0)).getProcedureName().equals(((Procedure)returnTo.getProcessDefinition()).getName())) {
							//��������
							hiSuperInstruction = EndState.SUPER_INSTRUCTION_JUMP + ":" + returnTo.getPath();	
						}					
						if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotRecoveryWhetherToContinue()))) {
							return;
						}
						lotManager.recoveryLot(lots, reworkProcedure.getObjectRrn(), startNode, endNode, null, false, hiSuperInstruction, 
								lotAction, LotStateMachine.TRANS_REWORK, Env.getSessionContext());
					}
				} else if (currentLot.getState().equals(LotStateMachine.STATE_FIN)) {
					if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotRecoveryWhetherToContinue()))) {
						return;
					}
					lotManager.recoveryFinishLot(lots, reworkProcedure.getObjectRrn(), startNode, endNode, lotAction, Env.getSessionContext());
				}
				UI.showInfo(Message.getString("wip.recovery_successed"));// ������ʾ��
				lotEnterPressedAdapter(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			((EntityForm)reworkInfoField.getControls()[0]).getMessageManager().setAutoUpdate(false);
		}
	}

	//��������
	private void splitRecoveryAdapter(Object object) {
		try {
			((EntityForm)reworkInfoField.getControls()[0]).getMessageManager().removeAllMessages();

			if (currentLot == null) {
				return;
			}
			if (reworkProcedure == null) {
				UI.showError(Message.getString("wip.lot_select_reworkProcedure"));
				return;
			}
			
			boolean saveFlag = true;
			if (!reworkInfoField.validate()) {
				saveFlag = false;
			}

			if (saveFlag) {												
                // ����Ƿ񳬹���󷵹�����
                if (!currentLot.getState().equals(LotStateMachine.STATE_FIN)
                    && !checkLotCanRework(currentLot, reworkProcedure)) {
                    return;
                }         
                
                TextField reworkQty = splitInfoField.getFieldByControlId("splitReworkQty", TextField.class);
				if (reworkQty.getValue() == null || StringUtil.isEmpty((String)reworkQty.getValue())) {
					UI.showInfo(Message.getString("wip.recovery_qty_num"));
					return;
				}
				BigDecimal childrenMainQty = new BigDecimal(reworkQty.getValue().toString());
				if (childrenMainQty.intValue() <= 0) {
					UI.showInfo(Message.getString("wip.recovery_qty_num"));
					return;
				}
                if (currentLot.getMainQty().compareTo(childrenMainQty) <= 0) {
					UI.showInfo(Message.getString("wip.recovery_qty_err"));
					return;
				}
                
                List<Lot> lots = (new ArrayList<Lot>());
				currentLot.setOperator1(Env.getUserName());
				lots.add(currentLot);
								
				LotRework lotRework = (LotRework) reworkInfoField.getValue();
				LotAction lotAction = new LotAction();
				lotAction.setActionCode(lotRework.getActionCode());
				lotAction.setActionReason(lotRework.getActionReason());
				lotAction.setActionComment(lotRework.getActionComment());
				lotAction.setRework(true);
				lotAction.setFutureStepState(futureStepState);
				lotAction.setAutoHold(booleanField.getValue() == null ? false : (boolean) booleanField.getValue());
				
				StepState startNode = null;
                StepState endNode = null;
                StepState returnTo = null;
                if (procedureFlowCustomComposite.getStartStepStateItem() != null 
                		&& !procedureFlowCustomComposite.getStartStepStateItem().isDisposed()) {
                	startNode = (StepState) procedureFlowCustomComposite.getStartStepStateItem().getData();
                }
                if (procedureFlowCustomComposite.getEndStepStateItem() != null
                		&& !procedureFlowCustomComposite.getEndStepStateItem().isDisposed()) {
                	endNode = (StepState) procedureFlowCustomComposite.getEndStepStateItem().getData();
                }
                if (currentSelectedItem != null
                		&& !currentSelectedItem.isDisposed()) {
                	returnTo = (StepState) currentSelectedItem.getData();
                }
                
                boolean isInCurrentProcedure = false;
                String hiSuperInstruction = null;
                if (LotStateMachine.STATE_WAIT.equals(currentLot.getState())) {
                	if (returnTo == null) {
                    	UI.showError(Message.getString("wipadv.future_flow_action_select_return"));
                    	return;
                    } 
                	PrdManager prdManager = Framework.getService(PrdManager.class);
                    isInCurrentProcedure = prdManager.isInProcedure(Env.getOrgRrn(), currentLot.getProcedureRrn(), returnTo);
                    
                    hiSuperInstruction = EndState.SUPER_INSTRUCTION_PARENT + ":" + returnTo.getPath();
    				List<Node> currentNodeList = prdManager.getProcessFlowList(currentLot.getProcessInstanceRrn());
    				if (!((ProcedureState)currentNodeList.get(0)).getProcedureName().equals(((Procedure)returnTo.getProcessDefinition()).getName())) {
    					//��������
    					hiSuperInstruction = EndState.SUPER_INSTRUCTION_JUMP + ":" + returnTo.getPath();	
    				}
                }                          
                
                LotManager lotManager = Framework.getService(LotManager.class);  
                //QtyUnit���ͷ���          
				if (QtyUnit.getUnitType().equals(currentLot.getSubUnitType())) {                
					List<Lot> children = new ArrayList<Lot>();
					Lot lt = lotManager.getLotByLotId(Env.getOrgRrn(), currentLot.getLotId());
					Lot child = (Lot) lt.clone();
					child.setLotId(null);
					child.setMainQty(childrenMainQty);
					child.setOperator1(Env.getUserName());
					children.add(child);			
					if (currentLot.getState().equals(LotStateMachine.STATE_WAIT)) {	
						if (isInCurrentProcedure) {
							if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotSplitRecoveryWhetherToContinue()))) {
								return;
							}
							currentLot = lotManager.splitLotAndRecoveryLot(currentLot, children, 
								reworkProcedure.getObjectRrn(), startNode, endNode, returnTo, lotAction, Env.getSessionContext());
						} else {				
							if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotSplitRecoveryWhetherToContinue()))) {
								return;
							}
							currentLot = lotManager.splitLotAndRecoveryLot(currentLot, children, 
									reworkProcedure.getObjectRrn(), startNode, endNode, null, false, hiSuperInstruction, lotAction, LotStateMachine.TRANS_REWORK, Env.getSessionContext());
						}
					} else if (currentLot.getState().equals(LotStateMachine.STATE_FIN)) {
						if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotSplitRecoveryWhetherToContinue()))) {
							return;
						}
						currentLot = lotManager.splitLotAndRecoveryFinishLot(currentLot, children, 
								reworkProcedure.getObjectRrn(), startNode, endNode, lotAction, Env.getSessionContext());
					}
					if (CollectionUtils.isNotEmpty(currentLot.getChildrenLots())) {
						lotFlowCustomComposite.getTxtId().setText(currentLot.getChildrenLots().get(0).getLotId());
					}
					
					UI.showInfo(Message.getString("wip.recovery_successed"));// ������ʾ��		
					lotEnterPressedAdapter(null);
					
				//ComponentUnit���ͷ�����֧��Sort
				} else if (ComponentUnit.getUnitType().equals(currentLot.getSubUnitType())) {							
					LotComponentSplitRecoveryDialog dialog = new LotComponentSplitRecoveryDialog(DIALOG_SPLIT_FORM_NAME, form.getAuthority(), eventBroker, currentLot, childrenMainQty);
					if (dialog.open() == Dialog.OK) {
						List<ComponentUnit> splitComponents = dialog.getSplitComponents();		
						LotSortingAction lotSortingAction = dialog.getLotSortingAction();
						lotSortingAction.setActionCode(lotAction.getActionCode());
						lotSortingAction.setActionComment(lotAction.getActionComment());
						lotSortingAction.setActionReason(lotAction.getActionReason());
						if (lotAction.getFutureStepState() != null && !"".equals(lotAction.getFutureStepState().toString())) {
							lotSortingAction.setFutureStepState(returnTo);
						} else {
							lotSortingAction.setFutureStepState(null);
						}				
						lotSortingAction.setAutoHold(lotAction.isAutoHold());
						lotSortingAction.setRework(true);
					
						StepState startStepState = startNode;
						StepState endStepState = endNode;
						StepState returnToStepState = returnTo;
						
						String superInstruction = hiSuperInstruction;
						boolean inCurrentProcedure = isInCurrentProcedure;
						
						SplitRecoveryQtimeDialog qtimeDialog = new SplitRecoveryQtimeDialog(DIALOG_SPLIT_QTIME_FORM_NAME, null, eventBroker, createFutureQTimer(currentLot, reworkProcedure, startStepState, returnToStepState));
						qtimeDialog.setCloseAdaptor(new Consumer<SplitRecoveryQtimeDialog>() {
							
							@Override
							public void accept(SplitRecoveryQtimeDialog t) {
								lotSortingAction.setFutureTimer(t.getFutureTimer());
								if (currentLot.getState().equals(LotStateMachine.STATE_WAIT)) {	
									if (inCurrentProcedure) {
										if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotSplitRecoveryWhetherToContinue()))) {
											return;
										}
										currentLot = lotManager.splitLotComponentAndRecoveryLot(currentLot, splitComponents, reworkProcedure.getObjectRrn(), startStepState, endStepState, 
												returnToStepState, lotSortingAction, Env.getSessionContext());		
									} else {
										if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotSplitRecoveryWhetherToContinue()))) {
											return;
										}
										currentLot = lotManager.splitLotComponentAndRecoveryLot(currentLot, splitComponents, reworkProcedure.getObjectRrn(), startStepState, endStepState, 
												null, false, superInstruction, lotAction, LotStateMachine.TRANS_REWORK, Env.getSessionContext());
									}
								} else if (currentLot.getState().equals(LotStateMachine.STATE_FIN)) {
									if (!UI.showConfirm(Message.getString(WipExceptionBundle.bundle.LotSplitRecoveryWhetherToContinue()))) {
										return;
									}
									currentLot = lotManager.splitLotComponentAndRecoveryFinishLot(currentLot, splitComponents,
											reworkProcedure.getObjectRrn(), startStepState, endStepState, lotSortingAction, Env.getSessionContext());
								}	
								
								if (CollectionUtils.isNotEmpty(currentLot.getChildrenLots())) {
									lotFlowCustomComposite.getTxtId().setText(currentLot.getChildrenLots().get(0).getLotId());
								}
								
								UI.showInfo(Message.getString("wip.recovery_successed"));// ������ʾ��		
								lotEnterPressedAdapter(null);
							}
						});
						qtimeDialog.open();																		
					}					
				}		
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			((EntityForm)reworkInfoField.getControls()[0]).getMessageManager().setAutoUpdate(false);
		}
	}

	/**
	 * ������η�������
	 * @param lot ����
	 * @param reworkProcedure ��������
	 * @return
	 */
	public boolean checkLotCanRework(Lot lot, Procedure reworkProcedure) {
        try {
            if (reworkProcedure.getMaxReworkCount() != null) {
                LotManager lotManager = Framework.getService(LotManager.class);
                LotRework thisLotRework = new LotRework();
                thisLotRework.setOrgRrn(Env.getOrgRrn());
                thisLotRework.setCreatedBy(Env.getUserName());
                thisLotRework.setUpdatedBy(Env.getUserName());
                thisLotRework.setLotRrn(lot.getObjectRrn());
                thisLotRework.setReworkTime(new Date());
                thisLotRework.setMaxReworkCountType(ReworkState.OBJECTTYPE_TOTAL);
                thisLotRework.setMaxReworkCount(reworkProcedure.getMaxReworkCount());
                if (StringUtils.isNotEmpty(lot.getStepName())) {
                    thisLotRework.setReworkStepName(lot.getStepName());
                } else {
                    thisLotRework.setReworkStepName(lot.getLastStepName());
                }
                thisLotRework.setReworkProcedureName(reworkProcedure.getName());
                lotManager.checkAndsaveLotRework(thisLotRework, true, Env.getSessionContext());
            }
            return true;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return false;
        }
    }
	
	private FutureMultiProcedureTimer createFutureQTimer(Lot lot, Procedure reworkProcedure, StepState startNode, StepState superStartNode) {
		try {		
			PrdManager prdManager = Framework.getService(PrdManager.class);
			if (superStartNode == null) {
				superStartNode = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
			}
			if (startNode == null) {
				PrdQueryAction queryAction = PrdQueryAction.newIntance();
				queryAction.setCopyNode(true);
				List<Node> reworkNodes = prdManager.getProcessDefinitionChildern(reworkProcedure, queryAction);
				Optional<Node> optional = reworkNodes.stream().filter(reworkNode -> reworkNode instanceof StepState)
						.sorted(Comparator.comparingInt(Node::getSeqNo).reversed()).findFirst();
				if (optional.isPresent()) {
					startNode = (StepState) optional.get();
				}
			}

			FutureMultiProcedureTimer futureTimer = new FutureMultiProcedureTimer();
			futureTimer.setOrgRrn(Env.getOrgRrn());
			futureTimer.setPartName(lot.getPartName());
			futureTimer.setPartVersion(lot.getPartVersion());
			futureTimer.setProcessName(lot.getProcessName());
			futureTimer.setProcessVersion(lot.getProcessVersion());
            futureTimer.setPath(lot.getProcedureName() + "/" + reworkProcedure.getName() + "/" + startNode.getName() + "/");
			futureTimer.setStepName(startNode.getStepName());
			futureTimer.setStepStateName(startNode.getName());
			futureTimer.setProcedureName(reworkProcedure.getName());
			futureTimer.setProcedureVersion(reworkProcedure.getVersion());
			futureTimer.setEndPath(superStartNode.getPath());
			futureTimer.setEndStepName(superStartNode.getStepName());
			futureTimer.setEndStepStateName(superStartNode.getName());
			ProcessDefinition processDefinition = superStartNode.getParent();
			futureTimer.setEndProcedureName(processDefinition.getName());
			futureTimer.setEndProcedureVersion(processDefinition.getVersion());
			return futureTimer;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	private void refreshAdapter(Object object) {		
		lotFlowCustomComposite.getTxtId().setText("");
		lotFlowCustomComposite.loadFlowTreeByLot(null);
		lotFlowCustomComposite.refresh();
		procedureFlowCustomComposite.loadFlowTreeByProcedure(null, null);
		procedureFlowCustomComposite.refresh();
		
		currentSelectedItem = null;
		init();
		statusChanged(null, null);
	}

}