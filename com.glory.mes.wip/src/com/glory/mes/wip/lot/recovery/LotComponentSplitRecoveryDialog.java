package com.glory.mes.wip.lot.recovery;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.lot.sorting.SortModel;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;

public class LotComponentSplitRecoveryDialog extends GlcBaseDialog { 

	private static final String FIELD_COMPONENTSPLITRECOVERY = "componentSplitRecovery";

	protected CustomField componentSplitRecoveryField;
	protected ComponentAssignCustomComposite componentAssignCustomComposite;

	protected LotSortingAction lotSortingAction;
	private List<ComponentUnit> splitComponents;
	private Lot lot;
	private BigDecimal reworkQty;
	
	public LotComponentSplitRecoveryDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, BigDecimal reworkQty) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
		this.reworkQty = reworkQty;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		componentSplitRecoveryField = form.getFieldByControlId(FIELD_COMPONENTSPLITRECOVERY, CustomField.class);
		componentAssignCustomComposite = (ComponentAssignCustomComposite) componentSplitRecoveryField.getCustomComposite();
		
		componentAssignCustomComposite.getSourceComponentComposite().setLot(lot.getLotId());
	}
	
	@Override
	protected void okPressed() {
		List<ComponentUnit> splitComponents = new ArrayList<ComponentUnit>();	
		for (ComponentUnit component : componentAssignCustomComposite.getTargetComponentComposite().getComponentComposite().getRealComponents()) {	
			splitComponents.add(component);
		}
		
		if (CollectionUtils.isEmpty(splitComponents)) {
			UI.showError(Message.getString("wip.target_component_is_empty"));
			return;
		}
		
		if (reworkQty.intValue() != splitComponents.size()) {
			UI.showError(Message.getString("wip.split") + Message.getString("wip.trackout_rework_qty") + reworkQty.intValue());
			return;
		}
		
		setSplitComponents(splitComponents);
		
		createLotAction();
		
		super.okPressed();	
	}

	public boolean createLotAction() {		
		try {
//			componentAssignCustomComposite.getSortingCustomComposite().getForm().getMessageManager().removeAllMessages();
			if (!componentAssignCustomComposite.getSortingCustomComposite().getForm().validate()) {
				return false;
			}
			Map<String, String> targetPosition = new HashMap<>();
			for (ComponentUnit component : getSplitComponents()) {
				targetPosition.put(component.getComponentId(), component.getPosition());
			}
			
			LotSortingAction lotSortingAction = new LotSortingAction();	
			lotSortingAction.setToPositionMap(targetPosition);
			lotSortingAction.setToDurableId(componentAssignCustomComposite.getTargetCarrierId());
			
			SortModel sort = (SortModel) componentAssignCustomComposite.getSortingCustomComposite().getValue();
			if (sort != null) {	
				lotSortingAction.setSplitType(sort.getActionType());
				lotSortingAction.setSort(sort.isSort());
				if (sort.isSort()) {
					ADManager adManager = Framework.getService(ADManager.class);
					Equipment equipment = null;
					if (!StringUtil.isEmpty(sort.getEquipmentId())) {
						equipment = new Equipment();
						equipment.setObjectRrn(Long.valueOf(sort.getEquipmentId()));
						equipment = (Equipment) adManager.getEntity(equipment);
					}
					// У��port��ʱ��һ��
					if (!StringUtil.isEmpty(sort.getFromPortId()) && !StringUtil.isEmpty(sort.getToPortId())) {
						if ((sort.getFromPortId()).equals(sort.getToPortId())) {
							UI.showError(Message.getString("wip.sorting_port_diverse"));
							return false;
						}
					}
					if (equipment != null) {
						lotSortingAction.setEquipmentId(equipment.getEquipmentId());
						lotSortingAction.setFromPortId(sort.getFromPortId());
						lotSortingAction.setToPortId(sort.getToPortId());
					}
					lotSortingAction.setSortingMode(LotSortingJob.SORTING_MODE_SPLIT_BYLOGIC);
				} 
				setLotSortingAction(lotSortingAction);		
			} else {
				return false;
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
//		finally {
//			componentAssignCustomComposite.getSortingCustomComposite().getForm().getMessageManager().setAutoUpdate(false);
//		}
		return true;
	}
	
	public LotSortingAction getLotSortingAction() {
		return lotSortingAction;
	}

	public void setLotSortingAction(LotSortingAction lotSortingAction) {
		this.lotSortingAction = lotSortingAction;
	}
	
	public List<ComponentUnit> getSplitComponents() {
		return splitComponents;
	}

	public void setSplitComponents(List<ComponentUnit> splitComponents) {
		this.splitComponents = splitComponents;
	}
	
	
}