package com.glory.mes.wip.lot.reserved;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.nebula.widgets.cdatetime.CDateTime;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.forms.field.DateTimeField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.flow.LotFlowSection;
import com.glory.mes.wip.lot.flow.LotFlowTreeManager;
import com.glory.mes.wip.model.LotReserved;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotReservedSection extends EntitySection {
	
	protected static final String IN_MAIN_MAT_TYPE_FIELD = "inMainMatType";
	protected static final String OUT_MAIN_MAT_TYPE_FIELD = "outMainMatType";

	private EntityForm entityForm;
	private QueryTableForm queryTableForm;

	protected ToolItem itemSave;
	protected AuthorityToolItem itemDelete;
	
	protected LotFlowTreeManager manager;
	protected LotFlowSection lotFlowSection;

	public static String KEY_SAVE = "save";
	public static String KEY_DELETE = "delete";
	
	public TextField inMainMatType;
	public TextField outMainMatType;
	
	static DateTimeField reserveTimeField;
	
	public LotReservedSection(ADTable adTable) {
		super(adTable);
		setAdObject(new LotReserved());
	}
	
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_SAVE);
		itemSave.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}

	protected void createToolItemDelete(ToolBar tBar) {
		itemDelete = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_DELETE);
		itemDelete.setText(Message.getString(ExceptionBundle.bundle.CommonDelete()));
		itemDelete.setImage(SWTResourceCache.getImage("delete"));
		itemDelete.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				deleteAdapter();
			}
		});
	}

	@Override
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		mmng = getMessageManager();

		MDSashForm sashForm = new MDSashForm(client, SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);
		
		LotReservedMediator lotMediator = new LotReservedMediator(this);

		lotFlowSection = new LotFlowSection(table, lotMediator, manager);
		lotFlowSection.createContents(form, sashForm);
		
		entityForm = new EntityForm(sashForm, SWT.NONE, this.getAdObject(), getADManger().getADTableDeep(table.getObjectRrn()), mmng);
		entityForm.setADManager(getADManger());
		detailForms.add(entityForm);
		
		reserveTimeField = (DateTimeField) getField("reserveTime");
	}
		
	@Override
	public ADBase save(ADBase obj) throws Exception {
		LotReserved reserved = (LotReserved) obj;
		// ��ǰֻ���豸Ԥ��
		reserved.setReservedType(LotReserved.RESERVED_TYPE_EQUIPMENT);
		// ����Seq
		Long seq = 10L;
		StringBuffer whereClause = new StringBuffer();
		whereClause.append(" status = '");	
		whereClause.append(LotReserved.STATUS_ACTVIE);
		whereClause.append("' ");
		whereClause.append(" AND checkFlag = '");	
		whereClause.append(reserved.getCheckFlag());
		whereClause.append("' ");
		whereClause.append(" AND lotId = '");
		whereClause.append(reserved.getLotId());
		whereClause.append("' ");
		whereClause.append(" AND stepName = '");
		whereClause.append(reserved.getStepName());
		whereClause.append("' ");
		//whereClause.append(" AND stepVersion = ");
		//whereClause.append(reserved.getStepVersion());
		
		List<LotReserved> activeReserveds = getADManger().getEntityList(
				Env.getOrgRrn(), LotReserved.class, 1, whereClause.toString(), "seqNo desc");
		if (CollectionUtils.isNotEmpty(activeReserveds) && activeReserveds.get(0).getSeqNo() != null) {
			seq += activeReserveds.get(0).getSeqNo();
		}
		reserved.setSeqNo(seq);
		
		// ��̨����
		LotManager lotManager = Framework.getService(LotManager.class);
//		List<LotReserved> lotReserveds = lotManager.manualReserveLot(Lists.newArrayList(reserved), Env.getSessionContext());
		List<LotReserved> lotReserveds = lotManager.manualReserveLot(Lists.newArrayList(reserved), Env.getSessionContext(),true);
		
		// ˢ���б�
		LotReserved result = lotReserveds.get(0);
//		queryTableForm.getTableManager().add(result);
		queryTableForm.refresh();
		List<LotReserved> lotReservedList =  (List<LotReserved>) queryTableForm.getTableManager().getInput();
		if (CollectionUtils.isNotEmpty(lotReservedList)) {
			List<LotReserved> manualLotReservedLot = lotReservedList.stream().filter(t->LotReservedEditor.RESERVED_SOURCE_MANUAL.equals(t.getReservedSource()))
					.sorted(Comparator.comparing(LotReserved::getReserveTime,Comparator.nullsFirst(Date::compareTo)).reversed()).collect(Collectors.toList());
			queryTableForm.getTableManager().setInput(manualLotReservedLot);
		}
		
		return result;
	}
	
	@Override
	protected void saveAdapter() {
		LotReserved lotReserved = (LotReserved) getAdObject();
		RefTableField eqpRefTablefield = (RefTableField) getField("equipmentId");
		RefTableField checkFlagRefTablefield = (RefTableField) getField("checkFlag");
		RefTableField portIdRefTablefield = (RefTableField) getField("portId");
//		DateTimeField reserveTimeField = (DateTimeField) getField("reserveTime");

		// ����δ֪������ڿؼ���ʵ��ֵ����ʾֵ��ͬ������ʾֵΪ׼
		if (reserveTimeField.getControls()[1] instanceof CDateTime) {
			CDateTime cDT =(CDateTime) reserveTimeField.getControls()[1];
			String dateStr = cDT.getText();
			Pattern p = Pattern.compile("[0-9]+");   
			Matcher m = p.matcher(dateStr); 
			StringBuffer sb = new StringBuffer();
			while(m.find()) {
				sb.append(m.group());
			}
			
			SimpleDateFormat sdf = new SimpleDateFormat( "yyyyMMddHHmmss" );
			Date newDate = null;
			try {
				newDate = sdf.parse(sb.toString());
			} catch (ParseException e) {
				e.printStackTrace();
			}
			if (newDate != null) {
				reserveTimeField.setValue(newDate);
				reserveTimeField.setData(newDate);
				cDT.setData(newDate);
				cDT.setSelection(newDate);
			}
		}
		
		reserveTimeField.refresh();

		if(lotReserved == null) return;
		
		lotReserved.setEquipmentId(eqpRefTablefield.getValue() == null ? null : String.valueOf(eqpRefTablefield.getValue()));
		lotReserved.setCheckFlag(checkFlagRefTablefield.getValue() == null ? null : String.valueOf(checkFlagRefTablefield.getValue()));
		lotReserved.setPortId(portIdRefTablefield.getValue() == null ? null : String.valueOf(portIdRefTablefield.getValue()));
		lotReserved.setReserveTime(reserveTimeField.getValue() == null ? null : (Date)reserveTimeField.getValue());

		if(StringUtils.isEmpty(lotReserved.getLotId())) {
			UI.showError(Message.getString("wip.lot_id_cannot_be_empty"));
			return;
		}
		if(StringUtils.isEmpty(lotReserved.getEquipmentId())) {
			UI.showError(Message.getString("ras.capaequipment_unselected"));
			return;
		}
		if(lotReserved.getReserveTime() == null) {
			UI.showError(Message.getString("wip.reserve_time_cannot_be_empty"));
			return;
		}

		if (inMainMatType != null ) {
			Port portInfo = (Port) portIdRefTablefield.getData();
			String stepMainMatType = inMainMatType.getValue() == null ? null : String.valueOf(inMainMatType.getValue());
			if (Objects.nonNull(portInfo) && StringUtils.isNotEmpty(portInfo.getMainMatType()) && StringUtils.isNotEmpty(stepMainMatType)) {
				if (!portInfo.getMainMatType().contains(stepMainMatType)) {
					UI.showError(String.format(Message.getString("wip.port_and_step_in_main_mat_type_not_match"),
							portInfo.getMainMatType(), stepMainMatType));
					return;
				}
			}
		}

		super.saveAdapter();
		// �������������object		
		if (Objects.nonNull(lotReserved) && Objects.nonNull(lotReserved.getObjectRrn())) {
			setAdObject(new LotReserved());
			refresh();
		}
	}

	@Override
	public boolean delete() {
		try {
			LotReserved lotReserved = (LotReserved) getAdObject();
			if (Objects.isNull(lotReserved) || Objects.isNull(lotReserved.getObjectRrn())) {
				return false;
			}
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.unReserveLot(Lists.newArrayList(lotReserved), Env.getSessionContext());
				
				queryTableForm.getTableManager().remove(lotReserved);;
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				setAdObject(new LotReserved());
				refresh();
				return true;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}

	public void setObject(LotReserved reserved, Step step) {
		/*// �ж�lotId��Text�еĲ���ͬ�����¼�������
		String textLotId = lotFlowSection.txtLot.getText();
		if (StringUtils.isEmpty(textLotId) || !textLotId.equals(reserved.getLotId())) {
			lotFlowSection.txtLot.setText(reserved.getLotId());
			lotFlowSection.excuteSearch();
		}

		LotFlowTreeField treeField = (LotFlowTreeField) lotFlowSection.getField("process");
		TreeItem[] parentItems = treeField.getTreeViewer().getTree().getItems();
		a:
		for (TreeItem parentItem : parentItems) {
			TreeItem[] treeItems = parentItem.getItems();
			for (TreeItem treeItem : treeItems) {
				
				if (!treeItem.getExpanded()) {
					treeField.getTreeViewer().expandToLevel(treeItem.getData(), 3);
				}
				TreeItem[] stepItems = treeItem.getItems();
				for (TreeItem stepItem : stepItems) {
					if (StringUtils.contains(stepItem.getText(), reserved.getStepName())) {
						treeField.getTreeViewer().getTree().setSelection(stepItem);
						break a;
					}
				}
			}
		}*/
		
		try {
			RASManager rasManager = Framework.getService(RASManager.class);
			List<Equipment> equipments = null;
			if (Objects.nonNull(step) && Objects.nonNull(step.getCapability())) {
				// ���ݹ����ѯ�����豸
				equipments = rasManager.getEquipmentsByCapa(step.getCapability());
			} else {
				// ����LotReserved��¼�е��豸�Ų�ѯ�豸���Ա��������б�����ʾ
				equipments = Lists.newArrayList(rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), reserved.getEquipmentId()));
			}
			RefTableField field = (RefTableField) getField("equipmentId");
			field.setInput(Lists.newArrayList());
			field.setInput(equipments);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		setAdObject(reserved);
		refresh();
		
		if (Objects.nonNull(step)) {
			inMainMatType = (TextField) getField(IN_MAIN_MAT_TYPE_FIELD);
			outMainMatType = (TextField) getField(OUT_MAIN_MAT_TYPE_FIELD);
			
			inMainMatType.setValue(step.getInMainMatType());
			outMainMatType.setValue(step.getOutMainMatType());
			inMainMatType.refresh();
			outMainMatType.refresh();
		}
	}
	
	public void removeQueryTableFormSelection() {
		queryTableForm.getTableManager().refresh();
	}

	public void setQueryTableForm(QueryTableForm queryTableForm) {
		this.queryTableForm = queryTableForm;
	}
}
