package com.glory.mes.wip.lot.reserved;

import java.util.Date;

import org.eclipse.swt.events.SelectionEvent;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotReserved;

public class LotReservedMediator extends LotMediator {
	
	private LotReservedSection section;
	private Lot lot;
	
	public LotReservedMediator(LotReservedSection section) {
		this.section = section;
	}

	@Override
	public void notifySection(ADBase adBase) {
		this.lot = (Lot) adBase;
	}
	
	@Override
	protected void selectionChangedAdapter(SelectionEvent event) {
		Object object = event.item.getData();
		if (object instanceof StepState) {
			StepState stepState = (StepState) object;
			Step step = stepState.getUsedStep();
			
			LotReserved reserved = new LotReserved();
			reserved.setLotId(lot.getLotId());
			reserved.setBatchId(lot.getBatchId());
			reserved.setWoId(lot.getWoId());
			reserved.setStepName(step.getName());
			reserved.setReserveTime(new Date());
			//reserved.setStepVersion(step.getVersion());
			
			section.setObject(reserved, step);
		}
		section.removeQueryTableFormSelection();
	}
}
