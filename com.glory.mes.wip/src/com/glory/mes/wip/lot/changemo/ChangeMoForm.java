package com.glory.mes.wip.lot.changemo;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pp.model.WorkOrder;

public class ChangeMoForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(ChangeMoForm.class);
	
	public static final String FIELD_CHANGEMO = "moId";
	private RefTableField fieldChangMo;

	public ChangeMoForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public ChangeMoForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public ChangeMoForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public ChangeMoForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}	
	
	@Override
	public void addFields() {
		super.addFields();
		try {
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
			ADTable adTable;
			FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());
			ADManager adManager = Framework.getService(ADManager.class);
			adTable  = adManager.getADTable(Env.getOrgRrn(), "WIPWorkOrder");
			ADRefTable refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setTextField("docId");
			ListTableManager tableManager = new ListTableManager(adTable);
			fieldChangMo = createRefTableFieldList(FIELD_CHANGEMO, Message.getString("wip.changemo_not_finished"), tableManager, refTable, 32);
		    addField(FIELD_CHANGEMO, fieldChangMo);
		} catch (Exception e) {
			logger.error("MoveLocationForm : Init listItem", e);
		}
	}
	
	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField)
						&& !f.equals(fieldChangMo)) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}
	
	@Override
	public void setEnabled() {
		if (object != null && object instanceof ADBase) {
			ADBase base = (ADBase) object;
			for (IField f : fields.values()) {
				ADField adField = adFields.get(f.getId());
				if (adField != null && !adField.getIsEditable()) {
					if (base.getObjectRrn() == null || base.getObjectRrn() == 0) {
						f.setEnabled(true);
					} else {
						f.setEnabled(false);
					}
				}
			}
			fieldChangMo.setEnabled(true);
		}
	}
	
	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			return true;
		}
		return false;
	}
	
	@Override
	public boolean validate() {
		boolean validFlag = true;
		if(fieldChangMo.getValue() != null
				&& !"".equals(String.valueOf(fieldChangMo.getValue()).trim())){
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(FIELD_CHANGEMO, String.format(Message
					.getString("common.ismandatry"), FIELD_CHANGEMO), null,
					IMessageProvider.ERROR, fieldChangMo.getControls()[1]);
		}
		return validFlag;
	}
	
	@Override
	public void refresh() {
		super.refresh();
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<WorkOrder> list = adManager.getEntityList(Env.getOrgRrn(), WorkOrder.class, Env.getMaxResult(), "(docStatus = 'APPROVED' or docStatus = 'STARTED')", null);
			fieldChangMo.setInput(list);
		} catch (Exception e) {
			logger.error("ChangeMoForm : refresh()", e);
		}
	}
}
