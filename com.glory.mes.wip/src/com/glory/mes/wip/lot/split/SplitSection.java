package com.glory.mes.wip.lot.split;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.QtyUnit;

public class SplitSection extends LotSection implements ISelectionChangedListener, MouseListener{
	private static final Logger logger = Logger.getLogger(SplitSection.class);

	public static final String KEY_SPLIT = "split";
	
	protected AuthorityToolItem itemSplit;
	protected ToolItem itemTCard;
	protected ToolItem itemLabel;
	protected SplitForm splitForm;
	protected Lot selectedElement;
	protected List<Lot> checkedElements;

	public SplitSection() {
		super();
	}

	public SplitSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.split_lot"));
		initAdObject();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSplit(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemTCard(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemSplit(ToolBar tBar) {
		itemSplit = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_SPLIT);
		itemSplit.setText(Message.getString("wip.split"));
		itemSplit.setImage(SWTResourceCache.getImage("split-lot"));
		itemSplit.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				splitAdapter(event);
			}
		});
	}
		
	protected void createToolItemTCard(ToolBar tBar) {
		itemTCard = new ToolItem(tBar, SWT.PUSH);
		itemTCard.setText(Message.getString("wip.tcard"));
		itemTCard.setImage(SWTResourceCache.getImage("tcard"));
		itemTCard.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				
			}
		});
	}
	
	protected void splitAdapter(SelectionEvent event) {
		try {
			Lot lot = (Lot)getAdObject();
			if (lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.input_lot_first"));
			} else {
				LotAction lotAction = null;
			    List<StepState> stepStates = splitForm.getFieldStepSate().getInput();
			    StepState stepState = null;
			    if (stepStates.size() > 0) {
	                stepState = (StepState) splitForm.getFieldStepSate().getData();
	                TextField fieldComment = (TextField)splitForm.getFieldComment();
	                String comment = fieldComment.getValue() + "";
//					if (StringUtil.isEmpty(comment) ) {
//						UI.showError(Message.getString("wip.split_comment_is_null"));
//						return;
//					}
					
	                lotAction = new LotAction();
	                lotAction.setFutureStepState(stepState);
	                lotAction.setActionComment(comment);
			    }
				SplitDialog sld = null;
				if (QtyUnit.class.getSimpleName().equals(lot.getSubUnitType())) {
					sld = new SplitQtyDialog(event.widget.getDisplay().getActiveShell(), form, lot, lotAction);
				} else if (ComponentUnit.class.getSimpleName().equals(lot.getSubUnitType())) {
//					sld = new SplitComponentDialog(event.widget.getDisplay().getActiveShell(), form, lot, lotAction);
					
					sld = new SplitComponentListTableDialog(event.widget.getDisplay().getActiveShell(), form, lot, lotAction);
				}
				
				if(sld == null) return;
				
				if(sld.open() == Dialog.OK) {
					refreshAdapter();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}	
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
	    splitForm = new SplitForm(composite, SWT.NONE, tab, mmng, this);
		return splitForm;
	}
	
	@Override
	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			Lot lot = (Lot)getAdObject();
			if (lot != null && lot.getObjectRrn() != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				lot = lotManager.getLotWithChildren(lot.getObjectRrn());
				setAdObject(lot);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		refresh();
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot lot = super.searchLot(lotId);
			LotManager lotManager = Framework.getService(LotManager.class);
			if (lot != null) {
			    List<StepState> stepStates = new ArrayList<StepState>();
			    StepState stepState = new StepState();
			    stepStates.add(stepState);
			    stepStates.addAll(lotManager.getFutureSteps(lot));
			    splitForm.getFieldStepSate().setInput(stepStates);
			    splitForm.getFieldStepSate().refresh();
				return lotManager.getLotWithChildren(lot.getObjectRrn());
			}
		} catch (Exception e) {
			logger.error("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus) || 
				LotStateMachine.STATE_FIN.equalsIgnoreCase(newStatus)) {
			itemSplit.setEnabled(true);
		} else {
			itemSplit.setEnabled(false);
		}
	}

	@Override
	public void selectionChanged(SelectionChangedEvent event) {
		StructuredSelection ss = (StructuredSelection) event.getSelection();
		if (!ss.isEmpty()) {
			selectedElement = (Lot) ss.getFirstElement();			
		} else {
			
		}
	}

	@Override
	public void mouseDoubleClick(MouseEvent e) {}

	@Override
	public void mouseDown(MouseEvent e) {
		Table table = (Table) e.widget;
		boolean hasItemChecked = false;
		checkedElements = new ArrayList<Lot>();
		for (TableItem item : table.getItems()) {
			if (item.getChecked()) {
				hasItemChecked = true;
				checkedElements.add((Lot) item.getData());
			}
		}
	}

	@Override
	public void mouseUp(MouseEvent e) {}
	
}
