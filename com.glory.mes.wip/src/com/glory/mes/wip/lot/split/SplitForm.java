package com.glory.mes.wip.lot.split;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.future.FutureMerge;
import com.glory.mes.wip.model.Lot;

public class SplitForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(SplitForm.class);
	private ListTableManager listTableManager; 
	private IField field;
	private IField fieldComment;
	private RefTableField fieldStepSate;
	
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";
	
    private static final String STEP_NAME = "FutureMergeStepName";
    private static final String STEP_NAME_ID = "futureMergeStepName";
	
	private static final String FIELDID_SPLITOT = "childrenLots";
	private static final String FIELDID_SEPARTOR = "splitSepartor";
	
	private static final String AD_TABLE_SPLIT = "WIPLotSplit";
	private SplitSection section;
	
	public SplitForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng, SplitSection section) {
		super(parent, style, tab, mmng);
		this.section = section;
		super.createForm();
	}

	public SplitForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public SplitForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public SplitForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void createForm() {}
	
	@Override
	public void addFields() {
		super.addFields();
        try {
            ADManager entityManager = Framework.getService(ADManager.class);

            List<ADRefTable> adRefTableFMS = entityManager.getEntityList(Env.getOrgRrn(), 
                    ADRefTable.class, 1, "name = 'WIPSplitFutureMergeStepState'", "");

            ADTable adTableFMS = entityManager.getADTable(adRefTableFMS.get(0).getTableRrn());

            adRefTableFMS.get(0).setAdTable(adTableFMS);
            ListTableManager listTableManager = new ListTableManager(adTableFMS);
//            TableViewer fieldViewerFMS = (TableViewer) listTableManager.createViewer(UI.getActiveShell(), new FormToolkit(Display.getCurrent()));
            fieldStepSate = createRefTableFieldList(STEP_NAME_ID, Message.getString("wip.split_fm_step"), listTableManager, adRefTableFMS.get(0), 32);
            addField(STEP_NAME, fieldStepSate);
            
            fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);
            ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			addField(COMMENT, fieldComment);
        } catch (Exception e) {
            e.printStackTrace();
            ExceptionHandlerManager.asyncHandleException(e);
        }
		getSplitLotField();
	}	

	public IField getSplitLotField(){
		String displayLabel = Message.getString("wip.child_lot");
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE_SPLIT);
			listTableManager = new ListTableManager(adTable);
			field = new SplitLotField(FIELDID_SPLITOT, displayLabel, listTableManager);
			ADField adField = new ADField();
			adField.setIsMandatory(true); //����
			adField.setIsSameline(true);
			field.setADField(adField);
			addField(FIELDID_SPLITOT, field);
		} catch (Exception e) {
			logger.error("SplitLotForm : Init tablelist", e);
		}
		return field;
	}
	
	@Override
	public void loadFromObject() {
		if (object != null) {
			Lot lot = (Lot) object;
			for (IField f : fields.values()) {
				if (!f.getId().equals(FIELDID_SEPARTOR) && !f.getId().equals(COMMENT_ID) ) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			if (lot.getObjectRrn() != null) {
				// ȡ��δ������
			    List<FutureMerge> futureMerges = getADManger().getEntityList(Env.getOrgRrn(), FutureMerge.class, 1, 
			    		"parentLotRrn = '" + lot.getObjectRrn() + "'", "");
			    if (futureMerges != null && futureMerges.size() > 0) {
			    	FutureMerge futureMerge = futureMerges.get(0);
			    	fieldStepSate.setValue(futureMerge.getStepName());
			    } else {
			    	fieldStepSate.setValue(null);
			    }
			}
			refresh();
			setEnabled();
		}
    }

    public RefTableField getFieldStepSate() {
        return fieldStepSate;
    }

    public void setFieldStepSate(RefTableField fieldStepSate) {
        this.fieldStepSate = fieldStepSate;
    }

	public IField getFieldComment() {
		return fieldComment;
	}

	public void setFieldComment(IField fieldComment) {
		this.fieldComment = fieldComment;
	}
    
    
}
