package com.glory.mes.wip.lot.split;

import javax.annotation.PostConstruct;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.Message;
import com.glory.mes.wip.lot.LotEditor;

public class SplitEditor extends LotEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.split.SplitEditor";

	@PostConstruct
	public void postConstruct(Composite parent) {
		super.postConstruct(parent);
		
		ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		createSection(adTable);
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);
		ManagedForm mform = new ManagedForm(toolkit, form);
		
		Composite body = form.getBody();
		configureBody(body);
		section.createContents(mform, body);
				
		mPart.setLabel(Message.getString("wip.split_editor"));
	}
	
	protected void createSection(ADTable adTable) {
		section = new SplitSection(adTable);
	}
	
	
}
