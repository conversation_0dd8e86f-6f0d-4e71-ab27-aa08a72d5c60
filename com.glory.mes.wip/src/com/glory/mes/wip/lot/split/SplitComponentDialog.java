package com.glory.mes.wip.lot.split;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.jface.viewers.ColumnWeightData;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.TableLayout;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class SplitComponentDialog extends SplitDialog {
	
	private static final Logger logger = Logger.getLogger(SplitComponentDialog.class);
	private String finalMessage;
	
	protected Lot lot;

	protected String[] columnsHeaders = new String[] {
			Message.getString("wip.component_id"),
			Message.getString("wip.component_position"),
			Message.getString("wip.state"), 
			Message.getString("wip.main_qty"), 
			Message.getString("wip.sub_qty")};
	
	public SplitComponentDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public SplitComponentDialog(Shell parentShell, IManagedForm form, Lot lot, LotAction lotAction) {
		super(parentShell, form, lot, lotAction);
		try {
			LotManager manager = Framework.getService(LotManager.class);
			lot = manager.getLotWithComponent(lot.getObjectRrn());
			this.lot = lot;
		} catch (Exception e) {
			logger.error("Constructor in SplitIdentifiedLotDialog",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	protected void crateOtherComponent(Composite composite, FormToolkit toolkit) {}

	@Override
	protected void createViewerComponent(Composite composite,FormToolkit toolkit) {
		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
        table = toolkit.createTable(tableContainer, SWT.CHECK | SWT.FULL_SELECTION | SWT.BORDER);
		table.setHeaderVisible(true);
		table.setLinesVisible(true);
		table.addSelectionListener(new SelectionListener() {
			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {}

			@Override
			public void widgetSelected(SelectionEvent e) {
				TableItem curSelectedItem = (TableItem) e.item;

				ComponentUnit comp = (ComponentUnit)curSelectedItem.getData();
//				if (!ComponentUnit.STATE_PRESENT.equals(comp.getState())) {
//					curSelectedItem.setChecked(false);
//					return;
//				} else {
//					Object[] units = viewer.getCheckedElements();
//					setMessage(finalMessage + "      " + units.length);
//				}		
				Object[] units = viewer.getCheckedElements();
				setMessage(finalMessage + "      " + units.length);
			}			
		});
		TableLayout tlayout = new TableLayout();
		table.setLayout(tlayout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = table.getItemHeight() * 13;
		table.setLayoutData(gd);
		
		viewer = new CheckboxTableViewer(table);
		if (columnsHeaders != null) {
			for (int i = 0; i < columnsHeaders.length; i++) {
				TableColumn column;
				column = new TableColumn(table, SWT.NONE);
				column.setText(columnsHeaders[i]);
				column.setResizable(true);
				tlayout.addColumnData(new ColumnWeightData(30));
			}
		}
        viewer.setLabelProvider(new QtyLabelProvider());
        viewer.setContentProvider(new QtyContentProvider());
        viewer.setInput(lot.getSubProcessUnit());
        filterComponentUnit();
	}
	
	private void filterComponentUnit() {
		TableItem[] items = table.getItems();
		for (int i = 0; i < items.length; i++) {
			TableItem item = items[i];
			ComponentUnit comp = (ComponentUnit) item.getData();
//			if (!ComponentUnit.STATE_PRESENT.equals(comp.getState())) {
//				item.setForeground(Display.getCurrent().getSystemColor(SWT.COLOR_DARK_GRAY));
//			}
		}
	}
	
	@Override
	protected Point getInitialSize() {
		return new Point(700,600);
	}

	@Override
	protected Point getInitialLocation(Point initialSize) {
		Point shellSize = Display.getCurrent().getActiveShell().getSize();
		return new Point((shellSize.x - initialSize.x) / 3 * 2, (shellSize.y - initialSize.y) / 2);
	}
	
	class QtyLabelProvider extends LabelProvider implements ITableLabelProvider {
		@Override
		public Image getColumnImage(Object element, int columnIndex) {
			return null;
		}
		@Override
		public String getColumnText(Object element, int columnIndex) {
			if (element instanceof ComponentUnit) {
				ComponentUnit component = (ComponentUnit) element;
				switch (columnIndex) {
					case 0:
						if (component.getComponentId() == null) {
							return "";
						}
						return component.getComponentId();
					case 1: 
						if (component.getPosition() == null) {
							return "";
						}
						return component.getPosition().toString();
					case 2: 
						if (component.getState() == null) {
							return "";
						}
						return component.getState();
					case 3: 
						if (component.getMainQty() == null) {
							return "";
						}
						return component.getMainQty().toString();
					case 4: 
						if (component.getSubQty() == null) {
							return "";
						}
						return component.getSubQty().toString();
				}
			}
			return "";
		}
	}
	
	class QtyContentProvider implements IStructuredContentProvider {
		@Override
		public Object[] getElements(Object inputElement) {
			if (inputElement instanceof List) {
				return ((List) inputElement).toArray();
			}
			return new Object[0];
		}
		@Override
		public void dispose() {
		}
		@Override
		public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		}
	}
	
	@Override
	protected void okPressed() {
		Object[] elements = viewer.getCheckedElements();
		List<ComponentUnit> splitComponents = new ArrayList<ComponentUnit>();
		for (int i = 0; i < elements.length; i++) {
			ComponentUnit comp = (ComponentUnit) elements[i];			
			splitComponents.add(comp);
		}
		if (splitComponents.size() == 0) {
			UI.showWarning(String.format(Message.getString("common.please_select"), ComponentUnit.class.getSimpleName()));
			return;
		}
		
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			lot = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
			lot.setOperator1(Env.getUserName());
			lotManager.splitLotComponent(lot, splitComponents, lotAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.split_success"));
		} catch (Exception e) {
			logger.error("Split Lot Failure at SplitLotDialog : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		super.okPressed();
	}
	
	@Override
	public void setMessage(String newMessage) {
		super.setMessage(newMessage);
		if (finalMessage == null) {
			this.finalMessage = newMessage;
		}
		
	}
	
}
