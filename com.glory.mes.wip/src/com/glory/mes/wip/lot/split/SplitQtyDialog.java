package com.glory.mes.wip.lot.split;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.jface.viewers.ColumnWeightData;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.TableLayout;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class SplitQtyDialog extends SplitDialog {
	private static final Logger logger = Logger.getLogger(SplitQtyDialog.class);
	
	protected Button remove, byNumAdd, qtyAdd, byQtyAdd;
	protected Text byNumText, mainText, subText, byMain, bySub;
	protected List<Lot> lotQtys = new ArrayList<Lot>();
	protected BigDecimal inputMainQty, inputSubQty, addMainQty, addSubQty;
	protected BigDecimal splitedMainQty = BigDecimal.ZERO, splitedSubQty = BigDecimal.ZERO;
	protected String buttonInfo;
	protected String titleMassageInfo;
	
	public SplitQtyDialog(Shell parent, IManagedForm form, Lot lot, LotAction lotAction) {
		super(parent, form, lot, lotAction);
	}
		
	@Override
	protected Control createDialogArea(Composite parent) {
		buttonInfo = Message.getString(ExceptionBundle.bundle.CommonAdd());
	    titleMassageInfo = Message.getString("wip.split_dialog_message");
		return super.createDialogArea(parent);
	}

	@Override
	protected void crateOtherComponent(Composite composite, FormToolkit toolkit) {
		Composite buttonComp = toolkit.createComposite(composite, SWT.NULL);        
        GridData gd = new GridData(GridData.FILL_BOTH);
        gd.horizontalAlignment = GridData.END;
        buttonComp.setLayoutData(gd);
        GridLayout gridLayout = new GridLayout(1, true);
        buttonComp.setLayout(gridLayout);
        
        remove = toolkit.createButton(buttonComp, Message.getString(ExceptionBundle.bundle.CommonDelete()), SWT.PUSH);
		decorateButton(remove);
		remove.addSelectionListener(getDeleteListener());
		
		Composite splitComp = toolkit.createComposite(composite, SWT.NULL);
		splitComp.setLayoutData(new GridData(GridData.FILL_BOTH));
		GridData bGd = new GridData(GridData.FILL_HORIZONTAL);
		bGd.horizontalSpan = 3;
		GridData tGd = new GridData(GridData.FILL_HORIZONTAL);
		GridLayout gl = new GridLayout(5, false);
		splitComp.setLayout(gl);
		toolkit.createLabel(splitComp, Message.getString("wip.split_bynumber"), SWT.NULL);
		byNumText = toolkit.createText(splitComp, "", SWT.BORDER);
		byNumText.setLayoutData(bGd);
		byNumAdd = toolkit.createButton(splitComp, buttonInfo, SWT.PUSH);
		
		toolkit.createLabel(splitComp, Message.getString("wip.split_outmain"), SWT.NULL);
		mainText = toolkit.createText(splitComp, "", SWT.BORDER);
		mainText.setLayoutData(tGd);
		toolkit.createLabel(splitComp, Message.getString("wip.split_outsub"), SWT.NULL);
		subText = toolkit.createText(splitComp, "", SWT.BORDER);
		subText.setLayoutData(tGd);
		qtyAdd = toolkit.createButton(splitComp, buttonInfo, SWT.PUSH);
		
		toolkit.createLabel(splitComp, Message.getString("wip.split_sublotmain"), SWT.PUSH);
		byMain = toolkit.createText(splitComp, "", SWT.NULL);
		byMain.setLayoutData(tGd);
		toolkit.createLabel(splitComp, Message.getString("wip.split_sublotsub"), SWT.PUSH);
		bySub = toolkit.createText(splitComp, "", SWT.NULL);
		bySub.setLayoutData(tGd);
		byQtyAdd = toolkit.createButton(splitComp, buttonInfo, SWT.PUSH);
		
		decorateButton(byNumAdd);
		decorateButton(qtyAdd);
		decorateButton(byQtyAdd);
		
		byNumAdd.addSelectionListener(getByNumAddListener());
		qtyAdd.addSelectionListener(getQtyAddListener());
		byQtyAdd.addSelectionListener(getByQtyAddListener());
		
		byNumText.addModifyListener(getModifyListener("integer"));
		mainText.addModifyListener(getModifyListener("double"));
		subText.addModifyListener(getModifyListener("double"));
		byMain.addModifyListener(getModifyListener("integer"));
		bySub.addModifyListener(getModifyListener("integer"));
	}

	@Override
	protected void createViewerComponent(Composite composite, FormToolkit toolkit) {
		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
        createTableViewer(tableContainer, toolkit);
        viewer.setLabelProvider(new QtyLabelProvider());
        viewer.setContentProvider(new QtyContentProvider());
	}
	
	@Override
    protected void okPressed() {
		List<Lot> children = new ArrayList<Lot>();
		try {
			for(Lot lq : lotQtys) {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lt = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
				Lot child = (Lot)lt.clone();
				child.setLotId(null);
				child.setMainQty(lq.getMainQty());
				child.setSubQty(lq.getSubQty());
				child.setOperator1(Env.getUserName());
				child.setLotId(null);
				children.add(child);
			}
			lot.setOperator1(Env.getUserName());
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.splitLot(lot, children, lotAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.split_success"));
		} catch(Exception e) {
			logger.error("Split Lot Failure at SplitLotDialog : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
        super.okPressed();
    }
	
	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == IDialogConstants.OK_ID) {
			if (lotQtys == null || lotQtys.size() == 0) {
				UI.showError(Message.getString("wip.split_not_qty"));
				byNumText.setFocus();
				return;
			}
		}
		super.buttonPressed(buttonId);
	}

	public void decorateButton(Button button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		int widthHint = 92; // IDialogConstants.BUTTON_WIDTH
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}
	
	protected void createTableViewer(Composite tc, FormToolkit toolkit) {
		String[] columnsHeaders = new String[] {Message.getString("wip.main_qty"), Message.getString("wip.lot_subQty") };
		table = toolkit.createTable(tc, SWT.CHECK | SWT.FULL_SELECTION | SWT.BORDER);
		table.setHeaderVisible(true);
		table.setLinesVisible(true);
		TableLayout tlayout = new TableLayout();
		table.setLayout(tlayout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = table.getItemHeight() * 13;
		table.setLayoutData(gd);
		
		viewer = new CheckboxTableViewer(table);
		if (columnsHeaders != null) {
			for (int i = 0; i < columnsHeaders.length; i++) {
				TableColumn column;
				column = new TableColumn(table, SWT.NONE);
				column.setText(columnsHeaders[i]);
				column.setResizable(true);
				tlayout.addColumnData(new ColumnWeightData(30));
			}
		}
	}
	
	protected SelectionListener getByNumAddListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (!"".equals(byNumText.getText().trim())) {
					int splitNum = Integer.parseInt(byNumText.getText());
					if (!valid(splitNum)) {
						return;
					}
					BigDecimal mCount = BigDecimal.ZERO, sCount = BigDecimal.ZERO;
					BigDecimal mResidue = BigDecimal.ZERO, sResidue = BigDecimal.ZERO;
					if (lot.getMainQty() != null) {
						BigDecimal mainQty = lot.getMainQty();
						mCount = computeCount(mainQty, splitNum, 1);
						mResidue = getResidue(mainQty, BigDecimal.valueOf(splitNum));
						if (mCount.compareTo(BigDecimal.ZERO) == 0 && mainQty.compareTo(BigDecimal.valueOf(splitNum)) < 0) {
							mCount = BigDecimal.ONE;
						}
					}
					if (lot.getSubQty() != null && lot.getSubQty().compareTo(BigDecimal.ZERO) != 0) {
						BigDecimal subQty = lot.getSubQty();
						sCount = computeCount(subQty, splitNum, 2);
						sResidue = getResidue(subQty, sCount);
						if (sCount.compareTo(BigDecimal.ZERO) == 0 && subQty.compareTo(BigDecimal.valueOf(splitNum)) < 0) {
							sCount = BigDecimal.ONE;
						}
					}
					if (lotQtys == null || lotQtys.size() != 0) {
						lotQtys = new ArrayList<Lot>();
					}
					int totalMainQty = 0, totalSubQty = 0;
					for (int i = 0; i < splitNum; i++) {
						Lot lq = new Lot();
						if (mCount.compareTo(BigDecimal.ONE) == 0) {
							if (totalMainQty < lot.getMainQty().doubleValue()) {
								lq.setMainQty(BigDecimal.ONE);
								totalMainQty++;
							}
						} else if (mCount.compareTo(BigDecimal.ZERO) != 0) {
							lq.setMainQty(mCount);
						}
						
						if (sCount.compareTo(BigDecimal.ONE) == 0) {
							if (totalSubQty < lot.getSubQty().doubleValue()) {
								lq.setSubQty(BigDecimal.ONE);
								totalSubQty++;
							}
						} else if (sCount.compareTo(BigDecimal.ZERO) != 0) {
							lq.setSubQty(sCount);
						}
						lotQtys.add(lq);
					}
					if (mResidue.compareTo(BigDecimal.ZERO) > 0 || sResidue.compareTo(BigDecimal.ZERO) > 0) {
						Lot lq = null;
						if (lotQtys.size() > 0) {
							lq = lotQtys.get(lotQtys.size() - 1);
						} else {
							lq = new Lot();
							lq.setMainQty(BigDecimal.ZERO);
							lq.setSubQty(BigDecimal.ZERO);
							lotQtys.add(lq);
						}
						if (lot.getMainQty() != null && mResidue.compareTo(BigDecimal.ZERO) > 0) {
							if (totalMainQty < lot.getMainQty().doubleValue()) {
								lq.setMainQty(lq.getMainQty().add(mResidue));
							}
						}
						if (lot.getSubQty() != null && sResidue.compareTo(BigDecimal.ZERO) > 0) {
							if (totalSubQty < lot.getSubQty().doubleValue()) {
								lq.setSubQty(lq.getSubQty().add(sResidue));
							}
						}
					}
					refresh();
					byNumText.setFocus();
				}
			}
		};
	}

	private boolean valid(int splitNum) {
		if (lot.getMainQty() != null && lot.getSubQty() == null) {
			if (lot.getMainQty().doubleValue() < splitNum) return false;
		} else if (lot.getMainQty() == null && lot.getSubQty() != null) {
			if (lot.getSubQty().doubleValue() < splitNum) return false;
		} else if (lot.getMainQty() != null && lot.getSubQty() != null) {
			if (lot.getMainQty().doubleValue() < splitNum
					&& lot.getSubQty().doubleValue() < splitNum) return false;
		}
		return true;
	}

	// ����dividend ���� divisor������
	private BigDecimal getResidue(BigDecimal dividend, BigDecimal divisor) {
		return dividend.remainder(divisor);
	}
	
	private BigDecimal computeCount(BigDecimal qty, int num, int index) {
		BigDecimal count = qty.divide(BigDecimal.valueOf(num), 3);
		switch(index) {
		case 1 : splitedMainQty = count;
			break;
		case 2 : splitedSubQty = count;
			break;
		}
		return count;
	}

	public void computeAddQty() {
		addMainQty = BigDecimal.ZERO;
		addSubQty = BigDecimal.ZERO;
		if (lotQtys != null) {
			for (Lot lq : lotQtys) {
				if (lq.getMainQty() != null) {
					addMainQty = addMainQty.add(lq.getMainQty());
				}
				if (lq.getSubQty() != null) {
					addSubQty = addSubQty.add(lq.getSubQty());
				}
			}
		}
	}
	
	public boolean isLessThanLotQtys() {
		computeAddQty();
		inputMainQty = inputSubQty = BigDecimal.ZERO;
		if (!"".equals(mainText.getText().trim())) {
			inputMainQty = new BigDecimal(mainText.getText());
		}
		if (!"".equals(subText.getText().trim())) {
			inputSubQty = new BigDecimal(subText.getText());
		}
		if (inputMainQty.compareTo(BigDecimal.ZERO) == 0 && inputSubQty.compareTo(BigDecimal.ZERO) == 0) {
			return false;
		}
		if (lot.getMainQty() != null) {
			if (lot.getMainQty().compareTo(inputMainQty.add(addMainQty)) < 0) {
				UI.showError(Message.getString("wip.split_morethan_main"));
				mainText.setText("");
				mainText.setFocus();
				return false;
			}
		}
		if (lot.getSubQty() != null) {
			if (lot.getSubQty().compareTo(inputSubQty.add(addSubQty)) < 0) {
				UI.showError(Message.getString("wip.split_morethan_sub"));
				subText.setText("");
				subText.setFocus();
				return false;
			}
		}
		return true;
	}
	
	protected SelectionListener getQtyAddListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (isLessThanLotQtys()) {
					Lot lotQty = new Lot();
					if (lot.getMainQty() != null && inputMainQty.compareTo(BigDecimal.ZERO) > 0) {
						lotQty.setMainQty(inputMainQty);
					}
					if (lot.getSubQty() != null && inputSubQty.compareTo(BigDecimal.ZERO) > 0) {
						lotQty.setSubQty(inputSubQty);
					}
					if (lotQty.getMainQty() == null && lotQty.getSubQty() == null) return;
					if (lotQtys != null) {
						lotQtys.add(lotQty);
						refresh();
					}
					mainText.setText("");
					subText.setText("");
				}
				inputMainQty = inputSubQty = BigDecimal.ZERO;
				mainText.setFocus();
			}
		};
	}
	
	private SelectionListener getByQtyAddListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (validate()) {
					int mNum = 0, sNum = 0;
					BigDecimal byMainQtyCount = BigDecimal.ZERO, bySubQtyCount = BigDecimal.ZERO;
					BigDecimal mResidue = BigDecimal.ZERO, sResidue = BigDecimal.ZERO;
					if (byMain.getText() != null && !"".equals(byMain.getText().trim())) {
						if (lot.getMainQty() != null) {
							BigDecimal mainQty = lot.getMainQty();
							byMainQtyCount = new BigDecimal(byMain.getText());
							mNum = computeCount(mainQty, byMainQtyCount.intValue(), 1).intValue();
							mResidue = getResidue(mainQty, byMainQtyCount);
						}
					}
					if (bySub.getText() != null && !"".equals(bySub.getText().trim())) {
						bySubQtyCount = new BigDecimal(bySub.getText());
						if (lot.getSubQty() != null) {
							BigDecimal subQty = lot.getSubQty();
							sNum = computeCount(subQty, bySubQtyCount.intValue(), 2).intValue();
							sResidue = getResidue(subQty, bySubQtyCount);
						}
					}
					splitLot(byMainQtyCount, bySubQtyCount, mNum, sNum, mResidue, sResidue);
				}
			}
		};
	}
	
	protected boolean validate() {
		if (byMain.getText() != null && !"".equals(byMain.getText().trim())) {
			if (lot.getMainQty() == null ||
					Integer.parseInt(byMain.getText()) > lot.getMainQty().intValue()) {
				UI.showError(Message.getString("wip.split_morethan_main"));
				byMain.setText("");
				byMain.setFocus();
				return false;
			}
		}
		if (bySub.getText() != null && !"".equals(bySub.getText().trim())) {
			if (lot.getSubQty() == null ||
					Integer.parseInt(bySub.getText()) > lot.getSubQty().intValue()) {
				UI.showError(Message.getString("wip.split_morethan_sub"));
				bySub.setText("");
				bySub.setFocus();
				return false;
			}
		}
		return true;
	}

	private void splitLot(BigDecimal byMainQtyCount, BigDecimal bySubQtyCount,
			int mNum, int sNum, BigDecimal mResidue, BigDecimal sResidue) {
		if (byMainQtyCount.compareTo(BigDecimal.ZERO) != 0 && bySubQtyCount.compareTo(BigDecimal.ZERO) == 0) {
			split(byMainQtyCount, mNum, mResidue, 1);
			refresh();
		} else if (byMainQtyCount.compareTo(BigDecimal.ZERO) == 0 && bySubQtyCount.compareTo(BigDecimal.ZERO) != 0) {
			split(bySubQtyCount, sNum, sResidue, 2);
			refresh();
		} else if (byMainQtyCount.compareTo(BigDecimal.ZERO) != 0 && bySubQtyCount.compareTo(BigDecimal.ZERO) != 0) {
			if (mNum <= sNum && mResidue.compareTo(BigDecimal.ZERO) == 0) {
				addLots(byMainQtyCount, bySubQtyCount, mNum -1, true);
				Lot lq = new Lot();
				lq.setMainQty(byMainQtyCount);
				BigDecimal remnant = lot.getSubQty().subtract(bySubQtyCount.multiply(BigDecimal.valueOf((mNum - 1))));
				lq.setSubQty(remnant);
				lotQtys.add(lq);
			} else if (mNum <= sNum && mResidue.compareTo(BigDecimal.ZERO) != 0) {
				addLots(byMainQtyCount, bySubQtyCount, mNum, true);
				Lot lq = new Lot();
				lq.setMainQty(mResidue);
				BigDecimal remnant = lot.getSubQty().subtract(bySubQtyCount.multiply(BigDecimal.valueOf(mNum)));
				lq.setSubQty(remnant);
				lotQtys.add(lq);
			} else if (mNum > sNum && sResidue.compareTo(BigDecimal.ZERO) == 0) {
				int dv = mNum - sNum;
				addLots(byMainQtyCount, bySubQtyCount, sNum, true);
				addLots(byMainQtyCount, BigDecimal.ZERO, dv, false);
				if (mResidue.compareTo(BigDecimal.ZERO) != 0) {
					Lot lq = new Lot();
					lq.setMainQty(mResidue);
					lotQtys.add(lq);
				}
			} else if (mNum > sNum && sResidue.compareTo(BigDecimal.ZERO) != 0) {
				int dv = mNum - sNum;
				addLots(byMainQtyCount, bySubQtyCount, sNum, true);
				{
					Lot lq = new Lot();
					lq.setMainQty(byMainQtyCount);
					lq.setSubQty(sResidue);
					lotQtys.add(lq);
				}
				addLots(byMainQtyCount, BigDecimal.ZERO, dv - 1, false);
				if (mResidue.compareTo(BigDecimal.ZERO) != 0 ) {
					Lot lq = new Lot();
					lq.setMainQty(mResidue);
					lotQtys.add(lq);
				}
			}
			refresh();
		}
		byMain.setFocus();
	}
	
	// ����num��Lot, Ȼ�󽫲�����Lot����Table��
	private void addLots(BigDecimal byMainQtyCount, BigDecimal bySubQtyCount, int num, boolean isClear) {
		if (isClear) {
			if (lotQtys == null || lotQtys.size() != 0) {
				lotQtys = new ArrayList<Lot>();
			}
		}
		for (int i = 0; i < num; i++) {
			Lot lq = new Lot();
			if (byMainQtyCount.compareTo(BigDecimal.ZERO) != 0) {
				lq.setMainQty(byMainQtyCount);
			}
			if (bySubQtyCount.compareTo(BigDecimal.ZERO) != 0 ) {
				lq.setSubQty(bySubQtyCount);
			}
			lotQtys.add(lq);
		}
	}
	
	// ����num��Lot, �������residue != 0, ��residue���뵽���һ��Lot��, Ȼ�󽫲�����Lot����Table��
	private void split(BigDecimal byQtyCount, int num, BigDecimal residue, int index) {
		if (lotQtys == null || lotQtys.size() != 0) {
			lotQtys = new ArrayList<Lot>();
		}
		for (int i = 0; i < num; i++) {
			Lot lq = new Lot();
			switch(index) {
				case 1 : 
					lq.setMainQty(byQtyCount);
					break;
				case 2 : 
					lq.setSubQty(byQtyCount);
			}
			lotQtys.add(lq);
		}
		if (residue.compareTo(BigDecimal.ZERO) > 0) {
			Lot lq = new Lot();
			switch(index) {
			case 1 : 
				lq.setMainQty(residue);
				break;
			case 2 : 
				lq.setSubQty(residue);
			}
			lotQtys.add(lq);
		}
		refresh();
		byMain.setFocus();
	}
	
	private void setSplitInfo() {
		computeAddQty();
		String oddMainQty = "0.0", oddSubQty = "0.0";
		if (lot.getMainQty() != null) {
			oddMainQty = String.valueOf(lot.getMainQty().subtract(addMainQty).doubleValue());
		}
		if (lot.getSubQty() != null) {
			oddSubQty = String.valueOf(lot.getSubQty().subtract(addSubQty).doubleValue());
		}
		if (splitedMainQty.compareTo(BigDecimal.ZERO) < 0) {
			setMessage(String.format(titleMassageInfo, String.valueOf(addMainQty), String.valueOf(addSubQty), lot.getLotId(),
					"0.0", oddSubQty ));
		} else if (splitedSubQty.compareTo(BigDecimal.ZERO) < 0) {
			setMessage(String.format(titleMassageInfo, String.valueOf(addMainQty), String.valueOf(addSubQty), lot.getLotId(),
					oddMainQty, "0.0"));
		} else {
			setMessage(String.format(titleMassageInfo, String.valueOf(addMainQty), String.valueOf(addSubQty), lot.getLotId(),
					oddMainQty, oddSubQty ));
		}
	}
	
	protected SelectionListener getDeleteListener() {
		return new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				Object[] os = viewer.getCheckedElements();
				removeSelected(os);
			}
		};
	}
	
	public void removeSelected(Object[] os) {
		if (os.length != 0) {
			for (Object o : os) {
				Lot pe = (Lot)o;
				lotQtys.remove(pe);			
			}
			refresh();
			setSplitInfo();
		}
	}
	
	public List<Lot> getLotQtys() {
		return lotQtys;
	}

	public void refresh() {
		if (getLotQtys() != null) {
			viewer.setInput(getLotQtys());
			viewer.refresh();
		}
		setSplitInfo();
	}
	
	public ModifyListener getModifyListener(final String dataType) {
		return new ModifyListener() {
			public void modifyText(ModifyEvent e) {
				Text text = (Text)e.widget;
				String value = text.getText().trim();
				if ("".equalsIgnoreCase(value.trim())) {
					return;
				}
				if (!discernQty(value)) {
					text.setText("");
					text.setFocus();
				}
			}
			public boolean discernQty(String value) {
				if (!ValidatorFactory.isValid(dataType, value)) {
					UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInputError()), Message.getString("common.inputerror_title"));
					return false;
				}
				return true;
			}
		};
	}

	private class QtyLabelProvider extends LabelProvider implements ITableLabelProvider {
		@Override
		public Image getColumnImage(Object element, int columnIndex) {
			return null;
		}
		
		@Override
		public String getColumnText(Object element, int columnIndex) {
			if (element instanceof Lot) {
				Lot lotQty = (Lot) element;
				switch (columnIndex) {
					case 0:
						if (lotQty.getMainQty() == null) {
							return "";
						}
						return lotQty.getMainQty().toString();
					case 1: {
						if (lotQty.getSubQty() == null) {
							return "";
						}
						return lotQty.getSubQty().toString();
					}
				}
			}
			return "";
		}
	}
	
	private class QtyContentProvider implements IStructuredContentProvider {
		@Override
		public Object[] getElements(Object inputElement) {
			if (inputElement instanceof List) {
				return ((List) inputElement).toArray();
			}
			return new Object[0];
		}
		
		@Override
		public void dispose() {}
		
		@Override
		public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {}
		
	}
	
}
