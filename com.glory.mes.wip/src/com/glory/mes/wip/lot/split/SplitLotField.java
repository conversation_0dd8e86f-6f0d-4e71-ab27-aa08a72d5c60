package com.glory.mes.wip.lot.split;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.mes.wip.model.Lot;

public class SplitLotField extends AbstractField {
	
	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
    protected ListTableManager viewer;
    
    public SplitLotField(String id, String label, ListTableManager viewer) {
        super(id);
        this.viewer = viewer;
        this.label = label;
    }
    
    public SplitLotField(String id, ListTableManager viewer, int style) {
        super(id);
        this.viewer = viewer;
        mStyle = style;
    }

	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		int i = 0;
		String labelStr = getLabel();
		if (labelStr != null) {
			mControls = new Control[2];
			Label label = toolkit.createLabel(composite, labelStr);
            mControls[0] = label;
			i = 1;
		} else {
			mControls = new Control[1];
		}
	
		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
		GridData gd2 = new GridData(GridData.FILL_BOTH);
		gd2.grabExcessHorizontalSpace = true;
		gd2.horizontalAlignment = SWT.FILL;
		tableContainer.setLayoutData(gd2);
		tableContainer.setLayout(new GridLayout());
		viewer.newViewer(tableContainer);
		viewer.refresh();

		mControls[i] = tableContainer;
	}

	@Override
	public void refresh() {
		List<Lot> val = (List<Lot>)getValue();
        if(val != null) {
        	viewer.setInput(val);
        } else {
        	viewer.setInput(new ArrayList<Lot>());
        }
	}
	
	@Override
    public void setEnabled(boolean enabled) {
    	super.setEnabled(enabled);
    }

}
