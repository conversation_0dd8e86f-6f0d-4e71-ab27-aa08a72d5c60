package com.glory.mes.wip.lot.split;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class SplitComponentListTableDialog extends SplitDialog {
	
	private final String AD_TABLE = "WIPSplitComponentUnit";

	private static int MIN_DIALOG_WIDTH = 700;
	private static int MIN_DIALOG_HEIGHT = 600;

	protected ListTableManager listTableManager = null;
		
	public SplitComponentListTableDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public SplitComponentListTableDialog(Shell parentShell, IManagedForm form, Lot lot, LotAction lotAction) {
		super(parentShell, form, lot, lotAction);
		try {
			LotManager manager = Framework.getService(LotManager.class);
			lot = manager.getLotWithComponent(lot.getObjectRrn());
			this.lot = lot;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	protected void createViewerComponent(Composite composite, FormToolkit toolkit) {

        try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE);
			listTableManager = new ListTableManager(adTable, true);
//			listTableManager.addICheckChangedListener(new ICheckChangedListener() {
//				
//				@Override
//				public void checkChanged(List<Object> arg0, boolean arg1) {
//					setMessage(finalMessage + "      " + arg0.size());
//				}
//			});
			
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
        
        listTableManager.setInput(lot.getSubProcessUnit());
        listTableManager.newViewer(composite);
        listTableManager.refresh();
	}

	@Override
	protected void crateOtherComponent(Composite composite, FormToolkit toolkit) {
		// TODO Auto-generated method stub

	}
	
	@Override
	protected void okPressed() {
		List<Object> checkedObject = listTableManager.getCheckedObject();
		List<ComponentUnit> splitComponents = new ArrayList<ComponentUnit>();
		for (Object obj : checkedObject) {
			ComponentUnit comp = (ComponentUnit) obj;			
			splitComponents.add(comp);
		}
		if (splitComponents.size() == 0) {
			UI.showWarning(String.format(Message.getString("common.please_select"), ComponentUnit.class.getSimpleName()));
			return;
		}
		
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			lot = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
			lot.setOperator1(Env.getUserName());
			//У��Ƭ�Ƿ��ڸ�ĸ����
			List<ProcessUnit> processUnits = lotManager.getLotWithComponent(lot.getObjectRrn()).getSubProcessUnit();
			List<ComponentUnit> componentUnits = new ArrayList<>();
			componentUnits = processUnits.stream().map(processUnit -> ((ComponentUnit)processUnit)).collect(Collectors.toList());
			for(ComponentUnit sComponentUnit : splitComponents) {
				if (!componentUnits.contains(sComponentUnit)) {
					UI.showInfo(sComponentUnit.getComponentId()+Message.getString("wip.component_not_in_lot"));
				}
			}
			
			lotManager.splitLotComponent(lot, splitComponents, lotAction, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.split_success"));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		super.okPressed();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
