package com.glory.mes.wip.lot.split;

import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Table;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;

public abstract class SplitDialog extends BaseTitleDialog {
	
	protected IManagedForm form;
	protected CheckboxTableViewer viewer;
	protected Table table;
	protected Lot lot;
	protected LotAction lotAction;
	
	public SplitDialog(Shell parentShell) {
		super(parentShell);
	}

	public SplitDialog(Shell parentShell, IManagedForm form, Lot lot, LotAction lotAction) {
		this(parentShell);
		this.form = form;
		this.lot = lot;
		this.lotAction = lotAction;
	}

	protected abstract void createViewerComponent(Composite composite, FormToolkit toolkit);

	protected abstract void crateOtherComponent(Composite composite, FormToolkit toolkit);

	@Override
	protected Control buildView(Composite parent) {
		FormToolkit toolkit = form.getToolkit();
	    setTitleImage(SWTResourceCache.getImage("common-dialog"));
	    setTitle(Message.getString("wip.split"));
	    setMessage(Message.getString("wip.split_dialog_message"));
	    
	    Composite content = toolkit.createComposite(parent);
	    content.setLayoutData(new GridData(GridData.FILL_BOTH));
	    content.setLayout(new GridLayout(1, false));
	    
	    createViewerComponent(content, toolkit);        
	    
	    crateOtherComponent(content, toolkit);		
		
		return parent;
	}

}