package com.glory.mes.wip.lot.condition;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.wip.condition.AbstractCondition;

public class ConditionDialog extends BaseTitleDialog {

	protected EntityForm form;
	
	private AbstractCondition condition;
	private ADTable table;
	private boolean isView;
	private Procedure procedure;
	
	public ConditionDialog(Shell parent, AbstractCondition condition, ADTable table, boolean isView) {
		super(parent);
		this.condition = condition;
		this.table = table;
		this.isView = isView;
	}
	
	public ConditionDialog(Shell parent, AbstractCondition condition, Procedure procedure, ADTable table, boolean isView) {
		this(parent, condition, table, isView);
		this.procedure = procedure;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		configureBody(parent);
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
		setTitle(Message.getString("prd.flow_condition_detail"));
		setMessage(Message.getString("prd.set_flow_condition_details_as_required"));
		//return new ConditionForm(parent, SWT.NONE, condition, table, null);
		form = new ConditionForm(parent, SWT.NONE, condition, table, procedure, null);
		return parent;
	}

	@Override
	protected void okPressed() {
		if (form.saveToObject()) {
			//condition = (AbstractCondition)form.getObject();

			super.okPressed();
		}
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
