package com.glory.mes.wip.lot.condition;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;

/**
 *  Ԥ���һЩSample
 *  ָ���û������ȷ�ı��ʽ
 */
public class LotConditionSample extends EntityForm {

	public LotConditionSample(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}
	
	@Override
	public void createForm() {
		
	}
}
