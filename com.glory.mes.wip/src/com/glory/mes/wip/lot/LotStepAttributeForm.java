package com.glory.mes.wip.lot;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.forms.AttributeValidatingFrom;
import com.glory.framework.base.model.BaseAttribute;
import com.glory.framework.base.model.BaseAttributeValue;
import com.glory.framework.base.ui.forms.field.DateTimeField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTextField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.model.ParameterDefinition;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.PrdSpecValue;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotAttribute;
import com.glory.mes.wip.model.LotAttributeValue;

public class LotStepAttributeForm extends AttributeValidatingFrom {
	
	protected static String defaultDateFormat = "yyyy-MM-dd HH:mm:ss";

	protected Lot lot;
	protected List<StepAttribute> stepAttributes;
	protected Equipment equipment;
	
	public LotStepAttributeForm(Composite parent, int style, 
			IMessageManager mmng, Lot lot, 
			List<StepAttribute> stepAttributes, Equipment equipment) {
		super(parent, style, null, mmng);
		this.lot = lot;
		this.stepAttributes = stepAttributes;
		this.equipment = equipment;
		this.setGridY(2);
	}
	
	public void createForm() {
		if (stepAttributes != null) {
			this.setObject(getDefaultValues());
			super.createForm();
		}
	}

	public List<LotAttributeValue> getDefaultValues() {
		List<LotAttributeValue> values = new ArrayList<LotAttributeValue>();
		try {
			//ȡ��LotAttributeValue
			LotManager lotManager = Framework.getService(LotManager.class);
			Map<String, LotAttributeValue> lotAttributeValueMap = lotManager.getLotAttributeValues(lot.getObjectRrn());
			
			//ȡ�ö�Ӧ��SPEC
			List<PrdSpecValue> stepSpecValues = null;
			Map<String, Object> paramMap = null;
			List<LotAttribute> attributes = new ArrayList<LotAttribute>();
			for (StepAttribute stepAttribute : stepAttributes){
			    ADManager adManager = Framework.getService(ADManager.class);
			    List<LotAttribute> lotAttributes = adManager.getEntityList(Env.getOrgRrn(), 
                        LotAttribute.class, 1, " name = '" + stepAttribute.getLotAttributeName() + "'", null);
			    if (lotAttributes != null && lotAttributes.size() > 0) {
			        LotAttribute attribute = lotAttributes.get(0);
	                if (StepAttribute.MAPPEDFROM_SPEC.equalsIgnoreCase(stepAttribute.getMappedFrom())) {
						List<ParameterDefinition> parameterDefinitions = adManager.getEntityList(Env.getOrgRrn(),
								ParameterDefinition.class, 1,
								" name= '" + stepAttribute.getMappedFromParameter() + "'", null);
						if (!parameterDefinitions.isEmpty()) {
							PrdManager prdManager = Framework.getService(PrdManager.class);
							stepSpecValues = prdManager.getSpecValues(Env.getOrgRrn(), parameterDefinitions.get(0).getName(), lot.getStepName(), lot);
							for (PrdSpecValue specValue : stepSpecValues) {
								if (specValue.getAttributeName() != null && attribute.getName().equals(specValue.getAttributeName())) {
									attribute.setMinValue(specValue.getMinValue());
									attribute.setMaxValue(specValue.getMaxValue());
									LotAttributeValue value = new LotAttributeValue((LotAttribute)attribute);
									value.setAttributeName(stepAttribute.getLotAttributeName());
									value.setAttributeValue(specValue.getDefaultValue());
									values.add(value);
									break;
								}
							}
						}
	                } else if (StepAttribute.MAPPEDFROM_PARAMETER.equalsIgnoreCase(stepAttribute.getMappedFrom())) {
	                    if (paramMap == null) {
	                        PrdManager prdManager = Framework.getService(PrdManager.class);
	                        paramMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
	                    }
	                    if (paramMap.containsKey(stepAttribute.getMappedFromParameter())) {
	                        LotAttributeValue value = new LotAttributeValue();
	                        value.setAttributeName(stepAttribute.getLotAttributeName());
	                        value.setAttributeValue(paramMap.get(stepAttribute.getMappedFromParameter()) == null ? "" : String.valueOf(paramMap.get(stepAttribute.getMappedFromParameter())));
	                        values.add(value);
	                    }
	                } else if (lotAttributeValueMap.containsKey(attribute.getName())) {
	                    //�ȴ�LotAttributeValue��ȡֵ
	                    values.add(lotAttributeValueMap.get(attribute.getName()));
	                }
	                
	                attribute.setMappedToParameter(stepAttribute.getMappedToParameter());
	                
	                attributes.add(attribute);
			    }
			}
			this.setAttributes(attributes);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return values;
	}
	
	@Override
	public boolean saveToObject() {
		if (!validate()) {
			return false;
		}
		List<LotAttributeValue> currentValues = new ArrayList<LotAttributeValue>();
		for (IField f : fields.values()){
			if (!(f instanceof SeparatorField)){
				LotAttributeValue value = new LotAttributeValue();
				value.setOrgRrn(lot.getOrgRrn());
				value.setTargetObjectRrn(lot.getObjectRrn());
				value.setAttributeName(f.getId());
				
				//ADFieldΪ���򴴽�,��ObjectRrn��ΪLotAttribute��ObjectRrn
				ADField adField = (ADField)f.getADField();
				for (BaseAttribute attribute : attributes) {
					if (adField.getObjectRrn().equals(attribute.getObjectRrn())) {
						value.setMappedToParameter(((LotAttribute)attribute).getMappedToParameter());
						break;
					}
				}
				if (f instanceof DateTimeField && f.getValue() != null) {
					String timeString = "";
					DateFormat sdf = new SimpleDateFormat(defaultDateFormat);
					if (!StringUtil.isEmpty(adField.getNamingRule())) {
						sdf = new SimpleDateFormat(adField.getNamingRule());
					}
					try {  
						timeString = sdf.format(((DateTimeField)f).getValue());  
			        } catch (Exception e) {  
			            throw e;
			        }  
					value.setAttributeValue(timeString);
				} else {
					value.setAttributeValue(PropertyUtil.getValueString(f.getValue()));
				}
				currentValues.add(value);
			}
		}
		this.object = currentValues;
		return true;		
	}
	
	public List<LotAttributeValue> getAttributeValues() {
		return (List<LotAttributeValue>)object;
	}
	
	public void setLot(Lot lot) {
		this.lot = lot;
		if (lot != null && lot.getObjectRrn() != null) {
			this.setObject(getDefaultValues());
			if (object != null) {
	            loadFromObject();
	        }
		} else {
			this.clear();
		}
	}

	public void clear() {
		for (IField field : fields.values()){
			field.setValue(null);
			field.refresh();
		}
	}
	
	public Lot getLot() {
		return lot;
	}
	
	@Override
    public void loadFromObject() {
		valueMap.clear();
		if (object != null){
			List<? extends BaseAttributeValue> values = (List<? extends BaseAttributeValue>)object;
			for (BaseAttributeValue value : values) {
				valueMap.put(value.getAttributeName(), value);
			}
		}
		for (IField f : fields.values()){
			if (!(f instanceof SeparatorField || f instanceof RefTextField)){
				Object o = null;
				if (valueMap.containsKey(f.getId())) {
					o = ((BaseAttributeValue)valueMap.get(f.getId())).getAttributeValue();
				} else {
					//��ȡĬ��ֵ
					Object defaultO = f.getDefaultValue();
					if (defaultO != null) {
						o = defaultO;
					}
				}
				//f.setValue(o);
				
				if (f instanceof DateTimeField && o != null) {
					ADField adField = (ADField)f.getADField();
					DateFormat sdf = new SimpleDateFormat(defaultDateFormat);
					if (!StringUtil.isEmpty(adField.getNamingRule())) {
						sdf = new SimpleDateFormat(adField.getNamingRule());
					}
					try {  
						Date dt = sdf.parse(o.toString());
						f.setValue(dt);
			        } catch (Exception e) {  
			        	e.printStackTrace();
			        }  
				} else {
					f.setValue(o);
				}
			}
		}
		refresh();
		setEnabled();
	}
}
