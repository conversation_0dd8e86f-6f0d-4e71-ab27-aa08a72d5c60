package com.glory.mes.wip.lot.componenteqp.hisquery;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.compress.utils.Lists;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotHistoryManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.his.ComponentEquipmentUnitHis;
import com.glory.mes.wip.his.ComponentUnitHis;
import com.glory.mes.wip.model.LotStateMachine;

public class ComponentHisDialog extends GlcBaseDialog {
	
	protected FormToolkit toolkit;
	protected ListTableManager listTableManager;
	private static int MIN_DIALOG_WIDTH = 400;
	private static int MIN_DIALOG_HEIGHT = 300;
	private ComponentUnitHis componentUnitHis;
	
	private final String AD_TABLE = "WIPComponentHisQueryTable";
	
	public ComponentHisDialog(String adFormName, String authority, IEventBroker eventBroker, ComponentUnitHis componentUnitHis) {
		super(adFormName, authority, eventBroker);
		this.setComponentUnitHis(componentUnitHis);
	}

	public ComponentUnitHis getComponentUnitHis() {
		return componentUnitHis;
	}

	public void setComponentUnitHis(ComponentUnitHis componentUnitHis) {
		this.componentUnitHis = componentUnitHis;
	}
	
	@Override
    protected Control buildView(Composite parent) {
		setTitle(Message.getString(WipExceptionBundle.bundle.WipComponentHis()));
		setMessage(Message.getString(WipExceptionBundle.bundle.WipComponentOtherDetial()));
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
		toolkit = new FormToolkit(parent.getDisplay());	
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
        composite.setBackground(new Color(Display.getCurrent(), 255,255,255));
        composite.setLayout( new GridLayout(1, false));
        composite.setLayoutData(new GridData(GridData.FILL_BOTH));
        
    	createTableViewer(composite);
        return composite; 
	}
	
	public void createTableViewer(Composite tableCom) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			LotHistoryManager lotHistoryManager = Framework.getService(LotHistoryManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), AD_TABLE);
			listTableManager = new ListTableManager(adTable);
			List<ComponentUnitHis> allHis = Lists.newArrayList();
			
			if (componentUnitHis != null) {
				String transType = componentUnitHis.getTransType();
				if (LotStateMachine.TRANS_CHAMBERIN.equals(transType) || LotStateMachine.TRANS_CHAMBEROUT.equals(transType)
						|| LotStateMachine.TRANS_PROCESSSTART.equals(transType) || LotStateMachine.TRANS_PROCESSEND.equals(transType)) {
					List<ComponentEquipmentUnitHis> componentEquipmentUnitHisList = lotHistoryManager.getComponentEqpUnitHisByComponentId(
	    					Env.getOrgRrn(), componentUnitHis.getComponentId(), null);
					componentEquipmentUnitHisList = componentEquipmentUnitHisList.stream().filter(e -> e.getTransType().equals(transType)).collect(Collectors.toList());
					for (ComponentEquipmentUnitHis componentEquipmentUnitHis : componentEquipmentUnitHisList) {
						ComponentUnitHis componentUnitHis = new ComponentUnitHis();
						componentUnitHis.setEquipmentId(componentEquipmentUnitHis.getEquipmentId());
						componentUnitHis.setUnitId(componentEquipmentUnitHis.getUnitId());
						componentUnitHis.setRecipeName(componentEquipmentUnitHis.getRecipeName());
						allHis.add(componentUnitHis);
					}
				} else {
					String whereClause = "hisSeq = '" + componentUnitHis.getHisSeq() + "' and transType = '" + transType + "'";
					List<ComponentUnitHis> componentUnitHisList = lotHistoryManager.getComponentHisByComponentId(Env.getOrgRrn(), componentUnitHis.getComponentId(), whereClause);
					allHis.addAll(componentUnitHisList);
				}
			}
			listTableManager.newViewer(tableCom);
			listTableManager.setInput(allHis);
			listTableManager.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}

}
