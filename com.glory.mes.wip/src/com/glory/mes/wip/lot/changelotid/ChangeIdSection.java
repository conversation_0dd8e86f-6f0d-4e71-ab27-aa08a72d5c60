package com.glory.mes.wip.lot.changelotid;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;

public class ChangeIdSection extends LotSection {

	
	public static final String KEY_CHANGE = "change";
	protected AuthorityToolItem change;
	protected ChangeIdForm changeIdForm;
	public ChangeIdSection() {
		super();
	}

	public ChangeIdSection(ADTable table) {
		super(table);
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		changeIdForm = new ChangeIdForm(composite, SWT.NONE, tab, mmng);
		return changeIdForm;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar toolBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChange(toolBar);
		new ToolItem(toolBar, SWT.SEPARATOR);
		createToolItemRefresh(toolBar);
		section.setTextClient(toolBar);
	}

	public void createToolItemChange(ToolBar tBar) {
		change = new AuthorityToolItem(tBar, SWT.NULL, getTable().getAuthorityKey() +"."+ KEY_CHANGE);
		change.setAuthEventAdaptor(this::changeAdapter);
		change.setText(Message.getString("wip.changeId_change"));
		change.setImage(SWTResourceCache.getImage("modify"));
//		change.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent e) {
//				changeAdapter();
//			}
//		});
	}

	public void changeAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			Lot lot = (Lot) this.getAdObject();
			if (lot == null) {//�ж��Ƿ��Ѵ���lot���󣬷���Ϊ�Ƿ�����
				UI.showError(Message.getString("wip.changeId_null"));
				return;
			}
			
			if (!LotStateMachine.STATE_WAIT.equals(lot.getState())) {
				return;
			}
			
			String value = ((String) getField("attribute1").getValue()).trim();
			
			SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
			String lotNamingRule = MesCfMod.getLotNamingRule(0, sysParameterManager);
			//������ʽ��֤
		    Pattern pattern = Pattern.compile(lotNamingRule);    
		    Matcher matcher = pattern.matcher(value); 
		    boolean flag = matcher.matches();
		    if (flag == false) {
		    	UI.showError(Message.getString("wip.changemo_checking"));
				return;
		    }
		    
			if (value == null || "".equals(value)) {//�ж��Ƿ����������κ�
				UI.showError(Message.getString("wip.changeId_empty"));
				return;
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<Lot> list = adManager.getEntityList(Env.getOrgRrn(),
					Lot.class, Env.getMaxResult(), "lotId='" + value + "' ",
					null);
			if (list.size() > 0) {//�ж����κ��Ƿ��Ѵ���
				UI.showError(Message.getString("wip.changeId_have"));
				return;
			}
			
			List<LotPrepare> lotPrepares = adManager.getEntityList(Env.getOrgRrn(),
					LotPrepare.class, Env.getMaxResult(), "lotId = '" + lot.getLotId() + "' ",
					null);
			if(CollectionUtils.isNotEmpty(lotPrepares)) {
				UI.showError(Message.getString("wip.lot_are_prepare"));
				return;
			}

			LotManager lotManager = Framework.getService(LotManager.class);
			//���ú�̨������������
			String newLotId = value;
			if (!isLotIdCaseSensitive()) {
				newLotId = newLotId.toUpperCase();
			}
			SessionContext sc = Env.getSessionContext();
			if (change.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
				sc.setUserName((String) change.getData(LotAction.ACTION_TYPE_OPERATOR));
			}
			
			lotManager.changeLotId(lot.getLotId(), newLotId, sc);
			UI.showInfo(Message.getString("wip.changeId_successed"));
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
				changeIdForm.setObject((Lot) getAdObject());//����ҳ����Ϣ
				txtLot.setText(((Lot) getAdObject()).getLotId());//����lotId
				changeIdForm.refresh();
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		super.refresh();
	}
	
	@Override
	public void statusChanged(String newStatus) {
		super.statusChanged(newStatus);
		if (LotStateMachine.STATE_WAIT.equals(newStatus)) {
			change.setEnabled(true);
		} else {
			change.setEnabled(false);
		}
	}
}
