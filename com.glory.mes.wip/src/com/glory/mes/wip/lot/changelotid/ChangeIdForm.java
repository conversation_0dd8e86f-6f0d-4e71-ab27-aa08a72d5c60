/**
 * 
 */
package com.glory.mes.wip.lot.changelotid;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Message;

public class ChangeIdForm extends EntityForm{
	private static final Logger logger = Logger.getLogger(ChangeIdForm.class);
	private IField lotField ;
	
	public ChangeIdForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public ChangeIdForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public ChangeIdForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public ChangeIdForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}	
	
	@Override
	public void addFields() {
		super.addFields();
		try {
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
			
			lotField = createText("attribute1", Message.getString("wip.changeId_field"),"", 32);
			this.addField("attribute1", lotField);
			
		} catch (Exception e) {
			logger.error("MoveLocationForm : Init listItem", e);
		}
	}
}
