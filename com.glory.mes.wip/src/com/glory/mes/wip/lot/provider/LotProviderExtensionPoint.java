package com.glory.mes.wip.lot.provider;

import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

public class LotProviderExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(LotProviderExtensionPoint.class);
	
	private static final LotProviderExtensionPoint instance = new LotProviderExtensionPoint();
	private static ILotProvider lotProvider;
	
    public final static String X_POINT = "com.glory.mes.wip.lotproviders";
    public final static String E_PROVIDER = "provider";
    public final static String A_CLASS = "class";
    public final static String A_PRIORITY = "priority";
    
    public static LotProviderExtensionPoint getInstance() {
    	return instance;
    }

	public static ILotProvider getLotProvider() {
		return lotProvider;
	}

	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
			for (int j = 0; j < configElements.length; j++) {
				try {
					lotProvider = (ILotProvider)configElements[j].createExecutableExtension(A_CLASS);
				} catch (Exception e){
					logger.error("LotProviderExtensionPoint : init ", e);
				}
			}
		}			
	}
	
}
