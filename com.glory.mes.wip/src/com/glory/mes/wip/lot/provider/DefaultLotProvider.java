package com.glory.mes.wip.lot.provider;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.idgenerator.GeneratorContext;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class DefaultLotProvider implements ILotProvider {

	@Override
	public Lot getLot(String lotAlias) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		return lotManager.getLotByLotId(Env.getOrgRrn(), lotAlias);
	}
	
	@Override
	public List<String> generateLotId(Lot lot, String transaction, int num) throws Exception {
		MBASManager basManager = Framework.getService(MBASManager.class);
		Map<String, Object> parameterMap = new HashMap<String, Object>(); 
		GeneratorContext gContext = new GeneratorContext(lot, parameterMap, 
				Env.getSessionContext(), GENERATORTYPE_LOT, num);
		return basManager.generatorId(Env.getOrgRrn(),gContext.getGeneratorRule(), gContext);
	}

}
