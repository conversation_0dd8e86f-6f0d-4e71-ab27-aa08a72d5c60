package com.glory.mes.wip.lot.provider;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.HistoryUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.util.LineNotMatchException;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotHistoryManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class LotProviderEntry {

	public static final String GENERATORTYPE_LOT = "Lot";
	public static final String GENERATORTRANS_SCHEDULE = "Schedule";
	public static final String GENERATORTRANS_RECEIVE = "Receive";
	
	public static Lot getLot(String lotAlias) throws Exception {
		Lot lot = null;
		//�������LotProvider��չ(�����CarrierIDȡ��Lot)
		ILotProvider lotProvider =  LotProviderExtensionPoint.getLotProvider();
		if (lotProvider == null) {
			lotProvider = new DefaultLotProvider();
		} 
		
		lot = lotProvider.getLot(lotAlias);
		if(lot != null) {
//			loadRecipeAndMask(lot);
		}
		return lot;
	}
	
	public static Lot getLotByLine(String lotAlias) throws Exception {
		Lot lot = getLot(lotAlias);
		if (lot != null && Env.isUseLine()) {
			//�����������߱�ʱ�ż���߱�
			if (lot.getLineId() != null && lot.getLineId().trim().length() > 0) {
				boolean checkLine = false;
				for (String lineId : Env.getLines()) {
					if (lineId.equals(lot.getLineId())) {
						checkLine = true;
						break;
					}
				}
				if (!checkLine) {
					//���߱𲻷���ʱ
					throw new LineNotMatchException();
				}
			}
		}
		
		return lot;
	}
	
	public static Lot loadRecipeAndMask(Lot lot) throws Exception {
//		SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
//		boolean isByEqpQueryPpid = MesCfMod.isByEqpQueryPpid(Env.getOrgRrn(), sysParamManager);
//		if (!isByEqpQueryPpid) {
//			return lot;
//		}
		
		if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
			// ��վʱ�Ѿ���PPID��Reticle�浽���α�����
			return lot;
		}
		
		if (lot != null && lot.getObjectRrn() != null) {
			Equipment equipment = null;
			if (!StringUtil.isEmpty(lot.getEquipmentId())) {
				RASManager rasManager = Framework.getService(RASManager.class);
				equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), lot.getEquipmentId(), false);
			}
			if (equipment == null) {
				return lot;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> lots = lotManager.calculatePPID(Lists.newArrayList(lot), equipment, true, Env.getSessionContext());
			if (CollectionUtils.isNotEmpty(lots)) {
				lot.setRecipeName(lots.get(0).getRecipeName());
				lot.setMask(lots.get(0).getMask());
				lot.setEquipmentRecipe(lots.get(0).getEquipmentRecipe());
				/*if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
					//��������ChamberRecipeѡ��Ŀ��ܲ����Ƽ��ĵ�һ��PPID,��Ҫֱ�Ӵ���ʷ��ȡ��
					LotHistoryManager lotHistoryManager = Framework.getService(LotHistoryManager.class);
					List<LotHis> his = lotHistoryManager.getLotHisByHistorySeq(Env.getOrgRrn(), lot.getCurSeq(), " transType = '" + 
							LotStateMachine.TRANS_TRACKIN + "' AND lotId = '" + lots.get(0).getLotId() + "'");
					if (CollectionUtils.isNotEmpty(his) && !StringUtil.isEmpty(his.get(0).getHisComment())) {
						String hisComment = his.get(0).getHisComment();
						String[] comments = hisComment.split(String.valueOf(HistoryUtil.HISCOMMENT_SEPERATOR));
						for (String comment : comments) {
							if (comment.startsWith("[" + LotHis.COMMENT_KEY_EQPRECIPE + "]")) {
								lot.setEquipmentRecipe(comment.replace("[" + LotHis.COMMENT_KEY_EQPRECIPE + "]", "").trim());
							}
						}
					}
				}*/
			}
		}	
		return lot;
	}
	
	public String generateLotId(Lot lot, String transaction) throws Exception {
		List<String> lotIds = generateLotId(lot, transaction, 1);
		if (lotIds.size() > 0) {
			return lotIds.get(0);
		}
		return null;
	}
	
	public List<String> generateLotId(Lot lot, String transaction, int num) throws Exception {
		ILotProvider lotProvider =  LotProviderExtensionPoint.getLotProvider();
		if (lotProvider == null) {
			lotProvider = new DefaultLotProvider();
		} 
		
		return lotProvider.generateLotId(lot, transaction, num);
	}
	
}
