package com.glory.mes.wip.lot.provider;

import java.util.List;

import com.glory.mes.wip.model.Lot;

public interface ILotProvider {
	
	public static final String GENERATORTYPE_LOT = "Lot";
	public static final String GENERATORTRANS_SCHEDULE = "Schedule";
	public static final String GENERATORTRANS_RECEIVE = "Receive";

	public Lot getLot(String lotAlias) throws Exception;
	
	public List<String> generateLotId(Lot lot, String transaction, int num) throws Exception;
		
}
