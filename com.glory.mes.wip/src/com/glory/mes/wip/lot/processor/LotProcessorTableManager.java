package com.glory.mes.wip.lot.processor;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;

public class LotProcessorTableManager extends ListTableManager {

	public LotProcessorTableManager(ADTable adTable) {
		super(adTable);
	}

	public LotProcessorTableManager(ADTable adTable, boolean checkFlag) {
		super(adTable, checkFlag);
	}
	
	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new InValidLotItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
	
}
