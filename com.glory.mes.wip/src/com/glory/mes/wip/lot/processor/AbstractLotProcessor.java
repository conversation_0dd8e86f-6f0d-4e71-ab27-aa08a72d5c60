package com.glory.mes.wip.lot.processor;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateModel;

public abstract class AbstractLotProcessor implements ILotProcessor {
	
	public static final Logger logger = Logger.getLogger(AbstractLotProcessor.class);

	private static final String TABLE_NAME_LOT_LIST = "WIPLotProcessorLotList";

	private boolean isBatch;
	
	public AbstractLotProcessor(boolean isBatch) {
		super();
		this.isBatch = isBatch;
	}

	/**
	 * ֻ����������У�鶼ͨ����ִ��(��ʾLotProcessorDialog)
	 * ������ʾ
	 */
	protected boolean isAllPass = false;
	
	protected String eventId;
	
	public abstract boolean checkLotState(Lot lot);
	
	public abstract void buildProcessForm(Composite parent, FormToolkit toolkit);
	
	/**
	 * �����ʾѡ�е�������Ϣ��̬��
	 */
	public ADTable getListADTable() {
		ADTable listTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			listTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_LOT_LIST);
		} catch (Exception e) {
			logger.error("AbstractLotProcessor getListADTable error:", e);
		}
		if (listTable == null) {
			listTable = getDefaultListADTable();
		}
		return listTable;
	}
	
	public boolean preValidate(Lot lot) {
		boolean isValid = true;
		Boolean result = checkLotStateModel(lot);
		if (result == null) {
			if (!checkLotState(lot)) {
				isValid = false;
			} 
			
		} else if (!result) {
			isValid = false;
		}
		if (!isValid) {
			lot.setConstraintFlag(true);
			lot.clearMessage();
            lot.addMessage("error.state_is_not_allow");
		}
		return isValid;
	}
	
	/**
	 * �򿪴���Dialog
	 * 
	 * @param lots �����������
	 */
	public void open(List<Lot> lots) {
		try {
			List<Lot> inVaildLots = new ArrayList<Lot>();
			
			for (Lot lot : lots) {
				if (!preValidate(lot)) {
					inVaildLots.add(lot);
				}
			}
			if (isBatch) {
				if (isAllPass && !inVaildLots.isEmpty()) {
					//��ʾ��Ч����,������
					openInValidDialog(inVaildLots);
					return;
				}
				openLotProcessorDialog(lots);
			} else {
				if (!inVaildLots.isEmpty()) {
					//��ʾ��Ч����,������
					openInValidDialog(inVaildLots);
					return;
				}
				openLotProcessorDialog(lots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void openInValidDialog(List<Lot> lots) {
		LotProcessorInValidDialog dialog = new LotProcessorInValidDialog(this, lots);
		if (dialog.open() == Dialog.OK) {}
	}
	
	public void openLotProcessorDialog(List<Lot> lots) {	
		LotProcessorDialog dialog = new LotProcessorDialog(this, lots);
		if (dialog.open() == Dialog.OK) {}
	}
	
	/**
	 * ����Ϊ�¼�����LotStateModel,���û�������򷵻�null,��ʱ��Ҫ����¼���Ĭ�Ϲ���
	 * ����Ϊÿ���¼����ö���LotStateModel,ֻҪ����һ������Ϳ���ִ�д��¼�(Button����)
	 * LotStateModel��鷽ʽΪ���ε�ǰ״̬��Hold״̬,��LotStateModelһ��
	 */
	public Boolean checkLotStateModel(Lot lot) {
		try {
			if (!StringUtil.isEmpty(eventId)) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotStateModel> models = adManager.getEntityList(Env.getOrgRrn(), LotStateModel.class, Integer.MAX_VALUE, " eventId = '" + eventId + "'", "");
				if (models.isEmpty()) {
					return null;
				}
				
				boolean isAllFail = true;
				for (LotStateModel model : models) {
					if (!StringUtil.isEmpty(model.getHoldState()) && !model.getHoldState().equals(lot.getHoldState())) {
						//���μ�鲻ͨ��,���������һ����
						continue;
					}
					if (!StringUtil.isEmpty(model.getLotState()) && !model.getLotState().equals(lot.getState())) {
						//���μ�鲻ͨ��,���������һ����
						continue;
					}
					isAllFail = false;
					break;
				}
				if(!isAllFail) {
					return true;
				} else {
					return false;
				}
			} else {
				return null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}

	/**
	 * ����Ĭ�ϲ�ѯ��̬��
	 */
	public ADTable getDefaultListADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();

		ADField adField1 = new ADField();
		adField1.setName("lotId");
		adField1.setIsMain(true);
		adField1.setIsDisplay(true);
		adField1.setLabel(Message.getString("wip.lot_id"));
		adField1.setLabel_zh(Message.getString("wip.lot_id"));
		adFields.add(adField1);

		ADField adField2 = new ADField();
		adField2.setName("partId");
		adField2.setIsMain(true);
		adField2.setIsDisplay(true);
		adField2.setDisplayLength(15l);
		adField2.setLabel(Message.getString("wip.part_id"));
		adField2.setLabel_zh(Message.getString("wip.part_id"));
		adFields.add(adField2);

		ADField adField3 = new ADField();
		adField3.setName("mainQty");
		adField3.setIsMain(true);
		adField3.setIsDisplay(true);
		adField3.setIsEditable(true);
		adField3.setLabel(Message.getString("wip.main_qty"));
		adField3.setLabel_zh(Message.getString("wip.main_qty"));
		adField3.setDataType("integer");
		adField3.setDisplayType("text");
		adFields.add(adField3);
		
		ADField adField4 = new ADField();
		adField4.setName("state");
		adField4.setIsMain(true);
		adField4.setIsDisplay(true);
		adField4.setIsEditable(true);
		adField4.setLabel(Message.getString("wip.state"));
		adField4.setLabel_zh(Message.getString("wip.state"));
		adField4.setDisplayType("text");
		adFields.add(adField4);
		
		ADField adField5 = new ADField();
		adField5.setName("holdState");
		adField5.setIsMain(true);
		adField5.setIsDisplay(true);
		adField5.setIsEditable(true);
		adField5.setLabel(Message.getString("wip.hold_state"));
		adField5.setLabel_zh(Message.getString("wip.hold_state"));
		adField5.setDisplayType("text");
		adFields.add(adField5);
		
//		ADField adField6 = new ADField();
//		adField6.setName("messageString");
//		adField6.setIsMain(true);
//		adField6.setIsDisplay(true);
//		adField6.setIsEditable(true);
//		adField6.setLabel(Message.getString("wip.lot_message"));
//		adField6.setLabel_zh(Message.getString("wip.lot_message"));
//		adField6.setDisplayType("text");
//		adFields.add(adField6);

		adTable.setFields(adFields);

		return adTable;
	}

	public boolean isBatch() {
		return isBatch;
	}

	public void setBatch(boolean isBatch) {
		this.isBatch = isBatch;
	}
	
	
}
