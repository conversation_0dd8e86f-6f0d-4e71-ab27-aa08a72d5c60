package com.glory.mes.wip.lot.processor;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckBoxDisableListener;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;

/**
 * ��ʾ����������Ϣ,�����������������
 * �ϲ��ָ��ݲ�ͬ�Ķ�����ʾ��ͬ�������
 * �²���Ϊѡ�е�������Ϣ,�������ܴ��������,���ܴ��������Ҳ��ʾ,����ʾ��ɫ�Ҳ���ѡ��
 */
public class LotProcessorDialog extends BaseDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;
	
	private ILotProcessor processor;
	private List<Lot> lots;
	
	private LotProcessorTableManager tableManager;
	
	public LotProcessorDialog(ILotProcessor processor, List<Lot> lots) {
		super();
		this.processor = processor;
		this.lots = lots;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		FormToolkit toolkit = new FormToolkit(parent.getDisplay()); 
		
		Section upSection = toolkit.createSection(parent, Section.TITLE_BAR | Section.EXPANDED);
		upSection.setLayout(new GridLayout(1, true));
		upSection.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		upSection.setText(Message.getString("wip.select_action_info"));
    	
		Composite upComposite = toolkit.createComposite(upSection, SWT.NONE);
      	upComposite.setLayout(new GridLayout(1, true));
      	upComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
      	AbstractLotProcessor abstractLotProcessor = (AbstractLotProcessor)processor;   	
      	abstractLotProcessor.buildProcessForm(upComposite, toolkit);    	
      	upSection.setClient(upComposite);
      	
      	if (abstractLotProcessor.isBatch()) {
      		Section downSection = toolkit.createSection(parent, Section.TITLE_BAR | Section.EXPANDED);
      		downSection.setLayout(new GridLayout(1, true));
      		downSection.setLayoutData(new GridData(GridData.FILL_BOTH));
      		downSection.setText(Message.getString("wip.lot_processor_list_title"));
    		
          	Composite downComposite = toolkit.createComposite(downSection, SWT.NONE);
          	downComposite.setLayout(new GridLayout(1, true));
          	downComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
    		
          	try {
          		tableManager = new LotProcessorTableManager(abstractLotProcessor.getListADTable(), true);
          		tableManager.newViewer(downComposite);
          		((CheckBoxTableViewerManager)tableManager.getTableManager()).setCheckBoxDisableListener(new ICheckBoxDisableListener() {
        			public boolean isDisable(Object object) {
        				Lot lot = (Lot)object;
        				if (lot.getConstraintFlag()) {
        					return true;
        				}
        				return false;
        			}
        		});
          		tableManager.setInput(lots);
          		if (lots != null && lots.size() > 0) {
          			List<Lot> validLots = new ArrayList<Lot>();
          			for (Lot lot : lots) {
          				if (!lot.getConstraintFlag()) {
          					validLots.add(lot);
        				}
              		}
              		tableManager.getCheckedObject().addAll(validLots);
              		tableManager.refresh();
          		}
          		
          	} catch (Exception e) {
          		ExceptionHandlerManager.asyncHandleException(e);
          	}
          	downSection.setClient(downComposite);
      	}
      	
		return parent;
	}
	
	@Override
	protected void okPressed() {
		AbstractLotProcessor abstractLotProcessor = (AbstractLotProcessor)processor;   	
		if (abstractLotProcessor.isBatch()) {
			List<Object> checkedObjects = tableManager.getCheckedObject();
			if (checkedObjects != null && checkedObjects.size() > 0) {
				List<Lot> checkLots = new ArrayList<Lot>();
				for (Object checkedObject : checkedObjects) {
					Lot lot = (Lot) checkedObject;
					checkLots.add(lot);
				}
				if (processor.process(checkLots)) {
					super.okPressed();
				}
			}
		} else {
			if (processor.process(lots)) {
				super.okPressed();
			}
		}
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		AbstractLotProcessor abstractLotProcessor = (AbstractLotProcessor)processor; 
		
		int width = MIN_DIALOG_WIDTH;
		int height = MIN_DIALOG_HEIGHT;
		if (!abstractLotProcessor.isBatch()) {
			width = MIN_DIALOG_WIDTH - 200;
			height = MIN_DIALOG_HEIGHT - 200;
		}
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(width), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(height),
						shellSize.y));
	}
}
