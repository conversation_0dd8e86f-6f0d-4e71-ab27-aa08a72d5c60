package com.glory.mes.wip.lot.processor.batch;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.idquery.IdQueryEntityQueryListSection;
import com.glory.mes.wip.lot.processor.ChangeGradeLotProcessor;
import com.glory.mes.wip.lot.processor.ChangePriorityLotProcessor;
import com.glory.mes.wip.lot.processor.ChangeWoIdLotProcessor;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * ����Ʒ��ѯ�б�,�����ڴ��б��ж����������ν��д���
 * ֧�ֵ��빦��
 */
public class LotProcessorLotListSection extends IdQueryEntityQueryListSection {
	
	public static final String KEY_CHANGE_PRIORITY = "priority";
	
	public static final String KEY_CHANGE_WO = "wo";
	
	public static final String KEY_CHANGE_GRADE = "grade";

	private AuthorityToolItem itemChangePriority;
	private AuthorityToolItem itemChangeWo;
	private AuthorityToolItem itemChangeGrade;
	
	public LotProcessorLotListSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChangeGrade(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemChangeWo(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemChangePriority(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSearch(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	private void createToolItemChangePriority(ToolBar tBar) {
		itemChangePriority = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_CHANGE_PRIORITY);
		itemChangePriority.setImage(SWTResourceCache.getImage("modify"));
		itemChangePriority.setText(Message.getString("wip.modify_priority"));
		itemChangePriority.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changePriorityAdapter(event);
			}
		});
	}
	
	protected void changePriorityAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<Lot> checkLots = new ArrayList<Lot>();
				for (Object object : objects) {
					checkLots.add((Lot)object);
				}
				ChangePriorityLotProcessor processor = new ChangePriorityLotProcessor(true);
				processor.open(checkLots);
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void createToolItemChangeWo(ToolBar tBar) {
		itemChangeWo = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_CHANGE_WO);
		itemChangeWo.setImage(SWTResourceCache.getImage("modify"));
		itemChangeWo.setText(Message.getString("wip.modify_docId"));
		itemChangeWo.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeWoAdapter(event);
			}
		});
	}
	
	protected void changeWoAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<Lot> checkLots = new ArrayList<Lot>();
				for (Object object : objects) {
					checkLots.add((Lot)object);
				}
				ChangeWoIdLotProcessor processor = new ChangeWoIdLotProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void createToolItemChangeGrade(ToolBar tBar) {
		itemChangeGrade = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_CHANGE_GRADE);
		itemChangeGrade.setImage(SWTResourceCache.getImage("modify"));
		itemChangeGrade.setText(Message.getString("wip.modify_grade"));
		itemChangeGrade.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeGradeAdapter(event);
			}
		});
	}
	
	protected void changeGradeAdapter(SelectionEvent event) {
		try {
			List<Object> objects = this.getTableManager().getCheckedObject();
			if (objects != null && objects.size() > 0) {
				List<Lot> checkLots = new ArrayList<Lot>();
				for (Object object : objects) {
					checkLots.add((Lot)object);
				}
				ChangeGradeLotProcessor processor = new ChangeGradeLotProcessor(true);
				processor.open(checkLots);
				refresh();
			}else {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public List<Object> getObjectsByInClause(List<String> ids) {
		try {
			Set idSet = new HashSet(ids);
			Map<String, Object> fieldMap = new HashMap<String, Object>();
			fieldMap.put("lotIds", idSet);
			ADManager adManager = Framework.getService(ADManager.class);
			List<Lot> lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, 
					Integer.MIN_VALUE, Integer.MAX_VALUE, " lotId in (:lotIds) ", "", fieldMap);
			return (List<Object>)(List)lots;
		} catch (Exception e) {
			logger.error("LotProcessorLotListSection getObjectsByInClause error:", e);
		}
		return null;
	}


}
