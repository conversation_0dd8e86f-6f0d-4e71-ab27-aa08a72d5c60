package com.glory.mes.wip.lot.processor;

import org.eclipse.swt.graphics.Color;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;

public class InValidLotItemAdapter extends ListItemAdapter<Lot> {

	@Override
	public Color getForeground(Object element, String id) {
		Lot lot = (Lot)element;
    	if (lot.getConstraintFlag()) {
    		return SWTResourceCache.getColor(SWTResourceCache.COLOR_RED_ORANGE);
    	} 
    	return null;
	}
	
}
