package com.glory.mes.wip.lot;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.base.model.ParameterDefinition;
import com.glory.mes.prd.parameter.ParameterForm;
import com.glory.mes.wip.model.LotParameter;

public class LotParameterForm extends ParameterForm {

	private static final Logger logger = Logger.getLogger(LotParameterForm.class);
	protected static final String PROPERTY_ID = "lotParameters";

	public LotParameterForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}
	
	@Override
	public boolean saveToObject() {
		if (object != null) {
			IField f = null;
			f = fields.get(PROPERTY_ID);
			List<ParameterDefinition> parameters = (List<ParameterDefinition> )f.getValue();
			if (parameters != null) {
				List<LotParameter> lotParamters = new ArrayList<LotParameter>();
				for (ParameterDefinition parameter : parameters){
					LotParameter lotParamter = new LotParameter(parameter.getName(), null, null, parameter.getDefValue());
					lotParamter.setIsActive(true);
					lotParamter.setOrgRrn(parameter.getOrgRrn());
					lotParamter.setType(parameter.getType());
					lotParamters.add(lotParamter);
				}
				PropertyUtil.setProperty(object, PROPERTY_ID, lotParamters);
			}
			return true;
		}
		return false;
	}
	
	@Override
	public void loadFromObject() {
		if (object != null) {
			IField f = fields.get(PROPERTY_ID);
			List<LotParameter> lotParamters = (List<LotParameter>)PropertyUtil.getPropertyForIField(object, PROPERTY_ID);
			if (lotParamters != null) {
				List<ParameterDefinition> parameters = new ArrayList<ParameterDefinition>();
	    		for (LotParameter lotParamter : lotParamters) {
	    			ParameterDefinition parameter = new ParameterDefinition();
	    			parameter.setName(lotParamter.getVariableName());
	    			parameter.setType(lotParamter.getType());
	    			parameter.setDefValue(lotParamter.getDefaultValue());
					parameters.add(parameter);
	    		}
	    		f.setValue(parameters);
	    		refresh();
			}
		}
	}
}