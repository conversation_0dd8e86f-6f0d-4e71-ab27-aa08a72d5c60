package com.glory.mes.wip.lot.lotprocedure.setup;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.mes.prd.workflow.graph.def.Node;

public class LotProcedureViewDialog extends GlcBaseDialog{

    private static final Logger logger = Logger.getLogger(LotProcedureViewDialog.class);

	private static final String CONTORL_FORM_STEP = "stepInfo";

	protected ListTableManagerField stepListTableField;
	
	protected List<Node> nodes;

	public LotProcedureViewDialog(String adFormName, String authority, IEventBroker eventBroker,
			List<Node> nodes) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
		this.nodes = nodes;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {  	
			stepListTableField = form.getFieldByControlId(CONTORL_FORM_STEP, ListTableManagerField.class);

			stepListTableField.getListTableManager().setInput(nodes);
        } catch (Exception e) {
            logger.error("LotProcedureViewDialog : Init createFormAction", e);
            e.printStackTrace();
        }
	}

}
