package com.glory.mes.wip.lot.lotprocedure;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.lot.lotprocedure.setup.LotProcedureViewDialog;
import com.glory.framework.core.exception.ExceptionBundle;

public class SelectTargetStepDialog extends LotProcedureViewDialog {

	 private static final Logger logger = Logger.getLogger(LotProcedureViewDialog.class);

	private static final String CONTORL_FORM_STEP = "stepInfo";

	protected ListTableManagerField stepListTableField;
	
	protected List<Node> nodes;
	protected String immediateStepStateName;

	public SelectTargetStepDialog(String adFormName, String authority, IEventBroker eventBroker, List<Node> nodes) {
		super(adFormName, authority, eventBroker, nodes);
		this.setBlockOnOpen(true);
		this.nodes = nodes;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {  	
			stepListTableField = form.getFieldByControlId(CONTORL_FORM_STEP, ListTableManagerField.class);

			stepListTableField.getListTableManager().setInput(nodes);
			setTitle(Message.getString("wip.target_step_selection"));
			setMessage(Message.getString("wip.select_a_step_as_the_target_step"));
        } catch (Exception e) {
            logger.error("LotProcedureViewDialog : Init createFormAction", e);
            e.printStackTrace();
        }
	}
	
	@Override
	protected void okPressed() {
		Object object = stepListTableField.getListTableManager().getSelectedObject();
		if(object == null) {
			UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			return;
		}
		Node node = (Node) object;
		immediateStepStateName = node.getName();
		super.okPressed();
	}

	public String getImmediateStepStateName() {
		return immediateStepStateName;
	}

}
