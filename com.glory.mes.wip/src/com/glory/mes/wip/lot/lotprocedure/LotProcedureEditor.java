package com.glory.mes.wip.lot.lotprocedure;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.inject.Inject;

import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.application.command.OpenEditorCommand;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.LotProcedure;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotProcedureManager;
import com.glory.mes.wip.lot.lotprocedure.setup.LotProcedureViewDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotProcedureChange;
import com.glory.mes.wip.model.LotProcedureChangeStep;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotProcedureEditor extends GlcEditor {
	
	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.lotprocedure.LotProcedureEditor";	
	
	public static final String FIELD_LOT_PROCEDURE_QUERY = "lotProcedureQuery";
	public static final String FIELD_LOT_PROCEDURE_STEP_LIST = "lotProcedureStepList";
	
	public static final String FIELD_LOT_PROCEDURE_LOT_ID = "lotId";
	
	public static final String AUTHORITY_NAME = "Wip.LotProcedureChangeSetup";
	
	public static final String BUTTON_VIEW_PROCEDURE = "viewProcedure";
	public static final String FORM_VIEW_PROCEDURE = "PRDStepViewForm";
	
	public QueryFormField lotProcedureQueryFormField; 
	public ListTableManagerField lotProcedureStepListTableField; 
	public TextField lotIdField;
	
	private ToolItem itemEdit, itemActive, itemInActive, itemClose, itemDelete;
	
	@Inject
	EPartService partService;
	
	@Inject
	EModelService modelService;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		//��ȡ��ѯ����
		lotProcedureQueryFormField = form.getFieldByControlId(FIELD_LOT_PROCEDURE_QUERY, QueryFormField.class);
		lotIdField = lotProcedureQueryFormField.getQueryForm().getFieldByControlId(FIELD_LOT_PROCEDURE_LOT_ID, TextField.class);
		
		//��ȡ�������̱��
		lotProcedureStepListTableField = form.getFieldByControlId(FIELD_LOT_PROCEDURE_STEP_LIST, ListTableManagerField.class);
		
		//�½���ť�¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);
			
		//�༭��ť�¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_EDIT), this::editorAdapter);		
		
		//���ť�¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ACTIVE), this::activeAdapter);		
		
		//ȡ�����ť�¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_INACTIVE), this::inActiveAdapter);		
				
		//�رհ�ť�¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_CLOSE), this::closeAdapter);		
						
		//ɾ���¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);		
		
		//RunCard��ѡ���¼�
		subscribeAndExecute(eventBroker, lotProcedureQueryFormField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::lotProcedureSelectAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_VIEW_PROCEDURE), this::viewProcedureAdapter);			
		
		lotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				// �س��¼�
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					String lotId = ((Text) event.widget).getText().trim();
					if (!StringUtil.isEmpty(lotId)) {
						lotProcedureQueryFormField.getQueryForm().getQueryButton().doButtonClicked();
						lotIdField.getTextControl().selectAll();
    				}
				}
			}
		});
		init();
	}
	
	private void init() {	
		itemEdit = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_EDIT);
		itemActive = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_ACTIVE);
		itemInActive = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_INACTIVE);
		itemClose = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_CLOSE);
		itemDelete = (ToolItem) form.getButtonByControl(null, ADButtonDefault.BUTTON_NAME_DELETE);
		
		itemEdit.setEnabled(false);
		itemActive.setEnabled(false);
		itemInActive.setEnabled(false);
		itemClose.setEnabled(false);
		itemDelete.setEnabled(false);
	}
	
	public void statusChanged(String state) {
		if (LotProcedureChange.STATE_CREATED.equals(state)) {
			itemEdit.setEnabled(true);
			itemActive.setEnabled(true);
			itemInActive.setEnabled(false);
			itemClose.setEnabled(false);
			itemDelete.setEnabled(true);
		} else if (LotProcedureChange.STATE_APPROVED.equals(state)) {
			itemEdit.setEnabled(false);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(true);
			itemClose.setEnabled(true);
			itemDelete.setEnabled(false);
		} else if (LotProcedureChange.STATE_COMPLETED.equals(state) 
				|| LotProcedureChange.STATE_CLOSED.equals(state)) {
			itemEdit.setEnabled(false);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(false);
			itemClose.setEnabled(false);
			itemDelete.setEnabled(false);
		} else if (LotProcedureChange.STATE_DELETED.equals(state)) {
			itemEdit.setEnabled(false);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(false);
			itemClose.setEnabled(false);
			itemDelete.setEnabled(false);
		} else {
			itemEdit.setEnabled(false);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(false);
			itemClose.setEnabled(false);
			itemDelete.setEnabled(false);
		}
	}
	
	protected void lotProcedureSelectAdapter(Object object) {
		try {		
			LotProcedureChange selected = (LotProcedureChange) lotProcedureQueryFormField.getSelectedObject();
			if (selected == null) {                                                 
			    return;                                                                         
			}
			
			statusChanged(selected.getState());
			
			LotProcedureManager lotProcedureManager = Framework.getService(LotProcedureManager.class);
			List<LotProcedureChangeStep> lotProcedureChangeSteps = lotProcedureManager.getLotProcedureChangeSteps(selected.getObjectRrn());
			
			lotProcedureStepListTableField.getListTableManager().setInput(lotProcedureChangeSteps);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void newAdapter(Object object) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADAuthority> authority = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + AUTHORITY_NAME + "'", "");
			if (authority.size() != 1) {
				return;
			}
			authority.get(0).setAttribute1("New");
			OpenEditorCommand.open(authority.get(0), partService, modelService);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void editorAdapter(Object object) {
		try {
			LotProcedureChange selected = (LotProcedureChange) lotProcedureQueryFormField.getSelectedObject();
			if (selected == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			if (!LotProcedureChange.STATE_CREATED.equals(selected.getState())) {
				UI.showError(Message.getString("wip-005013: wip.lot_procedure_change_state_not_allow"));
				return;
			}
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADAuthority> authority = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + AUTHORITY_NAME + "'", "");
			if (authority.size() != 1) {
				return;
			}
			authority.get(0).setAttribute1(selected.getControlId());
			OpenEditorCommand.open(authority.get(0), partService, modelService);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void activeAdapter(Object object) {                                                   
		try {  
			Event event = (Event) object;
			SessionContext sc = Env.getSessionContext();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				sc.setUserName((String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1));
			}
			
			LotProcedureChange selected = (LotProcedureChange) lotProcedureQueryFormField.getSelectedObject();
			if (selected == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			ADManager adManager = Framework.getService(ADManager.class);
			selected = (LotProcedureChange) adManager.getEntity(selected);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), selected.getLotId(), true);
			
			LotProcedureManager lotProcedureManager = Framework.getService(LotProcedureManager.class);
			
			String stepName = null;
			List<LotProcedureChangeStep> lotProcedureChangeSteps = (List<LotProcedureChangeStep>) lotProcedureStepListTableField.getListTableManager().getInput();
			Optional<LotProcedureChangeStep> optional  = lotProcedureChangeSteps.stream()
			.filter(changeStep -> LotProcedureChangeStep.ACTION_TYPE_REMOVESTEP.equals(changeStep.getActionType()) && lot.getStepName().equals(changeStep.getStepName())).findFirst();
			
			if (optional.isEmpty()) {
				stepName = lot.getStepName();
				if (lotProcedureManager.isEqualLotProcedureStatePath(lot, selected.getProcedureStatePath())) {
					if (!UI.showConfirm(String.format(Message.getString("wip.lot_procedure_change_effective_immediately"), stepName))) {
						return;
					}
				}
			} else {
				List<Node> nodes = getLotProceduteChangeNodes();
				SelectTargetStepDialog dialog = new SelectTargetStepDialog(FORM_VIEW_PROCEDURE, null, eventBroker, nodes);
				if (Dialog.OK == dialog.open()) {
					stepName = dialog.getImmediateStepStateName();
				}
			}
			
			if (!StringUtil.isEmpty(stepName)) {
				selected = lotProcedureManager.approveLotProcedureChange(selected, true, stepName, sc);
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonActiveSuccess()));
			}
			
			lotProcedureQueryFormField.refresh();			
			statusChanged(selected.getState());
		} catch (Exception e) {                                                                     
			ExceptionHandlerManager.asyncHandleException(e);                                        
			return;                                                                                 
		}                                                                                           
	}  
	
	protected void inActiveAdapter(Object object) {                                                   
		try { 
			Event event = (Event) object;
			SessionContext sc = Env.getSessionContext();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				sc.setUserName((String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1));
			}
			
			LotProcedureChange selected = (LotProcedureChange) lotProcedureQueryFormField.getSelectedObject();
			if (selected == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
					
			LotProcedureManager lotProcedureManager = Framework.getService(LotProcedureManager.class);
			selected = lotProcedureManager.unApproveLotProcedureChange(selected, true, sc);
					
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));		
			lotProcedureQueryFormField.refresh();
			statusChanged(selected.getState());
		} catch (Exception e) {                                                                     
			ExceptionHandlerManager.asyncHandleException(e);                                        
			return;                                                                                 
		}                                                                                           
	}
	
	protected void closeAdapter(Object object) {                                                   
		try {                                                                                       
			LotProcedureChange selected = (LotProcedureChange) lotProcedureQueryFormField.getSelectedObject();
			if (selected == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
			boolean confirmClose = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmClose())); 
			if (confirmClose) {             
				LotProcedureManager lotProcedureManager = Framework.getService(LotProcedureManager.class);
				lotProcedureManager.closeLotProcedureChange(selected, Env.getSessionContext());
				
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonCloseSuccessed()));
				lotProcedureQueryFormField.refresh();
				statusChanged(selected.getState());
			}
		} catch (Exception e) {                                                                     
			ExceptionHandlerManager.asyncHandleException(e);                                        
			return;                                                                                 
		}                                                                                           
	}                 
	
	protected void deleteAdapter(Object object) {
		try {
			LotProcedureChange selected = (LotProcedureChange) lotProcedureQueryFormField.getSelectedObject();
			if (selected == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			                                                                                  
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete())); 
			if (confirmDelete) {                                                                
				LotProcedureManager lotProcedureManager = Framework.getService(LotProcedureManager.class);
				lotProcedureManager.deleteLotProcedureChange(selected, Env.getSessionContext());
				lotProcedureQueryFormField.refresh();
				statusChanged(selected.getState());
			}                                                                                   
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void viewProcedureAdapter(Object object) {
		try {
			List<Node> nodes = getLotProceduteChangeNodes();
			if(nodes != null) {
				LotProcedureViewDialog dialog = new LotProcedureViewDialog(FORM_VIEW_PROCEDURE, null, eventBroker, nodes);
				dialog.open();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
		
	private List<Node> getLotProceduteChangeNodes(){
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			
			LotProcedureChange lotProcedureChange = (LotProcedureChange) lotProcedureQueryFormField.getSelectedObject();
			if (lotProcedureChange == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return null;
			} 
			
			ADManager adManager = Framework.getService(ADManager.class);
			lotProcedureChange = (LotProcedureChange) adManager.getEntity(lotProcedureChange);
			
			Lot currentLot = lotManager.getLotByLotId(Env.getOrgRrn(), lotProcedureChange.getLotId(), true);

//			//��ȡ����������StepState
			List<Node> fromStepStates = Lists.newArrayList();
			List<Node> nodeList = Lists.newArrayList();
			ProcessDefinition fromProcedure = prdManager.getLotProcedure(currentLot.getObjectRrn(), lotProcedureChange.getProcedureStatePath(), LotProcedure.TYPE_CHANGE, true);
			if(fromProcedure == null) {
				fromProcedure = new Procedure();
				fromProcedure.setOrgRrn(Env.getOrgRrn());
				fromProcedure.setName(lotProcedureChange.getProcedureName());
				fromProcedure.setVersion(lotProcedureChange.getProcedureVersion());
				fromProcedure = (Procedure)prdManager.getSimpleProcessDefinition(fromProcedure, false);
				
				PrdQueryAction action = PrdQueryAction.newIntance();
				action.setCopyNode(true);
				nodeList = prdManager.getProcessDefinitionChildern(fromProcedure, action);
			} else {
				nodeList = fromProcedure.getNodes();
			}
			for (Node node : nodeList) {
				if (node instanceof StepState) {
					fromStepStates.add(node);
				}
			}
			
			LotProcedure lotProcedure = new LotProcedure();
			lotProcedure.setOrgRrn(Env.getOrgRrn());
			lotProcedure.setLotRrn(currentLot.getObjectRrn());
			lotProcedure.setReplacedProcedureState(lotProcedureChange.getProcedureName());
			lotProcedure.setReplacedProcedureStatePath(lotProcedureChange.getProcedureStatePath());

			List<LotProcedureChangeStep> lotProcedureChangeSteps = lotProcedureChange.getLotProcedureChangeSteps();

			Map<String, List<LotProcedureChangeStep>> addLotProcedureChangeSteps = lotProcedureChangeSteps.stream()
					.filter(p -> LotProcedureChangeStep.ACTION_TYPE_ADDSTEP.equals(p.getActionType()))
					.collect(Collectors.groupingBy(LotProcedureChangeStep::getActionStepState));
			List<LotProcedureChangeStep> removeLotProcedureChangeSteps = lotProcedureChangeSteps.stream()
					.filter(p -> LotProcedureChangeStep.ACTION_TYPE_REMOVESTEP.equals(p.getActionType()))
					.collect(Collectors.toList());

			Map<String, List<Step>> addSteps = Maps.newHashMap();
			for (String addStepState : addLotProcedureChangeSteps.keySet()) {
				List<Step> steps = Lists.newArrayList();
				for (LotProcedureChangeStep addLotProcedureChangeStep : addLotProcedureChangeSteps.get(addStepState)) {
					Step step = new Step();
					step.setOrgRrn(Env.getOrgRrn());
					step.setName(addLotProcedureChangeStep.getStepName());
					step.setVersion(addLotProcedureChangeStep.getStepVersion());
					step = (Step) prdManager.getSimpleProcessDefinition(step, false);
					steps.add(step);
				}
				addSteps.put(addStepState, steps);
			}

			List<StepState> removeSteps = Lists.newArrayList();
			for (LotProcedureChangeStep removeStepState : removeLotProcedureChangeSteps) {
				Optional<Node> optNode = fromStepStates.stream()
						.filter(p -> p.getName().equals(removeStepState.getActionStepState())).findFirst();
				if(optNode.isPresent()) {
					removeSteps.add((StepState) optNode.get());
				}
			}

			lotProcedure = prdManager.createLotProcedureFrom(lotProcedure, fromProcedure, addSteps, removeSteps, false);

			List<Node> nodes = lotProcedure.getNodes();

//			nodes = ProcessDefinition.sortNodes(nodes, false, Maps.newHashMap());

			nodes = nodes.stream().filter(node -> node instanceof StepState || node instanceof ReworkState)
					.collect(Collectors.toList());

			return nodes;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
}
