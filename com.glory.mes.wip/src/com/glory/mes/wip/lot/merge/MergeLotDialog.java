package com.glory.mes.wip.lot.merge;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.rule.merge.BasicMergeRule;

public class MergeLotDialog extends BaseTitleDialog {
	private static final Logger logger = Logger.getLogger(MergeLotDialog.class);
	
	// Minimum dialog width (in dialog units)
	private static final int MIN_DIALOG_WIDTH = 500;

	// Minimum dialog height (in dialog units)
	private static final int MIN_DIALOG_HEIGHT = 400;
		
	protected IManagedForm form;
	protected Lot lot;
	protected List<Lot> searchedLots, selectedLots, existMergedLots;

	private static final String TABLE_MERGE = "WIPLotMerge";
	protected ListTableManager tableManager;
	protected String getType;
	
	public MergeLotDialog(Shell parent) {
		super(parent);
		this.getType = BasicMergeRule.GET_SOURCE;
	}

	public MergeLotDialog(Shell parent, IManagedForm form, Lot lot) {
		this(parent);
		this.form = form;
		this.lot = lot;
	}
	
	public MergeLotDialog(Shell parent, IManagedForm form, Lot lot, String getType) {
		this(parent, form, lot);
		this.form = form;
		this.lot = lot;
		this.getType = getType;
	}

	@Override
	protected Control buildView(Composite parent) {
		FormToolkit toolkit = null;
		if (form == null) {
			toolkit = new FormToolkit(parent.getDisplay());
		} else {
			toolkit = form.getToolkit();
		}
		
		setTitleImage(SWTResourceCache.getImage("search-dialog"));
		setTitle(Message.getString("wip.lot_merge"));
		setMessage(Message.getString("wip.please_select_lot_merge"));
	
		Composite content = toolkit.createComposite(parent);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setLayout(new GridLayout(1, false));

		Composite tableContainer = toolkit.createComposite(content, SWT.NULL);
		tableContainer.setLayout(new GridLayout());
		tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createTableViewer(tableContainer, toolkit);
		searchAdapter();
		tableManager.setInput(searchedLots);
		return parent;
	}

	protected void searchAdapter() {
		if (lot != null) {
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lt = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
				searchedLots = lotManager.getCanMergeLots(lt, getType);
				//  �����߱��������
				if(Env.isUseLine()){//��������߱���������ι���,���������Ŀ��û���߱�����򲻽���ѭ��
					for(Lot sLot : searchedLots){
						try {//�����߱����»�ȡ���Σ�������߱�ƥ���쳣��Ӽ�����ɾ��
							sLot = LotProviderEntry.getLotByLine(sLot.getLotId());
						} catch (Exception e) {
							searchedLots.remove(sLot);
						}
					}
				}
				
			} catch (Exception e) {
				logger.error(e);
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		
			String ld = lot.getLotId();
			Lot removeLot = null;
			
			if (searchedLots.size() != 0) {
				for (Lot l : searchedLots) {
					if (ld.equals(l.getLotId())) {
						removeLot = l;
					}
				}
				if (removeLot != null) {
					searchedLots.remove(removeLot);
				}
				
				if (existMergedLots != null && existMergedLots.size() > 0) {
					searchedLots.removeAll(existMergedLots);
				}
			}
		}
	}

	@Override
	protected void buttonPressed(int buttonId) {
		selectedLots = new ArrayList<Lot>();
		if (buttonId == IDialogConstants.OK_ID) {
			List<Object> os = tableManager.getCheckedObject();
			if (os.size() > 0) {
				for (Object o : os) {
					Lot selectedLot = (Lot) o;
					selectedLots.add(selectedLot);
				}
			}
		}
		super.buttonPressed(buttonId);
	}

	public void setExistMergedLots(List<Lot> existMergedLots) {
		this.existMergedLots = existMergedLots;
	}

	public List<Lot> getSelectionLots() {
		if (selectedLots == null)
			return null;
		return selectedLots;
	}

	protected void createTableViewer(Composite parent, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_MERGE);
			tableManager = new ListTableManager(table, true);
			tableManager.newViewer(parent);				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}


}
