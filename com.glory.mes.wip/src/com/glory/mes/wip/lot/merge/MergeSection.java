package com.glory.mes.wip.lot.merge;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class MergeSection extends LotSection {
	private static final Logger logger = Logger.getLogger(MergeSection.class);
	
	public static final String KEY_MERGE = "merge";
	
	protected Text text;
	protected AuthorityToolItem itemMerge;
	protected MergeForm mergeForm;

	public MergeSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.merge_section"));
		initAdObject();
	}

	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemMerge(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemMerge(ToolBar tBar) {
		itemMerge = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey()+ "." + KEY_MERGE);
		itemMerge.setText(Message.getString("wip.merge"));
		itemMerge.setImage(SWTResourceCache.getImage("merge-lot"));
		itemMerge.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				mergeAdapter(event);
			}
		});
	}

	protected void mergeAdapter(SelectionEvent event) {
		try {
			Lot lot = (Lot)getAdObject();
			if(lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.input_lot_first"));
			} else {
				List<Lot> mergedLots = mergeForm.getMergedLots();
				if(mergedLots == null || mergedLots.size() == 0) {
					UI.showError(Message.getString("wip.merge_nolot"));
					return;
				}
				
				TextField fieldComment = (TextField)mergeForm.getFieldComment();
				String comment = fieldComment.getValue() + "";
//				if (StringUtil.isEmpty(comment) ) {
//					UI.showError(Message.getString("wip.merge_comment_is_null"));
//					return;
//				}
				LotAction lotAction = new LotAction();
                lotAction.setActionComment(comment);
                
				try {
					LotManager lotManager = Framework.getService(LotManager.class);
					lot.setOperator1(Env.getUserName());
					lotManager.mergeLot(lot, mergedLots, lotAction, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.merge_success"));
					refreshAdapter();
				}catch(Exception e) {
					logger.error("Merge Lot Error at MergeSection mergeAdapter() : " + e);
					ExceptionHandlerManager.asyncHandleException(e);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		mergeForm.setLot((Lot) adObject);
	//	statusChanged(((Lot) getAdObject()).getState());
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		mergeForm = new MergeForm(composite, SWT.NONE, tab, form);
		return mergeForm;
	}
	
	@Override
	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus) || 
				LotStateMachine.STATE_FIN.equalsIgnoreCase(newStatus)) {
			itemMerge.setEnabled(true);
		} else {
			itemMerge.setEnabled(false);
		}
	}

}
