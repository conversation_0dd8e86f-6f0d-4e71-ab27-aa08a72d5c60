package com.glory.mes.wip.lot.merge;

import javax.annotation.PostConstruct;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.Message;
import com.glory.mes.wip.lot.LotEditor;

public class MergeEditor extends LotEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.merge.MergeEditor";
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		super.postConstruct(parent);
		
		ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);
		createSection(adTable, adEditor);
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);
		//form.setLayoutData(new GridData(GridData.FILL_BOTH));
		ManagedForm mform = new ManagedForm(toolkit, form);
		
		Composite body = form.getBody();
		configureBody(body);
		section.createContents(mform, body);
		
		mPart.setLabel(Message.getString("wip.merge_editor"));
	}
	
	protected void createSection(ADTable adTable, ADEditor adEditor) {
		section = new MergeSection(adTable);
	}
	
}
