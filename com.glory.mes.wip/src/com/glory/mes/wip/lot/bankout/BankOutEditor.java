package com.glory.mes.wip.lot.bankout;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.wip.action.LotBankAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class BankOutEditor extends GlcEditor {

	private static final Logger logger = Logger.getLogger(BankOutEditor.class);

    public static final String  CONTRIBUTION_URL        = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.bankout.BankOutEditor";
	
    public static final String  BUTTON_NAME_BANKOUT  = "BankOut";
    public static final String  BUTTON_NAME_MOVEBANK = "MoveBank";
	
    public static final String  CONTROL_QUERY_FORM   = "bankLotManageForm";
    public static final String  CONTROL_ACTION_FORM  = "bankActionForm";
	public static final String CONTROL_TECN_INFO = "tecnInfo";
	public static final String CONTROL_ECN_TYPE = "ecnType";
	
    public QueryFormField       bankLotqueryFormField;
    public EntityFormField      bankActionEntityFormField;
    public RefTableField        warehouseIdField;
    public RefTableField            locatorIdField;
    public TextField            lotCommentField;
	
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
        bankLotqueryFormField = form.getFieldByControlId(CONTROL_QUERY_FORM, QueryFormField.class);
		
        bankActionEntityFormField = form.getFieldByControlId(CONTROL_ACTION_FORM, EntityFormField.class);

        warehouseIdField = bankActionEntityFormField.getFieldByControlId("warehouseId", RefTableField.class);

        locatorIdField = bankActionEntityFormField.getFieldByControlId("locatorId", RefTableField.class);

        lotCommentField = bankActionEntityFormField.getFieldByControlId("lotComment", TextField.class);

        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_BANKOUT),
                            this::bankOutAdapter);
		
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_MOVEBANK),
                            this::moveBankAdapter);
				
	}

	
    protected void bankOutAdapter(Object object) {
		try {	
            List<Object> selectLots = bankLotqueryFormField.getQueryForm().getCheckedObject();

            if (CollectionUtils.isEmpty(selectLots)) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
            
            String lotComment = (String) lotCommentField.getValue();

            LotBankAction lotBankAction = new LotBankAction();
            lotBankAction.setActionComment(lotComment);

            List<Lot> lots = selectLots.stream().map(selectLot -> {
                Lot lot = (Lot) selectLot;
                lot.setWarehouseId(null);
                lot.setLocatorId(null);
                return lot;
            }).collect(Collectors.toList());

            LotManager lotManager = Framework.getService(LotManager.class);
            lotManager.bankOut(lots, lotBankAction, Env.getSessionContext());
            UI.showInfo(Message.getString("common.cancelBank_successed"));// ������ʾ��

            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
    protected void moveBankAdapter(Object object) {
		try {		
            List<Object> selectLots = bankLotqueryFormField.getQueryForm().getCheckedObject();
            if (CollectionUtils.isEmpty(selectLots)) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
            String warehouseId = (String) warehouseIdField.getValue();
            String locatorId = (String) locatorIdField.getValue();
            String lotComment = (String) lotCommentField.getValue();
            
            LotBankAction lotBankAction = new LotBankAction();
            lotBankAction.setActionComment(lotComment);
            lotBankAction.setWarehouseId(warehouseId);
            lotBankAction.setLocatorId(locatorId);

            List<Lot> lots = selectLots.stream().map(selectLot -> {
                Lot lot = (Lot) selectLot;
                lot.setWarehouseId(warehouseId);
                lot.setLocatorId(locatorId);
                return lot;
            }).collect(Collectors.toList());

            ADManager adManager = Framework.getService(ADManager.class);
            // �жϲֿ��¿�λ�Ƿ����
            List<Storage> storagelist = adManager.getEntityList(Env.getOrgRrn(), Storage.class, Env.getMaxResult(),
                                                                " warehouseId = '" + lotBankAction.getWarehouseId() + "' AND name = '"
                                                                                                                    + lotBankAction.getLocatorId()
                                                                                                                    + "'",
                                                                null);

            // ������ҵ����Ա�¼���λID
            if (!StringUtil.isEmpty(locatorId) && CollectionUtils.isEmpty(storagelist)) {
                UI.showInfo(Message.getString("mm.storage_no_exist"));// ������ʾ��
                return;
            }

            LotManager lotManager = Framework.getService(LotManager.class);
            lotManager.moveBank(lots, lotBankAction, Env.getSessionContext());
            UI.showInfo(Message.getString("common.bank_successed"));// ������ʾ��

            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}

    protected void refreshAdapter(Object object) {
        try {
            // ��ѯ�б�ˢ��
            bankLotqueryFormField.refresh();

            // �������ֵ
            warehouseIdField.setValue(null);
            locatorIdField.setValue(null);
            lotCommentField.setText(null);

        } catch (Exception e1) {
            ExceptionHandlerManager.asyncHandleException(e1);
            return;
        }
    }
	
}
