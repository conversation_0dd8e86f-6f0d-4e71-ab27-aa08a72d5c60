package com.glory.mes.wip.lot.production.offline;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.lot.LotSection;

public class LotProductionInputMediator extends LotMediator {
	private static final Logger logger = Logger.getLogger(LotProductionInputMediator.class);
	
	public static final String REFRESH = "Refresh";
	
	protected TreeViewer viewer;

	
	public LotProductionInputMediator(LotSection lotSection) {
		super(lotSection);
	}
	
	@Override
	protected void selectionChangedAdapter(SelectionEvent e) {
		TreeItem[] items = getTreeViewer().getTree().getSelection();		
		if(getTreeViewer() != null) {
			resetChangedContent(items);
		}
	}
	
	private void resetChangedContent(TreeItem[] items) {
		if(items != null && items.length > 0) {
			TreeItem item = items[0];
			if(item.getData() instanceof StepState) {				
				StepState ss = ((StepState)item.getData());
				((LotProductionInputSection)lotSection).setState(ss);	
				((LotProductionInputSection)lotSection).statusChanged("StepState");
			}
			lotSection.initAdObject();
		}
	}

	protected TreeViewer getTreeViewer() {
		if(viewer == null) {
			if(treeField != null) {
				viewer = treeField.getTreeViwer();
				return viewer;
			} 
		} else {
			return viewer;
		}
		return null;
	}
}
