package com.glory.mes.wip.lot.production.offline;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADQuery;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.TableEditorField;
import com.glory.framework.base.ui.nattable.editor.row.ListRowEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotProduction;

public class LotProductionInputAddFrom extends EntityForm {

	public static final Logger logger = Logger.getLogger(LotProductionInputAddFrom.class);
	public static final String TABLE_NAME_PARAMETER = "WIPLotProduction";
	
	private ADManager adManager;
	public TableEditorField editorField;
	protected ADTable adTable;
	protected StepState currentStepState;

	
	public LotProductionInputAddFrom(Composite parent, int style, Object object, List<ADField> adfields, int gridY, IMessageManager mmng,ADTable adTable) {
		super(parent, style, object, adfields, gridY, mmng);
		
	}
	
	public LotProductionInputAddFrom(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}
	
	@Override
	public void addFields() {
		// TODO Auto-generated method stub
		super.addFields();
		try {
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_PARAMETER);
			ListRowEditorTableManager tableManager = new ListRowEditorTableManager(adTable, true);
			editorField = new TableEditorField("", tableManager);
			ADField editorAdField = new ADField();
			editorAdField.setIsSameline(true);
			editorField.setADField(editorAdField);
			editorField.setHeight(20);
			addField("", editorField);
		} catch (Exception e) {
			logger.error("ShipForm : Init listItem", e);
		}
	}
	
	@Override
	public void loadFromObject() {
		super.loadFromObject();
		try {
			String where = null;
			if (object != null && currentStepState != null) {
				where = "lotId = '"+((Lot)object).getLotId()+"' and stepName = '"+currentStepState.getName()+"' and createdBy = '"+Env.getUserName()+"'";
			}else {
				where = "1 != 1";
			}
			ADManager adManager = Framework.getService(ADManager.class);
			List<LotProduction> value = adManager.getEntityList(Env.getOrgRrn(), LotProduction.class, ADQuery.maxCount, where, "");	
			List<LotProduction>  base = (List<LotProduction>) value;
			editorField.setValue(base);
			editorField.refresh();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public List<Object> getEditorObject() {
		return editorField.getEditorObject();
	}
	
	public List<Object> getDeleteObject() {
		return editorField.getDeleteObject();
	}
	
	public void setCurrentStepState(StepState currentStepState) {
		this.currentStepState = currentStepState;
	}
	
	public StepState getCurrentStepState() {
		return currentStepState;
	}

}
