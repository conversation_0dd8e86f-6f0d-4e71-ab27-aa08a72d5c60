package com.glory.mes.wip.lot.batchrelease;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADQuery;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.ADQueryEntityListSection;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.framework.security.model.ADUserGroup;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.Activator;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.framework.core.exception.ExceptionBundle;

public class BatchReleaseSection extends ADQueryEntityListSection {

	public static final String KEY_BATCHRELEASE = "batchRelease";
	
	public CheckBoxFixEditorTableManager manager;
	protected AuthorityToolItem itemBatchRelease;
	protected LotAction lotAction = new LotAction();

	protected EntityForm batchReleaseForm;
	protected List<String> currentUserGroups;
	private Boolean isCaseSensitive;
	private List<Object> adList = new ArrayList<Object>();;
	private String queryName;

	public BatchReleaseSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public BatchReleaseSection(String queryName, ListTableManager tableManager) {
		super(queryName, tableManager);
		this.queryName = queryName;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemBatchRelease(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemBatchRelease(ToolBar tBar) {
		itemBatchRelease = new AuthorityToolItem(tBar, SWT.PUSH , getADTable().getAuthorityKey() + "." + KEY_BATCHRELEASE);
		itemBatchRelease.setAuthEventAdaptor(this::batchReleaseAdapter);
		itemBatchRelease.setText(Message.getString("wip.batch_release_dorelease"));
		itemBatchRelease.setImage(SWTResourceCache.getImage("release-lot"));
//		itemBatchRelease.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				batchReleaseAdapter(event);
//			}
//		});
	}

	public void createContents(final IManagedForm form, Composite parent, int sectionStyle) {
		super.createContents(form, parent, sectionStyle);
		Composite client = (Composite) section.getClient();
		
		batchReleaseForm = new EntityForm(client, SWT.NONE, new LotAction(), getADTable1(), form.getMessageManager());
		batchReleaseForm.setLayout(new GridLayout());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.heightHint = 100;
		batchReleaseForm.setLayoutData(gd);
		 
		TextField textField = (TextField) getQueryForm().getFields().get("lotId");
		Text text = textField.getTextControl();
		text.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					Lot lot = managerInputLot(lotId);
					if (lot == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
					}
				}
			}
		});
		text.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});
	}
	
	private void intUserGroup() {
		try {
			ADUser User = new ADUser();
			User.setObjectRrn(Env.getUserRrn());
			User.setOrgRrn(Env.getOrgRrn());
			ADUser currUser = (ADUser) adManager.getEntity(User);
			List<ADUserGroup> userGroups = currUser.getUserGroups();
			userGroups = userGroups.stream().filter(ls -> ls.getOrgRrn().equals(Env.getOrgRrn()))
					.collect(Collectors.toList());
			currentUserGroups = new ArrayList<String>();
			for (int i = 0; i < userGroups.size(); i++) {
				currentUserGroups.add(userGroups.get(i).getName());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	protected void batchReleaseAdapter(SelectionEvent event) {
		try {
			if (batchReleaseForm.validate()) {
				managedForm.getMessageManager().removeAllMessages();
				LinkedHashMap<String, IField> fields = batchReleaseForm.getFields();

				for (IField f : fields.values()) {
					if (f.getId().equals("actionCode")) {
						String hCode = (String) f.getValue();
						lotAction.setActionCode(hCode);
						continue;
					}
					if (f.getId().equals("actionReason")) {
						String hReason = (String) f.getValue();
						lotAction.setActionReason(hReason);
						continue;
					}
					if (f.getId().equals("actionComment")) {
						String hComment = (String) f.getValue();
						lotAction.setActionComment(hComment);
						continue;
					}
					if (f.getId().equals("isForbiddenFlag")) {
						boolean isForbiddenFlag = (boolean) f.getValue();
						lotAction.setIsForbiddenFlag(isForbiddenFlag);
						continue;
					}
				}
				List<Object> selectObjs = tableManager.getCheckedObject();
				LotManager lotManager = Framework.getService(LotManager.class);
				List<LotHold> lotHolds = new ArrayList<LotHold>();
				if (selectObjs.size() > 0) {
					for (int i = 0; i < selectObjs.size(); i++) {
						Map<String, Object> selectObjMap = (Map<String, Object>) selectObjs.get(i);
						if (!selectObjMap.containsKey("HOLDRRN")) {
							UI.showError(Message.getString("wip.release_not_null"));
							return;
						}
						List<LotHold> lotHold = adManager.getEntityList(Env.getOrgRrn(), LotHold.class, Integer.MAX_VALUE,
								" objectRrn = " + selectObjMap.get("HOLDRRN"), "");
						lotHolds.addAll(lotHold);
					}
					
					SessionContext sc = Env.getSessionContext();
					if (itemBatchRelease.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
						sc.setUserName((String) itemBatchRelease.getData(LotAction.ACTION_TYPE_OPERATOR));
					}
					
					lotManager.releaseLots(lotHolds, lotAction, sc);
					UI.showError(Message.getString("wip.release_successed"));
					refresh();
				} else {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				}
			} else {
				UI.showWarning(Message.getString("warn.required_entry"));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPBatchReleaseAction");
		List<ADField> adFields = midTable.getFields();
		if (CollectionUtils.isNotEmpty(adFields) && !Activator.isRTDAvailable()) {
			adFields = adFields.stream().filter(f -> !"isForbiddenFlag".equals(f.getName())).collect(Collectors.toList());
			midTable.setFields(adFields);
		}
		return midTable;
	}
	
	@Override
	public Map<String, Object> getParameterMap() {
		intUserGroup();
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("holdOwner", currentUserGroups);
		paramMap.put("orgRrn", Env.getOrgRrn());
		return paramMap;
	}

	public Lot managerInputLot(String lotId) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);	
			String whereClause = null;
			if (StringUtil.isEmpty(getWhereClause())) {
				whereClause = " lotId = '"+lotId+"'";
			}else {
				whereClause = getWhereClause() +"and lotId = '"+lotId+"'";
			}			
			List<ADQuery> querys = this.adManager.getEntityList(0L, ADQuery.class, 1, " name = '" + this.queryName + "'", "");
			if (querys.size() != 0) {
				ADQuery query = (ADQuery)querys.get(0);
				String queryText = query.getQueryText();
				List<Map> currentList = this.adManager.getEntityMapListByQueryText(queryText, getParameterMap(), 0, Env.getMaxResult(), whereClause, "");
				if (currentList != null) {
					if (!adList.containsAll(currentList)) {
						adList.addAll(currentList);
					}	
					tableManager.getInput().clear();
					tableManager.setInput(adList);
				}
			}					
			return lot;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	@Override
	public void refresh() {
		super.refresh();
		lotAction = new LotAction();
		batchReleaseForm.setObject(lotAction);
		batchReleaseForm.loadFromObject();
	}
	
	@Override
	protected void queryAdapter() {
		adList.clear();
		super.queryAdapter();
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}

}
