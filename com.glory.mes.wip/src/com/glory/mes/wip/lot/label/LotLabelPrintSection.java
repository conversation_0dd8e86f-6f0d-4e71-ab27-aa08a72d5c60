package com.glory.mes.wip.lot.label;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.label.client.LabelManager;
import com.glory.common.label.model.Label;
import com.glory.framework.base.application.event.AppEvent;
import com.glory.framework.base.application.event.AppEventManager;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotLabelPrintSection extends QueryEntityListSection implements IRefresh {
	
	String whereClause;
	private ToolItem itemPrint;
	private ToolItem itemPreview;
	
	public LotLabelPrintSection(ListTableManager tableManager) {
		super(tableManager);
	}
		
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemPreview(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemPrint(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTest(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	private void testAdapter() {
		try {
			Object object = tableManager.getSelectedObject();
			if (object == null) {
				UI.showInfo(Message.getString("wip_not_select_lot"));
				return;
			}
			Lot selectLot = (Lot) object;
			
			LabelManager labelManager = Framework.getService(LabelManager.class);
			Label label = labelManager.getLabelByContext(selectLot, null, Env.getSessionContext());
			
			AppEvent appEvent = new AppEvent();
			appEvent.setEventParam1("TEST"); //labelType
			appEvent.setEventParam2("LotList"); //objectType
			appEvent.setEventParam3(selectLot); //object
			appEvent.setEventParam4(label);
			AppEventManager.postEvent(AppEvent.EVENT_ID_PRINTLABEL, appEvent);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}       
	}
	
	protected void previewAdapter() {
		try {
			Object object = tableManager.getSelectedObject();
			if (object == null) {
				UI.showInfo(Message.getString("wip_not_select_lot"));
				return;
			}
			Lot selectLot = (Lot) object;
					
			AppEvent appEvent = new AppEvent();
			appEvent.setEventParam1("PREVIEW"); //actionType
			appEvent.setEventParam2("LotList"); //objectType
			appEvent.setEventParam3(selectLot); //object
			AppEventManager.postEvent(AppEvent.EVENT_ID_PRINTLABEL, appEvent);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}       
	}
	
	protected void printAdapter() {
		try {
			List<Object> checkedObjects = tableManager.getCheckedObject();
			if (checkedObjects.isEmpty()) {
				UI.showInfo(Message.getString("wip_not_select_lot"));
				return;
			}
			
			LabelManager labelManager = Framework.getService(LabelManager.class);
			Label label = labelManager.getLabelByContext(checkedObjects.get(0), null, Env.getSessionContext());
			
			for (Object obj : checkedObjects) {
				Lot selectLot = (Lot) obj;
				AppEvent appEvent = new AppEvent();
				appEvent.setEventParam1("PRINT"); //actionType
				appEvent.setEventParam2("LotList"); //objectType
				appEvent.setEventParam3(selectLot); //object
				appEvent.setEventParam4(label);
				AppEventManager.postEvent(AppEvent.EVENT_ID_PRINTLABEL, appEvent);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}       
	}
	
	protected void createToolItemPrint(ToolBar tBar) {
        itemPrint = new ToolItem(tBar, SWT.PUSH);
        itemPrint.setText(Message.getString(ExceptionBundle.bundle.CommonPrint()));
        itemPrint.setImage(SWTResourceCache.getImage("print"));
        itemPrint.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                printAdapter();
            }
        });
    }
	
	protected void createToolItemPreview(ToolBar tBar) {
		itemPreview = new ToolItem(tBar, SWT.PUSH);
		itemPreview.setText(Message.getString(ExceptionBundle.bundle.CommonPreview()));
		itemPreview.setImage(SWTResourceCache.getImage("preview"));
		itemPreview.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				previewAdapter();
			}
		});
	}
	
	protected void createToolItemTest(ToolBar tBar) {
        itemPrint = new ToolItem(tBar, SWT.PUSH);
        itemPrint.setText(Message.getString("label.print_test"));
        itemPrint.setImage(SWTResourceCache.getImage("print"));
        itemPrint.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                testAdapter();
            }
        });
    }
	
	@Override
	public void createContents(IManagedForm form, Composite parent,
			int sectionStyle) {
		super.createContents(form, parent, sectionStyle);
	}
}