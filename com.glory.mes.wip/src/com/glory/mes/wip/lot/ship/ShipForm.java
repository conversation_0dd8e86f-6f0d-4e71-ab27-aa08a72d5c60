package com.glory.mes.wip.lot.ship;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class ShipForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(ShipForm.class);
	
	private IField fieldShipLot;
	private static final String SHIPLOT = "ShipLot";
	private static final String SHIPLOT_ID = "shipLot";
	private IField fieldShipCode;

	private static final String SHIPCODE = "ShipCode";
	private static final String SHIPCODE_ID = "shipCode";
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";
	private LotAction lotAction = new LotAction();
	
	private ListTableManager listTableManager; 

	public ShipForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public ShipForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public ShipForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public ShipForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "WIPShip");
			listTableManager = new ListTableManager(adTable, false);	
			listTableManager.setInput(new ArrayList<Lot>());
			fieldShipLot = new ShipLotTableField(SHIPLOT_ID, Message.getString("wip.ship_lot")+ "*", listTableManager);

			fieldShipCode = createUserRefList(SHIPCODE_ID, Message.getString("wip.ship_code") + "*", SHIPCODE, true);
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);
			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldShipLot.setADField(adField);
			fieldComment.setADField(adField);
			addField(SHIPLOT, fieldShipLot);
			addField(SHIPCODE, fieldShipCode);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("ShipForm : Init listItem", e);
		}
	}

	
	public void addLot(Lot lot) {
		List<Lot> lots = (List<Lot>)listTableManager.getInput();
		if (!lots.contains(lot)) {
			if (LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState())) {
				lots.add(lot);	
			} else {
				UI.showError(Message.getString("error.state_not_allow"));
			}
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) 
						&& !f.equals(fieldShipLot)
						&& !f.equals(fieldShipCode)
						&& !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			lotAction.setActionCode((String) fieldShipCode.getValue());
			lotAction.setActionComment((String) fieldComment.getValue());
			return true;
		}
		return false;
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			fieldShipCode.setValue("");
			fieldShipCode.refresh();
			((TextField) fieldComment).setText("");
		} catch (Exception e) {
			logger.error("ShipForm : refresh()", e);
		}
	}
	
	public void clearShipLot(){
		listTableManager.getInput().clear();
	}
	
	public List<Lot> getLots() {
		return (List<Lot>)listTableManager.getInput();
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	@Override
	public boolean validate() {
		boolean validFlag = super.validate();
		if(fieldShipCode.getValue() == null
				|| "".equals(String.valueOf(fieldShipCode.getValue()).trim())){
			validFlag = false;
			mmng.addMessage(SHIPCODE_ID, String.format(Message
					.getString("common.ismandatry"), SHIPCODE_ID), null,
					IMessageProvider.ERROR, fieldShipCode.getControls()[1]);
		}
		return validFlag;
	}
}
class ShipLotTableField extends AbstractField {

	protected int mStyle = SWT.READ_ONLY | SWT.BORDER;
    protected ListTableManager viewer;
    
    public ShipLotTableField(String id, String label, ListTableManager viewer) {
        super(id);
        this.viewer = viewer;
        this.label = label;
    }
    
	public ShipLotTableField(String id, String label, ListTableManager viewer, int style) {
        super(id);
        this.viewer = viewer;
        mStyle = style;
        this.label = label;
    }
    
	@Override
	public void createContent(Composite composite, FormToolkit toolkit) {
		int i = 0;
		String labelStr = getLabel();
        if (labelStr != null) {
        	mControls = new Control[2];
        	Label label = toolkit.createLabel(composite, labelStr);
            mControls[0] = label;
            i = 1;
        } else {
        	mControls = new Control[1];
        }
        
        Composite tableContainer = toolkit.createComposite(composite, SWT.NONE);
        GridData gd = new GridData();
        gd.grabExcessHorizontalSpace = true;
        gd.horizontalAlignment = SWT.FILL;
        gd.heightHint = 150;
		gd.widthHint = 420;
        tableContainer.setLayoutData(gd);
        tableContainer.setLayout(new GridLayout());
        viewer.newViewer(tableContainer);
		viewer.refresh();
		    	
        mControls[i] = tableContainer;
	}

    public Label getLabelControl() {
        Control[] ctrl = getControls();
        if(ctrl.length >  1) {
            return (Label)ctrl[0];
        } else {
            return null;
        }
    }
    
	@Override
	public void refresh() {
		//DO Nothing
	}
	
	public String getFieldType() {
		return "tablelist";
	}
}
