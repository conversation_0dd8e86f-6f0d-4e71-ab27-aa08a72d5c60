package com.glory.mes.wip.lot.changeline;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.lot.hold.HoldForm;

public class ChangeLineForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(HoldForm.class);

	private IField fieldChangeLine;
	private static final String CHANGELINE = "ChangeLine";
	private static final String CHANGELINE_ID = "changeLine";
	
	public ChangeLineForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}
	
	@Override
	public void addFields() {
		super.addFields();
		try {
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "BASLINE");
			ADRefTable refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setKeyField("name");
			refTable.setTextField("name");
			fieldChangeLine = createSearchField(CHANGELINE_ID, Message.getString("wip.changeline"), adTable, refTable,"", SWT.READ_ONLY);
			fieldChangeLine.setADManager(adManager);
			addField(CHANGELINE, fieldChangeLine);
			
		} catch (Exception e) {
			logger.error("HoldLotForm : Init listItem", e);
		}
	}
	
	public String getChangeLine() {
		return fieldChangeLine.getValue().toString();
	}
	
}
