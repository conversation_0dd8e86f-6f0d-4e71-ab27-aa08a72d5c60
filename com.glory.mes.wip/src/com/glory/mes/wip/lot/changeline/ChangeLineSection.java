package com.glory.mes.wip.lot.changeline;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class ChangeLineSection extends LotSection {
	private static final Logger logger = Logger.getLogger(EntitySection.class);

	protected ToolItem itemChangeLine;
	protected ChangeLineForm itemForm;
	
	public ChangeLineSection() {
		super();
	}

	public ChangeLineSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.hold_sectiontitle"));
		initAdObject();
	}

	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChangeLine(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemChangeLine(ToolBar tBar) {
		itemChangeLine = new ToolItem(tBar, SWT.PUSH);
		itemChangeLine.setText(Message.getString("wip.changeline"));
		itemChangeLine.setImage(SWTResourceCache.getImage("change-lot-info"));
		itemChangeLine.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeLineAdapter(event);
			}
		});
	}

	protected void changeLineAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						String lotHold = ((ChangeLineForm) detailForm).getChangeLine();
						LotManager lotManager = Framework.getService(LotManager.class);
						Lot lot = (Lot) this.getAdObject();
						lot.setLineId(lotHold);
						lotManager.saveLot(lot);
					}
					UI.showInfo(Message.getString("wip.hold_successed"));// ������ʾ��
					refresh();
				}
			}
			txtLot.setFocus();
		} catch (Exception e) {
			logger.error("HoldSection : holdAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		itemForm = new ChangeLineForm(composite, SWT.NONE, tab, mmng);
		return itemForm;
	}

	
//	@Override
//	public void statusChanged(String newStatus){
//		if (newStatus != null) {
//			if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus)
//					|| LotStateMachine.STATE_RUN.equalsIgnoreCase(newStatus)
//					|| LotStateMachine.STATE_TRACKOUT.equalsIgnoreCase(newStatus)) {
//				itemChangeLine.setEnabled(true);
//			} else {
//				itemChangeLine.setEnabled(false);
//			}
//		} else {
//			itemChangeLine.setEnabled(false);
//		}
//	}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));				
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
	}
}
