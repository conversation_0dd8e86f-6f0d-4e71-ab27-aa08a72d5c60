package com.glory.mes.wip.lot.mylotlist;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.mes.wip.model.Lot;

public class MyLotListDialog extends GlcBaseDialog {
	private static final Logger logger = Logger.getLogger(MyLotListDialog.class);

	protected QueryFormField myGroupLotListQueryForm;
	private static final String FIELD_LOTLIST= "lotList";
	protected List<Lot> lotlist;

	public MyLotListDialog(String adFormName, String authority, IEventBroker eventBroker, List<Lot> lotlist) {
		super(adFormName, authority, eventBroker);
		this.lotlist = lotlist;
	}

	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		myGroupLotListQueryForm = form.getFieldByControlId(FIELD_LOTLIST, QueryFormField.class);
	}

	@Override
	protected void okPressed() {
		try {
			List<Object> objects = myGroupLotListQueryForm.getQueryForm().getCheckedObject();
			if(objects != null && objects.size() > 0) {
				for(Object object : objects) {
					Lot lot = (Lot)object;
					lotlist.add(lot);
				}
			}
		} catch (Exception e) {
			logger.error("NpwControlInstanceDialog saveInstance error:", e);
			e.printStackTrace();
		}
		close();
	}

}
