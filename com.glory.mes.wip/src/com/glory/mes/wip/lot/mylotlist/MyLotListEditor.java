package com.glory.mes.wip.lot.mylotlist;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMyGroup;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * ��Lot��Group Id �� Owner
 */

public class MyLotListEditor extends GlcEditor {
	
	private static final Logger logger = Logger.getLogger(MyLotListEditor.class);

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.mylotlist.MyLotListEditor";
	
	private static final String FIELD_LOTMAYGROUP= "LotMayGroup";
	private static final String FIELD_LOTINFOR= "LotInfor";
	private static final String FIELD_GROUPID= "groupId";
	private static final String FIELD_MYGROUPLOTTABLE= "MyGroupLotTable";
	
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_ADD = "add";
	private static final String BUTTON_REMOVE = "remove";
	private static final String BUTTON_CLEAR = "clear";
	private static final String BUTTON_SAVE = "save";
	
	private GlcFormField lotInforGlcForm;
	protected QueryFormField lotMayGroupQueryForm;
	protected EntityFormField controlEntityForm;
	protected TextField textField;
	protected ListTableManagerField myGroupLotListTableManager;
	
	public static final String DIALOG_FORM_NAME = "MyGroupLotListDialog";
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		lotInforGlcForm =  form.getFieldByControlId(FIELD_LOTINFOR, GlcFormField.class);
		lotMayGroupQueryForm =  form.getFieldByControlId(FIELD_LOTMAYGROUP, QueryFormField.class);
		
		myGroupLotListTableManager = lotInforGlcForm.getFieldByControlId(FIELD_MYGROUPLOTTABLE, ListTableManagerField.class);
		textField = lotInforGlcForm.getFieldByControlId(FIELD_GROUPID, TextField.class);
		textField.getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				// �س��¼�
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					String lotId = ((Text) event.widget).getText().trim();
					if (!StringUtil.isEmpty(lotId)) {
						//getRunCardByLotId(lotId);
    				}
				}
			}	
		});	
		
		textField.setEnabled(false);
		
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ADD), this::addAdapter);
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
        subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
       
        
      //��ѯ���ѡ���¼�
      	subscribeAndExecute(eventBroker, lotMayGroupQueryForm.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
        subscribeAndExecute(eventBroker, lotMayGroupQueryForm.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
	}
	
	protected void clearAdapter(Object object) {
		try {
			myGroupLotListTableManager.getListTableManager().setInput(Lists.newArrayList());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void removeAdapter(Object object) {
		try {
			Lot lot = (Lot)myGroupLotListTableManager.getListTableManager().getSelectedObject();
			List<Lot> selectlist = (List<Lot>)myGroupLotListTableManager.getListTableManager().getInput();
			if(lot != null && selectlist!=null && selectlist.size() > 0) {
				selectlist.remove(lot);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	
	protected void addAdapter(Object object) {
		try {
			if(textField.getValue() == null || StringUtil.isEmpty(textField.getValue().toString())) {
				UI.showInfo(Message.getString("wip.groupid_is_null"));
				return;
			}
			List<Lot> lotlist = new ArrayList<Lot>();
			MyLotListDialog dialog = new MyLotListDialog(DIALOG_FORM_NAME, null, eventBroker, lotlist);
			if (Dialog.OK == dialog.open()) {
				if(lotlist != null && lotlist.size() > 0) {
					List<Lot> selectlist = (List<Lot>)myGroupLotListTableManager.getListTableManager().getInput();
					List<String> lotIds = new ArrayList<String>();
					if(selectlist != null && selectlist.size() > 0) {
						for(Lot lot: selectlist) {
							if(!lotIds.contains(lot.getLotId())) {
								lotIds.add(lot.getLotId());
							}
						}
					}
					if(lotlist != null && lotlist.size() > 0) {
						for(Lot lot : lotlist) {
							if(!lotIds.contains(lot.getLotId())) {
								selectlist.add(lot);
								lotIds.add(lot.getLotId());
							}
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	
	private void newAdapter(Object obj) {
		refreshAdapter(obj);
	}
	
	private void refreshAdapter(Object obj) {
		textField.setEnabled(true);
		textField.setValue(null);
		textField.refresh();
		myGroupLotListTableManager.getListTableManager().setInput(Lists.newArrayList());
	}
	
	
	private void selectionChanged(Object object) {
		try {
			Event event = (Event) object;
			LotMyGroup lotMyGroup = (LotMyGroup)event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotMyGroup != null && !StringUtil.isEmpty(lotMyGroup.getGroupId())) {
				LotManager lotManager =  Framework.getService(LotManager.class);
				textField.setEnabled(false);
				textField.setValue(lotMyGroup.getGroupId());
				textField.refresh();
				
				List<Lot> lots = lotManager.getLotsByGroupId(Env.getOrgRrn(), lotMyGroup.getGroupId(), false);
				myGroupLotListTableManager.getListTableManager().setInput(lots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void saveAdapter(Object obj) {
		try {
			if(textField.getValue() != null && StringUtil.isEmpty(textField.getValue().toString())) {
				UI.showError(Message.getString("wip.groupid_is_null"));
				return;
			}
			LotManager lotManger = Framework.getService(LotManager.class);
			List<Object> selectlist = (List<Object>)myGroupLotListTableManager.getListTableManager().getInput();
			LotMyGroup lotMyGroup = new LotMyGroup();
			lotMyGroup.setGroupId(textField.getValue().toString());
			if(selectlist != null && selectlist.size() > 0) {
				List<LotMyGroup> lotMyGroups = new ArrayList<LotMyGroup>();
				for(Object object: selectlist) {
					LotMyGroup LotMyGroup = new LotMyGroup((Lot)object, textField.getValue().toString(),Env.getSessionContext());
					lotMyGroups.add(LotMyGroup);
				}
				lotManger.saveMyGroupLots(textField.getValue().toString(), lotMyGroups, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
				queryAdapter(obj);
			}else {//ɾ��GroupID
				lotManger.saveMyGroupLots(textField.getValue().toString(),Lists.newArrayList(), Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
				queryAdapter(obj);
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	private void queryAdapter(Object object) {
		try {
			// ��ѯ��ť��д
			ADManager adManager = Framework.getService(ADManager.class);
			// �õ�Query��ѯ����
			LinkedHashMap<String, IField> fields = lotMayGroupQueryForm.getQueryForm().getFields();
			StringBuffer whereCluase = new StringBuffer(" 1=1 ");
			if (fields != null) {
				for (IField f : fields.values()) {
					Object value = f.getValue();
					if (value != null && !StringUtil.isEmpty(value.toString())) {
						whereCluase.append(" And " + f.getId() + " = '" + f.getValue() + "'");
					}
				}
			}
			whereCluase.append(" And owner = '" + Env.getUserName() + "'");
			List<LotMyGroup> lotMyGroupList = adManager.getEntityList(Env.getOrgRrn(), LotMyGroup.class, Integer.MAX_VALUE, whereCluase.toString(), null);
			lotMayGroupQueryForm.getQueryForm().getTableManager().setInput(lotMyGroupList);
		} catch (Exception e) {
			logger.error("NpwControlInstanceQueryEditor queryAdapter:", e);
		}

	}
	
}
