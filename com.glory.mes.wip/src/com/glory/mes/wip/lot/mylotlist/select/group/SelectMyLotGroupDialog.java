package com.glory.mes.wip.lot.mylotlist.select.group;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMyGroup;

public class SelectMyLotGroupDialog extends GlcBaseDialog {
	private static final Logger logger = Logger.getLogger(SelectMyLotGroupDialog.class);

	protected EntityFormField entityFormField;
	
	protected RefTableField refTableField;
	
	private static final String FIELD_MYGROUPID= "myGroupID";
	
	private static final String FIELD_GROUPID= "groupId";
	
	List<String> lotIdList = new ArrayList<String>();

	public SelectMyLotGroupDialog(String adFormName, String authority, IEventBroker eventBroker, List<String> lotIdList) {
		super(adFormName, authority, eventBroker);
		this.lotIdList = lotIdList;
	}

	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		entityFormField = form.getFieldByControlId(FIELD_MYGROUPID, EntityFormField.class);
		refTableField =  entityFormField.getFieldByControlId(FIELD_GROUPID, RefTableField.class);
		List<LotMyGroup> lotMyGroups = removeDuplicateGroupId();
		if(lotMyGroups == null) {
			lotMyGroups = new ArrayList<LotMyGroup>();
		}
		lotMyGroups.add(new LotMyGroup());
		refTableField.setInput(lotMyGroups);
		refTableField.refresh();
	}

	@Override
	protected void okPressed() {
		try {
			LotMyGroup lotMyGroup = (LotMyGroup) entityFormField.getValue();
        	if(lotMyGroup !=null && lotMyGroup.getGroupId() != null) {
        		LotManager lotManager =  Framework.getService(LotManager.class);
        		List<Lot> lots = lotManager.getLotsByGroupId(Env.getOrgRrn(), lotMyGroup.getGroupId(), false);
        		if(CollectionUtils.isNotEmpty(lots)) {
        			lotIdList = new ArrayList<String>();
        			for (Lot lot : lots) {
        				lotIdList.add(lot.getLotId());
        			}
        		}
        	}else {
        		lotIdList = null;
			}
		} catch (Exception e) {
			logger.error("NpwControlInstanceDialog saveInstance error:", e);
			e.printStackTrace();
		}
		close();
	}
	
	 private static ArrayList<LotMyGroup> removeDuplicateGroupId(){
			try {
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotMyGroup> lotMyGroups = adManager.getEntityList(Env.getOrgRrn(), LotMyGroup.class);
				if(CollectionUtils.isNotEmpty(lotMyGroups)) {
					Set<LotMyGroup> set = new TreeSet<LotMyGroup>(new Comparator<LotMyGroup>() {
						@Override
						public int compare(LotMyGroup o1, LotMyGroup o2) {
							return o1.getGroupId().compareTo(o2.getGroupId());
						}
					});
					set.addAll(lotMyGroups);
					return new ArrayList<LotMyGroup>(set);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
		}

	public List<String> getLotIdList() {
		return lotIdList;
	}

	public void setLotIdList(List<String> lotIdList) {
		this.lotIdList = lotIdList;
	}

	protected void cancelPressed() {
		super.cancelPressed();
	}

	@Override
	 protected Point getInitialSize() {
	    return new Point(600,400);
	 }
}
