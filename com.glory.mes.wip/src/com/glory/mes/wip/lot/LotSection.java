package com.glory.mes.wip.lot;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.bylot.RunByLotCarrierLotComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotRework;
import com.glory.mes.wip.model.LotStateModel;

public class LotSection extends EntitySection {
	
	private static final Logger logger = Logger.getLogger(LotSection.class);
	
	protected List<ILotChangeListener> lotChangeListeners = new LinkedList<ILotChangeListener>();
	
	private String lotTableName;
	private String compTableName;
	
	private Boolean isCaseSensitive;
	
	public HeaderText txtLot;
	public RunByLotCarrierLotComposite carrierLotComposite;
 
	public ADBase prestoreObject;
	public String eventId;
	public Boolean checkFlag = false;
	public Boolean showLotFlag = true;
	public Boolean showDetailFlag = false;
	public Boolean showOperatorFlag = false;
	
	public Boolean showSimpleHeader = false;
	
	public LotSection() {
		super();
	}

	public LotSection(ADTable table) {
		this(table, null);
	}
	
	public LotSection(ADTable table,boolean checkFlag) {
		this(table, null);
		this.checkFlag = checkFlag;
	}
	
	public LotSection(ADTable table, ADBase prestoreObject){
		super(table);
		this.prestoreObject = prestoreObject;
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		
		client.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
	        if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
	        	Composite composite = toolkit.createComposite(client, SWT.NONE);
	        	composite.setLayout(new GridLayout());
				GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
				gridData.heightHint = DPIUtil.autoScaleUpUsingNativeDPI(234);
				gridData.verticalAlignment = SWT.TOP;
				composite.setLayoutData(gridData);
	        	//���ʹ���ؾ�
				carrierLotComposite = new RunByLotCarrierLotComposite(this, client, SWT.NONE, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
				carrierLotComposite.setCompTableName(getCompTableName());
				carrierLotComposite.setLotTableName(getLotTableName());
				carrierLotComposite.createForm(toolkit, composite);
				if (carrierLotComposite.getLotTableManager().isCheckFlag()) {
					carrierLotComposite.getLotTableManager().addICheckChangedListener(new ICheckChangedListener() {
						
						@Override
						public void checkChanged(List<Object> arg0, boolean arg1) {
							try {
								List<Object> objects = carrierLotComposite.getLotTableManager().getCheckedObject();
								if (objects != null && objects.size() > 0) {
									List<Lot> checkLots = objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
									carrierLotComposite.loadComponentUnits(checkLots);
									
									Lot lot = checkLots.get(0);
									if (lot != null) {
										lot = searchLot(lot.getLotId());
									}
									setAdObject(lot);
									refresh();
								}
							} catch (Exception e) {
								ExceptionHandlerManager.asyncHandleException(e);
								return;
							}						
						}
					});
				} else {
					carrierLotComposite.getLotTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
						@Override
						public void selectionChanged(SelectionChangedEvent event) {
							StructuredSelection selection = (StructuredSelection) event.getSelection();
							Lot lot = (Lot) selection.getFirstElement();
							if (lot != null) {
								lot = searchLot(lot.getLotId());
							}
							setAdObject(lot);
							refresh();
						}
					});
				}
				txtLot = carrierLotComposite.getTxtLotId();
	        } else {
	        	GridData gd = new GridData(GridData.FILL_HORIZONTAL);
	    		gd.verticalAlignment = SWT.TOP;
	        	//�����ʹ���ؾ�
				Composite top = toolkit.createComposite(client);
				top.setLayout(new GridLayout(3, false));
				top.setLayoutData(gd);
				Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
				label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
				txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
				txtLot.setTextLimit(32);
				txtLot.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
				txtLot.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						Text tLotId = ((Text) event.widget);
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
						switch (event.keyCode) {
						case SWT.CR:
						case SWT.KEYPAD_CR:
							Lot lot = null;
							String lotId = tLotId.getText();
							if (!isLotIdCaseSensitive()) {
								lotId = lotId.toUpperCase();
							}
							tLotId.setText(lotId);
							lot = searchLot(lotId);
							if (lot == null) {
								tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
								try {
			        				setAdObject(createAdObject());		        			
			        			} catch(Exception en) {
			        				logger.error("createADObject error at searchEntity Method!");
			        			}
								txtLot.warning();
							} else {
								setAdObject(lot);
								tLotId.selectAll();
								txtLot.focusing();
							}
							refresh();
							break;
						}
					}
		
				});
				txtLot.addFocusListener(new FocusListener() {
					public void focusGained(FocusEvent e) {
					}
		
					public void focusLost(FocusEvent e) {
						Text tLotId = ((Text) e.widget);
						String lotId = tLotId.getText();
						if (!isLotIdCaseSensitive()) {
							lotId = lotId.toUpperCase();
						}
						tLotId.setText(lotId);
					}
				});
				
				Composite right = toolkit.createComposite(top);
				GridLayout layout = new GridLayout(2, false);
				right.setLayout(layout);
				gd = new GridData(GridData.FILL_HORIZONTAL);
				gd.horizontalAlignment = SWT.END;
				gd.grabExcessHorizontalSpace = true;
				right.setLayoutData(gd);
	        }
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
	
	@Override
	public void setFocus() {
		txtLot.setFocus();
	}

	public Lot searchLot(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			notifyLotChangeListeners(this, lot);
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public void refresh() {
		super.refresh();
		if (txtLot != null) {
			txtLot.selectAll();
		}
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		super.setAdObject(adObject);
		Lot newBase = (Lot) this.getAdObject();
		if (newBase != null) {	
			statusChanged(newBase.getState());		
			lotStatusChanged(newBase);
			try {
				if (newBase.getObjectRrn() != null) {
					SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
					if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
						if (carrierLotComposite != null && carrierLotComposite.getLotTableManager() != null) {
							carrierLotComposite.getLotTableManager().update(newBase);
						}
			        }
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
	        
		} else {				
			statusChanged("");
			lotStatusChanged(null);
		}
	}
	
	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}
	
	public void statusChanged(String newStatus){
	}
	
	public void lotStatusChanged(Lot lot){
	}

	/**
	 * ����Ϊ�¼�����LotStateModel,���û�������򷵻�null,��ʱ��Ҫ����¼���Ĭ�Ϲ���
	 * ����Ϊÿ���¼����ö���LotStateModel,ֻҪ����һ������Ϳ���ִ�д��¼�(Button����)
	 * LotStateModel��鷽ʽΪ���ε�ǰ״̬��Hold״̬,��LotStateModelһ��
	 */
	public Boolean checkLotStateModel(Lot lot) {
		try {
			if (!StringUtil.isEmpty(eventId)) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotStateModel> models = adManager.getEntityList(Env.getOrgRrn(), LotStateModel.class, Integer.MAX_VALUE, " eventId = '" + eventId + "'", "");
				if (models.isEmpty()) {
					return null;
				}
				
				boolean isAllFail = true;
				for (LotStateModel model : models) {
					if (!StringUtil.isEmpty(model.getHoldState()) && !model.getHoldState().equals(lot.getHoldState())) {
						//���μ�鲻ͨ��,���������һ����
						continue;
					}
					if (!StringUtil.isEmpty(model.getLotState()) && !model.getLotState().equals(lot.getState())) {
						//���μ�鲻ͨ��,���������һ����
						continue;
					}
					isAllFail = false;
					break;
				}
				if(!isAllFail) {
					return true;
				} else {
					return false;
				}
			} else {
				return null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
	
	public void setLotById(String lotId) {
		if (!StringUtil.isEmpty(lotId)) {
			if(txtLot != null) {
				txtLot.setText(lotId);
			}
			
			if (carrierLotComposite != null) {				
				carrierLotComposite.getLotByLotId(lotId);
			}					
			
			if (carrierLotComposite == null) {
				Lot lot = new Lot();
				lot = searchLot(lotId);
				setAdObject(lot);
				refresh();
			}
			
		}
	}
	
	public void addLotChangeListener(ILotChangeListener listener) {
		synchronized (lotChangeListeners) {
			lotChangeListeners.add(listener);
		}
	}

	public void removeLotChangeListener(ILotChangeListener listener) {
		synchronized (lotChangeListeners) {
			lotChangeListeners.remove(listener);
		}
	}

	public void notifyLotChangeListeners(Object sender, Lot newLot) {
		synchronized (lotChangeListeners) {
			for (ILotChangeListener listener : lotChangeListeners) {
				try {
					listener.lotChanged(sender, newLot);
				} catch (Throwable t) {
					t.printStackTrace();
				}
			}
		}
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
    public boolean checkLotCanRework(Lot lot, Procedure reworkProcedure) {
        try {
            if (reworkProcedure.getMaxReworkCount() != null) {

                LotManager lotManager = Framework.getService(LotManager.class);
                LotRework thisLotRework = new LotRework();
                thisLotRework.setOrgRrn(Env.getOrgRrn());
                thisLotRework.setCreatedBy(Env.getUserName());
                thisLotRework.setUpdatedBy(Env.getUserName());
                thisLotRework.setLotRrn(lot.getObjectRrn());
                thisLotRework.setReworkTime(new Date());
                thisLotRework.setMaxReworkCountType(ReworkState.OBJECTTYPE_TOTAL);
                thisLotRework.setMaxReworkCount(reworkProcedure.getMaxReworkCount());
                if (StringUtils.isNotEmpty(lot.getStepName())) {
                    thisLotRework.setReworkStepName(lot.getStepName());
                } else {
                    thisLotRework.setReworkStepName(lot.getLastStepName());
                }
                thisLotRework.setReworkProcedureName(reworkProcedure.getName());

                lotManager.checkAndsaveLotRework(thisLotRework, true, Env.getSessionContext());

            }
            return true;
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return false;
        }
    }

	@Override
	protected void refreshAdapter() {
		Lot lot = (Lot) getAdObject();	
		if (lot != null) {
			setLotById(lot.getLotId());
		}
		// ����Ҫ�����������setLotById�Ѿ�����������
		// super.refreshAdapter();
	}

	public String getLotTableName() {
		return lotTableName;
	}

	public void setLotTableName(String lotTableName) {
		this.lotTableName = lotTableName;
	}

	public String getCompTableName() {
		return compTableName;
	}

	public void setCompTableName(String compTableName) {
		this.compTableName = compTableName;
	}

	public Boolean getCheckFlag() {
		return checkFlag;
	}

	public void setCheckFlag(Boolean checkFlag) {
		this.checkFlag = checkFlag;
	}

	public Boolean getShowLotFlag() {
		return showLotFlag;
	}

	public void setShowLotFlag(Boolean showLotFlag) {
		this.showLotFlag = showLotFlag;
	}

	public Boolean getShowDetailFlag() {
		return showDetailFlag;
	}

	public void setShowDetailFlag(Boolean showDetailFlag) {
		this.showDetailFlag = showDetailFlag;
	}

	public Boolean getShowOperatorFlag() {
		return showOperatorFlag;
	}

	public void setShowOperatorFlag(Boolean showOperatorFlag) {
		this.showOperatorFlag = showOperatorFlag;
	}

	public Boolean getShowSimpleHeader() {
		return showSimpleHeader;
	}

	public void setShowSimpleHeader(Boolean showSimpleHeader) {
		this.showSimpleHeader = showSimpleHeader;
	}

}
