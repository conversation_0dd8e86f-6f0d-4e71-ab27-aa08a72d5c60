package com.glory.mes.wip.lot;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityListSection;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;

public class LotListSection extends EntityListSection {
	
	private static final Logger logger = Logger.getLogger(LotListSection.class);
	public HeaderText txtLot;
	public ADBase adObject;
	
	public LotListSection(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		top.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
		Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
		txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
		txtLot.setTextLimit(32);
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					lot = searchLot(lotId);
					if (lot == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
	        				setAdObject(new Lot());		        			
	        			} catch(Exception en) {
	        				logger.error("createADObject error at searchEntity Method!");
	        			}
						txtLot.warning();
					} else {
						setAdObject(lot);
						tLotId.selectAll();
						txtLot.focusing();
					}
					refresh();
					break;
				}
			}

		});
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});
		
		/*Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);*/
	}
	
	public void setFocus() {
		txtLot.setFocus();
	}

	public Lot searchLot(String lotId) {
		try {
			return LotProviderEntry.getLot(lotId);
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public void refresh() {
		super.refresh();
		if (txtLot != null) {
			txtLot.selectAll();
		}
	}

	public void setAdObject(ADBase adObject) {
		this.adObject = adObject;
	}

	public ADBase getAdObject() {
		return adObject;
	}

	public void setLotById(String lotId) {
		if (!StringUtil.isEmpty(lotId)) {
			txtLot.setText(lotId);
			Lot lot = searchLot(lotId);
			setAdObject(lot);
			refresh();
		}
	}
	
	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
