package com.glory.mes.wip.lot.receive;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.ILotChangeListener;
import com.glory.mes.wip.model.Lot;

public class ReceiveLotSection extends EntitySection implements IValueChangeListener {

    private static final Logger logger = Logger.getLogger(ReceiveLotSection.class);
    public static final String TABLE_NAME = "WIPReceiveLot";

    private static final String FIELD_DISPLAY_TYPE = "reftable";
    private static final String FIELD_ID_PORTID = "orgRrn";
    protected IField orgRefTableField;
    protected ADField orgNameField;

    public CheckBoxFixEditorTableManager manager;
    protected List<ILotChangeListener> lotChangeListeners = new LinkedList<ILotChangeListener>();

    public Text txtLotNo;
    protected List<Lot> lots = new ArrayList<Lot>();

    private ToolItem itemReceive;

    public ReceiveLotSection() {
        super();
    }

    public ReceiveLotSection(ADTable table) {
        super(table);
    }

    @Override
    protected void createSectionTitle(Composite client) {

        final FormToolkit toolkit = form.getToolkit();
        GridData gd = new GridData(GridData.FILL_HORIZONTAL);
        gd.verticalAlignment = SWT.TOP;
        Composite top = toolkit.createComposite(client);
        top.setLayout(new GridLayout(4, false));
        top.setLayoutData(new GridData(700, 35));
        
        Label label = toolkit.createLabel(top, Message.getString("wip.org_rrn") + ":");
        label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

        try {
            ADManager entityManager = Framework.getService(ADManager.class);
            ADRefTable refTable = new ADRefTable();
            for (ADTab tab : table.getTabs()) {
                for (ADField adField : tab.getFields()) {
                    if (FIELD_ID_PORTID.equals(adField.getName())
                            && FIELD_DISPLAY_TYPE.equals(adField.getDisplayType())) {
                        orgNameField = adField;
                    }
                }
            }
            refTable.setObjectRrn(orgNameField.getReftableRrn());
            refTable = (ADRefTable) entityManager.getEntity(refTable);
            ADTable adTable = entityManager.getADTable(refTable.getTableRrn());

            ListTableManager tableManager = new ListTableManager(adTable);
            if (refTable.getWhereClause() == null
                    || "".equalsIgnoreCase(refTable.getWhereClause().trim())
                    || StringUtil.parseClauseParam(refTable.getWhereClause()).size() == 0) {
                List<ADBase> list = entityManager.getEntityList(Env.getOrgRrn(),
                        adTable.getObjectRrn(), Env.getMaxResult(), refTable.getWhereClause(),
                        refTable.getOrderByClause());
                tableManager.setInput(list);
            }
            int mStyle = SWT.BORDER;
            orgRefTableField = new RefTableField("", tableManager, refTable, mStyle, true);

            orgRefTableField.setLabel(null);
            orgRefTableField.addValueChangeListener(this);
            orgRefTableField.createContent(top, toolkit);
        } catch (Exception e) {
            logger.error("LogEventSection : creatSectionTitle", e);
        }

        Label LotLabel = toolkit.createLabel(top, Message.getString("wip.lot_id") + ":");
        LotLabel.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
        txtLotNo = toolkit.createText(top, "", SWT.BORDER);
        txtLotNo.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_LARGE));
        GridData lotText = new GridData();
        lotText.widthHint = 300;
        lotText.heightHint = 20;
        txtLotNo.setLayoutData(lotText);
        txtLotNo.setTextLimit(32);
        txtLotNo.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent event) {

                Text tLotId = ((Text) event.widget);
                tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
                switch (event.keyCode) {
                case SWT.CR:
                case SWT.KEYPAD_CR:
                    String orgRrnValue = (String) orgRefTableField.getValue();
                    if (StringUtil.isEmpty(orgRrnValue)) {
                        UI.showError(Message.getString("error.no_orgrrn_input"));
                    } else {
                        Lot lot = null;
                        String lotId = tLotId.getText();
                        if (!isLotIdCaseSensitive()) {
							lotId = lotId.toUpperCase();
						}
                        tLotId.setText(lotId);
                        lot = searchLot(Long.parseLong(orgRrnValue), lotId);
                        tLotId.selectAll();
						if (lot == null) {
							manager.refresh();
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							try {
								setAdObject(createAdObject());
							} catch (Exception e) {
								logger.error("createADObject error at searchEntity Method!");
							}
						} else if (lots.contains(lot)) {
							UI.showWarning(Message.getString("error.lot_exist"));
						} else {
							setAdObject(lot);
							lots.add(lot);
							manager.setInput(lots);
						}
                        refresh();
                        break;
                    }
                }
            }
        });
    }

    @Override
    protected void createSectionContent(Composite client) {
        super.createSectionContent(client);

        try {
            final FormToolkit toolkit = form.getToolkit();
            mmng = form.getMessageManager();
            createBasicSection(client);

            Composite bodyComposite = toolkit.createComposite(client, SWT.NONE);
            bodyComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
            GridLayout layoutBody = new GridLayout(1, true);
            bodyComposite.setLayout(layoutBody);

            GridData gdLeftLest = new GridData(GridData.FILL_BOTH);
            Composite parameterCompLeft = toolkit.createComposite(bodyComposite, SWT.BORDER);
            gdLeftLest.heightHint = 400;
            parameterCompLeft.setLayoutData(gdLeftLest);
            GridLayout layoutLeft = new GridLayout(1, true);
            parameterCompLeft.setLayout(layoutLeft);

            ADManager adManager = (ADManager) Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
            manager = new CheckBoxFixEditorTableManager(adTable);
            manager.newViewer(parameterCompLeft);

        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }

    }

    @Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemReceive(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }

    protected void createToolItemReceive(ToolBar tBar) {
        itemReceive = new ToolItem(tBar, SWT.PUSH);
        itemReceive.setText(Message.getString("common.receive"));
        itemReceive.setImage(SWTResourceCache.getImage("currentstep"));
        itemReceive.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                receiveLotAdapter(lots);
            }
        });

    }

    private void receiveLotAdapter(List<Lot> lots) {

        try {
            if (lots == null || lots.size() == 0) {
                UI.showWarning(Message.getString("common.lot.can.not.be.empity"));
            } else {
                LotAction lotAction = new LotAction();
                LotManager lotManager = Framework.getService(LotManager.class);
                lotManager.transferLot(lots, Env.getOrgRrn(), null, lotAction,
                        Env.getSessionContext());
                UI.showInfo(Message.getString("common.receive.success"));
                refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
    }

    public Lot searchLot(Long orgRrn, String lotId) {
        try {
			ADManager adManager = Framework.getService(ADManager.class);

			List<Lot> lots = adManager.getEntityList(orgRrn, Lot.class,
					Integer.MAX_VALUE, "lotId = '" + lotId + "'"
							+ " and mainQty > 0 and state = 'FIN'", null);
			if (lots == null || lots.size() == 0) {
				return null;
			}
			return lots.get(0);
        } catch (Exception e) {
            logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
        }
        return null;
    }

    @Override
    public void valueChanged(Object arg0, Object arg1) {

    }
    
	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}

}
