package com.glory.mes.wip.lot.disassembly;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotListSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotDisassembly;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class DisassemblySection extends LotListSection {
	
	protected ToolItem itemDisassembly;
	
	public DisassemblySection(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemDisassembly(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemDisassembly(ToolBar tBar) {
		itemDisassembly = new ToolItem(tBar, SWT.PUSH);
		itemDisassembly.setEnabled(false);
		itemDisassembly.setImage(SWTResourceCache.getImage("disassembly"));
		itemDisassembly.setText(Message.getString("wip.lot_disassembly"));
		itemDisassembly.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				disassemblyAdapter();
			}
		});
	}

	@SuppressWarnings("unchecked")
	protected void disassemblyAdapter() {
		try {
			Lot lot = (Lot) getAdObject();
			List<LotDisassembly> disassemblies = (List<LotDisassembly>) getTableManager().getInput();
			
			if (lot != null && lot.getObjectRrn() != null && CollectionUtils.isNotEmpty(disassemblies)) {
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.lotDisassembly(lot, Lists.newArrayList(disassemblies), Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				
				txtLot.setText("");
				getTableManager().setInput(Lists.newArrayList());
				itemDisassembly.setEnabled(false);
			}
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void refresh(){
		try {
			Lot lot = (Lot) getAdObject();
			
			if (!(LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_FIN.equals(lot.getState()))) {
				UI.showWarning(Message.getString("wip.lot_state_not_allow"));
				return;
			}
			
			if (lot != null && lot.getObjectRrn() != null) {
				// ����LotAssembly�齨Disassembly�б�
				LotManager lotManager = Framework.getService(LotManager.class);
				List<LotDisassembly> adList = lotManager.getLotDisassembliesByLotAssemblies(Env.getOrgRrn(), lot.getLotId());
				if (CollectionUtils.isEmpty(adList)) {
					return;
				}
				
				showNumber = adList.size();
				createSectionDesc(section);
				tableManager.setInput(adList);
				itemDisassembly.setEnabled(true);
			}
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
}
