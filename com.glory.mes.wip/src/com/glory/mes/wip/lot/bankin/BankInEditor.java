package com.glory.mes.wip.lot.bankin;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.inv.model.Storage;
import com.glory.mes.wip.action.LotBankAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class BankInEditor extends GlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.bankin.BankInEditor";

	private static final String BUTTON_BANKIN = "bankIn";

	private static final String FIELD_QUERYFORM = "WIPLotBankIn";

	private static final String FIELD_ENTITYFORM = "WIPBankOperationLot";

	private static final String FIELD_WAREHOUSEID = "warehouseId";

	private static final String FIELD_LOCATORID = "locatorId";

	private static final String FIELD_LOTCOMMENT = "lotComment";

	private QueryFormField queryFormField;

	private EntityFormField entityFormField;
	private RefTableField warehouseIdField;
	private RefTableField locatorIdField;
	private TextField lotCommentField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_BANKIN), this::bankInAdaptor);

		init();
	}

	private void init() {
		queryFormField = form.getFieldByControlId(FIELD_QUERYFORM, QueryFormField.class);
		entityFormField = form.getFieldByControlId(FIELD_ENTITYFORM, EntityFormField.class);
		warehouseIdField = entityFormField.getFieldByControlId(FIELD_WAREHOUSEID, RefTableField.class);
		locatorIdField = entityFormField.getFieldByControlId(FIELD_LOCATORID, RefTableField.class);
		lotCommentField = entityFormField.getFieldByControlId(FIELD_LOTCOMMENT, TextField.class);
	}

	private void bankInAdaptor(Object obj) {
		try {
			if (!checkWarehouseId()) {
				return;
			}
			List<Object> objs = queryFormField.getCheckedObjects();
			List<Lot> lots = objs.stream().map(o -> {
				Lot lot = (Lot) o;
				lot.setWarehouseId(warehouseIdField.getValue().toString());
				lot.setLocatorId(locatorIdField.getValue().toString());
				return lot;
			}).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(lots)) {
				UI.showInfo(Message.getString("wip_not_select_lot"));
				return;
			}

			LotBankAction lotBankAction = new LotBankAction();
			lotBankAction.setActionComment((String) lotCommentField.getValue());

			Framework.getService(LotManager.class).bankIn(lots, lotBankAction, Env.getSessionContext());
			UI.showInfo(Message.getString("common.bank_successed"));// ������ʾ��
			refreshAdaptor(obj);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	private boolean checkWarehouseId() throws ClientException, Exception {
		if (StringUtils.isEmpty((String) warehouseIdField.getValue())) {
			UI.showInfo(Message.getString("error.no_warehouse_input"));
			return false;
		}
		if (StringUtils.isEmpty((String) locatorIdField.getValue())) {
			UI.showInfo(Message.getString("wms.storage_can_not_null"));
			return false;
		}
		// �жϲֿ��¿�λ�Ƿ����
		List<Storage> storagelist = Framework.getService(ADManager.class).getEntityList(Env.getOrgRrn(), Storage.class,
				Env.getMaxResult(),
				" warehouseId = '" + warehouseIdField.getValue() + "' AND name = '" + locatorIdField.getValue() + "'",
				null);
		// ������ҵ����Ա�¼���λID
		if (CollectionUtils.isEmpty(storagelist)) {
			UI.showInfo(Message.getString("mm.storage_no_exist"));
			return false;
		} else {
			return true;
		}
	}

	private void refreshAdaptor(Object obj) {
		queryFormField.refresh();
		warehouseIdField.setValue(null);
		locatorIdField.setValue(null);
		lotCommentField.setText(null);
	}
}
