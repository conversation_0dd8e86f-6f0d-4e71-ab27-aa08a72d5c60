package com.glory.mes.wip.lot.reassign;

import org.eclipse.swt.widgets.Composite;
import com.glory.mes.wip.lot.sorting.LotSortingComposite;

public class LotSortingReassignLeftComposite extends LotSortingComposite {

	public LotSortingReassignLeftComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
	}
	
}
