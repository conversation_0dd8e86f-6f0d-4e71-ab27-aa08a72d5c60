package com.glory.mes.wip.lot.reassign;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.comp.ComponentAssignComposite;
import com.glory.mes.wip.comp.ComponentComposite.DiffType;
import com.glory.mes.wip.lot.sorting.SortModel;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.sorting.LotSortingAction;
import com.glory.mes.wip.sorting.LotSortingJob;

public class LotReassignSection extends EntitySection {
	
	public ComponentAssignComposite assignComposite;
	
	private LotReassignEditor lotReassignEditor;

	protected ToolItem itemReassign;
	
	public LotReassignSection(ADTable adTable) {
		super(adTable);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemReassign(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemReassign(ToolBar tBar) {
		itemReassign = new ToolItem(tBar, SWT.PUSH);
		itemReassign.setText(Message.getString("wip.reassign"));
		itemReassign.setImage(SWTResourceCache.getImage("assign"));
		itemReassign.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				reassignAdapter();
			}
		});
	}

	protected void reassignAdapter() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), assignComposite.txtTargetCarrierId.getText().substring(assignComposite.txtTargetCarrierId.getText().indexOf(":") + 1));
			if (targetCarrier == null) {
				UI.showError(Message.getString("mm.durable_not_found"));
				return;
			} 
			
			Lot lot = assignComposite.sourceLot;
			List<ComponentUnit> addComponents = assignComposite.targetComponentComposite.getComponentsDiff().get(DiffType.ADD);
			if (addComponents != null && addComponents.size() != 0) {
				// �����������Լ��������ͬʱ��   
				Map<Long, Integer> map = new HashMap<Long, Integer>(); 
				for (ComponentUnit componentUnit : addComponents) {
					map.put(componentUnit.getParentUnitRrn(), 1);
				}
				
				Set<Long> keySet = map.keySet();
				if (keySet.size() > 1) {
					UI.showInfo(Message.getString("wip.not_allow_more_than_one_lot"));
					return;
				}
				
				for (Long objectRrn : keySet) {
					if (!assignComposite.sourceLot.getObjectRrn().equals(objectRrn)) {
						UI.showInfo(Message.getString("wip.source_lot_component_not_match"));
						return;
					}
				}
				
				lotReassignEditor.getSourceCarrierLotComposite().getCarrierForm().saveToObject();
				SortModel sort = (SortModel) lotReassignEditor.getSourceCarrierLotComposite().getCarrierForm().getObject();
				// �Ƿ�sort
				if (sort.isSort()) {
					// У���豸�Ƿ���Ready����Start״̬��SortingJob
					Equipment equipment = null;
					if (StringUtil.isEmpty(sort.getEquipmentId())) {
						equipment = new Equipment();
						equipment.setObjectRrn(Long.valueOf(sort.getEquipmentId()));
						equipment = (Equipment) adManager.getEntity(equipment);
					}

					// У��port��ʱ��һ��
					if ((sort.getFromPortId()).equals(sort.getToPortId())) {
						UI.showError(Message.getString("wip.sorting_port_diverse"));
						return;
					}

					LotSortingAction lotSortingAction = new LotSortingAction();
					lotSortingAction.setSort(true);
					if (equipment != null) {
						lotSortingAction.setEquipmentId(equipment.getEquipmentId());
						lotSortingAction.setFromPortId(sort.getFromPortId());
						lotSortingAction.setToPortId(sort.getToPortId());
					}

					lotSortingAction.setToDurableId(targetCarrier.getDurableId());
					Map<String, String> toPositionMap = addComponents.stream()
							.collect(Collectors.toMap(ComponentUnit::getComponentId, ComponentUnit::getPosition));
					lotSortingAction.setToPositionMap(toPositionMap);

					// ���Ŀ���ؾ�
					for (int j = 0; j < addComponents.size(); j++) {
						addComponents.get(j).setDurable(targetCarrier.getDurableId());
					}

					//������δ���ExchangeSort���͹������ֶ�����Exchange���͵�SortingJobʱ��JobTypeΪAuto, �����Զ���ɹ���
					Step step = new Step();
					step.setObjectRrn(lot.getStepRrn());
					step = (Step) adManager.getEntity(step);
					
					String jobType = LotSortingJob.JOB_TYPE_MANUAL;
					if (Step.USE_CATEGORY_EXCHANGESORT.equals(step.getUseCategory())) {
						jobType = LotSortingJob.JOB_TYPE_AUTO;
					}
					
					SortingManager sortingManager = Framework.getService(SortingManager.class);
					sortingManager.saveExchangeSortingJob(addComponents, lotSortingAction,
							jobType, Env.getSessionContext());

					UI.showInfo(Message.getString("wip.assign_sort_job_successed"));
				} else {
					if (!LotStateMachine.STATE_WAIT.equals(lot.getState())
							&& !LotStateMachine.STATE_FIN.equals(lot.getState())) {
						throw new ClientException("error.state_not_allow");
					}
					
					targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(), assignComposite.txtTargetCarrierId.getText().substring(assignComposite.txtTargetCarrierId.getText().indexOf(":") + 1));
					//TODO ���������
					//1,��Դ��ȫ���ƶ���Ŀ���ؾ�(Ŀ���ؾ�Ϊ���ؾ�),����reassignCarrierLot(targetCarrier, false, null, addComponents, false, true, Env.getSessionContext());
					//2,��Դ��ȫ���ƶ���Ŀ���ؾ�(Ŀ���ؾ����Ѿ�������),����mergeAndReassignCarrierLot����
					//3,��Դ�������ƶ���Ŀ���ؾ�(Ŀ���ؾ�Ϊ���ؾ�),����splitAndReassignCarrierLot����
					//4,��Դ�������ƶ���Ŀ���ؾ�(Ŀ���ؾ߲�Ϊ���ؾ�),����splitMergeAndReassignCarrierLot����
					
					//carrierLotManager.reassignCarrierLot(targetCarrier, false, lot.getLotId(), addComponents, false, true, Env.getSessionContext());
					
					UI.showInfo(Message.getString("wip.reassign_successed"));
				}
				
				addComponents.clear();
				refresh();
			} else {
				UI.showError(Message.getString("wip.need_add_assign_component"));
				return;
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void refresh() {
		String sourceCarrierId = assignComposite.txtSourceCarrierId.getText().substring(assignComposite.txtSourceCarrierId.getText().indexOf(":") + 1);
		String targetCarrierId = assignComposite.txtTargetCarrierId.getText().substring(assignComposite.txtTargetCarrierId.getText().indexOf(":") + 1);
		// ˢ��carrierLotComposite
		assignComposite.searchCarrier(sourceCarrierId, assignComposite.sourceComponentComposite, true);
		assignComposite.searchCarrier(targetCarrierId, assignComposite.targetComponentComposite, true);
	}
	
	@Override
	protected void createSectionContent(Composite parent) {
		try {
			assignComposite = new ComponentAssignComposite(parent, SWT.NONE, true, true, true);
			assignComposite.txtSourceCarrierId.setEditable(false);
			assignComposite.txtTargetCarrierId.setEditable(false);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void setTxtSourceCarrierId(String sourceCarrierId) {
		assignComposite.txtSourceCarrierId.setText(sourceCarrierId);
	}
	
	public void setTxtTargetCarrierId(String targetCarrierId) {
		assignComposite.txtTargetCarrierId.setText(targetCarrierId);
	}

	public LotReassignEditor getLotReassignEditor() {
		return lotReassignEditor;
	}

	public void setLotReassignEditor(LotReassignEditor lotReassignEditor) {
		this.lotReassignEditor = lotReassignEditor;
	}

	public ComponentAssignComposite getAssignComposite() {
		return assignComposite;
	}

	public void setAssignComposite(ComponentAssignComposite assignComposite) {
		this.assignComposite = assignComposite;
	}

	public ToolItem getItemReassign() {
		return itemReassign;
	}

	public void setItemReassign(ToolItem itemReassign) {
		this.itemReassign = itemReassign;
	}

}
