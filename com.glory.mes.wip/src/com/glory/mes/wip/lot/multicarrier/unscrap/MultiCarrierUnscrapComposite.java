package com.glory.mes.wip.lot.multicarrier.unscrap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.mes.wip.model.LotScrap;

public class MultiCarrierUnscrapComposite extends MultiCarrierComposite {

	public MultiCarrierUnscrapComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
	}

	@Override
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);

			List<LotMultiCarrier> newLotMultiCarriers = new ArrayList<LotMultiCarrier>();

			Lot lot = null;
			if (carrier != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				LotManager lotManager = Framework.getService(LotManager.class);
				String whereClause = " carrierId = '" + carrierId + "'";
				List<LotMultiCarrier> lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
						Integer.MAX_VALUE, whereClause, null);
				LotMultiCarrier lotMultiCarrier;
				if (lotMultiCarriers.size() > 0) {
					newLotMultiCarriers.add(lotMultiCarrier = lotMultiCarriers.get(0));
					lot = lotManager.getLot(lotMultiCarrier.getLotRrn());
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				} else {
					// ��ȫ������ʱ���ϵ�����ڣ�����Ҫֱ�Ӳ�ѯLotScrap
					List<LotScrap> lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class,
							Integer.MAX_VALUE, "actionReason like '%" + carrierId + "%'", null);
					if (lotScraps.size() > 0) {
						lotMultiCarrier = new LotMultiCarrier();
						lotMultiCarrier.setCarrierId(carrierId);
						lotMultiCarrier.setCurrentQty(BigDecimal.ZERO);
						lotMultiCarrier.setLotRrn(lotScraps.get(0).getLotRrn());
						newLotMultiCarriers.add(lotMultiCarrier);
						lot = lotManager.getLot(lotMultiCarrier.getLotRrn());
						txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					} else {
						txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
					}
				}
			} else {
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
			
			if (showLotFlag) {
				txtLotId.setText(lot != null && lot.getLotId() != null ? lot.getLotId() : "");
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
			}

			if (lotDetailsForm != null) {
				lotDetailsForm.setObject(lot != null ? lot : new Lot());
				lotDetailsForm.loadFromObject();
			}
			
			lotTableManager.setInput(newLotMultiCarriers);
			lotTableManager.refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh() {
		try {
			Lot lot = (Lot) lotDetailsForm.getObject();
			if (lot != null) {
				// �����б�
				ADManager adManager = Framework.getService(ADManager.class);
				String whereClause = " lotRrn = " + lot.getObjectRrn();
				List<LotMultiCarrier> lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
						Integer.MAX_VALUE, whereClause, null);
				lotTableManager.setInput(lotMultiCarriers);
				
				// �������α�
				lot = (Lot) adManager.getEntity(lot);
				lotDetailsForm.setObject(lot);
				lotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
