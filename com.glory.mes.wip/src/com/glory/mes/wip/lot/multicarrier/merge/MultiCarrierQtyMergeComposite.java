package com.glory.mes.wip.lot.multicarrier.merge;

import java.awt.Toolkit;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierQtyAssignComposite;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierQtyComposite;

public class MultiCarrierQtyMergeComposite extends MultiCarrierQtyAssignComposite {
	
	private static final String  TARGET_TABLE_NAME = "WIPLotByMultiCarrierMerge";

	public MultiCarrierQtyMergeComposite(Composite parent, int style, boolean sourceIsCarrier, boolean showTotalFlag) {
		super(parent, style, sourceIsCarrier, showTotalFlag);
	}

	@Override
	protected void createTargetComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData();
		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
		gd.heightHint = (int) (Toolkit.getDefaultToolkit().getScreenSize().height / 1.1);
		targetComp.setLayoutData(gd);

		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable  = adManager.getADTable(Env.getOrgRrn(), TARGET_TABLE_NAME);
			targetQtyComposite = new MultiCarrierQtyComposite(targetComp, adTable, true, false, true, true, showTotalFlag);
			targetQtyComposite.init();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
