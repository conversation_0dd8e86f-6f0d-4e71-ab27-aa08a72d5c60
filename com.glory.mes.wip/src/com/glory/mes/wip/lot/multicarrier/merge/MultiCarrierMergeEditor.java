package com.glory.mes.wip.lot.multicarrier.merge;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;

public class MultiCarrierMergeEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.multicarrier.merge.MultiCarrierMergeEditor";

	private MultiCarrierMergeSection section;

	protected MultiCarrierMergeComposite sourceMultiCarrierLotComposite;
	protected MultiCarrierMergeComposite targetMultiCarrierLotComposite;
	protected ListTableManager targetCarrierTableManager;

	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected MPart mPart;

	@PostConstruct
	public void postConstruct(Composite parent) {
		try {
			configureBody(parent);

			FormToolkit toolkit = new FormToolkit(parent.getDisplay());
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_BOTH));
			ManagedForm mform = new ManagedForm(toolkit, form);

			Composite body = mform.getForm().getBody();
			GridLayout layout = new GridLayout(2, false);
			body.setLayout(layout);
			body.setLayoutData(new GridData(GridData.FILL_VERTICAL));
			// body.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));

			createLeftViewContent(body, toolkit);
			createRightSectionContent(body, toolkit, mform);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createLeftViewContent(Composite parent, FormToolkit toolkit) {
		Composite left = toolkit.createComposite(parent, SWT.NONE);
		GridData gdLeft = new GridData(GridData.FILL_VERTICAL);
//		gdLeft.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 3;
		left.setLayout(new GridLayout(1, false));
		left.setLayoutData(gdLeft);

		// left sourceCarrierComposite
		sourceMultiCarrierLotComposite = new MultiCarrierMergeComposite(left, SWT.BORDER, false, true, true, false, true);
		sourceMultiCarrierLotComposite.setLblCarrier(Message.getString("wip.source_carrier_id"));
		sourceMultiCarrierLotComposite.createPartControl();
		Text sourceCarrierId = sourceMultiCarrierLotComposite.getTxtCarrierId();
		sourceCarrierId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					changeSourceLot(sourceMultiCarrierLotComposite,
							sourceMultiCarrierLotComposite.getTxtLotId().getText());
				}
			}
		});
		Text sourceLotId = sourceMultiCarrierLotComposite.getTxtLotId();
		sourceLotId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					changeSourceLot(sourceMultiCarrierLotComposite, ((Text) event.widget).getText());
				}
			}
		});

		// left targetCarrierComposite
		targetMultiCarrierLotComposite = new MultiCarrierMergeComposite(left, SWT.BORDER, false, true, true, false,
				true, 120, false);
		targetMultiCarrierLotComposite.setLblCarrier(Message.getString("wip.new_durable"));
		targetMultiCarrierLotComposite.createPartControl();
		targetMultiCarrierLotComposite.setEnabled(false);

		Text txtTargetCarrierId = targetMultiCarrierLotComposite.getTxtCarrierId();
		txtTargetCarrierId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					if (targetMultiCarrierLotComposite.getLot() != null) {
						String targetCarrierId = ((Text) event.widget).getText();
						Lot lot = (Lot) section.getAdObject();
						if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
							section.qtyComposite.sourceLot = lot;
							section.qtyComposite.searchCarrier(targetCarrierId, section.qtyComposite.targetQtyComposite, true, false, true);
						} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
							section.assignComposite.sourceLot = lot;
							section.assignComposite.searchCarrier(targetCarrierId,
									section.assignComposite.targetComponentComposite, true, true);
						}
					}
				}
			}
		});

		Text targetLotId = targetMultiCarrierLotComposite.getTxtLotId();
		targetLotId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
					changeSourceLot(targetMultiCarrierLotComposite, ((Text) event.widget).getText());
				}
			}
		});
	}

	protected void createRightSectionContent(Composite parent, FormToolkit toolkit, ManagedForm mform) {
		ADTable adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		Composite right = toolkit.createComposite(parent, SWT.NONE);
		right.setLayout(new GridLayout(1, false));
		right.setLayoutData(new GridData(GridData.FILL_BOTH));
		// right.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));
		createSection(adTable);
		section.createContents(mform, right);
		section.setMultiCarrierMergeEditor(this);
		mPart.setLabel(Message.getString("wip.bylot.multiCarrier_merge"));
	}

	protected void changeSourceLot(MultiCarrierMergeComposite multiCarrierMergeComposite, String sourceLotId) {
		try {
			Lot lot = LotProviderEntry.getLot(sourceLotId);
			if (lot != null) {
				targetMultiCarrierLotComposite.setEnabled(true);
			} else {
				targetMultiCarrierLotComposite.setEnabled(false);
				UI.showWarning(sourceLotId + Message.getString("wip.lot_is_not_exist"));
			}
			multiCarrierMergeComposite.setLot(lot);
			// ����������ĸ���������������
			if (sourceMultiCarrierLotComposite == multiCarrierMergeComposite) {
				targetMultiCarrierLotComposite.getTxtCarrierId().setText("");
				targetMultiCarrierLotComposite.getTxtLotId().setText("");
				targetMultiCarrierLotComposite.getLotTableManager().setInput(new ArrayList<LotMultiCarrier>());
				targetMultiCarrierLotComposite.setLot(null);
				targetMultiCarrierLotComposite.getLotMultiCarriers(null);
				section.setAdObject(lot);// ֻ��ĸ����
				section.reflow();
			} else {
				// ��������
				if (sourceLotId.equals(sourceMultiCarrierLotComposite.getTxtLotId().getText())) {
					UI.showWarning(Message.getString("wip.lotid_repeat"));
					targetMultiCarrierLotComposite.getLotTableManager().setInput(new ArrayList<LotMultiCarrier>());
				}
				if (lot != null && !lot.getSubUnitType().equals(((Lot) section.getAdObject()).getSubUnitType())) {
					UI.showWarning(Message.getString("wip.merge_unit_different"));
				}

				// ��մ������б�,compˢ��ĸ���б�
				if (section.assignComposite != null) {
					section.assignComposite.sourceComponentComposite.removeAll();
					section.assignComposite.targetComponentComposite.removeAll();
					
					// �����������ؾ���Ϣ��source�б�ȫ��չʾ,��ȫѡ
					List<? extends Object> objs = targetMultiCarrierLotComposite.getLotTableManager().getInput();
					for (Object obj : objs) {
						LotMultiCarrier lotMultiCarrier = (LotMultiCarrier) obj;
						section.assignComposite.sourceLot = lot;
						section.assignComposite.searchCarrier(lotMultiCarrier.getCarrierId(), section.assignComposite.sourceComponentComposite, true, true);
					}
					section.assignComposite.sourceComponentComposite.checkedAll();
				}
				if (section.qtyComposite != null) {
					section.qtyComposite.targetQtyComposite.removeAll();
					
					// �����������ؾ���Ϣȫ��չʾ
					List<? extends Object> objs = targetMultiCarrierLotComposite.getLotTableManager().getInput();
					for (Object obj : objs) {
						LotMultiCarrier lotMultiCarrier = (LotMultiCarrier) obj;
						section.qtyComposite.sourceLot = lot;
						section.qtyComposite.searchCarrier(lotMultiCarrier.getCarrierId(), section.qtyComposite.sourceQtyComposite, true, true, true);
					}
				}

				targetMultiCarrierLotComposite.setEnabled(true);
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	protected void createSection(ADTable adTable) {
		section = new MultiCarrierMergeSection(adTable);
	}

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

}
