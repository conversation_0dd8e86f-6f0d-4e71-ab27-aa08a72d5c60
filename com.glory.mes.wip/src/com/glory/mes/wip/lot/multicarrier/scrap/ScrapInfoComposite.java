package com.glory.mes.wip.lot.multicarrier.scrap;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.model.Lot;

public class ScrapInfoComposite extends Composite {

	private static final Logger logger = Logger.getLogger(ScrapInfoComposite.class);
	
	public static final String DEFAULT_SCRAP_TABLE = "ScrapCode";
	
	protected Lot lot;
    protected XCombo comboScrapCode;
    protected Text commentText;
	
	protected boolean checkFlag;
	protected boolean showLotFlag;
	protected boolean showDetailFlag = false;
	protected boolean showOperatorFlag = false;
	
	protected ListTableManager lotTableManager;
	protected EntityForm lotDetailsForm;
	protected String lblCarrier;
	protected Text txtCarrierId;
	protected Text txtLotId;
	protected Text txtOperator;

	protected int tableHeigthHint = 120;

	public ScrapInfoComposite(Composite parent, int style) {
		super(parent, style);
	}


	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(2, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			Composite scrapInfoComp = new Composite(this, SWT.NONE);
			scrapInfoComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			GridLayout gl = new GridLayout(5, false);
			scrapInfoComp.setLayout(gl);
			
			Label scrapCodeLab = new Label(scrapInfoComp, SWT.NONE);
	        scrapCodeLab.setText(Message.getString("wip.scrap_code"));
	        scrapCodeLab.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
			comboScrapCode = RCPUtil.getUserRefListCombo(scrapInfoComp, getScrapCode(), Env.getOrgRrn());
	        GridData cGd = new GridData(GridData.FILL_HORIZONTAL);
	        cGd.horizontalSpan = 4;
	        comboScrapCode.setLayoutData(cGd);
	        
	        Label scrapCmtLab = new Label(scrapInfoComp, SWT.NONE);
	        scrapCmtLab.setText(Message.getString("wip.comment"));
	        scrapCmtLab.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
	        commentText = new Text(scrapInfoComp, SWT.BORDER);
	        commentText.setLayoutData(cGd);

		} catch (Exception e) {
			logger.error("ScrapInfoComposite.createPartControl() error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);

				lotTableManager.setInput(lots);
				// Ĭ��ȫѡ
				if (checkFlag) {
					for (Lot lot : lots) {
						lotTableManager.setCheckedObject(lot);
					}
				}
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				lotTableManager.refresh();
			} else {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
			
			if (lotDetailsForm != null) {
				lotDetailsForm.setObject(new Lot());
				lotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			logger.error("ScrapInfoComposite.getLotsByCarrierId(String) error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	  protected String getScrapCode() {
		  String scrapCode = null;
	        try {
	            PrdManager prdManager = Framework.getService(PrdManager.class);
	            if (getLot() != null && getLot().getObjectRrn() != null
	                    && getLot().getStepRrn() != null) {
	                Step step = new Step();
	                step.setObjectRrn(getLot().getStepRrn());
	                step = (Step) prdManager.getSimpleProcessDefinition(step);
	                scrapCode = step.getScrapCodeSrc();
	            } else {
	                scrapCode = DEFAULT_SCRAP_TABLE;
	            }
	            if (scrapCode == null || scrapCode.trim().length() == 0) {
	                scrapCode = DEFAULT_SCRAP_TABLE;
	            }
	        } catch (Exception e) {
	            logger.error("ScrapInfoComposite : getScrapCode() ", e);
	        }
	        return scrapCode;
	  }
		  
	  public void setLot(Lot lot) {
		  
		  this.lot = lot;
	  }

	  public Lot getLot() {
		  return this.lot;
	  }
}
