package com.glory.mes.wip.lot.multicarrier;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierQtyComposite {

	protected final Color ColorBackground = SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE);

	private LotMultiCarrier selectedObj;
	private String currentCarrierId;

	private Composite parent;
	private ADTable adTable;
	private ListTableManager tableManager;

	private String carrierId;

	private boolean indexFlag;
	private boolean checkFlag;
	private boolean editorFlag;
	private boolean showCarrierCurrentQtyFlag;
	private boolean showTotalFlag;

	public Label lblTotal;

	private Map<String, BigDecimal> initSplitMap = new LinkedHashMap<String, BigDecimal>();

	private Map<String, LotMultiCarrier> carrierMap = new LinkedHashMap<String, LotMultiCarrier>();

	public MultiCarrierQtyComposite(Composite parent, boolean indexFlag, boolean checkFlag, boolean editorFlag) {
		this.parent = parent;
		this.indexFlag = indexFlag;
		this.checkFlag = checkFlag;
		this.editorFlag = editorFlag;
	}

	public MultiCarrierQtyComposite(Composite parent, ADTable adTable, boolean indexFlag, boolean checkFlag,
			boolean editorFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.indexFlag = indexFlag;
		this.checkFlag = checkFlag;
		this.editorFlag = editorFlag;
	}

	public MultiCarrierQtyComposite(Composite parent, ADTable adTable, boolean indexFlag, boolean checkFlag,
			boolean editorFlag, boolean showTotalFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.indexFlag = indexFlag;
		this.checkFlag = checkFlag;
		this.editorFlag = editorFlag;
		this.showTotalFlag = showTotalFlag;
	}

	public MultiCarrierQtyComposite(Composite parent, ADTable adTable, boolean indexFlag, boolean checkFlag,
			boolean editorFlag, boolean showCarrierCurrentQtyFlag, boolean showTotalFlag) {
		this.parent = parent;
		this.adTable = adTable;
		this.indexFlag = indexFlag;
		this.checkFlag = checkFlag;
		this.editorFlag = editorFlag;
		this.showCarrierCurrentQtyFlag = showCarrierCurrentQtyFlag;
		this.showTotalFlag = showTotalFlag;
	}

	public void init() {
		if (showTotalFlag) {
			Composite labelCompsite = new Composite(parent, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(1, false));

			lblTotal = new Label(labelCompsite, SWT.NONE);
			lblTotal.setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ": 0    ");
		}
		if (adTable == null) {
			adTable = getDefaultADTable();
		}
		if (editorFlag) {
			tableManager = new ListEditorTableManager(adTable, checkFlag);
		} else {
			tableManager = new ListTableManager(adTable, checkFlag);
		}
		tableManager.setSortFlag(false);
		tableManager.setAutoSizeFlag(true);
		tableManager.setIndexFlag(indexFlag);
		tableManager.newViewer(parent);
		tableManager.setTableBackground(ColorBackground);

		tableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection structuredSelection = (StructuredSelection) event.getSelection();
				selectedObj = (LotMultiCarrier) structuredSelection.getFirstElement();
				if (selectedObj != null) {
					currentCarrierId = selectedObj.getCarrierId();
				}
				changeTotal();
			}
		});
		initLotMultiCarriers(new ArrayList<LotMultiCarrier>());
	}

	public void initLotMultiCarriers(List<LotMultiCarrier> lotMultiCarriers) {
		if (showCarrierCurrentQtyFlag) {
			LotMultiCarrier lotMultiCarrier = new LotMultiCarrier();
			lotMultiCarrier.setCarrierId(carrierId);
			lotMultiCarrier.setCurrentQty(BigDecimal.ZERO);
			lotMultiCarrier.setTransQty(BigDecimal.ZERO);
			if (carrierId != null) {
				carrierMap.put(carrierId, lotMultiCarrier);
			}
		}
		for (LotMultiCarrier lotMultiCarrier : lotMultiCarriers) {
			lotMultiCarrier.setTransQty(BigDecimal.ZERO);
			carrierMap.put(lotMultiCarrier.getCarrierId(), lotMultiCarrier);
		}
		tableManager.setInput(getLotMultiCarriers());
		selectedObj = null;
	}

	public List<LotMultiCarrier> getLotMultiCarriers() {
		List<LotMultiCarrier> carrierList = new ArrayList<LotMultiCarrier>();
		for (LotMultiCarrier comp : carrierMap.values()) {
			carrierList.add(comp);
		}
		return carrierList;
	}

	public void remove(String carrierId) {
		Map<String, LotMultiCarrier> newCarrierMap = new LinkedHashMap<String, LotMultiCarrier>();
		List<LotMultiCarrier> lotMultiCarrierList = new ArrayList<LotMultiCarrier>();
		for (String position : carrierMap.keySet()) {
			LotMultiCarrier lotMultiCarrier = carrierMap.get(position);
			if (lotMultiCarrier.getCarrierId().equals(carrierId)) {
				initSplitMap.remove(carrierId);
			} else {
				newCarrierMap.put(lotMultiCarrier.getCarrierId(), lotMultiCarrier);
				lotMultiCarrierList.add(lotMultiCarrier);
			}
		}
		carrierMap = newCarrierMap;
		tableManager.setInput(lotMultiCarrierList);
		tableManager.refresh();
	}
	
	public void removeAll() {
		carrierMap.clear();
		tableManager.setInput(new ArrayList<LotMultiCarrier>());
		changeTotal();
	}

	public void changeTotal() {
		List<LotMultiCarrier> lotMultiCarriers = (List<LotMultiCarrier>) tableManager.getInput();
		BigDecimal sumQty = lotMultiCarriers.stream()
				.filter(x -> x.getTransQty() != null && x.getTransQty().compareTo(BigDecimal.ZERO) > 0)
				.map(LotMultiCarrier::getTransQty).reduce(BigDecimal.ZERO, BigDecimal::add);
		lblTotal.setText(Message.getString(ExceptionBundle.bundle.CommonTotal()) + ":" + sumQty);
	}

	public void refreshLotMultiCarriers(List<LotMultiCarrier> lotMultiCarriers) {
		for (LotMultiCarrier lotMultiCarrier : lotMultiCarriers) {
			carrierMap.put(lotMultiCarrier.getCarrierId(), lotMultiCarrier);
		}
		tableManager.setInput(getLotMultiCarriers());
		selectedObj = null;
	}

	public void initSplit() {
		initSplitMap.clear();
		List<LotMultiCarrier> lotMultiCarriers = (List<LotMultiCarrier>) tableManager.getInput();
		for (LotMultiCarrier lotMultiCarrier : lotMultiCarriers) {
			initSplitMap.put(lotMultiCarrier.getCarrierId(), lotMultiCarrier.getTransQty());
		}
	}

	public static ADTable getDefaultADTable() {
		ADTable adTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();

		ADField adFieldDurable = new ADField();
		adFieldDurable.setName("carrierId");
		adFieldDurable.setIsMain(true);
		adFieldDurable.setIsDisplay(true);
		adFieldDurable.setDataType(DataType.SEQUENCE);
		adFieldDurable.setLabel(Message.getString("wip.durable"));
		adFieldDurable.setLabel_zh(Message.getString("wip.durable"));
		adFields.add(adFieldDurable);

		ADField adFieldMainQty = new ADField();
		adFieldMainQty.setName("currentQty");
		adFieldMainQty.setIsMain(true);
		adFieldMainQty.setIsDisplay(true);
		adFieldMainQty.setDisplayLength(15l);
		adFieldMainQty.setLabel(Message.getString("aps.monthy_current_qty"));
		adFieldMainQty.setLabel_zh(Message.getString("aps.monthy_current_qty"));
		adFields.add(adFieldMainQty);

		ADField adFieldTransQty = new ADField();
		adFieldTransQty.setName("transQty");
		adFieldTransQty.setIsMain(true);
		adFieldTransQty.setIsDisplay(true);
		adFieldTransQty.setIsEditable(true);
		adFieldTransQty.setDisplayLength(15l);
		adFieldTransQty.setLabel(Message.getString("mm.qty"));
		adFieldTransQty.setLabel_zh(Message.getString("mm.qty"));
		adFieldTransQty.setDataType("integer");
		adFieldTransQty.setDisplayType("text");
		adFields.add(adFieldTransQty);

		adTable.setFields(adFields);

		return adTable;
	}

	public LotMultiCarrier getSelectedObj() {
		return selectedObj;
	}

	public void setSelectedObj(LotMultiCarrier selectedObj) {
		this.selectedObj = selectedObj;
	}

	public ListTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(ListTableManager tableManager) {
		this.tableManager = tableManager;
	}

	public Map<String, LotMultiCarrier> getCarrierMap() {
		return carrierMap;
	}

	public Map<String, BigDecimal> getInitSplitMap() {
		return initSplitMap;
	}

	public String getCurrentCarrierId() {
		return currentCarrierId;
	}

	public void setCurrentCarrierId(String currentCarrierId) {
		this.currentCarrierId = currentCarrierId;
	}

	public String getCarrierId() {
		return carrierId;
	}

	public void setCarrierId(String carrierId) {
		this.carrierId = carrierId;
	}

	public Label getLblTotal() {
		return lblTotal;
	}

	public void setLblTotal(Label lblTotal) {
		this.lblTotal = lblTotal;
	}
}
