package com.glory.mes.wip.lot.multicarrier.changecarrier;


import org.eclipse.swt.widgets.Composite;

import com.glory.mes.wip.lot.multicarrier.MultiCarrierComposite;

public class MultiCarrierChangeCarrierComposite extends MultiCarrierComposite {
	
	public MultiCarrierChangeCarrierComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
	}
}
