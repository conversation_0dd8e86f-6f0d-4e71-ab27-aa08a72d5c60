package com.glory.mes.wip.lot.multicarrier.unscrap;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentAssignComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.mes.wip.model.LotScrap;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiCarrierUnscrapSection extends EntitySection {

	private static final Logger logger = Logger.getLogger(MultiCarrierUnscrapSection.class);
	
	protected static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");
	
	private MultiCarrierUnscrapEditor multiCarrierScrapEditor;
	public ComponentAssignComposite assignComposite;
	protected ManagedForm managedForm;

	protected ToolItem itemSortingSplit;
	protected ToolItem itemSortingSplitAndMerge;
	protected MultiCarrierCompUnscrapComposite multiCarrierComponentComposite;
	protected MultiCarrierQtyUnscrapComposite multiCarrierQtyComposite;
	
	protected ToolItem itemUnScrapByScrap;
	
	protected ScrolledForm sForm;
	protected Composite client;
	
	protected Lot lot;
	
	protected List<LotMultiCarrier> lotMulitCarriers;
	
	private static final String  TABLE_NAME_COMPTYPE = "WIPLotMultiCarrierUnScrapunit";
	private static final String  TABLE_NAME_QTYTYPE = "WIPLotMultiCarrierUnScrapqty";

	public MultiCarrierUnscrapSection(ADTable adTable,MultiCarrierUnscrapEditor multiCarrierScrapEditor) {
		super(adTable);
		this.multiCarrierScrapEditor = multiCarrierScrapEditor;
	}
	

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemUnScrapByScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemUnScrapByScrap(ToolBar tBar) {
		itemUnScrapByScrap = new ToolItem(tBar, SWT.PUSH);
		itemUnScrapByScrap.setEnabled(false);
		itemUnScrapByScrap.setText(Message.getString("wip.unscrap_lot_by_scrap"));
		itemUnScrapByScrap.setImage(SWTResourceCache.getImage("unscrap"));
		itemUnScrapByScrap.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				unScrapByScrapAdapter(event);
			}
		});
	}
	
	/**
	 * ȡ�����ϸ�������unit����ʹ�ò�ͬ��ȡ�����Ϸ���
	 * */
	protected void unScrapByScrapAdapter(SelectionEvent event) {
		if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
			unScrapComponentTypeByScrap();
		}else {
			unScrapQtyTypeByScrap();
		}
	}
	
	/**
	 * ȡ������Component����
	 * */
	protected void unScrapComponentTypeByScrap() {
		try {
			if (multiCarrierComponentComposite.validat()) {
				//��ȡ��Ҫȡ�����ϵ����
				ADManager adManager = Framework.getService(ADManager.class);
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				
				List<LotUnScrapAction> unScrapActions = new ArrayList<LotUnScrapAction>();
				Map<String, List<ComponentUnit>> targetComponentUnitMap = new HashMap<String,List<ComponentUnit>>();
				
				List<Object> checkedObject = multiCarrierComponentComposite.getTableManager().getCheckedObject();
				for (Object object : checkedObject) {
					ComponentUnit component =(ComponentUnit)object;
					if (StringUtil.isEmpty(component.getDurable())) {
						UI.showInfo(component.getComponentId() + Message.getString("wip.durable") + Message.getString(ExceptionBundle.bundle.CommonIsNotExist()));
						return;
					}
					if (StringUtil.isEmpty(component.getPosition())) {
						UI.showInfo(component.getComponentId() + Message.getString("wip.component_position") + Message.getString(ExceptionBundle.bundle.CommonIsNotExist()));
						return;
					}
					LotUnScrapAction unAction = new LotUnScrapAction();
					unAction.setActionCode(component.getActionCode());
					List<LotScrap> lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class, 1,
							" componentRrn = " + component.getObjectRrn(), null);
					LotScrap lotScrap = lotScraps.get(0);
					lotScrap.setUnScrapCode(component.getActionCode());
					lotScrap.setUnScrapMainQty(lotScrap.getMainQty());
					lotScrap.setUnScrapSubQty(component.getSubQty());
					unAction.setLotScrap(lotScrap);
					
					// Ŀ���ؾ���comp�б�
					if (component.getDurable() != null && !"".equals(component.getDurable())) {
						if (targetComponentUnitMap.containsKey(component.getDurable())) {
							List<ComponentUnit> durableComponents = targetComponentUnitMap.get(component.getDurable());
							targetComponentUnitMap.put(component.getDurable(), durableComponents);
							component.setDurable(null);
							durableComponents.add(component);
						} else {
							List<ComponentUnit> durableComponents = new ArrayList<ComponentUnit>();
							targetComponentUnitMap.put(component.getDurable(), durableComponents);
							component.setDurable(null);
							durableComponents.add(component);
						}
					}
					unAction.setActionUnits(Lists.newArrayList(component));
					unScrapActions.add(unAction);
				}
				ComponentUnit unit0 =(ComponentUnit)checkedObject.get(0);
				LotAction action = new LotAction();
				action.setActionCode(unit0.getActionCode());
				
				lot = carrierLotManager.unScrapMultiCarrierLot(lot, unScrapActions, action,  null, targetComponentUnitMap, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.unscrapLot_success"));
				reload();
			}
		} catch (Exception e) {
			logger.error("Error at MultiCarrierUnscrapSection.unScrapComponentTypeByScrap()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	/**
	 * ȡ������QTY����
	 * 
	 * */
	protected void unScrapQtyTypeByScrap() {
		try {
			if (multiCarrierQtyComposite.validat()) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotScrap> inputList = (List<LotScrap>)multiCarrierQtyComposite.getTableManager().getInput();
				//����ȡ�������б�
				inputList = filteLotScrap(inputList);
				if (inputList != null && inputList.size() > 0) {
					List<LotUnScrapAction> unScrapActions = new ArrayList<LotUnScrapAction>();
					List<LotMultiCarrier> targetLotMultiCarrierList = new ArrayList<LotMultiCarrier>();
					for (LotScrap lotScrap : inputList) {
						LotUnScrapAction unScrapAction = new LotUnScrapAction();
						lotScrap.setUnScrapCode(lotScrap.getActionCode());
						lotScrap.setUnScrapMainQty(new BigDecimal(lotScrap.getAttribute2().toString()));
						unScrapAction.setLotScrap(lotScrap);
						unScrapActions.add(unScrapAction);
						if (StringUtil.isEmpty((String) lotScrap.getAttribute1())) {
							UI.showInfo(lotScrap.getComponentId() + Message.getString("wip.durable") + Message.getString(ExceptionBundle.bundle.CommonIsNotExist()));
							return;
						}
						String whereClause = " lotRrn = " + lot.getObjectRrn() + " AND carrierId = '" + lotScrap.getAttribute1()  + "'";
						List<LotMultiCarrier> targetLotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class, Integer.MAX_VALUE, whereClause, null);
						LotMultiCarrier targetLotMultiCarrier = null;
						BigDecimal assignQty = BigDecimal.valueOf(Long.parseLong((String) lotScrap.getAttribute2()));
						if (targetLotMultiCarriers != null && !targetLotMultiCarriers.isEmpty()) {
							targetLotMultiCarrier = targetLotMultiCarriers.get(0);
							targetLotMultiCarrier.setTransQty(assignQty);
						} else {
							targetLotMultiCarrier = new LotMultiCarrier();
							targetLotMultiCarrier.setOrgRrn(Env.getOrgRrn());
							targetLotMultiCarrier.setCarrierId((String) lotScrap.getAttribute1());
							targetLotMultiCarrier.setLotRrn(lot.getObjectRrn());
							targetLotMultiCarrier.setCurrentQty(BigDecimal.ZERO);
							targetLotMultiCarrier.setTransQty(assignQty);
							targetLotMultiCarrier.setIsVirtual(false);
						}
						targetLotMultiCarrierList.add(targetLotMultiCarrier);
					}
					LotAction lotAction = new LotAction();
					lotAction.setActionCode(inputList.get(0).getActionCode());
					lot = carrierLotManager.unScrapMultiCarrierLot(lot, unScrapActions, lotAction,  targetLotMultiCarrierList, null, Env.getSessionContext());
					reload();
					UI.showInfo(Message.getString("wip.unscrapLot_success"));
				}
			}
			
		} catch (Exception e) {
			logger.error("Error at MultiCarrierUnscrapSection.unScrapQtyTypeByScrap()",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ����Qty���ͱ����У�δ��д��������Ϊ0��
	 * */
	private List<LotScrap> filteLotScrap(List<LotScrap> lotScrapList){
		List<LotScrap> finalScrapList = new ArrayList<LotScrap>();
		for (LotScrap lotScrap : lotScrapList) {
			if (lotScrap.getAttribute2() != null) {
				BigDecimal scrapQty = new BigDecimal(lotScrap.getAttribute2().toString());
				if (scrapQty.compareTo(BigDecimal.ZERO) > 0) {
					finalScrapList.add(lotScrap);
				}
			}
		}
		return finalScrapList;
	}
	
	
	@Override
	protected void createSectionContent(Composite parent) {
		try {
			this.client = parent;
			FormToolkit toolkit = form.getToolkit();
			sForm = toolkit.createScrolledForm(client);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			managedForm = new ManagedForm(toolkit, sForm);
			Composite body = sForm.getForm().getBody();
			configureBody(body);
			if (lot != null) {
				buildComposite(lot.getSubUnitType(),body);
			}
			
		} catch (Exception e) {
			logger.error("Error at MultiCarrierUnscrapSection.createSectionContent(Composite)",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void buildComposite(String unitType,Composite body) {
		try {
			
			ADManager adManager = Framework.getService(ADManager.class);
			if (unitType.equals(Lot.UNIT_TYPE_COMPONENT)) {
				
				ADTable compAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_COMPTYPE);
				multiCarrierComponentComposite = new MultiCarrierCompUnscrapComposite(body, compAdTable, 25, true, true, false, true,this.multiCarrierScrapEditor);
				multiCarrierComponentComposite.init();
			}else {
				ADTable qtyAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_QTYTYPE);
				multiCarrierQtyComposite = new MultiCarrierQtyUnscrapComposite(body, qtyAdTable, true, false, true,true,multiCarrierScrapEditor);
				multiCarrierQtyComposite.init();
			}
			itemUnScrapByScrap.setEnabled(true);
		} catch (Exception e) {
			logger.error("Error at MultiCarrierUnscrapSection.buildComposite(String, Composite)",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	public void reflow() {
		if (sForm != null && !sForm.isDisposed()) {
			sForm.dispose();
			sForm = null;
		}
		createSectionContent(client);
		client.layout();
	}
	
	public void setLotObject(Lot lot) {
		
		this.lot = lot;
	}
	
	/**
	 * �����������ʹ����Ҳ��б�����
	 * 
	 * */
	public void buildScrapUnitByLotUnitType(){
		if (lot != null) {
			if (lot.getSubUnitType().equals(Lot.UNIT_TYPE_COMPONENT)) {
				buildScrapCompUnit();
			}else {
				buildScrapQty();
			}
		}
	}
	
	
	
	/**
	 * 
	 * ������Ƭ�����б�
	 * */
	private void buildScrapCompUnit() {
		try {
			List<ComponentUnit> subProcessUnit = new ArrayList<ComponentUnit>();
			LotManager lotManager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			List<LotScrap> lotScraps = lotManager.getLotScrap(lot, Env.getSessionContext());
			for (LotScrap lotScrap : lotScraps) {
				ComponentUnit componentUnit = new ComponentUnit();
				componentUnit.setObjectRrn(lotScrap.getComponentRrn());
				componentUnit = (ComponentUnit) adManager.getEntity(componentUnit);
				componentUnit.setAttribute2(lotScrap.getActionCode());
				componentUnit.setAttribute3(lotScrap.getActionComment());
				componentUnit.setActionCode(null);
				// ȡ������ʱ��Ҫ�ҵ�ԭ���󶨵��ؾ�
				if (lotScrap.getActionReason() != null && lotScrap.getActionReason().contains("-")) {
					String positon = lotScrap.getActionReason().substring(lotScrap.getActionReason().lastIndexOf("-") + 1);
					String durable = lotScrap.getActionReason().substring(0, lotScrap.getActionReason().lastIndexOf("-"));
					componentUnit.setDurable(durable);
					componentUnit.setPosition(positon);
				}
				subProcessUnit.add(componentUnit);
			}
			multiCarrierComponentComposite.getTableManager().setInput(subProcessUnit);
		} catch (Exception e) {
			logger.error("Error at MultiCarrierUnscrapSection.buildScrapCompUnit()",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * �������������������б�
	 * */
	private void buildScrapQty() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			List<LotScrap> lotScraps = lotManager.getLotScrap(lot, Env.getSessionContext());
			// ȡ������ʱ��Ҫ�ҵ�ԭ���󶨵��ؾ�
			for (LotScrap lotScrap : lotScraps) {
				lotScrap.setAttribute1(lotScrap.getActionReason());
			}
			multiCarrierQtyComposite.getTableManager().setInput(lotScraps);
		} catch (Exception e) {
			logger.error("Error at MultiCarrierUnscrapSection.buildScrapQty()",e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public MultiCarrierUnscrapEditor getMultiCarrierScrapEditor() {
		return multiCarrierScrapEditor;
	}

	public void setMultiCarrierScrapEditor(MultiCarrierUnscrapEditor multiCarrierScrapEditor) {
		this.multiCarrierScrapEditor = multiCarrierScrapEditor;
	}
	
	public void reload() {
		multiCarrierScrapEditor.multiCarrierComposite.refresh();
		buildScrapUnitByLotUnitType();
	}
	
	/*public void reload() {
		multiCarrierScrapEditor.multiCarrierComposite.getLotMultiCarriers(lot.getLotId());
		reflow();
	}*/

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;		
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
