package com.glory.mes.wip.lot.multicarrier.changecarrier;

import org.eclipse.swt.widgets.Composite;

import com.glory.mes.wip.lot.multicarrier.split.MultiCarrierSplitTargetComposite;

public class MultiCarrierChangeCarrierTargetComposite extends MultiCarrierSplitTargetComposite {

	public MultiCarrierChangeCarrierTargetComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
	}
}
