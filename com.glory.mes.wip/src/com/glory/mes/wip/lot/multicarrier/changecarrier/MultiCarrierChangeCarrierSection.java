package com.glory.mes.wip.lot.multicarrier.changecarrier;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComponentAssignComposite;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComponentComposite.DiffType;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierQtyAssignComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.glory.mes.wip.model.LotStateMachine;

public class MultiCarrierChangeCarrierSection extends EntitySection {

	private MultiCarrierChangeCarrierEditor multiCarrierChangeCarrierEditor;

	public MultiCarrierComponentAssignComposite assignComposite;
	public MultiCarrierQtyAssignComposite qtyAssignComposite;

	protected ToolItem itemReassign;
	protected MultiCarrierChangeCarrierTargetComposite targetMultiCarrierLotComposite;
	protected Composite parent;

	public MultiCarrierChangeCarrierSection(ADTable adTable) {
		super(adTable);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemReassign(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemReassign(ToolBar tBar) {
		itemReassign = new ToolItem(tBar, SWT.PUSH);
		itemReassign.setText(Message.getString("wip.change_button"));
		itemReassign.setImage(SWTResourceCache.getImage("assign"));
		itemReassign.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				reassignAdapter();
			}
		});
	}

	protected void reassignAdapter() {
		Lot lot = (Lot) getAdObject();

		if (!LotStateMachine.STATE_WAIT.equals(lot.getState()) && !LotStateMachine.STATE_FIN.equals(lot.getState())) {
			throw new ClientException("error.state_not_allow");
		}
		if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
			splitQty(lot);
		} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
			splitComponent(lot);
		}
	}

	// QTY���ͱ��
	@SuppressWarnings("unchecked")
	protected void splitQty(Lot lot) {
		try {
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			DurableManager durableManager = Framework.getService(DurableManager.class);

			List<LotMultiCarrier> targetLotMultiCarrierList = new ArrayList<LotMultiCarrier>();
			List<LotMultiCarrier> sourceLotMultiCarrierList = new ArrayList<LotMultiCarrier>();

			BigDecimal sourceTranQty = BigDecimal.ZERO;
			if (qtyAssignComposite.sourceQtyComposite.getTableManager().getInput() != null
					&& qtyAssignComposite.sourceQtyComposite.getTableManager().getInput().size() != 0) {
				for (LotMultiCarrier lotMultiCarrier : (List<LotMultiCarrier>) qtyAssignComposite.sourceQtyComposite
						.getTableManager().getInput()) {
					if (lotMultiCarrier.getTransQty().compareTo(BigDecimal.ZERO) < 0) {
						UI.showError(lotMultiCarrier.getCarrierId()
								+ Message.getString("wip.remainder_can_not_less_zero"));
						return;
					}
					if (lotMultiCarrier.getTransQty().compareTo(lotMultiCarrier.getCurrentQty()) > 0) {
						UI.showError(lotMultiCarrier.getCarrierId()
								+ Message.getString("wip.remainder_is_bigger_than_current_qty"));
						return;
					}
					LotMultiCarrier sourceLotMultiCarrier = (LotMultiCarrier) lotMultiCarrier.clone();
					sourceLotMultiCarrier.setObjectRrn(lotMultiCarrier.getObjectRrn());
					sourceLotMultiCarrier.setTransQty(lotMultiCarrier.getCurrentQty().subtract(lotMultiCarrier.getTransQty()));
					
					sourceTranQty = sourceTranQty.add(sourceLotMultiCarrier.getTransQty());
					sourceLotMultiCarrierList.add(sourceLotMultiCarrier);
				}
			} else {
				UI.showError(Message.getString("wip.source_list_is_empty"));
				return;
			}

			if (qtyAssignComposite.targetQtyComposite.getTableManager().getInput() != null
					&& qtyAssignComposite.targetQtyComposite.getTableManager().getInput().size() != 0) {
				BigDecimal targetTranQty = BigDecimal.ZERO;
				for (LotMultiCarrier targetLotMultiCarrier : (List<LotMultiCarrier>) qtyAssignComposite.targetQtyComposite
						.getTableManager().getInput()) {
					Carrier targetCarrier = durableManager.getCarrierById(Env.getOrgRrn(),
							targetLotMultiCarrier.getCarrierId());
					if (targetCarrier == null) {
						UI.showWarning(Message.getString("mm.carrier_is_not_exist"));
						return;
					}
					BigDecimal availableQty = targetCarrier.getCapacity()
							.subtract(targetLotMultiCarrier.getCurrentQty());
					if (targetLotMultiCarrier.getTransQty().compareTo(BigDecimal.ZERO) > 0) {
						if (targetLotMultiCarrier.getTransQty().compareTo(availableQty) > 0) {
							UI.showError(targetLotMultiCarrier.getCarrierId()
									+ Message.getString("wip.change_qty_more_than_carrier_avaliable_qty"));
							return;
						} else {
							targetTranQty = targetTranQty.add(targetLotMultiCarrier.getTransQty());
							targetLotMultiCarrierList.add(targetLotMultiCarrier);
						}
					}
				}
				if (sourceTranQty.compareTo(targetTranQty) != 0) {
					UI.showError(Message.getString("wip.target_change_qty_is_not_equal_source _qty"));
					return;
				}
			} else {
				UI.showError(Message.getString("wip.target_list_is_empty"));
				return;
			}
			
			lot = carrierLotManager.reassignMultiCarrierLotQty(lot, lot, sourceLotMultiCarrierList, targetLotMultiCarrierList, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.change_successed"));
			setAdObject(lot);
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// COMPONENT���ͱ��
	protected void splitComponent(Lot lot) {
		try {
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			Map<String, List<ComponentUnit>> sourceComponentUnitMap = new LinkedHashMap<String, List<ComponentUnit>>();
			Map<String, List<ComponentUnit>> targetComponentUnitMap = new LinkedHashMap<String, List<ComponentUnit>>();

			List<ComponentUnit> addComponents = assignComposite.targetComponentComposite.getComponentsDiff()
					.get(DiffType.ADD);
			if (!assignComposite.sourceLot.getObjectRrn().equals(lot.getObjectRrn())) {
				UI.showInfo(Message.getString("wip.source_lot_component_not_match"));
			}
			if (addComponents != null && addComponents.size() > 0) {
				// ԭ���ؾ���comp�б�
				sourceComponentUnitMap = assignComposite.sourceComponentComposite.getSourceComponents();

				// Ŀ���ؾ���comp�б�
				for (ComponentUnit component : addComponents) {
					if (component.getDurable() != null && !"".equals(component.getDurable())) {
						if (targetComponentUnitMap.containsKey(component.getDurable())) {
							List<ComponentUnit> durableComponents = targetComponentUnitMap.get(component.getDurable());
					
							durableComponents.add(component);
							targetComponentUnitMap.put(component.getDurable(), durableComponents);
						} else {
							List<ComponentUnit> durableComponents = new ArrayList<ComponentUnit>();
							durableComponents.add(component);
							targetComponentUnitMap.put(component.getDurable(), durableComponents);
						}
					}
				}
			} else {
				UI.showError(Message.getString("wip.need_add_assign_component"));
				return;
			}
			lot = carrierLotManager.reassignMultiCarrierLotComp(lot, sourceComponentUnitMap, targetComponentUnitMap, true,
					Env.getSessionContext());
			UI.showInfo(Message.getString("wip.change_successed"));
			setAdObject(lot);
			refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void refresh() {
		Lot lot = (Lot) getAdObject();
		multiCarrierChangeCarrierEditor.sourceMultiCarrierLotComposite.getLotMultiCarriers(lot.getLotId());
		multiCarrierChangeCarrierEditor.changeSourceLot(lot.getLotId());
	}

	@Override
	protected void createSectionContent(Composite parent) {
		try {
			this.parent = parent;
			if (getAdObject() != null) {
				Lot lot = (Lot) getAdObject();
				if (Lot.UNIT_TYPE_QTY.equals(lot.getSubUnitType())) {
					qtyAssignComposite = new MultiCarrierQtyAssignComposite(parent, SWT.NONE, true, true);
				} else if (Lot.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
					assignComposite = new MultiCarrierComponentAssignComposite(parent, SWT.NONE, true, true);
				}
				itemReassign.setEnabled(true);
			} else {
				itemReassign.setEnabled(false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public MultiCarrierChangeCarrierEditor getMultiCarrierChangeCarrierEditor() {
		return multiCarrierChangeCarrierEditor;
	}

	public void setMultiCarrierChangeCarrierEditor(MultiCarrierChangeCarrierEditor multiCarrierChangeCarrierEditor) {
		this.multiCarrierChangeCarrierEditor = multiCarrierChangeCarrierEditor;
	}

	public MultiCarrierComponentAssignComposite getAssignComposite() {
		return assignComposite;
	}

	public void setAssignComposite(MultiCarrierComponentAssignComposite assignComposite) {
		this.assignComposite = assignComposite;
	}

	public MultiCarrierQtyAssignComposite getQtyAssignComposite() {
		return qtyAssignComposite;
	}

	public void setQtyAssignComposite(MultiCarrierQtyAssignComposite qtyAssignComposite) {
		this.qtyAssignComposite = qtyAssignComposite;
	}

	public void reflow() {
		if (assignComposite != null && !assignComposite.isDisposed()) {
			assignComposite.dispose();
			assignComposite = null;
		}
		if (qtyAssignComposite != null && !qtyAssignComposite.isDisposed()) {
			qtyAssignComposite.dispose();
			qtyAssignComposite = null;
		}
		createSectionContent(parent);
		parent.layout();
	}
}
