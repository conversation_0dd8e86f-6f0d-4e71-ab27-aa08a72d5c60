package com.glory.mes.wip.lot.multicarrier.split;


import org.eclipse.swt.widgets.Composite;

import com.glory.mes.wip.lot.multicarrier.MultiCarrierComposite;

public class MultiCarrierSplitComposite extends MultiCarrierComposite {
	
	public MultiCarrierSplitComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
	}
}
