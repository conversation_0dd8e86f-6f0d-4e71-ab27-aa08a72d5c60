package com.glory.mes.wip.lot.multicarrier.split;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.multicarrier.MultiCarrierComposite;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.model.LotMultiCarrier;

public class MultiCarrierSplitTargetComposite extends MultiCarrierComposite {

	public MultiCarrierSplitTargetComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
	}

	@SuppressWarnings("unchecked")
	@Override
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);

			List<LotMultiCarrier> newLotMultiCarriers = new ArrayList<LotMultiCarrier>();
			if (lotTableManager.getInput() != null) {
				newLotMultiCarriers.addAll((List<LotMultiCarrier>) lotTableManager.getInput());
				newLotMultiCarriers = newLotMultiCarriers.stream().filter(x -> !carrierId.equals(x.getCarrierId()))
						.collect(Collectors.toList());
			}

			if (carrier != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				String whereClause = " carrierId = '" + carrierId + "'";
				List<LotMultiCarrier> lotMultiCarriers = adManager.getEntityList(Env.getOrgRrn(), LotMultiCarrier.class,
						Integer.MAX_VALUE, whereClause, null);
				LotMultiCarrier lotMultiCarrier;
				if (lotMultiCarriers.size() > 0) {
					newLotMultiCarriers.add(lotMultiCarrier = lotMultiCarriers.get(0));
				} else {
					lotMultiCarrier = new LotMultiCarrier();
					lotMultiCarrier.setCarrierId(carrierId);
					lotMultiCarrier.setCurrentQty(BigDecimal.ZERO);
					newLotMultiCarriers.add(lotMultiCarrier);
				}
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
			} else {
				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
			lotTableManager.setInput(newLotMultiCarriers);
			lotTableManager.refresh();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@SuppressWarnings("unchecked")
	public void removeCarrier(String carrierId) {
		try {
			List<Object> objs = (List<Object>) lotTableManager.getInput();
			for (Object obj : objs) {
				LotMultiCarrier lotMultiCarrier = (LotMultiCarrier) obj;
				if (lotMultiCarrier.getCarrierId().equals(carrierId)) {
					lotTableManager.remove(obj);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
