package com.glory.mes.wip.lot.multicarrier.merge;


import org.eclipse.swt.widgets.Composite;

import com.glory.mes.wip.lot.multicarrier.MultiCarrierComposite;
import com.glory.mes.wip.model.Lot;

public class MultiCarrierMergeComposite extends MultiCarrierComposite {
	
	public Lot lot;
	
	private boolean isSearchByCarrierId;
	
	public MultiCarrierMergeComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag, boolean isSearchByCarrierId) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
		this.isSearchByCarrierId = isSearchByCarrierId;
	}
	
	public MultiCarrierMergeComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag,
			boolean showDetailFlag, boolean showOperatorFlag, boolean showLotIdFirstFlag, int tableHeigthHint, boolean isSearchByCarrierId) {
		super(parent, style, checkFlag, showLotFlag, showDetailFlag, showOperatorFlag, showLotIdFirstFlag, tableHeigthHint);
		this.isSearchByCarrierId = isSearchByCarrierId;
	}
		
	@Override
	public void getLotsByCarrierId(String carrierId) {
		if (isSearchByCarrierId) {
			super.getLotsByCarrierId(carrierId);
		}
	}
	
	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}
}
