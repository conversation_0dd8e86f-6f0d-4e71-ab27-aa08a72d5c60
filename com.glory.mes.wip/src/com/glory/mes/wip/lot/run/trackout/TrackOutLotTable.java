package com.glory.mes.wip.lot.run.trackout;

import java.util.Arrays;
import java.util.List;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.row.CheckBoxFixRowEditorTableManager;

public class TrackOutLotTable extends CheckBoxFixRowEditorTableManager {
	
	public TrackOutLotTable(ADTable adTable) {
		super(adTable);
	}
	
	@Override
	public void setInput(List<? extends Object> input) {
		
		List<TrackOutLot> curOutLot = (List<TrackOutLot>)input;
		for (int i = 0; i < curOutLot.size(); i++) {
			if (!curOutLot.get(i).getIsRework()) {
				addUnEditorCells(i, Arrays.asList(TrackOutStartPage.FIELD_REWORKMAINQTY));
			}
			
		}
		super.setInput(input);
	}
	
}
