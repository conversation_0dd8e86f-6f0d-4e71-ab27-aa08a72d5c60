package com.glory.mes.wip.lot.run.trackin.consumablematerial;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.base.ui.util.Message;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.pp.model.WorkOrderBomLine;

public class ConsumeEqpMaterialPage extends ConsumeStepMaterialPage {

	public void setPageTitle() {
		setTitle(Message.getString("wip.raw_material_and_tool_info"));
		setMessage(Message.getString("wip.eqpattach_raw_material_and_tool_info"));
	}
	
	public void createContent(Composite composite, List<WorkOrderBomLine> bomLines) {
		try {
			List<WorkOrderBomLine> stepMaterialBomLines = new ArrayList<WorkOrderBomLine>();
			List<WorkOrderBomLine> stepToolBomLines = new ArrayList<WorkOrderBomLine>();
			for (WorkOrderBomLine bomLine : bomLines) {
				if (BomLine.ITEMCATEGORY_TOOLS.equals(bomLine.getItemCategory())) {
					stepToolBomLines.add(bomLine);
				} else {
					stepMaterialBomLines.add(bomLine);
				}
			}
			if (stepToolBomLines.size() > 0) {
				ShowAttachToolForm showAttachToolForm = new ShowAttachToolForm(composite, SWT.NONE, context);
				forms.add(showAttachToolForm);
			}
			if (stepMaterialBomLines.size() > 0) {
				ConumableEqpMatreialForm conumableMatreialForm = new ConumableEqpMatreialForm(composite, SWT.NONE, context);
				forms.add(conumableMatreialForm);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
