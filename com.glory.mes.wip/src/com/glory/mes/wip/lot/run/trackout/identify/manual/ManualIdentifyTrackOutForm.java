package com.glory.mes.wip.lot.run.trackout.identify.manual;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

public class ManualIdentifyTrackOutForm extends EntityForm {

	protected static final String PROPERTY_ID = "scheduleSources";
	private Lot lot;
	private List<ComponentUnit> componentUnitList = new ArrayList<ComponentUnit>();
	private ListEditorTableManager tableManager;

	public ManualIdentifyTrackOutForm(Composite parent, int style, Object object,
			IMessageManager mmng) {
		super(parent, style, object, mmng);
		this.lot = (Lot)object;
		this.createForm();
	}

	@Override
	public void createForm() {
		try {
			super.createForm();
			Composite body = form.getBody();
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "WIPComponentUnitManual");
			tableManager = new ListEditorTableManager(adTable, false);
			tableManager.newViewer(body);
			
			for (int i = 0; i < lot.getMainQty().intValue(); i++) {
				ComponentUnit component = new ComponentUnit();
				component.setIsActive(true);
				component.setCreatedBy(Env.getUserName());
				component.setCreated(new Date());
				component.setOrgRrn(lot.getOrgRrn());
				component.setParentUnitRrn(lot.getObjectRrn());
				component.setPosition(String.valueOf((i+1)));
				component.setMainQty(new BigDecimal(1));
				componentUnitList.add(component);
			}
			tableManager.setInput(componentUnitList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public List<ComponentUnit> getComponentUnitList() {
		return componentUnitList;
	}

}
