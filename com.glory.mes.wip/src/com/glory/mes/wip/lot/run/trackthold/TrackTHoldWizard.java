package com.glory.mes.wip.lot.run.trackthold;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;

public class TrackTHoldWizard extends TrackOutWizard {

	private static final Logger logger = Logger.getLogger(TrackTHoldWizard.class);

    public TrackTHoldWizard() {
	}
	
	public TrackTHoldWizard(TrackOutContext context) {
		super(context);
	}

	public void invokeTrackOut() throws Exception {
		TrackOutContext context = (TrackOutContext)this.getContext();
		for (Lot lot : context.getOutLots()) {
			lot.setOperator1(context.getOperator1());
		}
		InContext inContext = context.getInContent();
		inContext.setLots(context.getOutLots());
		inContext.setAttributeValues(context.getLotAttributeValues());
		inContext.setOperator1(context.getOperator1());
		
		LotManager lotManager = Framework.getService(LotManager.class);
		OutContext outContext = lotManager.trackTHoldLot(inContext, Env.getSessionContext());
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll(outContext.getLots());
		lots.addAll(outContext.getReworkLots());
		context.setOutLots(lots);
	}
}
