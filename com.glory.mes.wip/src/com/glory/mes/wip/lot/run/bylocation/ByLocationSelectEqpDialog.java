package com.glory.mes.wip.lot.run.bylocation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.Location;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.RASColor;

public class ByLocationSelectEqpDialog extends BaseTitleDialog {
	
	private Location location;
	private Location subLocation;
	
	private Map<String, ByLocationEqpLed> eqpLedMap = new HashMap<String, ByLocationEqpLed>();

	private Text eqpId;
	private Text eqpState;
	private Text holdState;
	
	private Equipment currentEqp;
	
	public ByLocationSelectEqpDialog(Shell parentShell, Location location, Location subLocation) {
		super(parentShell);
		this.location = location;
		this.subLocation = subLocation;
	}
	
	@Override
	public Control buildView(Composite parent) {
		setTitle(Message.getString("wip.location_eqp"));
		setMessage(Message.getString("wip.please_select_run_eqp"));
		
		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite runComp = toolkit.createComposite(parent, SWT.NONE);
		configureBody(runComp);
		runComp.setBackgroundMode(SWT.INHERIT_FORCE);

		Composite container = toolkit.createComposite(runComp, SWT.NULL);
		GridData gd = new GridData(GridData.FILL_BOTH);
		container.setLayoutData(gd);
		GridLayout gl = new GridLayout(1, false);
		gl.horizontalSpacing = 30;
		gl.marginWidth = 0;
		gl.marginLeft = 5;
		gl.marginRight = 0;
		container.setLayout(gl);
		
		Set<String> states = RASColor.getStates();
		Composite stateContainer = toolkit.createComposite(container, SWT.NONE);
		gl = new GridLayout(14, false);
		gl.horizontalSpacing = 10;
		stateContainer.setLayout(gl);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.grabExcessHorizontalSpace = false;
		stateContainer.setLayoutData(gd);

		GridData gdStateColor = new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1);
		gdStateColor.minimumHeight = 30;
		gdStateColor.minimumWidth = 40;
		for (String state : states) {
			Label lblState = toolkit.createLabel(stateContainer, state, SWT.NONE);
			lblState.setAlignment(SWT.LEFT);
			Label lblStateColor = toolkit.createLabel(stateContainer, " ", SWT.NONE);
			lblStateColor.setLayoutData(gdStateColor);
			lblStateColor.setBackground(RASColor.getColor(state));
		}
		
		ScrolledComposite eqpContainer = new ScrolledComposite(container, SWT.BORDER | SWT.V_SCROLL);
		configureBody(eqpContainer);
		Composite eqpBoard = toolkit.createComposite(eqpContainer, SWT.NONE);
		gl = new GridLayout(1, false);
		eqpBoard.setLayout(gl);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.grabExcessHorizontalSpace = true;
		eqpBoard.setLayoutData(gd);
		
		try {
			RASManager rasManager = Framework.getService(RASManager.class);
			List<Equipment> equipments = new ArrayList<>();
			if (subLocation == null) {
				equipments = rasManager.getEquipmentsByLocationId(Env.getOrgRrn(), location.getName());
			} else {
				equipments = rasManager.getEquipmentsBySubLocationId(Env.getOrgRrn(), subLocation.getName());
			} 
			if (CollectionUtils.isNotEmpty(equipments)) {
				// ȥ�����豸
				equipments = equipments.stream().filter(e -> e.getParentEqpRrn() == null).collect(Collectors.toList());
			}
			
			if (CollectionUtils.isNotEmpty(equipments)) {
				Map<String, List<Equipment>> eqpMap = equipments.stream().collect(Collectors.groupingBy(Equipment::getEqpType));
				for (String eqpType : eqpMap.keySet()) {
					Composite typeComp = toolkit.createComposite(eqpBoard, SWT.NONE);
					gl = new GridLayout(1, false);
					typeComp.setLayout(gl);
					gd = new GridData(GridData.FILL_HORIZONTAL);
					gd.grabExcessHorizontalSpace = true;
					typeComp.setLayoutData(gd);
					toolkit.createLabel(typeComp, eqpType);
					
					Composite eqpComp = toolkit.createComposite(eqpBoard, SWT.NONE);
					gl = new GridLayout(5, false);
					eqpComp.setLayout(gl);
					gd = new GridData(GridData.FILL);
					eqpComp.setLayoutData(gd);
					for(Equipment eqp : eqpMap.get(eqpType)){
						ByLocationEqpLed eqpLed = new ByLocationEqpLed(eqpComp, eqp, this);
						eqpLed.stateChanged(eqp);
						eqpLedMap.put(eqp.getEquipmentId(), eqpLed);
					}
				}
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		eqpContainer.setContent(eqpBoard);
		eqpContainer.setExpandHorizontal(true);
		eqpContainer.setExpandVertical(true);
		eqpContainer.setMinSize(eqpBoard.computeSize(SWT.DEFAULT, SWT.DEFAULT));
		
		Composite eqpInfo = toolkit.createComposite(container, SWT.NONE);
		gl = new GridLayout(6, false);
		eqpInfo.setLayout(gl);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.grabExcessHorizontalSpace = true;
		eqpInfo.setLayoutData(gd);
		
		GridData txtGd = new GridData(GridData.FILL_HORIZONTAL);
		txtGd.grabExcessHorizontalSpace = true;
		
		toolkit.createLabel(eqpInfo, Message.getString("wip.selecteqp_title"));
		eqpId = toolkit.createText(eqpInfo, "");
		eqpId.setLayoutData(txtGd);
		eqpId.setEnabled(false);
		
		toolkit.createLabel(eqpInfo, Message.getString("wip.eqp_status"));
		eqpState = toolkit.createText(eqpInfo, "");
		eqpState.setLayoutData(txtGd);
		eqpState.setEnabled(false);
		
		toolkit.createLabel(eqpInfo, Message.getString("wip.hold_status"));
		holdState = toolkit.createText(eqpInfo, "");
		holdState.setLayoutData(txtGd);
		holdState.setEnabled(false);
		return runComp;
	}
	
	public void notifyEquipmentChangeListeners(ByLocationEqpLed byLocationEqpLed, Equipment equipment) {
		if (MapUtils.isNotEmpty(eqpLedMap)) {
			for (String eqpId : eqpLedMap.keySet()) {
				if (!eqpId.equals(equipment.getEquipmentId())) {
					ByLocationEqpLed eqpLed = eqpLedMap.get(eqpId);
					if (eqpLed.isSelected()) {
						eqpLed.setSelected(false);
					}
				}
			}
		}
		
		eqpId.setText(equipment.getEquipmentId());
		eqpState.setText(equipment.getState());
		holdState.setText(equipment.getHoldState());
		
		this.currentEqp = equipment;
	}
	
	@Override
	protected void okPressed() {
		if (currentEqp == null) {
			UI.showInfo(Message.getString("wip.please_select_main_eqp"));
			return;
		}
		super.okPressed();
	}
	
	protected Point getInitialSize() {
		return new Point(1200, 800);
	}

	public Equipment getCurrentEqp() {
		return currentEqp;
	}

	public void setCurrentEqp(Equipment currentEqp) {
		this.currentEqp = currentEqp;
	}
	
}
