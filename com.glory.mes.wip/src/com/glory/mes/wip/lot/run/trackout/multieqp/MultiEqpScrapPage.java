package com.glory.mes.wip.lot.run.trackout.multieqp;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutLot;
import com.glory.mes.wip.lot.run.trackout.TrackOutScrapPage;
import com.glory.mes.wip.lot.run.trackout.TrackOutScrapQty;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class MultiEqpScrapPage extends TrackOutScrapPage {

	public MultiEqpScrapPage() {
		super();
	}

	public MultiEqpScrapPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
				if (!scrapComponent.validate()) {
					return "";
				}
				if (!checkScrapQty()) {
					return "";
				}
				// ������ʱ����
				List<LotAction> scrapLotActions = scrapComponent.getScrapLotActions();

				LotManager lotManager = Framework.getService(LotManager.class);
				Lot realLot = lotManager.getLotByLotId(Env.getOrgRrn(), lots.get(0).getLotId());

				for (LotAction scrapLotAction : scrapLotActions) {
					for (Lot lot : lots) {
						if (scrapLotAction.getLotRrn().equals(lot.getObjectRrn())) {
							scrapLotAction.setLotRrn(realLot.getObjectRrn());
							List<ProcessUnit> units = scrapLotAction.getActionUnits();
							for (ProcessUnit processUnit : units) {
								processUnit.setParentUnitRrn(realLot.getObjectRrn());
								processUnit.setEquipmentId(lot.getEquipmentId());
							}
							scrapLotAction.setActionUnits(units);
						}
					}
				}

				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> oldScrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							oldScrapLotActions.add(lotAction);
						}
					}
					list.removeAll(oldScrapLotActions);
					list.addAll(scrapLotActions);
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapLotActions);
				}
			} else {
				if (!checkScrapQty()) {
					return "";
				}
				// ������ʱ����
				List<LotAction> scrapLotActions = scrapQty.getScrapLotActions();

				LotManager lotManager = Framework.getService(LotManager.class);
				Lot realLot = lotManager.getLotByLotId(Env.getOrgRrn(), lots.get(0).getLotId());

				for (LotAction scrapLotAction : scrapLotActions) {
					for (Lot lot : lots) {
						if (scrapLotAction.getLotRrn().equals(lot.getObjectRrn())) {
							scrapLotAction.setLotRrn(realLot.getObjectRrn());
							List<ProcessUnit> units = scrapLotAction.getActionUnits();
							for (ProcessUnit processUnit : units) {
								processUnit.setParentUnitRrn(realLot.getObjectRrn());
								processUnit.setEquipmentId(lot.getEquipmentId());
							}
							scrapLotAction.setActionUnits(units);
						}
					}
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> existScrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							existScrapLotActions.add(lotAction);
						}
					}
					list.removeAll(existScrapLotActions);
					list.addAll(scrapLotActions);
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapLotActions);
				}
			}
			if (context.isReworkFlag()) {
				return TrackOutDialog.REWORK_PAGE;
			} else {
				((TrackOutWizard) this.getWizard()).invokeTrackOut();
				return getDefaultDirect();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		if (context.getInContent().getActions() != null) {
			List<LotAction> scrapLotActions = new ArrayList<LotAction>();
			for (LotAction lotAction : context.getInContent().getActions()) {
				if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
					scrapLotActions.add(lotAction);
				}
			}
			context.getInContent().getActions().removeAll(scrapLotActions);
		}
		return super.doPrevious();
	}

	@Override
	public void createControl(Composite parent) {
		setTitle(Message.getString("wip.lot_scrap_operate"));
		List<Lot> lots = context.getLots();

		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));

		scrapComponent = null;
		scrapQty = null;
		List<Lot> scrapLots = new ArrayList<Lot>();
		for (Lot lot : lots) {
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				if (trackOutLot.getLot().getObjectRrn().equals(lot.getObjectRrn())) {
					if (context.isInputScrap()) {
						if (trackOutLot.getScrapMainQty() != null
								&& trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
							scrapLots.add(lot);
						}
					} else {
						if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) < 0) {
							scrapLots.add(lot);
						}
					}
					break;
				}
			}
		}
		if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			scrapComponent = new MultiEqpScrapComp(composite, scrapLots, context);
			scrapComponent.createContent();
		} else if (QtyUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			scrapQty = new TrackOutScrapQty(composite, scrapLots, context);
			scrapQty.createContent();
		}
		setControl(composite);
	}
}
