package com.glory.mes.wip.lot.run.trackin;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.UnavailableEqpComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.extensionpoints.TrackInCheckExtensionPoint;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotBatchJob;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;

public class SelectEquipmentGlcPage extends GlcFlowWizardPage {

	private static final Logger logger = Logger.getLogger(SelectEquipmentGlcPage.class);
	
	protected static final String TABLE_EQUIPMENT = "WIPTrackInSelEqp";
    protected static final String FIELD_CONDITION = "equipmentRecipeCondition";
	
	protected IManagedForm managedForm;
	protected Lot lot;
	protected Step step;
	protected FormToolkit toolkit;
	protected EntityForm entityForm;	
	protected boolean canFlipNextPage = true;
	protected boolean isByEqpQueryPpid = false;
	protected ListTableManager selectEqpTableManager;
	
	public static final String FIELD_EQUIPMENTLIST = "equipmentList";
	public static final String FIELD_EQUIPMENTINFO = "equipmentInfo";
	public static final String FIELD_RECIPERETICLEINFO = "recipeReticleInfo";

	protected ListTableManagerField equipmentListField;
	protected CustomField equipmentInfoField;
	protected UnavailableEqpComposite unavailableEqpComposite;
	protected EntityFormField recipeReticleInfoField;
	
	protected List<Equipment> availableEqps;

	public SelectEquipmentGlcPage() {
		super();
	}
	
	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
		List<Lot> lots = context.getLots();
		lot = lots.get(0);
	}
	
	@Override
	protected boolean skipPageValidate() {
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			step = context.getStep();
			
			if (step == null || step.getCapability() == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.WipStepCapabilityIsNull()));
				context.setReturnCode(TrackInContext.FAILED_ID);
				if (this.getShell() != null && !this.getShell().isDisposed()) {
					this.getShell().dispose();
				}
				return true;
			}
			
			//�ڰ��豸��ҵʱ�������豸
			List<Equipment> selectedEqps = context.getSelectEquipments();
			
			//���û��ѡ���豸
			if (selectedEqps == null || selectedEqps.size() == 0) {
				LotManager manager = Framework.getService(LotManager.class);
			    availableEqps = manager.getAvailableEquipments(lot, Env.getSessionContext());
			    //��ҳ��ֻ��ʾ���豸
			    availableEqps =  availableEqps.stream().filter(p -> p.getParentEqpRrn() == null).collect(Collectors.toList());
			    sortEquipments(availableEqps);
			} else {
				availableEqps = selectedEqps;
			}
			
			if (!step.getIsRequireEqp()) {
				//������Ǳ����豸����û�п����豸����ֱ����ת���¸�ҳ��
				if (availableEqps == null || availableEqps.size() == 0) {
					((FlowWizard) this.getWizard()).getDialog().skipPressed();
					return true;
				}
				canFlipNextPage = false;
				((RunWizard)getWizard()).getDialog().updateButtons();
			} else {
				if (!isByEqpQueryPpid) {
					//����Ѿ����ú����豸��Ҳֱ����ת���¸�ҳ��
					if (selectedEqps != null && selectedEqps.size( )> 0) {
						((FlowWizard) this.getWizard()).getDialog().skipPressed();
						return true;
					}	
				}		
				canFlipNextPage = false;
				((RunWizard)getWizard()).getDialog().updateButtons();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return super.skipPageValidate();
	}
	
	@Override
	protected ADTable changeADTable(ADTable adTable) {
		List<ADTab> tabs = adTable.getTabs();
		List<ADField> adFields = tabs.get(0).getFields();
		List<Equipment> inValiableEqp = new ArrayList<Equipment>();
		if (availableEqps != null && availableEqps.size() > 0) {
			for (int i = 0; i < availableEqps.size(); i++) {
				Equipment equ = availableEqps.get(i);
				if (!equ.getIsAvailable()) {
					inValiableEqp.add(equ);
				}
			}
		}
		
		if (CollectionUtils.isEmpty(inValiableEqp)) {
			Optional<ADField> adField = adFields.stream().filter(field -> Objects.equals(field.getName(), FIELD_EQUIPMENTINFO)).findFirst();
			if (adField.isPresent()) {
				adFields.remove(adField.get());
			}
		}
		
		if (!isByEqpQueryPpid) {
			Optional<ADField> adField = adFields.stream().filter(field -> Objects.equals(field.getName(), FIELD_RECIPERETICLEINFO)).findFirst();
			if (adField.isPresent()) {
				adFields.remove(adField.get());
			}
		}
		tabs.get(0).setFields(adFields);
		adTable.setTabs(tabs);
		return adTable;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		setPageTitle();
		
		equipmentListField = form.getFieldByControlId(FIELD_EQUIPMENTLIST, ListTableManagerField.class);
		equipmentInfoField = form.getFieldByControlId(FIELD_EQUIPMENTINFO, CustomField.class);
		if (equipmentInfoField != null) {
			unavailableEqpComposite = (UnavailableEqpComposite) equipmentInfoField.getCustomComposite();
		}
		recipeReticleInfoField = form.getFieldByControlId(FIELD_RECIPERETICLEINFO, EntityFormField.class);
		
		subscribeAndExecute(getWizard().getEventBroker(), equipmentListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::equipmentListSelectionChanged);
		
		if (recipeReticleInfoField != null) {
			TextField conditionField = recipeReticleInfoField.getFieldByControlId(FIELD_CONDITION, TextField.class);
            conditionField.getTextControl().setVisible(false);
            conditionField.getLabelControl().setVisible(false);
            conditionField.addValueChangeListener(new IValueChangeListener() {
                @Override
                public void valueChanged(Object source, Object target) {
                    if (target != null) {
                        String targetValue = (String) target;
                        if (StringUtils.isNotEmpty(targetValue)) {
                            conditionField.getTextControl().setVisible(true);
                            conditionField.getLabelControl().setVisible(true);
                        } else {
                            conditionField.getTextControl().setVisible(false);
                            conditionField.getLabelControl().setVisible(false);
                        }
                    }
                }
            });
		}
		
		List<Equipment> showEqps =  availableEqps.stream().filter(p -> p.getIsAvailable() == true).collect(Collectors.toList());
		equipmentListField.getListTableManager().setInput(showEqps);
		if (unavailableEqpComposite != null) {
			unavailableEqpComposite.setValue(availableEqps);
			unavailableEqpComposite.refresh();
		}
	}
	
	/**
	 * �����豸ѡ���¼�
	 * 1,����Button״̬
	 * 2,���»�ȡRecipe����Ϣ
	 */
	protected void equipmentListSelectionChanged(Object object) {
		try {
			Object selectObj  = equipmentListField.getListTableManager().getSelectedObject();
			if (selectObj != null) {
				if (recipeReticleInfoField != null) {
					Equipment equipment = (Equipment)selectObj;
					
					RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
					List<Lot> lots = context.getLots();
					SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
					boolean isNotUseEquipmentRecipe = isNotUseEquipmentRecipe(sysParamManager, lots, Arrays.asList(equipment));
					
					Lot recipeLot = ByEqpUtil.getEquipmentRecipeAndReticle(lots, true, true, !isNotUseEquipmentRecipe, equipment);
					recipeReticleInfoField.setValue(recipeLot);					
					recipeReticleInfoField.refresh();
				}
				canFlipNextPage = true;
			}
			((RunWizard)getWizard()).getDialog().updateButtons();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ����EQP ID����
	 * @param availableEqps
	 */
	protected void sortEquipments(List<Equipment> availableEqps) {
		Comparator<Equipment> comparator = new Comparator<Equipment>() {
			@Override
			public int compare(Equipment eqp1, Equipment eqp2) {
				return eqp1.getEquipmentId().compareTo(eqp2.getEquipmentId());
			}
		};
		Collections.sort(availableEqps,comparator);
	}

	protected List<Equipment> getSelectedEquipments() throws Exception {
		List<Equipment> equipments = Lists.newArrayList();
		Equipment equipment = (Equipment) equipmentListField.getListTableManager().getSelectedObject();
		if (equipment != null) {
			equipments.add(equipment);
		}
		return equipments;
	}

	protected void setPageTitle() {
		setTitle(Message.getString(WipExceptionBundle.bundle.WipSelectEqpTitle()));
		setMessage(Message.getString(WipExceptionBundle.bundle.WipSelectEqpMessage()));
	}
	
	@Override
	public String doNext() {
		try {
			TrackInContext trackInContext = (TrackInContext)((RunWizard) getWizard()).getContext();
			List<Equipment> equipments = getSelectedEquipments();	
			
			String onlineEquipmentId = null;
			for (Equipment equipment : equipments) {
				if (Equipment.COMMUNICATION_STATE_ONLINE.equals(equipment.getCommunicationState())) {
					onlineEquipmentId = equipment.getEquipmentId();
					break;
				}
			}
			
			if (!StringUtil.isEmpty(onlineEquipmentId)) {
				if (!UI.showConfirm(String.format(Message.getString(WipExceptionBundle.bundle.WipLotTrackManualOnlineConfirm()), equipments.get(0).getEquipmentId()) )) {
					return "";
				}
			}
			
			trackInContext.setSelectEquipments(equipments);
			
			//ѡ�豸����һ��ʱ�����йؼ���
			trackInContext = TrackInCheckExtensionPoint.executeTrackInCheck(trackInContext, TrackInCheckExtensionPoint.CHECK_POINT_SELECTEQP);
			if (TrackInContext.FAILED_ID == trackInContext.getCheckCode()) {
				return null;
			}
			
			if (equipments == null || equipments.size() == 0) {
				if (!trackInContext.getStep().getIsRequireEqp()) {
					return getDefaultDirect();
				}
				throw new ClientException("ras.not_select_equipment");
			}
				
			// �ж��豸�Ƿ����batch��ҵ
			if (trackInContext.getLots().size() > 1 && equipments.size() > 0) {
				// ͨ��batchjob����Ƿ�Ϊ����
				String batchId = trackInContext.getLots().get(0).getBatchId();
				if (!StringUtil.isEmpty(batchId)) {
					LotManager lotManager = Framework.getService(LotManager.class);
					List<LotBatchJob> batchJobs = lotManager.getBatchJobsByBatchId(Env.getOrgRrn(), batchId);
					if (CollectionUtils.isNotEmpty(batchJobs)) {
						List<Lot> bathcLots = lotManager.getLotsByBatch(Env.getOrgRrn(), batchId);
						//���MeasureLot 
						LotBatchJob batchjob = batchJobs.stream().filter(bjob-> LotBatchJob.MEASURE_FLAG_MEASURE.equals(bjob.getMeasureFlag())).findFirst().get();
						Lot measureLot = bathcLots.stream().filter(blot -> blot.getLotId().equals(batchjob.getLotId())).findFirst().get();
						trackInContext.setMeasureLot(measureLot);
						trackInContext.setMeasure(true);
						trackInContext.setLots(bathcLots);
					} else {
						for (Equipment equipment : equipments) {
							if (!equipment.getIsBatch()) {
								UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipTrackInCannotBatch()));
								return null;
							}
						}
					}
				}
			}
			
			// ����ȡ����Reticle��Logic Recipe
			if (entityForm != null) {
				Lot lotRecipeMask = (Lot) entityForm.getObject();
				String mask = DBUtil.toString(lotRecipeMask.getMask());
				String logicRecipe = DBUtil.toString(lotRecipeMask.getRecipeName());
				
				for (Lot lot : trackInContext.getLots()) {	
					LotManager lotManager = Framework.getService(LotManager.class);
					//У���豸��LogicRecipe��PPID�Ƿ�ƥ�䣬recipe�Ƿ�Ϊhold
					if(StringUtils.isNotEmpty(lotRecipeMask.getRecipeName()) && StringUtils.isNotEmpty(lotRecipeMask.getEquipmentRecipe())) {
//						SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
//						boolean isNotUseEquipmentRecipe = isNotUseEquipmentRecipe(sysParamManager, Arrays.asList(lot), equipments);
//						if (!isNotUseEquipmentRecipe) {
//							lotManager.validRecipeEquipment(Env.getOrgRrn(), equipments.get(0).getEquipmentId(), lotRecipeMask.getRecipeName(), lotRecipeMask.getEquipmentRecipe());
//						}
						lotManager.validRecipeEquipment(Env.getOrgRrn(), equipments.get(0).getEquipmentId(), lotRecipeMask.getRecipeName(), lotRecipeMask.getEquipmentRecipe());
					}		
					
					lot.setEquipmentMask(lotRecipeMask.getEquipmentMask());
					lot.setEquipmentRecipe(lotRecipeMask.getEquipmentRecipe());
					String lotMask =  DBUtil.toString(lot.getMask());
					String lotLogicRecipe = DBUtil.toString(lot.getRecipeName());
					// ������ֲ�һ�£���Ҫ����Աȷ���Ƿ�ʹ�õ����µ�Reticle��Recipe��ˢ�½������ܻ��һ�£�
					if (!Objects.equals(mask, lotMask)) {
						if (!UI.showConfirm(String.format(Message.getString(WipExceptionBundle.bundle.MeasureLotIsDifferentExcept()), lotMask, mask, lot.getLotId()))) {
							return null;
						}
					} else if (!Objects.equals(logicRecipe, lotLogicRecipe)) {
						if (!UI.showConfirm(String.format(Message.getString(WipExceptionBundle.bundle.MeasureLotIsDifferentExcept()), lotLogicRecipe, logicRecipe, lot.getLotId()))) {
							return null;
						}
					}
					
					// ��������Ҫ���¸�ֵ
					lot.setMask(mask);
					lot.setRecipeName(logicRecipe);
				}
			}
			
			if (trackInContext.getStep().getIsMultiEqp()) {
				if (equipments.size() == 1) {
					Equipment equipment = equipments.get(0);
					List<Lot> lots = trackInContext.getLots();
					for (Lot lot : lots) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
					return getDefaultDirect();
				} else if (equipments.size() > 1) {
					List<ProcessUnit> processUnits = new ArrayList<ProcessUnit>();
					for (Equipment e : equipments) {
						QtyUnit unit = new QtyUnit();
						unit.setEquipmentId(e.getEquipmentId());
						unit.setParentProcessUnit(lot);
						unit.setParentUnitRrn(lot.getObjectRrn());
						processUnits.add(unit);
					}
					lot.setSubUnitType(QtyUnit.getUnitType());
					lot.setSubProcessUnit(processUnits);
					return getDefaultDirect();
				}
			} else {
				Equipment equipment = equipments.get(0);
				if (equipment != null) {
					List<Lot> lots = trackInContext.getLots();
					for (Lot lot : lots) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
				}
			}
		} catch (Exception e) {
			logger.error("EntityForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
		return getDefaultDirect();
	}
	
	private boolean isNotUseEquipmentRecipe(SysParameterManager sysParameterManager, List<Lot> lots, List<Equipment> equipments) {
//		boolean isNotUseEquipmentRecipe = MesCfMod.isNotUseEquipmentRecipe(Env.getOrgRrn(), sysParameterManager);
//		try {
//			if(!isNotUseEquipmentRecipe) {
//				if(MesCfMod.isDummyNotUseEquipmentRecipe(Env.getOrgRrn(), sysParameterManager)) {
//					Boolean isDummy = false;
//					Boolean isFurnaceEqp = false;
//					LotManager lotManager = Framework.getService(LotManager.class);
//					RASManager rasManager = Framework.getService(RASManager.class);
//					for (Lot lot: lots) {
//						isDummy = lotManager.isDummy(Env.getOrgRrn(), lot.getLotType());
//						if(!isDummy) {
//							return false;
//						}
//					}
//					if (isDummy) {
//						for(Equipment equipment: equipments) {
//							isFurnaceEqp = rasManager.isFurnaceEqp(Env.getOrgRrn(), equipment.getEqpType());
//							if(!isFurnaceEqp) {
//								return false;
//							}
//						}
//						isNotUseEquipmentRecipe = true;
//					}
//				}
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//		return isNotUseEquipmentRecipe;
		
		return true;
	}
	
	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public IWizardPage getNextPage() {
		return null;
	}

	@Override
	public String doSkip() {
		return this.getDefaultDirect();
	}

	@Override
	public boolean canFlipToNextPage() {
		return canFlipNextPage;
	}

}
