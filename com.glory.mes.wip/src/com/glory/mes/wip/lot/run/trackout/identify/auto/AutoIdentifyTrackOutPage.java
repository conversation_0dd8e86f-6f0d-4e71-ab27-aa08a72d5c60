package com.glory.mes.wip.lot.run.trackout.identify.auto;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;
import com.google.common.collect.Lists;

public class AutoIdentifyTrackOutPage extends FlowWizardPage {

	protected FormToolkit toolkit;
	protected Color gray = new Color(null, 236, 233, 216);
	protected List<String> list = new ArrayList<String>();
	protected TrackOutWizard tw;
	protected TrackOutContext context;
	protected AutoIdentifyTrackOutForm autoIdentifyform;
	private List<ComponentUnit> componentUnitList;

	public AutoIdentifyTrackOutPage() {
		super();
	}
	
	public AutoIdentifyTrackOutPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public String doNext() {
		try {
			componentUnitList = (List<ComponentUnit>)autoIdentifyform.getComponentUnitList();
			for (ComponentUnit componentUnit : componentUnitList) {
				if (componentUnit.getComponentId() == null || componentUnit.getComponentId().trim().equals("")) {
					UI.showError(Message.getString("wip.identify.manual_null_error"));
					return null;
				}
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			InContext inContext = new InContext();
			List<Lot> tracklots = Lists.newArrayList();
			for(Lot lot : context.getLots()) {
				lot = lotManager.getLotWithComponent(lot.getObjectRrn());
				tracklots.add(lot);
			}
			inContext.setLots(tracklots);
			inContext.setAttributeValues(context.getLotAttributeValues());	
			inContext.setOperator1(context.getOperator1());
			OutContext outContext = lotManager.trackOut(inContext, Env.getSessionContext());
			List<Lot> lots = new ArrayList<Lot>();
			lots.addAll(outContext.getLots());
			lots.addAll(outContext.getReworkLots());
			context.setOutLots(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return "";
		}
		return getDefaultDirect();
	}

	@Override
	public String doPrevious() {
		return null;
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return true;
	}

	@Override
	public void createControl(Composite parent) {
		tw = (TrackOutWizard) this.getWizard();
		context = tw.getContext();
		toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setBackground(gray);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite tabCom = toolkit.createComposite(composite, SWT.NONE);
		tabCom.setLayout(new GridLayout(1, true));
		tabCom.setLayoutData(new GridData(GridData.FILL_BOTH));
		List<Lot> lotList = context.getLots();
		autoIdentifyform = new AutoIdentifyTrackOutForm(tabCom, SWT.NONE, lotList.get(0), null);
		autoIdentifyform.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		setControl(composite);
		setTitle("TrackOut");
		setDescription(Message.getString("common.trackOut_first_title"));
	}
}


