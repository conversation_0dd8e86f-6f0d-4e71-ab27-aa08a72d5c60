package com.glory.mes.wip.lot.run.trackin;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.trackin.extensionpoints.TrackInCheckExtensionPoint;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;

public class TrackInWizard extends RunWizard {
	
	public TrackInWizard() {	
	}
	
	public TrackInWizard(TrackInContext context) {
		super(context);
	}

	@Override
	public boolean performFinish() {
		try {
			TrackInContext context = this.getContext();
			
			//ѡ�豸����һ��ʱ�����йؼ���
			context = TrackInCheckExtensionPoint.executeTrackInCheck(context, TrackInCheckExtensionPoint.CHECK_POINT_FINISH);
			if (TrackInContext.FAILED_ID == context.getCheckCode()) {
				return false;
			}
					
			LotManager lotManager = Framework.getService(LotManager.class);
			for (Lot lot : context.getLots()) {
				lot.setOperator1(context.getOperator1());
			}

			if (context.getStep().getIsRequireEqp() && context.getSelectEquipments() != null
					&& context.getSelectEquipments().size() > 0) {
				Equipment equipment = context.getSelectEquipments().get(0);
				for (Lot lot : context.getLots()) {
					if (lot.getEquipmentId() == null) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
				}
			}
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setAttributeValues(context.getLotAttributeValues());
			inContext.setEquipments(context.getSelectEquipments());
			inContext.setOperator1(context.getOperator1());
			inContext.setCheckLotPrepare(false);
			inContext.setMeasureLot(context.getMeasureLot());
			inContext.setMeasure(context.isMeasure());
			
			lotManager.trackIn(inContext, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.trackin_success"));
			context.setReturnCode(TrackInContext.OK_ID);
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		context.setReturnCode(TrackInContext.FAILED_ID);
		return false;
	}

	public void setContext(TrackInContext context) {
		this.context = context;
	}
	
	public TrackInContext getContext() {
		return (TrackInContext)context;
	}
}
