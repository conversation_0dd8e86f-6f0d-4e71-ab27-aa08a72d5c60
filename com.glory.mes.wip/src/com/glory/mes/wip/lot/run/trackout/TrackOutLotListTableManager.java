package com.glory.mes.wip.lot.run.trackout;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.mes.wip.model.Lot;

public class TrackOutLotListTableManager extends ListTableManager {
	
	public TrackOutLotListTableManager(ADTable adTable) {
		super(adTable);
	}
	
	public TrackOutLotListTableManager(ADTable adTable, boolean checkFlag) {
		super(adTable, checkFlag);
	}
		
	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Lot.class, new TrackOutLotListItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
	
}
