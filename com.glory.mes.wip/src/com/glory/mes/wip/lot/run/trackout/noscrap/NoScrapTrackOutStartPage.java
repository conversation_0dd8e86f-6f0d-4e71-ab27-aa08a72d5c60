package com.glory.mes.wip.lot.run.trackout.noscrap;

import java.util.List;

import org.eclipse.jface.wizard.Wizard;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.core.exception.ClientException;
import com.glory.mes.wip.lot.run.trackout.TrackOutLot;
import com.glory.mes.wip.lot.run.trackout.TrackOutStartPage;

public class NoScrapTrackOutStartPage extends TrackOutStartPage {
	
	public NoScrapTrackOutStartPage() {
		super();
	}
	
	public NoScrapTrackOutStartPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}
	
	@Override
	public ADTable getTrackOutADTable(List<TrackOutLot> trackOutLots)
			throws Exception, ClientException {
		ADTable adTable = super.getTrackOutADTable(trackOutLots);
		List<ADField> adFields = adTable.getFields();
		for (ADField adField : adFields) {
			if (FIELD_OUTMAINQTY.equals(adField.getName())) {
				//OutMainQty��λ���ܱ༭
				adField.setIsDisplay(true);
				adField.setIsMain(true);
				adField.setIsEditable(false);
				break;
			}
		}
		return adTable;
	}
}