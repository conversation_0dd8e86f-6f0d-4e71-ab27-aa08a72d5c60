package com.glory.mes.wip.lot.run.operation;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.mes.prd.model.Operation;

public class StepOperationDialog extends BaseTitleDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;
	
	public Text detailText;
	public Operation curOperation;
	
	public StepOperationDialog(Shell parentShell, Operation operation) {
		super(parentShell);
		curOperation = operation;
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitle("Operation detail");
		GridLayout layout = new GridLayout();
		parent.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		parent.setLayoutData(gd);
		parent.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		detailText = new Text(parent, SWT.MULTI | SWT.BORDER | SWT.V_SCROLL);
		detailText.setLayoutData(new GridData(GridData.FILL_BOTH));
		detailText.setEditable(false);
		
		detailText.setText(curOperation.getDesciption());
		return parent;
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
}
