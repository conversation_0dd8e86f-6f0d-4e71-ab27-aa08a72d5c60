package com.glory.mes.wip.lot.run.trackthold;

import java.util.List;

import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.TableEditor;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableItem;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.viewers.FixEditorTableManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutLot;
import com.glory.mes.wip.lot.run.trackout.TrackOutStartPage;

public class TrackTHoldLotTable extends FixEditorTableManager {
	
	public TrackTHoldLotTable(ADTable adTable) {
		super(adTable);
	}
	
	@Override
	public void setInput(Object input) {
		super.setInput(input);
		
		final Table table = ((TableViewer)this.viewer).getTable();
		TableItem[] items = table.getItems();
		List<TrackOutLot> trackOutLots = (List<TrackOutLot>)input;
		for (TrackOutLot trackOutLot : trackOutLots) {
			TableItem curItem = null;
			for (TableItem item : items) {
				TrackOutLot curOutLot = (TrackOutLot)item.getData();
	    		if (trackOutLot.equals(curOutLot)) {
	    			curItem = item;
	    		}
			}
			if (curItem != null) {
				if (!trackOutLot.getIsRework()){
					Object data = curItem.getData(EDITOR_PREFIX + TrackOutStartPage.FIELD_REWORKMAINQTY);
					if (data != null) {
						TableEditor editor = (TableEditor)data;
						editor.getEditor().setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_GRAY));
						editor.getEditor().setEnabled(false);
					}
				}
			}
		}
	}
	
	
	
}
