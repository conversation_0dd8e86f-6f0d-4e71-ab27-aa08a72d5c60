package com.glory.mes.wip.lot.run.trackout;

import java.util.List;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.model.Lot;

public class TrackOutReworkQtyTable extends FixEditorTableManager {
	
	private Lot lot;
	
	public TrackOutReworkQtyTable(ADTable adTable, Lot lot) {
		super(adTable);
		this.lot = lot;
	}
	
	@Override
	public List<ADBase> getFieldList(ADField field) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			StepState state = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
			List<Transition> reworkTransitions = prdManager.getReworkTransitions(state, true);
			return (List)reworkTransitions;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
}
