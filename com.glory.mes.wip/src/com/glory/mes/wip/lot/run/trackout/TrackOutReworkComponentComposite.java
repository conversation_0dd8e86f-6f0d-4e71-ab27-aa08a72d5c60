package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ControlEvent;
import org.eclipse.swt.events.ControlListener;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.PaintEvent;
import org.eclipse.swt.events.PaintListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.MessageBox;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class TrackOutReworkComponentComposite extends Composite {
	
	private static final Logger logger = Logger.getLogger(TrackOutReworkComponentComposite.class);

	protected static String HEADER_REWORK_CODE = Message.getString("wip.trackout_reworkcode");
	protected final static String TABLE_REWORK_PROCESS_UNIT = "WIPTrackOutReworkProcessUnit";
	
	private Lot lot;
	protected Step step;
	private TrackOutContext context;
	
	protected XCombo comboReworkCode;
	protected XCombo comboReworkProcedure;
	protected Text commentText;
	protected ManagedForm mform;
	
	protected CheckBoxTableViewerManager tableViewerManager;
	
	protected ADRefTable adRefTable;
	
	protected List<Object> scrapUnits = new ArrayList<Object>();
	
	public TrackOutReworkComponentComposite(Composite parent, int style, Lot lot, TrackOutContext context) {
		super(parent, style);
		this.step = context.getStep();
		this.context=context;
		try {
			LotManager manager = Framework.getService(LotManager.class);
			lot = manager.getLotWithComponent(lot.getObjectRrn());
			this.setLot(lot);
			createForm();
		} catch (Exception e) {
			logger.error(e);
		}
	}
	
	public void createForm() {
		FormToolkit toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout(1, false);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		setLayoutData(new GridData(GridData.FILL_BOTH));
		
		ScrolledForm sform = toolkit.createScrolledForm(this);
		sform.setLayoutData(new GridData(GridData.FILL_BOTH));
		mform = new ManagedForm(toolkit, sform);

		Composite body = sform.getBody();
		layout = new GridLayout(1, true);
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createTableComponent(body, toolkit);
		createLowerComponent(body, toolkit);
		
	}
	
	protected void createTableComponent(Composite composite, FormToolkit toolkit) {		
		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
	    tableContainer.setLayout(new GridLayout(1, true));
	    tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
	    try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_REWORK_PROCESS_UNIT);
			
			tableViewerManager = new TrackOutReworkComponentTable(adTable);
			tableViewerManager.newViewer(tableContainer);
			tableViewerManager.setInput(getLot().getSubProcessUnit());
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	    
	    tableViewerManager.addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObject, boolean checked) {
				
				if (checked) {
					boolean flag = true;
					if(comboReworkProcedure.getText()==null || "".equals(comboReworkProcedure.getText().trim())){
						UI.showWarning(Message.getString("wip.lot_select_reworkProcedure"));
						flag = false;
					}else if(comboReworkCode.getText() == null || "".equals(comboReworkCode.getText().trim())){
						UI.showWarning(Message.getString("wip.lot_select_reworkCode"));
						flag = false;
					}
						
					for (Object object : eventObject) {
						if (flag) {
							((ComponentUnit)object).setActionCode(comboReworkCode.getText());
							((ComponentUnit)object).setReworkTransition(comboReworkProcedure.getText());
						}else {
							tableViewerManager.unCheckObject(object);
							((ComponentUnit)object).setActionCode("");
							((ComponentUnit)object).setReworkTransition("");
						}
					}
				}else {
					for (Object object : eventObject) {
						((ComponentUnit)object).setActionCode("");
						((ComponentUnit)object).setReworkTransition("");
					}
				}
				
				for (Object object : eventObject) {
					if (scrapUnits.contains(object)) {
						tableViewerManager.unCheckObject(object);
						((ComponentUnit)object).setActionCode("");
						((ComponentUnit)object).setReworkTransition("");
					}
				}
			}
		});
		
		tableViewerManager.refresh();
	}
	
	protected void createLowerComponent(Composite composite, FormToolkit toolkit) {
		Composite reworkComp = toolkit.createComposite(composite, SWT.NULL);
		reworkComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		GridLayout gl = new GridLayout(5, false);
		reworkComp.setLayout(gl);
		
		GridData cGd = new GridData(GridData.FILL_BOTH);
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			List<ADRefTable> adRefTables = entityManager.getEntityList(Env.getOrgRrn(), 
					ADRefTable.class, 1, "name = 'WIPTrackOutReworkTransition'", "");
			
			for(ADRefTable field:adRefTables){
				this.adRefTable = field;
				}
			ADTable adTable = entityManager.getADTable(adRefTable.getTableRrn());
			adRefTable.setAdTable(adTable);
			ListTableManager listTableManager=new ListTableManager(adTable);
			
			listTableManager.setInput(getFieldList());
			toolkit.createLabel(reworkComp, Message.getString("wip.trackout.rework.procedure"), SWT.READ_ONLY);
			comboReworkProcedure = new XCombo(reworkComp, listTableManager, adRefTable.getKeyField(), adRefTable.getTextField(), SWT.READ_ONLY, false);
			comboReworkProcedure.setLayoutData(cGd);
			comboReworkProcedure.select(0);
		} catch (ClientException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}

		toolkit.createLabel(reworkComp, Message.getString("wip.trackout_reworkcode"), SWT.READ_ONLY);
		comboReworkCode = RCPUtil.getUserRefListCombo(reworkComp, getReworkCode(), Env.getOrgRrn());
		comboReworkCode.select(0);
		comboReworkCode.setEditable(false);
		cGd.horizontalSpan = 4;
		comboReworkCode.setLayoutData(cGd);
		
		toolkit.createLabel(reworkComp, Message.getString("wip.comment"), SWT.NULL);
		commentText = toolkit.createText(reworkComp,"", SWT.BORDER);
		commentText.setLayoutData(cGd);
	}
	
	protected String getReworkCode() {
		try {
			if (step != null && step.getReworkCodeSrc() != null 
					&& step.getReworkCodeSrc().trim().length() > 0) {
				return step.getReworkCodeSrc();
			} 
		} catch (Exception e) {
			logger.error("ScrapLotDialog : initComoContent() ", e);
		}
		return "ReworkCode";
	}
	
	public List<ADBase> getFieldList() {
		try {
			List<ADBase> fieldList = new ArrayList<ADBase>();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			StepState state = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
			List<Transition> reworkTransitions = prdManager.getReworkTransitions(state, true);
			return (List)reworkTransitions;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
						
	public boolean validate() {
		IMessageManager mmng = mform.getMessageManager();
		mmng.removeAllMessages();
		boolean validateFlag = true;
		boolean sourceIsNull = GenericValidator.isBlankOrNull(comboReworkCode.getText());
		if(sourceIsNull){
			mmng.addMessage(HEADER_REWORK_CODE, 
					String.format(Message.getString("common.ismandatry"), HEADER_REWORK_CODE), null, IMessageProvider.ERROR, comboReworkCode);
			validateFlag = false;
		}
		return validateFlag;
	}

	public List<ProcessUnit> getReworkUnits() {
		List<ProcessUnit> reworkUnits = new ArrayList<ProcessUnit>();
		List<Object> objs = tableViewerManager.getCheckedObject();
		for (Object obj : objs) {
			ProcessUnit unit = (ProcessUnit)obj;
			// unit.setReworkTransition(comboReworkProcedure.getText());
			unit.setEquipmentId(lot.getEquipmentId());
			reworkUnits.add(unit);
		}
		return reworkUnits;
	}
	
	public LotAction getReworkAction() {
		LotAction lotAction = new LotAction();
		lotAction.setActionCode(comboReworkCode.getText());
		lotAction.setActionComment(commentText.getText());
		return lotAction;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public Lot getLot() {
		return lot;
	}
	

	private class TrackOutReworkComponentTable extends CheckBoxTableViewerManager {
		public TrackOutReworkComponentTable(ADTable adTable) {
			super(adTable);
			registerAdapterFactory();
		}
		
		public void registerAdapterFactory() {
			setAdapterFactory(createAdapterFactory());
		}

		public ItemAdapterFactory createAdapterFactory() {
			ItemAdapterFactory factory = new ItemAdapterFactory();
			factory.registerAdapter(Object.class, new FilterComponentUnitItemAdapter());
			return factory;
		}
	}
	
	private class FilterComponentUnitItemAdapter extends ListItemAdapter<ComponentUnit> {
		@Override
		public Color getBackground(Object element, String id) {
			ComponentUnit unit = (ComponentUnit) element;
			if (context.getInContent().getActions() != null) {			
				List<LotAction> scrapLotActions = new ArrayList<LotAction>();
				for (LotAction lotAction : context.getInContent().getActions()) {
					if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
						scrapLotActions.add(lotAction);
					}
				}
				for (LotAction scrapLotAction : scrapLotActions) {
					if (unit.getParentUnitRrn().equals(scrapLotAction.getLotRrn())) {
						List<ProcessUnit> comps = scrapLotAction.getActionUnits();
						if (comps.contains(unit)) {
							if (!scrapUnits.contains(unit)) {
								scrapUnits.add(unit);
							}
							return (Display.getCurrent().getSystemColor(SWT.COLOR_GRAY));
						}
					}
				}
			}
			return super.getBackground(element, id);
		}
	}
}
