package com.glory.mes.wip.lot.run.trackout.multieqp;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutReworkPage;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class MultiEqpReworkPage extends TrackOutReworkPage {

	public MultiEqpReworkPage() {
		super();
	}

	public MultiEqpReworkPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot realLot = lotManager.getLotByLotId(Env.getOrgRrn(), lots.get(0).getLotId());
			if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
				if (!reworkComponent.validate()) {
					return "";
				}
				if (!checkReworkQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> reworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						lotAction.setLotRrn(realLot.getObjectRrn());
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							reworkLotActions.add(lotAction);
						}
					}
					list.removeAll(reworkLotActions);
					list.addAll(reworkComponent.getReworkLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkComponent.getReworkLotActions());
				}
			} else {
				if (!checkReworkQty()) {
					return "";
				}
				List<LotAction> reworkLotActions = reworkQty.getReworkLotActions();

				for (LotAction reworkLotAction : reworkLotActions) {
					for (Lot lot : lots) {
						if (reworkLotAction.getLotRrn().equals(lot.getObjectRrn())) {
							reworkLotAction.setLotRrn(realLot.getObjectRrn());
							List<ProcessUnit> units = reworkLotAction.getActionUnits();
							for (ProcessUnit processUnit : units) {
								processUnit.setEquipmentId(lot.getEquipmentId());
								processUnit.setParentUnitRrn(realLot.getObjectRrn());
							}
							reworkLotAction.setActionUnits(units);
						}
					}

				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> existReworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							existReworkLotActions.add(lotAction);
						}
					}
					list.removeAll(existReworkLotActions);
					list.addAll(reworkLotActions);
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkLotActions);
				}
			}
			((TrackOutWizard) this.getWizard()).invokeTrackOut();
			return getDefaultDirect();

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

}
