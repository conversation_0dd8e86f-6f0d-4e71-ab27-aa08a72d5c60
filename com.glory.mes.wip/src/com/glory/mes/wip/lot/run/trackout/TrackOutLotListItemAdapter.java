package com.glory.mes.wip.lot.run.trackout;

import org.eclipse.swt.graphics.Color;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;

public class TrackOutLotListItemAdapter extends ListItemAdapter<Lot> {

	@Override
	public Color getBackground(Object element, String id) {
		if (element instanceof Lot){
			Lot lot = (Lot)element;
			if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
				return SWTResourceCache.getColor(SWTResourceCache.COLOR_RED);
			}
		}
		return null;
	}
	
}
