package com.glory.mes.wip.lot.run.trackout;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.core.exception.ExceptionBundle;

public class TrackOutLotListPage extends FlowWizardPage {

	protected FormToolkit toolkit;
	protected Composite parent;
	protected TrackOutWizard tw;
	protected TrackOutContext context;
	public static final String TABLE_NAME = "WIPTrackOutNextInfo";

	public TrackOutLotListPage() {
		super();
	}
	
	public TrackOutLotListPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public IWizardPage getNextPage() {
		return null;
	}

	@Override
	public IWizardPage getPreviousPage() {
		return null;
	}
	
	@Override
	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}

	@Override
	public void createControl(Composite parent) {
		this.parent = parent;
		tw = (TrackOutWizard) this.getWizard();		
		context = (TrackOutContext)tw.getContext();
		toolkit = new FormToolkit(parent.getDisplay());
		/*Composite composite = toolkit.createComposite(parent, SWT.BORDER);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));*/
		
		Composite tabCom = toolkit.createComposite(parent, SWT.NONE);
		tabCom.setLayout(new GridLayout(1, true));
		tabCom.setLayoutData(new GridData(GridData.FILL_BOTH));
		try {
			GridLayout layout = new GridLayout(1, false);
			tabCom.setLayout(layout);
			tabCom.setLayoutData(new GridData(GridData.FILL_BOTH));
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotNext = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ListTableManager tableManager = new TrackOutLotListTableManager(lotNext);
			tableManager.newViewer(tabCom);
			tableManager.setInput(context.getOutLots());
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		setControl(parent);
		setTitle(Message.getString("wip.trackout_title"));
		setDescription(Message.getString("wip.lot_next_batch"));
		tw.getDialog().updateButtonName(IDialogConstants.NEXT_ID, Message.getString(ExceptionBundle.bundle.CommonComplete()));
		tw.getDialog().getSquareButton(IDialogConstants.CANCEL_ID).setEnabled(false);
	}

	@Override
	public String doNext() {
		return getDefaultDirect();
	}

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	@Override
	public String doPrevious() {
		return "";
	}

}