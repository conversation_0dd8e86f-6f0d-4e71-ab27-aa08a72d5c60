package com.glory.mes.wip.lot.run.trackout.multieqp;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutScrapComponent;
import com.glory.mes.wip.lot.scrap.ScrapComponentUnitForm;
import com.glory.mes.wip.model.Lot;

public class MultiEqpScrapComp extends TrackOutScrapComponent {

	public MultiEqpScrapComp(Composite parent, List<Lot> scrapLots, TrackOutContext context) {
		super(parent, scrapLots, context);
	}

	@Override
	public Composite createUnit(Composite com, Object obj) {
		Group group = new Group(com, SWT.NONE);
		GridLayout gd = new GridLayout(1, true);
		gd.numColumns = 1;
		gd.marginRight = 1;
		gd.marginLeft = 1;
		group.setLayout(gd);
		group.setLayoutData(new GridData(GridData.FILL_BOTH));
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));

		Lot lot = (Lot) obj;
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}

		ScrapComponentUnitForm form = new MultiEqpScrapCompForm(group, SWT.NONE, lot);
		return form;
	}
}
