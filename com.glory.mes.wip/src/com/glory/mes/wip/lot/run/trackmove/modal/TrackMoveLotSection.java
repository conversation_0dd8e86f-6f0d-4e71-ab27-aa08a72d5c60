package com.glory.mes.wip.lot.run.trackmove.modal;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.ShellAdapter;
import org.eclipse.swt.events.ShellEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.model.LineUser;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class TrackMoveLotSection extends LotSection {
	
	private static final Logger logger = Logger.getLogger(TrackMoveLotSection.class);
	
	protected TrackOutWizard wizard;
	protected TrackMoveContext context;
	protected LotStepAttributeForm attributeForm;
	protected Step step;

	protected ToolItem itemTrackMove;
	protected ToolItem itemClose;

	protected Text txtOperator;
	protected Composite composite;

	protected RefTableField eqpRefTableField;
	protected ADField eqpNameField;

	protected static final String FIELD_ID_EQUIPMENT = "equipmentId";
	protected static final String FIELD_DISPLAY_TYPE = "reftable";
	
	protected boolean isMultiply = true;//�ж��Ƿ��Ƕ��˲����Ĺ�����Ĭ����

	public TrackMoveLotSection(ADTable table, TrackOutWizard wizard) {
		super(table);
		this.wizard = wizard;
		this.context = (TrackMoveContext) wizard.getContext();
		this.step = context.getStep();
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(6, false));
		top.setLayoutData(gd);
		Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
		label.setForeground(SWTResourceCache
				.getColor(SWTResourceCache.COLOR_TITLE));
		txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
		txtLot.setTextLimit(32);
		
		if(isMultiply()){
			createOperatorText(toolkit, top);
		}

		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
					lot = searchLot(lotId);
					tLotId.selectAll();
					if (lot == null) {
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						try {
							setAdObject(createAdObject());
							refresh();
						} catch (Exception en) {
							logger.error("createADObject error at searchEntity Method!");
						}
						txtLot.warning();
					} else {
						setAdObject(lot);
						itemTrackMove.setEnabled(true);
						txtOperator.setFocus();
						txtOperator.selectAll();
						refresh();
						attributeForm.setLot(lot);
						
						txtLot.focusing();
					}
					break;
				}
			}
		});
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (!isLotIdCaseSensitive()) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});

		Composite right = toolkit.createComposite(top);
		GridLayout layout = new GridLayout(2, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.END;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
	}
	
	protected void createOperatorText(FormToolkit toolkit, Composite top){
		toolkit.createLabel(top, Message.getString("wip.operator"));
		txtOperator = toolkit.createText(top, "", SWT.BORDER);
		txtOperator.setTextLimit(32);
		GridData oText = new GridData(GridData.END);
		oText.widthHint = 216;
		txtOperator.setLayoutData(oText);
		txtOperator.setFocus();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemTrackMove(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClose(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemTrackMove(ToolBar tBar) {
		itemTrackMove = new ToolItem(tBar, SWT.PUSH);
		itemTrackMove.setText(Message.getString("wip.trackmove") + "(F2)");
		itemTrackMove.setImage(SWTResourceCache.getImage("trackmove"));
		itemTrackMove.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackMoveAdapter();
			}
		});
	}

	protected void createToolItemClose(ToolBar tBar) {
		itemClose = new ToolItem(tBar, SWT.PUSH);
		itemClose.setText(Message.getString(ExceptionBundle.bundle.CommonClose()) + "(F10)");
		itemClose.setImage(SWTResourceCache.getImage("close"));
		itemClose.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				closeAdapter();
			}
		});
	}

	@Override
	protected void createSectionContent(Composite client) {
		section.setText(buildStepInfo());
		this.composite = client;
		Lot selectLot = context.getLots().get(0);
		setAdObject(selectLot);
		txtLot.setText(selectLot.getLotId());
		super.createSectionContent(composite);
		createOtherForm(composite);
		// ��������������Ϣ���֣�������ʾ
		if (getStepAttribute().size() > 0 || step.getIsRequireEqp()) {
			createAttributeForm(composite);
		}
		// ����������Ϣ����
		registerAccelerator(client);
	}

	private String buildStepInfo() {
		StringBuffer buffer = null;
		if (step != null && step.getObjectRrn() != null) {
			buffer = new StringBuffer(step.getName());
			buffer.append("<");
			buffer.append(step.getDescription());
			buffer.append(">");
			return buffer.toString();
		}
		return "StepInfo";
	}

	public void createAttributeForm(Composite parent) {
		try {
			final FormToolkit toolkit = form.getToolkit();
			Section section = toolkit.createSection(parent, Section.TITLE_BAR);
			section.setText(Message.getString("wip.trackmove_enter_step_info"));
			GridData gd = new GridData(1808);
			section.setLayoutData(gd);
			toolkit.createCompositeSeparator(section);
			Composite sectionClient = toolkit.createComposite(section);
			GridLayout gl = new GridLayout(1, false);
			sectionClient.setLayout(gl);

			Equipment eqp = null;
			if (context.getSelectEquipments() != null
					&& context.getSelectEquipments().size() > 0) {
				eqp = context.getSelectEquipments().get(0);
			}
			
			Lot selectLot = context.getLots().get(0);
			
			if(step.getIsRequireEqp()){//��Ҫ�豸�б�����ʾ
				createEqpField(sectionClient, toolkit);
			}
			
			if (getStepAttribute().size() > 0){//��attribute����ʾ
				final IMessageManager mmng = form.getMessageManager();
				attributeForm = new LotStepAttributeForm(sectionClient, SWT.NONE, mmng,
						selectLot, getStepAttribute(), eqp);
				attributeForm.createForm();
				GridData gd1 = new GridData(GridData.FILL_HORIZONTAL);
				attributeForm.setLayoutData(gd1);
			}
			
			section.setClient(sectionClient);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public void createOtherForm(Composite parent) {
	}

	private List<StepAttribute> getStepAttribute() {
		List<StepAttribute> stepAttributes = new ArrayList<StepAttribute>();
		try {
			Long stepRrn = step.getObjectRrn();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			stepAttributes = prdManager.getStepAttribute(stepRrn,
					StepAttribute.CATEGORY_TRACKOUT);
			return stepAttributes;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return stepAttributes;
	}

	protected void trackMoveAdapter() {
		try {
			/*boolean saveFlag = true;
			if (!attributeForm.saveToObject()) {
				saveFlag = false;
				return;
			}
			
			String userName = txtOperator.getText().trim();
			
			switch (formValidate(userName)) {
			case 0:
				break;
			case 1:
				UI.showError(Message.getString("wip.trackmove_user_not_exist"));
				return;
			case 2:
				UI.showError(Message.getString("wip.trackmove_no_line_authority"));
				return;
			case 3:
				UI.showWarning(Message.getString("wip.trackmove_no_eqp"));
				return;
			}

			if (saveFlag) {
				List<Lot> outLots = new ArrayList<Lot>();
				Lot lot = (Lot) getAdObject();
				lot.setOperator1(txtOperator.getText().trim());
				if(eqpRefTableField != null && eqpRefTableField.getData() != null){
					Equipment equipment = (Equipment) eqpRefTableField.getData();
					lot.setEquipmentId(equipment.getEquipmentId());
					lot.setEquipmentRrn(equipment.getObjectRrn());
				}
				outLots.add(lot);

				TrackOutContext context = wizard.getContext();
				context.setLotAttributeValues(attributeForm
						.getAttributeValues());
				context.setOutLots(outLots);

				if (wizard.performFinish()) {
					clear();
					attributeForm.clear();
					itemTrackMove.setEnabled(false);
				}
			}*/
			boolean saveFlag = true;
			if(attributeForm != null){//��֤attribute�����Ƿ�Ϸ�
				if (!attributeForm.saveToObject()) {
					saveFlag = false;
				}else{
					context.setLotAttributeValues(attributeForm.getAttributeValues());	
				}
			}

			if (saveFlag) {
				
				String userName = txtOperator.getText().trim();
				
				switch (formValidate(userName)) {
				case 0:
					break;
				case 1:
					UI.showError(Message.getString("wip.trackmove_user_not_exist"));
					return;
				case 2:
					UI.showError(Message.getString("wip.trackmove_no_line_authority"));
					return;
				case 3:
					UI.showWarning(Message.getString("wip.trackmove_no_eqp"));
					return;
				}
				
				List<Lot> outLots = new ArrayList<Lot>();
				Lot lot = (Lot) getAdObject();
				lot.setOperator1(userName);
				
				if(eqpRefTableField != null && eqpRefTableField.getValue() != null){//���豸�򱣴��վ�豸��Ϣ
					Equipment equipment = (Equipment) eqpRefTableField.getData();
					lot.setEquipmentId(equipment.getEquipmentId());
				}
				outLots.add(lot);
				context.setOutLots(outLots);

				if (wizard.performFinish()) {
					clear();
					attributeForm.clear();
					itemTrackMove.setEnabled(false);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void closeAdapter() {
		TrackMoveModalDialog dialog = (TrackMoveModalDialog) wizard.getDialog();
		dialog.getShell().close();
	}

	/**
	 * ��֤�û��Ƿ���ڣ��Ƿ���������߱����Ȩ��
	 * 
	 * @return boolean
	 */
	protected int formValidate(String userName) {
		try {
			if (userName != null && userName.length() > 0) {
				ADManager adManager = Framework.getService(ADManager.class);
				String whereCause = " userName = '" + userName + "' ";
				List<ADUser> adUsers = adManager.getEntityList(Env.getOrgRrn(),
						ADUser.class, Env.getMaxResult(), whereCause, null);

				if (adUsers == null || adUsers.size() == 0) {
					return 1;
				}

				if (Env.isUseLine()) {
					Lot lot = (Lot) getAdObject();
					whereCause = " lineId = '" + lot.getLineId()
							+ "' AND userName = '" + userName + "'";
					List<LineUser> lineUsers = adManager.getEntityList(Env
							.getOrgRrn(), LineUser.class, Env.getMaxResult(),
							whereCause, null);

					if (lineUsers == null || lineUsers.size() == 0) {
						return 2;
					}
				}
			}else{
				return 1;
			}
			if(step.getIsRequireEqp()){
				if(eqpRefTableField != null && eqpRefTableField.getData() == null){
					return 3;
				}
			}
		} catch (Exception e) {
			logger.info(" validateUser() : wrong");
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return 0;
	}

	/**
	 * �����豸�б�
	 * @param client
	 * @param toolkit
	 * @return
	 */
	protected RefTableField createEqpField(Composite client, FormToolkit toolkit) {
		Composite comp = toolkit.createComposite(client);
		//comp.setBackground(new Color(Display.getCurrent(),0 ,0 ,255));
		GridLayout layout = new GridLayout(4, false);
		comp.setLayout(layout);
		GridData g = new GridData(GridData.BEGINNING);
		comp.setLayoutData(g);
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			for (ADTab tab : table.getTabs()) {
				for (ADField adField : tab.getFields()) {
					if (FIELD_ID_EQUIPMENT.equals(adField.getName())
							&& FIELD_DISPLAY_TYPE.equals(adField
									.getDisplayType())) {
						eqpNameField = adField;
					}
				}
			}

			ADRefTable refTable = new ADRefTable();
			refTable.setObjectRrn(eqpNameField.getReftableRrn());
			refTable = (ADRefTable) entityManager.getEntity(refTable);

			ADTable adTable = entityManager.getADTable(refTable.getTableRrn());

			ListTableManager tableManager = new ListTableManager(adTable);
			
			LotManager manager = Framework.getService(LotManager.class);

			Lot currentLot = context.getLots().get(0);
			List<Equipment> list = manager.getAvailableEquipments(currentLot,
					Env.getSessionContext());
			tableManager.setInput(list);
			int mStyle = SWT.BORDER | SWT.READ_ONLY;
			eqpRefTableField = new RefTableField("", tableManager, refTable, mStyle,
					true);
			eqpRefTableField.setLabel(Message.getString("ras.equipment_id")+"*");
			eqpRefTableField.createContent(comp, toolkit);
		} catch (Exception e1) {
			logger.error("PartFlowSection : creatSectionTitle", e1);
		}
		return eqpRefTableField;
	}

	public void clear() {
		txtLot.setText("");
		txtLot.setFocus();
		txtOperator.setText("");
		eqpRefTableField.setValue(null);
		setAdObject(new Lot());
		refresh();
	}
	
	@Override
	public Lot searchLot(String lotId) {
		Lot lot = super.searchLot(lotId);
		if(lot != null){
			if(!lot.getStepName().equals(step.getName())){
				lot = null;
			}
		}
		return lot;
	}

	protected void registerAccelerator(Composite client) {
		final Listener listener = acceleratorListener();
		client.getDisplay().addFilter(SWT.KeyDown, listener);
		client.getShell().addShellListener(new ShellAdapter() {
			public void shellClosed(final ShellEvent e) {
				e.display.removeFilter(SWT.KeyDown, listener);
			}
		});    
	}

	protected Listener acceleratorListener() {
		return new Listener() {
			public void handleEvent(Event e) {
				switch (e.keyCode) {
				case SWT.F2:
					if (itemTrackMove != null && itemTrackMove.isEnabled()) {
						trackMoveAdapter();
					}
					break;
				case SWT.F10:
					if (itemClose != null && itemClose.isEnabled()) {
						closeAdapter();
					}
					break;
				}
			}
		};
	}

	public boolean isMultiply() {
		return isMultiply;
	}

	public void setMultiply(boolean isMultiply) {
		this.isMultiply = isMultiply;
	}
}
