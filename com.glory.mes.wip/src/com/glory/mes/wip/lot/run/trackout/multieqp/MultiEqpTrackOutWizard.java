package com.glory.mes.wip.lot.run.trackout.multieqp;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;
import com.google.common.collect.Lists;

public class MultiEqpTrackOutWizard extends TrackOutWizard {
	
	boolean isTrackOutSplit = false;
	
	@Override
	public void invokeTrackOut() throws Exception {
		TrackOutContext context = (TrackOutContext)this.getContext();
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> outLots = context.getOutLots();
		Lot outLot = outLots.get(0);
		Lot realLot = lotManager.getLotByLotId(Env.getOrgRrn(), outLot.getLotId());
		
		List<ProcessUnit> units = Lists.newArrayList();
		for (Lot lot : outLots) {
			units.addAll(lot.getSubProcessUnit());
		}
		
		realLot.setSubProcessUnit(units);
		realLot.setOperator1(context.getOperator1());
		
		InContext inContext = context.getInContent();
		inContext.setLots(Lists.newArrayList(realLot));
		inContext.setAttributeValues(context.getLotAttributeValues());
		inContext.setmLots(context.getmLots());
		inContext.setOperator1(context.getOperator1());
		inContext.setMultiEqp(true);
		inContext.setSplitTrackOut(isTrackOutSplit);
		
		OutContext outContext = lotManager.trackOutPartialMultiEqp(inContext, units,  Env.getSessionContext());
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll(outContext.getLots());
		if (CollectionUtils.isNotEmpty(outContext.getReworkLots())) {
			for (Lot reworkLot : outContext.getReworkLots()) {
				if (!lots.contains(reworkLot)) {
					lots.add(reworkLot);
				}
			}
		}
		context.setOutLots(lots);
	}

	public boolean isTrackOutSplit() {
		return isTrackOutSplit;
	}

	public void setTrackOutSplit(boolean isTrackOutSplit) {
		this.isTrackOutSplit = isTrackOutSplit;
	}
	
}
