package com.glory.mes.wip.lot.run.trackout.bin.action;

import java.util.List;

import com.glory.edc.model.EdcDataItem;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.model.Lot;

public class BinActionTrackOutContext extends TrackOutContext {	
	
    protected List<Lot> scrapLots;
	protected List<Lot> reworkLots;
	protected List<Lot> spiltTrackOutLots;
	protected List<Lot> mergeLots;
	
	protected List<EdcDataItem> binDataItems;
	
    public BinActionTrackOutContext(TrackOutContext context) {
    	super(context);
	}

	public List<Lot> getScrapLots() {
		return scrapLots;
	}

	public void setScrapLots(List<Lot> scrapLots) {
		this.scrapLots = scrapLots;
	}

	public List<Lot> getReworkLots() {
		return reworkLots;
	}

	public void setReworkLots(List<Lot> reworkLots) {
		this.reworkLots = reworkLots;
	}

	public List<Lot> getSpiltTrackOutLots() {
		return spiltTrackOutLots;
	}

	public void setSpiltTrackOutLots(List<Lot> spiltTrackOutLots) {
		this.spiltTrackOutLots = spiltTrackOutLots;
	}

	public List<Lot> getMergeLots() {
		return mergeLots;
	}

	public void setMergeLots(List<Lot> mergeLots) {
		this.mergeLots = mergeLots;
	}

	public List<EdcDataItem> getBinDataItems() {
		return binDataItems;
	}

	public void setBinDataItems(List<EdcDataItem> binDataItems) {
		this.binDataItems = binDataItems;
	}
	
}
