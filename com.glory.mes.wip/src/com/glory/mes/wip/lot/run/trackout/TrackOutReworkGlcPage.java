package com.glory.mes.wip.lot.run.trackout;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.graphics.Color;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.custom.TrackOutReworkComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class TrackOutReworkGlcPage extends GlcFlowWizardPage {

	public static final Color GRAY = null;
	protected TrackOutContext context;
	
	public static final String FIELD_REWORKCOMPOSITE = "reworkComposite";

	protected CustomField reworkCompositeField;
	protected TrackOutReworkComposite reworkComposite;
	
	public TrackOutReworkGlcPage() {
		super();
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		setTitle(Message.getString(WipExceptionBundle.bundle.WipLotReworkOperate()));
		reworkCompositeField = form.getFieldByControlId(FIELD_REWORKCOMPOSITE, CustomField.class);
		reworkComposite = (TrackOutReworkComposite) reworkCompositeField.getCustomComposite();
		
		List<Lot> lots = context.getLots();
		List<Lot> reworkLots = new ArrayList<Lot>();
		for (Lot lot : lots) {
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				if (trackOutLot.getLot().getObjectRrn().equals(lot.getObjectRrn())) {
					if (trackOutLot.getReworkMainQty() != null) {
						reworkLots.add(lot);
					}
					break;
				}
			}
		}
		
		reworkComposite.setObjects(reworkLots);
		reworkComposite.setStep(context.getStep());
		reworkComposite.setContext(context);
		reworkComposite.refresh();
	}
	
	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		context = (TrackOutContext)((TrackOutWizard)this.getWizard()).getContext();
	}
	
	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		if (context.getInContent().getActions() != null) {
			List<LotAction> reworkLotActions = new ArrayList<LotAction>();
			for (LotAction lotAction : context.getInContent().getActions()) {
				if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
					reworkLotActions.add(lotAction);
				}
			}
			context.getInContent().getActions().removeAll(reworkLotActions);
		}
		return super.doPrevious();
	}

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	protected boolean checkReworkQty() throws Exception {
		List<TrackOutLot> trackOutLots = context.getTrackOutLots();
		
		//��������2
		List<LotAction> reworkUnits  = reworkComposite.getReworkLotActions();
		
		// ���units�ķ���transition�Ƿ�Ϊ�գ�Ϊ�ղ�����һ��
		Optional<LotAction> op = reworkUnits.stream().filter(u -> u.getReworkTransition() == null).findFirst();
		if (op.isPresent()) {
			setErrorMessage(Message.getString(WipExceptionBundle.bundle.WipLotSelectReworkProcedure()));
			return false;
		}
		for (TrackOutLot trackOutLot : trackOutLots) {
			BigDecimal qty = trackOutLot.getReworkMainQty();
			
			if (qty != null) {
				BigDecimal reworkMainQty = BigDecimal.ZERO;
				BigDecimal reworkSubQty = null;
				if (trackOutLot.getSubQty() != null) {
					reworkSubQty = BigDecimal.ZERO;
				}
				
				for (LotAction reworkUnit : reworkUnits) {
					if (reworkUnit.getLotRrn().equals(trackOutLot.getLot().getObjectRrn())) {
						for (ProcessUnit unit : reworkUnit.getActionUnits()) {
							//�б�����2
							reworkMainQty = reworkMainQty.add(unit.getMainQty());
							if (reworkSubQty != null) {
								reworkSubQty = reworkSubQty.add(unit.getSubQty() != null ? unit.getSubQty() : BigDecimal.ZERO);
							}
						}
					}
				}
				if (reworkMainQty.compareTo(qty) != 0) {
					setErrorMessage(trackOutLot.getLotId() + Message.getString(WipExceptionBundle.bundle.WipTrackOutReworkQty()) + qty);
					return false;
				}
			}	
		}
		return true ;
	}

	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if(ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())){
				if (!reworkComposite.validate()) {
					return "";
				}
				if(!checkReworkQty()){
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> reworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							reworkLotActions.add(lotAction);
						}
					}
					list.removeAll(reworkLotActions);
					list.addAll(reworkComposite.getReworkLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkComposite.getReworkLotActions());
				}
			} else {
				if (!checkReworkQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> reworkLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_REWORK.equals(lotAction.getActionType())) {
							reworkLotActions.add(lotAction);
						}
					}
					list.removeAll(reworkLotActions);
					list.addAll(reworkComposite.getReworkLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(reworkComposite.getReworkLotActions());
				}
			}
			((TrackOutWizard)this.getWizard()).invokeTrackOut();
			return getDefaultDirect();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

}
