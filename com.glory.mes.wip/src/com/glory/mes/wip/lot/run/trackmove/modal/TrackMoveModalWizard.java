package com.glory.mes.wip.lot.run.trackmove.modal;

import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.IWizardPage;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.track.model.InContext;

public class TrackMoveModalWizard extends TrackOutWizard {

	private static final Logger logger = Logger.getLogger(TrackMoveModalWizard.class);

    public TrackMoveModalWizard() {
	}
	
	public TrackMoveModalWizard(TrackMoveContext context) {
		super(context);
	}

	@Override
	public boolean performFinish() {
		try {
			TrackMoveContext context = (TrackMoveContext)this.getContext();
			InContext inContext = context.getInContent();
			inContext.setLots(context.getOutLots());
			inContext.setAttributeValues(context.getLotAttributeValues());
			inContext.setOperator1(context.getOperator1());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			lotManager.trackMove(inContext, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.trackmove_success"));
			
			context.setReturnCode(TrackMoveContext.OK_ID);
			return true;
		} catch (Exception e) {
			logger.error("performFinish() : error");
			ExceptionHandlerManager.asyncHandleException(e);
		}
		context.setReturnCode(TrackMoveContext.FAILED_ID);
		return false;
	}
	
	@Override
	public void invokeTrackOut() throws Exception {
		//Do Nothing
	}
	
	@Override
	public IWizardPage getStartingPage() {
		return startPage;
	}

}
