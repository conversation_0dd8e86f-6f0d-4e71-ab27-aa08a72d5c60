package com.glory.mes.wip.lot.run.trackmove.modal;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;

public class TrackMoveModalDialog extends TrackOutDialog {
	
	public TrackMoveModalDialog(Shell parentShell, FlowWizard newWizard) {
		super(parentShell, newWizard);
	}
	
	@Override
	protected Control createDialogArea(Composite parent) {
		// create the top level composite for the dialog area
		Composite composite = new Composite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		composite.setLayout(layout);
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		composite.setFont(parent.getFont());
		// Build the separator line
		Label titleBarSeparator = new Label(composite, SWT.HORIZONTAL
				| SWT.SEPARATOR);
		titleBarSeparator.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		// Build the Page container
		pageContainer = createPageContainer(composite);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.widthHint = pageWidth;
		gd.heightHint = pageHeight;
		pageContainer.setLayoutData(gd);
		pageContainer.setFont(parent.getFont());
		// Insert a progress monitor
		GridLayout pmlayout = new GridLayout();
		pmlayout.numColumns = 1;

		return composite;
	}
	
	@Override
	protected Control createButtonBar(Composite parent) {
		return null;
	}

	@Override
	public void setTitle(String newTitle) {
		super.setTitle(Message.getString("wip.bystep"));
	}

}
