package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.entitymanager.forms.ScorllFormComposite;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutScrapQty extends ScorllFormComposite {

	protected TrackOutContext context;
	
	public TrackOutScrapQty(Composite parent, List<Lot> scrapLots, TrackOutContext context) {
		super(parent, scrapLots);
		this.context = context;
	}

	@Override
	public Composite createUnit(Composite com, Object obj) {
		Group group = new Group(com, SWT.NONE);
		GridLayout layout = new GridLayout(1, true);
		layout.numColumns = 1;
		layout.marginRight = 1;
		layout.marginLeft = 1;
		group.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.widthHint = 250;
		group.setLayoutData(gd);
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		Lot lot = (Lot) obj;
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}
		
		TrackOutScrapQtyComposite form = new TrackOutScrapQtyComposite(group, SWT.NONE, lot, context.getStep());
		return form;
	}
	
	public List<LotAction> getScrapLotActions() {
		List<LotAction> scrapLotAction = new ArrayList<LotAction>();
		for (Composite unit : units) {
			TrackOutScrapQtyComposite scrapUnit = (TrackOutScrapQtyComposite)unit;
			for (ProcessUnit processUnit : scrapUnit.getScrapUnits()) {
				QtyUnit qtyUnit = (QtyUnit)processUnit;
				LotAction lotAction = new LotAction();
				
				lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
				lotAction.setActionCode(qtyUnit.getActionCode());	
				List<ProcessUnit> qtyUnits = new ArrayList<ProcessUnit>();
				qtyUnits.add(qtyUnit);
				lotAction.setActionUnits(qtyUnits);
				
				String equipmentId = qtyUnit.getEquipmentId();
				if (StringUtil.isEmpty(equipmentId)) {
					equipmentId = scrapUnit.getLot().getEquipmentId();
				}
				lotAction.setEquipmentId(equipmentId);
				
				lotAction.setLotRrn(scrapUnit.getLot().getObjectRrn());
				scrapLotAction.add(lotAction);	
			}				
		}
		return scrapLotAction;
	}
	
}
