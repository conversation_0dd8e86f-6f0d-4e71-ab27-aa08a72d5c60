package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutScrapQtyComposite extends Composite {
	
	private static final Logger logger = Logger.getLogger(TrackOutScrapQtyComposite.class);
	private static String TABLE_NAME = "WIPTrackOutScrapQty";
	 
	protected Lot lot;
	protected Step step;
	protected ListEditorTableManager tableManager;
	protected ManagedForm mform;
	
	public TrackOutScrapQtyComposite(Composite parent, int style, Lot lot, Step step) {
		super(parent, style);
		this.step = step;
		this.lot = lot;
		this.setLayoutData(new GridData(GridData.FILL_BOTH));
		createForm();
	}
	
	public void createForm() {
		FormToolkit toolkit = new FormToolkit(getDisplay());
		
		GridLayout layout = new GridLayout(1, false);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		
		ScrolledForm sform = toolkit.createScrolledForm(this);
		sform.setLayoutData(new GridData(GridData.FILL_BOTH));
		sform.setLayout(layout);
		mform = new ManagedForm(toolkit, sform);
		
		Composite body = sform.getBody();
		layout = new GridLayout();
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createViewerComponent(body, toolkit);
	}
	
	protected void createViewerComponent(Composite composite, FormToolkit toolkit) {
		ADManager adManager;
		try {
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListEditorTableManager(adTable, false);
			tableManager.setIndexFlag(true);
			tableManager.newViewer(composite);
	
			String scrapCode = getScrapCode();
			List<ADURefList> list = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, 
					Env.getMaxResult(), "referenceName = '" + scrapCode +"' ", "");
			List<QtyUnit> scrapCodeList = new ArrayList<QtyUnit>();
			for (ADURefList ref : list) {
				QtyUnit scrap = new QtyUnit();
				scrap.setActionCode(ref.getText());
				scrap.setDescription(ref.getDescription());
				scrapCodeList.add(scrap);
			}
			tableManager.setInput(scrapCodeList);			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected String getScrapCode() {
		try {
			if (step != null && step.getScrapCodeSrc() != null 
					&& step.getScrapCodeSrc().trim().length() > 0) {
				return step.getScrapCodeSrc();
			} 
		} catch (Exception e) {
			logger.error("ScrapLotDialog : initComoContent() ", e);
		}
		return "ScrapCode";
	}
	
	public boolean validate() {
		IMessageManager mmng = mform.getMessageManager();
		mmng.removeAllMessages();
		boolean validateFlag = true;
		return validateFlag;
	}
	
	public List<ProcessUnit> getScrapUnits() {
		List<ProcessUnit> scrapUnits = new ArrayList<ProcessUnit>();
		for (Object unit : tableManager.getInput()) {
			QtyUnit qtyUnit = (QtyUnit)unit;
			if (qtyUnit.getMainQty() != null) {
				qtyUnit.setEquipmentId(lot.getEquipmentId());
				scrapUnits.add(qtyUnit);
			}
		}
		return scrapUnits;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public Lot getLot() {
		return lot;
	}
}
