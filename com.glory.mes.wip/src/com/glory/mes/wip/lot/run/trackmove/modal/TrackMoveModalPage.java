package com.glory.mes.wip.lot.run.trackmove.modal;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;

public class TrackMoveModalPage extends FlowWizardPage {

	public static final String TABLE_NAME = "WIPLotQuery";
	
	protected TrackOutWizard wizard;
	protected TrackMoveContext context;

	protected Lot lot;
	protected ADTable adTable;
	protected TrackMoveLotSection lotSection;
	protected LotStepAttributeForm attributeForm;
	
	protected ManagedForm form;
	
	public TrackMoveModalPage() {
		super();
	}
	
	public TrackMoveModalPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}
	
	@Override
	public void createControl(Composite parent) {
		form = new ManagedForm(parent);
		wizard = (TrackMoveModalWizard) this.getWizard();
		context = (TrackMoveContext)wizard.getContext();
		
		Composite composite = form.getForm().getBody();
		GridLayout layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
		composite.setLayoutData(gd);
		
		getLotADTableByStep();
		createLotSection(composite);
		setControl(composite);
	}
	
	public void createLotSection(Composite parent) {
		lotSection = new TrackMoveLotSection(adTable, wizard); 
		lotSection.createContents(form, parent);
	}
	
	
	protected void getLotADTableByStep() {
		try {
			ADManager manager = Framework.getService(ADManager.class);
			try {
				//ȡ�ø�Step��Ӧ��ADTable,���û����ʹ��Ĭ�ϵ�ADTable
				Step step = wizard.getContext().getStep();
				adTable = manager.getADTable(Env.getOrgRrn(), TABLE_NAME + step.getName());
			} catch (Exception e) {
			}
			if (adTable == null) {
				adTable = manager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			}
			adTable = (ADTable) manager.getADTableDeep(adTable.getObjectRrn());
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public String doNext() {
		return null;
	}
	
	
}
