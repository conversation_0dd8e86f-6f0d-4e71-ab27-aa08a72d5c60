package com.glory.mes.wip.lot.run.trackout.bin.action;

import java.util.ArrayList;
import java.util.List;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;

public class BinActionTrackOutWizard extends TrackOutWizard {

	public BinActionTrackOutWizard() {
	}
	
	public BinActionTrackOutWizard(BinActionTrackOutContext context) {
		super(context);
	}

	public void invokeTrackOut() throws Exception {
		BinActionTrackOutContext context = (BinActionTrackOutContext)this.getContext();
		for (Lot lot : context.getOutLots()) {
			lot.setOperator1(context.getOperator1());
		}
		InContext inContext = context.getInContent();
		inContext.setLots(context.getOutLots());
		inContext.setAttributeValues(context.getLotAttributeValues());
		inContext.setOperator1(context.getOperator1());
		
		LotManager lotManager = Framework.getService(LotManager.class);
		OutContext outContext = lotManager.binActionTrackOut(inContext, context.getBinDataItems(), Env.getSessionContext());
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll(outContext.getLots());
		for (Lot reworkLot : outContext.getReworkLots()) {
			if (!lots.contains(reworkLot)) {
				lots.add(reworkLot);
			}
		}
		context.setOutLots(lots);
	}
	
	@Override
	public void setContext(TrackOutContext context) {
		BinActionTrackOutContext ftContext = new BinActionTrackOutContext(context);
		super.setContext(ftContext);
	}
}
