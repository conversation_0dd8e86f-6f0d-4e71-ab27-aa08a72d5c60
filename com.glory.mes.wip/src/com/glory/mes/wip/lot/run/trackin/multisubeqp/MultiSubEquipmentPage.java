package com.glory.mes.wip.lot.run.trackin.multisubeqp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.UnavailableEqpComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

public class MultiSubEquipmentPage extends GlcFlowWizardPage {

	private static final Logger logger = Logger.getLogger(MultiSubEquipmentPage.class);
	
	protected static final String TABLE_EQUIPMENT = "WIPTrackInSelEqp";
	
	protected Lot lot;
	protected Step step;
	protected boolean canFlipNextPage=true;

	public static final String FIELD_AVAILABLEPARENTEQPS = "availableParentEqps";
	public static final String FIELD_AVAILABLECHILDEQPS = "availableChildEqps";
	public static final String FIELD_ERRORINFO = "errorInfo";

	protected ListTableManagerField availableParentEqpsField;
	protected ListTableManagerField availableChildEqpsField;
	protected CustomField errorInfoField;
	protected UnavailableEqpComposite unavailableEqpComposite;
	protected List<Equipment> availableParentEqps = Lists.newArrayList();
	protected List<Equipment> inValiableEqp = Lists.newArrayList();
	
	public MultiSubEquipmentPage() {
		super();
	}
	
	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
		List<Lot> lots = context.getLots();
		lot = lots.get(0);
	}
	
	@Override
	protected boolean skipPageValidate() {
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			step = context.getStep();
			
			if (step == null || step.getCapability() == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.WipStepCapabilityIsNull()));
				context.setReturnCode(TrackInContext.FAILED_ID);
				if (this.getShell() != null && !this.getShell().isDisposed()) {
					this.getShell().dispose();
				}
				return true;
			}
			
			//�ڰ��豸��ҵʱ�������豸
			List<Equipment> selectedEqps = context.getSelectEquipments();
			
			//���û��ѡ���豸
			if (selectedEqps == null || selectedEqps.size() == 0) {
				LotManager manager = Framework.getService(LotManager.class);
				List<Equipment> availableEqps = manager.getAvailableEquipments(lot, Env.getSessionContext());
				for (Equipment equipment : availableEqps) {
					Long parentEqpRrn = equipment.getParentEqpRrn();
					if (parentEqpRrn == null || parentEqpRrn == 0 ) {
						availableParentEqps.add(equipment);
					}
				}
				sortEquipments(availableParentEqps);
			}
			
			if (!step.getIsRequireEqp()) {
				//������Ǳ����豸����û�п����豸����ֱ����ת���¸�ҳ��
				if (availableParentEqps == null || availableParentEqps.size() == 0) {
					((FlowWizard) this.getWizard()).getDialog().skipPressed();
					return true;
				}
			} else {
				//����Ѿ����ú����豸��Ҳֱ����ת���¸�ҳ��
				if (selectedEqps != null && selectedEqps.size( )> 0) {
					((FlowWizard) this.getWizard()).getDialog().skipPressed();
					return true;
				}
				canFlipNextPage = false;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return super.skipPageValidate();
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		availableParentEqpsField = form.getFieldByControlId(FIELD_AVAILABLEPARENTEQPS, ListTableManagerField.class);
		availableChildEqpsField = form.getFieldByControlId(FIELD_AVAILABLECHILDEQPS, ListTableManagerField.class);
		errorInfoField = form.getFieldByControlId(FIELD_ERRORINFO, CustomField.class);

		subscribeAndExecute(getWizard().getEventBroker(), availableParentEqpsField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::availableParentEqpsSelectionChanged);
	
		List<Equipment> showEqps =  availableParentEqps.stream().filter(p -> p.getIsAvailable() == true).collect(Collectors.toList());
		availableParentEqpsField.getListTableManager().setInput(showEqps);
		
		unavailableEqpComposite = (UnavailableEqpComposite) errorInfoField.getCustomComposite();
		unavailableEqpComposite.setValue(availableParentEqps);
		unavailableEqpComposite.refresh();
		setPageTitle();
	}

	private void availableParentEqpsSelectionChanged(Object object) {
		try {
			Equipment selectObj = (Equipment) availableParentEqpsField.getListTableManager().getSelectedObject();
			if (selectObj != null) {
				canFlipNextPage = true;
				availableChildEqpsField.getListTableManager().getCheckedObject().clear();
				availableChildEqpsField.getListTableManager().getInput().clear();
				LotManager manager = Framework.getService(LotManager.class);
				List<Equipment> availableSubEqps = manager.getAvailableSubEquipments(lot, selectObj.getEquipmentId(), Env.getSessionContext());
				sortEquipments(availableSubEqps);
				List<Equipment> availableAllEqps = Lists.newArrayList();
				availableAllEqps.addAll(availableParentEqps);
				availableAllEqps.addAll(availableSubEqps);
				unavailableEqpComposite.setValue(availableAllEqps);
				unavailableEqpComposite.refresh();
				availableSubEqps = availableSubEqps.stream().filter(eqp -> eqp.getIsAvailable()).collect(Collectors.toList());
				availableChildEqpsField.getListTableManager().setInput(availableSubEqps);
				availableChildEqpsField.getListTableManager().refresh();
			}
			((RunWizard)getWizard()).getDialog().updateButtons();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * ����EQP ID����
	 * @param availableEqps
	 */
	private void sortEquipments(List<Equipment> availableEqps) {
		Comparator<Equipment> comparator = new Comparator<Equipment>(){
			@Override
			public int compare(Equipment eqp1, Equipment eqp2) {
				return eqp1.getEquipmentId().compareTo(eqp2.getEquipmentId());
			}
		};
		Collections.sort(availableEqps,comparator);
	}

	protected List<Equipment> getSelectedEquipments() {
		List<Equipment> equipments = new ArrayList<>();
		
		// ���豸
		Object selectedObject = availableParentEqpsField.getListTableManager().getSelectedObject();
		if (selectedObject != null) {
			Equipment equipment =  (Equipment)selectedObject;
			equipments.add(equipment);
		}
		
		// ���豸
		List<? extends Object> input = availableChildEqpsField.getListTableManager().getInput();
		if (input != null && !input.isEmpty()) {
			List<Object> checkedObject = availableChildEqpsField.getListTableManager().getCheckedObject();
			if (checkedObject == null || checkedObject.isEmpty()) {
				throw new ClientException("wip.select_child_eqp_please");
			}
			
			for (Object object : checkedObject) {
				Equipment equipment =  (Equipment)object;
				equipments.add(equipment);
			} 
		}
		return equipments;
	}
	
	protected List<Equipment> getSelectedChildEquipments() {
		List<Equipment> equipments = new ArrayList<>();
		// ���豸
		List<? extends Object> input = availableChildEqpsField.getListTableManager().getInput();
		if (input != null && !input.isEmpty()) {
			List<Object> checkedObject = availableChildEqpsField.getListTableManager().getCheckedObject();
			if (checkedObject == null || checkedObject.isEmpty()) {
				throw new ClientException("wip.select_child_eqp_please");
			}
			
			for (Object object : checkedObject) {
				Equipment equipment =  (Equipment)object;
				equipments.add(equipment);
			} 
		}
		return equipments;
	}
	
	protected void setPageTitle() {
		setTitle(Message.getString("wip.selecteqp_title"));
		setMessage(Message.getString("wip.selecteqp_message"));
	}
	
	@Override
	public String doNext() {
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			List<Equipment> equipments = getSelectedEquipments();
			if (equipments == null || equipments.size() == 0) {
				if (!context.getStep().getIsRequireEqp()) {
					return getDefaultDirect();
				}
				throw new ClientException("wip.error.must_select_eqp");
			}
			context.setSelectEquipments(equipments);
			if (context.getStep().getIsMultiEqp()) {
				//��֧�ֶ��豸
				throw new ClientException("wip.trackin_unsupport_mulit_eqp");
			} else {
				Equipment equipment = equipments.get(0);
				if (equipment != null) {
					List<Lot> lots = context.getLots();
					for (Lot lot : lots) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
				}
				equipment.setSubEquipments(getSelectedChildEquipments());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
		return getDefaultDirect();
	}

	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public IWizardPage getNextPage() {
		return null;
	}

	@Override
	public String doSkip() {
		return this.getDefaultDirect();
	}

	@Override
	public boolean canFlipToNextPage() {
		return canFlipNextPage;
	}
	
}
