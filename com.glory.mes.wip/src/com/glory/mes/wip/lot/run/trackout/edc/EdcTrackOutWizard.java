package com.glory.mes.wip.lot.run.trackout.edc;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.glory.edc.model.EdcData;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;

public class EdcTrackOutWizard extends TrackOutWizard {
	
	private List<EdcData> edcDatas;
	
	@Override
	public void invokeTrackOut() throws Exception {
		try {
			if (CollectionUtils.isEmpty(edcDatas)) {
				super.invokeTrackOut();
			} else {
				// EDC Out
				TrackOutContext context = (TrackOutContext)this.getContext();
				for (Lot lot : context.getOutLots()) {
					lot.setOperator1(context.getOperator1());
				}
				InContext inContext = context.getInContent();
				inContext.setLots(context.getOutLots());
				inContext.setAttributeValues(context.getLotAttributeValues());
				inContext.setmLots(context.getmLots());
				inContext.setOperator1(context.getOperator1());
				
				LotManager lotManager = Framework.getService(LotManager.class);
				OutContext outContext = lotManager.trackOutEdc(inContext, edcDatas, Env.getSessionContext());
				List<Lot> lots = new ArrayList<Lot>();
				lots.addAll(outContext.getLots());
				for (Lot reworkLot : outContext.getReworkLots()) {
					if (!lots.contains(reworkLot)) {
						lots.add(reworkLot);
					}
				}
				context.setOutLots(lots);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public List<EdcData> getEdcDatas() {
		return edcDatas;
	}


	public void setEdcDatas(List<EdcData> edcDatas) {
		this.edcDatas = edcDatas;
	}
	
}
