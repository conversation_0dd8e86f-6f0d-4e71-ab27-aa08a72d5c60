package com.glory.mes.wip.lot.run.trackout.carrierbind;

import java.util.ArrayList;
import java.util.List;

import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutScrapPage;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;

/**
 * ���ص�TrackOutScrapPage���ı���תҳ��
 * <AUTHOR>
 *
 */
@Deprecated
public class CarriersBindScrapPage extends TrackOutScrapPage {
	
	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
				if (!scrapComponent.validate()) {
					return "";
				}
				if (!checkScrapQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							scrapLotActions.add(lotAction);
						}
					}
					list.removeAll(scrapLotActions);
					list.addAll(scrapComponent.getScrapLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapComponent.getScrapLotActions());
				}
				
			} else {
				if (!checkScrapQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							scrapLotActions.add(lotAction);
						}
					}
					list.removeAll(scrapLotActions);
					list.addAll(scrapQty.getScrapLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapQty.getScrapLotActions());
				}
			}
			if (context.isReworkFlag()) {
				return TrackOutDialog.REWORK_PAGE;
			} else {
				// ��ԭ�治֮ͬ��
				// ((TrackOutWizard)this.getWizard()).invokeTrackOut();
				return getDefaultDirect();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

}
