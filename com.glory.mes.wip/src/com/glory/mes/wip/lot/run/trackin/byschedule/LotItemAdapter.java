package com.glory.mes.wip.lot.run.trackin.byschedule;

import org.eclipse.swt.graphics.Color;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class Lot<PERSON>temAdapter extends ListItemAdapter<Lot> {

	@Override
	public Color getBackground(Object element, String id) {
		Lot lot = (Lot)element;
    	if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
    		return SWTResourceCache.getColor(SWTResourceCache.COLOR_RED_ORANGE);
    	} else if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
    		return SWTResourceCache.getColor(SWTResourceCache.COLOR_GREEN);
    	}
    	return null;
	}
	
}
