package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.List;


import org.apache.log4j.Logger;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutReworkQtyComposite extends Composite {
	
	private static final Logger logger = Logger.getLogger(TrackOutReworkQtyComposite.class);
	private static String TABLE_NAME = "WIPTrackOutReworkQty";
	 
	protected Lot lot;
	protected Step step;
	
	protected ManagedForm mform;
	protected FixEditorTableManager tableManager;
	
	public TrackOutReworkQtyComposite(Composite parent, int style, Lot lot, Step step) {
		super(parent, style);
		this.step = step;
		this.lot = lot;
		this.setLayoutData(new GridData(GridData.FILL_BOTH));
		createForm();
	}
	
	public void createForm() {
		FormToolkit toolkit = new FormToolkit(getDisplay());
		
		GridLayout layout = new GridLayout(1, false);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		
		ScrolledForm sform = toolkit.createScrolledForm(this);
		sform.setLayoutData(new GridData(GridData.FILL_BOTH));
		sform.setLayout(layout);
		mform = new ManagedForm(toolkit, sform);
		
		Composite body = sform.getBody();
		layout = new GridLayout();
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		createViewerComponent(body, toolkit);
	}
	
	protected void createViewerComponent(Composite composite, FormToolkit toolkit) {
		ADManager adManager;
		try {
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new TrackOutReworkQtyTable(adTable, lot);
			tableManager.newViewer(composite);
	
			String reworkCode = getReworkCode();
			List<ADURefList> list = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, 
					Env.getMaxResult(), "referenceName='" + reworkCode +"' ", "");
			List<QtyUnit> reworkCodeList = new ArrayList<QtyUnit>();
			for (ADURefList ref : list) {
				QtyUnit rework = new QtyUnit();
				rework.setActionCode(ref.getText());
				rework.setDescription(ref.getDescription());
				reworkCodeList.add(rework);
			}
			tableManager.setInput(reworkCodeList);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected String getReworkCode() {
		try {
			if (step != null && step.getReworkCodeSrc() != null 
					&& step.getReworkCodeSrc().trim().length() > 0) {
				return step.getReworkCodeSrc();
			} 
		} catch (Exception e) {
			logger.error("ScrapLotDialog : initComoContent() ", e);
		}
		return "ReworkCode";
	}
	
	public boolean validate() {
		IMessageManager mmng = mform.getMessageManager();
		mmng.removeAllMessages();
		boolean validateFlag = true;
		return validateFlag;
	}
	
	public List<ProcessUnit> getReworkUnits() {
		List<ProcessUnit> reworkUnits = new ArrayList<ProcessUnit>();
		List<Object> qtyUnits = (List<Object>) tableManager.getInput();
		if (qtyUnits == null){
			return reworkUnits;
		}
		for (Object qtyObj : qtyUnits) {
			QtyUnit qtyUnit = (QtyUnit) qtyObj;
			if (qtyUnit.getMainQty() != null) {
				qtyUnit.setEquipmentId(lot.getEquipmentId());
				reworkUnits.add(qtyUnit);
			}
		}
		return reworkUnits;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	public Lot getLot() {
		return lot;
	}
	
}
