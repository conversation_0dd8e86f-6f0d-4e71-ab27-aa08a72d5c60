package com.glory.mes.wip.lot.run.trackout.carrierbind;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.PaintEvent;
import org.eclipse.swt.events.PaintListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotMultiCarrier;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

/**
 * ���ؾ�ID����
 * <AUTHOR> He
 *
 */
@Deprecated
public class CarriersBindPage extends FlowWizardPage {
	
	protected static final String TABLE_NAME = "LotMulitCarrierAdd";

	protected Text txt;
	protected FormToolkit toolkit;
	protected ListTableManager tableManager;
	
	protected Lot lot;
	protected String bindUserName;

	public CarriersBindPage() {
		super();
	}
	
	public CarriersBindPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		TrackOutWizard tw = (TrackOutWizard) this.getWizard();
		TrackOutContext context = (TrackOutContext)tw.getContext();
		lot = context.getOutLots().get(0);
		bindUserName = context.getOperator1();
		
		toolkit = new FormToolkit(Display.getCurrent());
		Composite composite = new Composite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
		composite.setLayoutData(gd);

		createContent(composite);
		setControl(composite);
		setPageTitle();

	}
	
	protected void setPageTitle() {
		setTitle(Message.getString("assembly.trackout_carrier_bind"));
		setMessage(Message.getString("assembly.trackout_carrier_bind_detail"));
	}

	protected void createContent(Composite composite) {
		Composite parent = toolkit.createComposite(composite, SWT.NONE);
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite input = toolkit.createComposite(composite, SWT.NONE);
		input.setLayout(new GridLayout(3, false));
		input.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		Label label = toolkit.createLabel(input, Message.getString("assembly.track_carrier_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		
		txt = toolkit.createText(input, "", SWT.BORDER);
		GridData gText = new GridData();
		gText.widthHint = 216;
		txt.setLayoutData(gText);
		txt.setTextLimit(32);
		txt.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		
		txt.addPaintListener(new PaintListener() {
			@Override
			public void paintControl(PaintEvent e) {
				txt.setFocus();
			}
		});
		
		txt.addKeyListener(new KeyAdapter() {
			@SuppressWarnings("unchecked")
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String id = tLotId.getText();
					tLotId.selectAll();
					if (!Strings.isNullOrEmpty(id)) {
						// У���ؾ�ID�Ƿ��Ѿ�¼��
						List<LotMultiCarrier> inputLotMulitCarriers = Lists.newArrayList((List<LotMultiCarrier>) tableManager.getInput());
						if (Objects.nonNull(inputLotMulitCarriers) && !inputLotMulitCarriers.isEmpty()) {
							Optional<LotMultiCarrier> op = inputLotMulitCarriers.stream().filter(lc -> lc.getCarrierId().equals(id)).findFirst();
							if (op.isPresent()) {
								UI.showWarning(Message.getString("assembly.trackout_carrier_bind_repeat"));
								break;
							}
						}
						
						LotMultiCarrier lotCarrier = new LotMultiCarrier();
						lotCarrier.setLotRrn(lot.getObjectRrn());
						lotCarrier.setOrgRrn(Env.getOrgRrn());
						lotCarrier.setUpdatedBy(bindUserName);
						lotCarrier.setCarrierId(id);
						tableManager.add(lotCarrier);
						refresh();
					}
					break;
				}
			}
		});

		Group tabGroup = new Group(parent, SWT.NONE);
		tabGroup.setText(Message.getString("assembly.track_carrier_list"));
		tabGroup.setBackground(new Color(null, 255, 255, 255));
		tabGroup.setLayout(new GridLayout(2, true));
		tabGroup.setLayoutData(new GridData(GridData.FILL_BOTH));

		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListTableManager(adTable);
			tableManager.newViewer(tabGroup);
			tableManager.setInput(Lists.newArrayList());
			tableManager.getNatTable().setLayoutData(new GridData(GridData.FILL_BOTH));
			tableManager.addDoubleClickListener(new IMouseAction() {//����˫��ɾ���¼�
                @Override
                public void run(NatTable natTable, MouseEvent event) {
                	LotMultiCarrier selectedLotCarrier = (LotMultiCarrier) tableManager.getSelectedObject();
                    tableManager.getInput().remove(selectedLotCarrier);
                    tableManager.refresh();
                }
            });
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}



	@SuppressWarnings("unchecked")
	@Override
	public String doNext() {
		try {
			List<LotMultiCarrier> lotCarriers = Lists.newArrayList((List<LotMultiCarrier>) tableManager.getInput()) ;
			TrackOutWizard wizard = ((TrackOutWizard)this.getWizard());
			if (Objects.isNull(lotCarriers) || lotCarriers.isEmpty()) {
				if (UI.showConfirm(Message.getString("assembly.trackout_carrier_empty_confirm"))) {
					wizard.invokeTrackOut();
					return getDefaultDirect();
				} else {
					return "";
				}
			}
			
			for (Lot lot : wizard.getContext().getLots()) {
				String lotCarrierStr = "CarrierId:";
				for (LotMultiCarrier lotCarrier : lotCarriers) {
					lotCarrierStr = lotCarrierStr + lotCarrier.getCarrierId() + ";";
				}
				if (lotCarriers != null && lotCarriers.size() > 0) {
					lotCarrierStr = lotCarrierStr.substring(0, lotCarrierStr.length() - 1);
					lot.setLotComment(lotCarrierStr);
				}
			}	
//			wizard.getContext().getInContent().setLotCarriers(lotCarriers);
			wizard.invokeTrackOut();
			return getDefaultDirect();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

}
