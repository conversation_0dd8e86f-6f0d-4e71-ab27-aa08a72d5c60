package com.glory.mes.wip.lot.run.trackin.consumablematerial;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class ConumableMatreialForm implements IForm {

	private static final Logger logger = Logger.getLogger(ConumableMatreialForm.class);
	
	private static final String TABLE_NAME_MATERIAL = "WIPStepMaterialNameList";
	private static final String TABLE_NAME_MLOT = "WIPStepMaterialLotList";
	
	public ListTableManager materialManager;
	public CheckBoxFixEditorTableManager mLotManager;
	public Button add, delete;
	public Text txtLot;
	
	protected FormToolkit toolkit;
	protected RunWizardContext context;
	
	public ConumableMatreialForm(Composite parent, int style, RunWizardContext context) {
		this.context = context;
		createForm(parent);
		if (context != null) {
			loadFromObject();
	    }
	}
	
	public void createForm(Composite parent) {		
		try {	
			toolkit = new FormToolkit(Display.getCurrent());
			
			Group materialGroup = new Group(parent, SWT.BORDER);
			materialGroup.setText(Message.getString("wip.record_raw_material_info"));
			materialGroup.setBackground(new Color(null, 255, 255, 255));
			materialGroup.setLayout(new GridLayout(1, false));
			materialGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			ADManager adManager = Framework.getService(ADManager.class);	
			ADTable adTable0 = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MATERIAL);
			materialManager = new ListTableManager(adTable0);
			materialManager.setIndexFlag(true);
			materialManager.newViewer(materialGroup);
			
			ADTable adTable1 = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_MLOT);
			mLotManager = new CheckBoxFixEditorTableManager(adTable1);
			mLotManager.setIndexFlag(true);
			mLotManager.newViewer(materialGroup);
			
			Composite btnComposite = toolkit.createComposite(materialGroup, SWT.NONE);
			GridLayout layoutBtn = new GridLayout(4, false);
			btnComposite.setLayout(layoutBtn);
			GridData btngd = new GridData(GridData.FILL_HORIZONTAL);
			btngd.horizontalAlignment = SWT.RIGHT;	
			btnComposite.setLayoutData(btngd);
			
			Label lblLot = toolkit.createLabel(btnComposite, Message.getString("mm.mlot_id"));
			lblLot.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
			txtLot = toolkit.createText(btnComposite, "", SWT.BORDER);
			GridData gText = new GridData();
			gText.widthHint = 216;
			gText.heightHint = 25;
			txtLot.setLayoutData(gText);
			txtLot.setTextLimit(32);
			txtLot.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtLot.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					Text tLotId = ((Text) event.widget);
					tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
					case SWT.KEYPAD_CR:
						addAdapter();
						break;
					}
				}
			});
			
			add = toolkit.createButton(btnComposite, "  " + Message.getString(ExceptionBundle.bundle.CommonAdd()) + "  ", SWT.NONE);
			add.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						addAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}						
			});				
			delete = toolkit.createButton(btnComposite, "  " + Message.getString(ExceptionBundle.bundle.CommonDelete()) + "  ", SWT.NONE);
			delete.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
						delAdapter();
					} catch (Exception e) {
						e.printStackTrace();
					}
				};				
			});	
		} catch (Exception e) {
			logger.error("ConumableMatreialForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void addAdapter() {
		try {
			List<WorkOrderBomLine> bomLines = (List<WorkOrderBomLine>)(List<? extends Object>)materialManager.getInput();
			if (bomLines == null || bomLines.size() == 0) {
				return;
			}
			
			String mLotId = txtLot.getText();
			if (StringUtil.isEmpty(mLotId)) {
				UI.showError(Message.getString("mm.please_enter_mlot_id"));
				return;
			}
			
			MMManager mmManager = Framework.getService(MMManager.class);
			MLot enterMLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId, true);
			if (enterMLot.getMainQty().compareTo(BigDecimal.ZERO) <= 0) {
				UI.showError(Message.getString("mm.mlot_qty_less_than_or_equal_zero"));
				return;
			}
			
			WorkOrderBomLine thisBomLine = null;
			boolean flag = true;
			for (WorkOrderBomLine bomLine : bomLines) {	
				if (enterMLot.getMaterialName().equals(bomLine.getMaterialName())) {
					thisBomLine = bomLine;
					flag = false;		
				}
			}
			if (flag) {
				UI.showError(Message.getString("mm.mlot_belong_to_this_material"));
				return;
			}
			
			List<MLot> allMLot = (List<MLot>)(List<? extends Object>)mLotManager.getInput();
			List<MLot> mLots = new ArrayList<MLot>();
			mLots.addAll(allMLot);
			//��������ʹ���˶��١�
			BigDecimal usedQty = BigDecimal.ZERO;
			for (MLot mLot : mLots) {
				if (mLot.getMaterialName().equals(thisBomLine.getMaterialName())) {
					usedQty = usedQty.add(mLot.getTransMainQty());
				}
			}
			
			//��λ���������Ƕ���
			BigDecimal totalQty = thisBomLine.getTransQty();
			
			//��ȥʹ����
			totalQty = totalQty.subtract(usedQty);
				
			if (totalQty.compareTo(BigDecimal.ZERO) <= 0) {
				UI.showError(Message.getString("wip.wo_material_enough"));
				return;
			}
			if (totalQty.compareTo(enterMLot.getMainQty()) >= 0) {
				enterMLot.setTransMainQty(enterMLot.getMainQty());
			} else {
				enterMLot.setTransMainQty(totalQty);
			}
			mLots.add(enterMLot);
			
			mLotManager.setInput(mLots);
			txtLot.setText("");
			txtLot.setFocus();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}		
	
	private void delAdapter() {
		try {
			List<MLot> allMLot = (List<MLot>)(List<? extends Object>)mLotManager.getInput();
			List<MLot> mLots = new ArrayList<MLot>();
			for (MLot mLot : allMLot) {
				mLots.add(mLot);
			}				
			List<Object> os = mLotManager.getCheckedObject();
			if (os.size() != 0) {
				for (Object object : os) {
					MLot lot = (MLot) object;
					mLots.remove(lot);	
				}
			}
			mLotManager.setInput(mLots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}	
	
	@Override
	public boolean saveToObject() {
		if (context != null) {
			if (!validate()){
				return false;
			}
			List<MLot> allMLot = (List<MLot>)(List<? extends Object>)mLotManager.getInput();
			List<MLot> newMLot = new ArrayList<MLot>();
			newMLot.addAll(allMLot);
			context.setmLots(newMLot);
			return true;
		}
		return false;
	}

	@Override
	public void loadFromObject() {
		try {
			//��λ���������Ƕ���
			BigDecimal totalQty = BigDecimal.ZERO;
			for (Lot lot : context.getLots()) {
				totalQty = totalQty.add(lot.getMainQty());
			}
			List<WorkOrderBomLine> stepMaterialBomLines = new ArrayList<WorkOrderBomLine>();
			for (WorkOrderBomLine bomLine : context.getBomLines()) {
				if (BomLine.ITEMCATEGORY_COMPONENT.equals(bomLine.getItemCategory())) {
					bomLine.setTransQty(totalQty.multiply(bomLine.getUnitQty())); //���һ��Ҫ����
					stepMaterialBomLines.add(bomLine);
				}
			}	
			materialManager.setInput(stepMaterialBomLines);
		} catch (Exception e) {
			logger.error("ConumableMatreialForm : loadFromObject", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public Object getObject() {
		return context;
	}

	@Override
	public void setObject(Object object) {
		this.context = (TrackInContext) object;
	}

	@Override
	public boolean validate() {
		List<MLot> allMLot = (List<MLot>)(List<? extends Object>)mLotManager.getInput();
		//�ж�����Ҫ�������Ϊ������
		List<MLot> newMLots = new ArrayList<MLot>();
		for (MLot mLot : allMLot) {
			if (mLot.getTransMainQty() == null || mLot.getTransMainQty().compareTo(BigDecimal.ZERO) <= 0) {
				UI.showError(Message.getString("mm.mlot_qty_less_than_or_equal_zero"));
				return false;
			}
			newMLots.add(mLot);
		}	
		//�ж������Ƿ��ظ�
		String mlot = null;
	    for (int i = 0; i < newMLots.size() - 1; i++) {
	        mlot = newMLots.get(i).getmLotId();
	        for (int j = i + 1; j < newMLots.size(); j++) {
	        	if (mlot.equals(newMLots.get(j).getmLotId())) {
	                UI.showError(Message.getString("mm.mlot_not_repeat"));
					return false;
	            }
	        }
	    }
	    
	    List<WorkOrderBomLine> materialBomLines = (List<WorkOrderBomLine>)(List<? extends Object>)materialManager.getInput();		
	    StringBuffer sb = new StringBuffer();
	    boolean flag = true;
		for (WorkOrderBomLine materialBomLine : materialBomLines) {
			BigDecimal totalQty = BigDecimal.ZERO;
			for (MLot mLot : allMLot) {
				if (materialBomLine.getMaterialName().equals(mLot.getMaterialName())) {
					totalQty = totalQty.add(mLot.getTransMainQty());
				}
			}
			BigDecimal lackQty = materialBomLine.getTransQty().subtract(totalQty);//ȱ����
			if (totalQty.compareTo(materialBomLine.getTransQty()) < 0) {
				flag = false;
				sb.append(materialBomLine.getMaterialName() + Message.getString("common.object_is_missing") + lackQty + materialBomLine.getUomId() + "! \n");
			}
		}
		if (!flag) {
			sb.append(Message.getString("common.whether_to_continue"));
			boolean confirmContinue = UI.showConfirm(sb.toString());
			if (!confirmContinue) {
				return false;
			} 
		}
		return true;
	}

	@Override
	public List<String> getCopyProperties() {
		return null;
	}

	@Override
	public void setEnabled(boolean enabled) {}
	
	@Override
	 public void dispose() {
       if (toolkit != null) {
           toolkit.dispose();
           toolkit = null;
       }
   } 
	
}
