package com.glory.mes.wip.lot.run.trackout.simple;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.Form;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutLot;
import com.glory.mes.wip.lot.run.trackout.TrackOutLotTable;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;

public class SimpleTrackOutStartPage extends FlowWizardPage {

	public static String TABLE_NAME = "WIPTrackOutLotSimple";
	
	protected Form form;
	protected FormToolkit toolkit;
	protected TrackOutWizard tw;
	protected TrackOutContext context;
	
	protected String tableName;
	protected TrackOutLotTable tableManager;
	protected LotStepAttributeForm attributeForm;
	protected Button saveCurrentBatch;
	protected Button btnTrackOutHold;
	
	public SimpleTrackOutStartPage() {
		super();
	}
	
	public SimpleTrackOutStartPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		try {
			tw = (TrackOutWizard) this.getWizard();
			context = (TrackOutContext)tw.getContext();
			toolkit = new FormToolkit(parent.getDisplay());
			
			Composite composite = toolkit.createComposite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			setControl(composite);
			
			Composite tableComp = toolkit.createComposite(composite, SWT.NONE);
			tableComp.setLayout(new GridLayout(1, true));
			tableComp.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			createLotListComposite(tableComp);

			//��ʾAttribute��Ϣ
			Long stepRrn = context.getStep().getObjectRrn();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			List<StepAttribute> stepAttributes = prdManager.getStepAttribute(stepRrn, StepAttribute.CATEGORY_TRACKOUT);
			if (stepAttributes != null && stepAttributes.size() > 0){
				Group attribute = new Group(composite, SWT.NONE);
				attribute.setText(Message.getString("common.attribute"));
				attribute.setLayout(layout);
				attribute.setLayoutData(gd);
								
				Equipment eqp = null;
				if (context.getSelectEquipments() != null && context.getSelectEquipments().size() > 0) {
					eqp = context.getSelectEquipments().get(0);
				}
				attributeForm = new LotStepAttributeForm(attribute, SWT.NONE , null, 
						context.getLots().get(0), stepAttributes, eqp);
				attributeForm.createForm();
				attributeForm.setLayoutData(gd);
			}
		
			//��ʾ��������
			Composite labCom = toolkit.createComposite(composite, SWT.NONE);
			labCom.setLayout(new GridLayout(1, true));
			labCom.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));

			btnTrackOutHold = toolkit.createButton(labCom, Message.getString("wip.trackout_hold"), SWT.CHECK);
			
			if (context.getTrackOutLots().size() > 1) {
				saveCurrentBatch = toolkit.createButton(labCom, Message.getString("wip.bylot.trackout_keepcurrentbatch"), SWT.CHECK);
			}
			setTitle("TrackOut");
			setDescription(Message.getString("common.trackOut_first_title"));
		
		} catch(Exception e){
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void createLotListComposite(Composite parent) throws Exception {
		//�����ҪTrackOut������
		List<Lot> lots = new ArrayList<Lot>();
		if (context.getLots() != null && context.getLots().size() > 0) {
			lots.addAll(context.getLots());
		}
		
		List<TrackOutLot> trackOutLots = new ArrayList<TrackOutLot>();
		for (Lot lot : lots) {
			TrackOutLot trackOutLot = new TrackOutLot(lot);
			trackOutLot.setOutMainQty(trackOutLot.getMainQty());
			trackOutLots.add(trackOutLot);
		}
		
		ADManager adManager = Framework.getService(ADManager.class);
		if (tableName == null || tableName.trim().length() == 0) {
			tableName = TABLE_NAME;
		}
		
		ADTable adTable = adManager.getADTable(Env.getOrgRrn(), tableName);		
		tableManager = new TrackOutLotTable(adTable);
		tableManager.newViewer(parent);
		tableManager.setInput(trackOutLots);
		
		//����Ĭ��ѡ����
		if (context.getOutLots() != null && context.getLots() != null) {
			List<Object> selectedTrackOutLots = new ArrayList<Object>();
			for (Lot outLot : context.getLots()) {
				for (TrackOutLot trackOutLot : trackOutLots) {
					if (trackOutLot.getLot().equals(outLot)) {
						selectedTrackOutLots.add(trackOutLot);
						break;
					}
				}
			}
			tableManager.setCheckedObject(selectedTrackOutLots);
		}
		context.setTrackOutLots(trackOutLots);
	}
	
	@Override
	public String doNext() {
		String returnStr = "";
		try {
	 		if (!validate()) {
	 			return "";
	 		}
	 		if (attributeForm != null){
	 			//����Attribute��Ϣ
				context.setLotAttributeValues(attributeForm.getAttributeValues());	
			}
	 		
	 		List<TrackOutLot> trackOutLots = context.getTrackOutLots();
			List<Lot> outLots = new ArrayList<Lot>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				outLots.add(trackOutLot.getLot());
			}
			context.setTrackOutLots(trackOutLots);
			context.setOutLots(outLots);
			
			if (btnTrackOutHold != null) {
				if (btnTrackOutHold.getSelection()) {
					context.getInContent().setTrackOutHold(true);
				} else {
					context.getInContent().setTrackOutHold(false);
				}
			}
			//��¼Comments
			List<LotAction> actions = new ArrayList<LotAction>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				if (trackOutLot.getComment() != null && trackOutLot.getComment().trim().length() > 0) {
					LotAction lotAction = new LotAction();
					lotAction.setLotRrn(trackOutLot.getLot().getObjectRrn());
					lotAction.setActionComment(trackOutLot.getComment());
					actions.add(lotAction);
				}
			}
			context.getInContent().setActions(actions);
			
			tw.invokeTrackOut();
			returnStr = TrackOutDialog.LOT_LIST_PAGE;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return returnStr;
	}

	public boolean validate() {
		List<Object> selectObjs = tableManager.getCheckedObject();
		List<TrackOutLot> trackOutLots = new ArrayList<TrackOutLot>();
		for (Object selectObj : selectObjs) {
			trackOutLots.add((TrackOutLot)selectObj);
		}
		if (trackOutLots == null || trackOutLots.size() == 0){
			setErrorMessage(Message.getString("wip.lot_select_alert"));
			return false;
		}
		context.setTrackOutLots(trackOutLots);
		
		if (attributeForm != null){
 			//���Attribute��Ϣ
			if (!attributeForm.saveToObject()) {
				return false;
			}					
		}
		
		//����ʵ����������Flag
		context.setReworkFlag(false);
		context.setScrapFlag(false);
		return true;
	}
	
	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	protected List<Lot> getLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = null;
		if (lot.getBatchId() != null) {
			lotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
		} else {
			lotList = new ArrayList<Lot>();
			lot = lotManager.getLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getTableName() {
		return tableName;
	}

}