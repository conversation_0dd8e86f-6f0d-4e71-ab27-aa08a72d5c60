package com.glory.mes.wip.lot.run.trackin.byschedule;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;

public class LotListTableManager extends FixSizeListTableManager {

	public LotListTableManager(ADTable adTable) {
		super(adTable);
	}
	
	public LotListTableManager(ADTable adTable,  boolean checkFlag) {
		super(adTable, checkFlag);
	}
	
	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new LotItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
	
}
