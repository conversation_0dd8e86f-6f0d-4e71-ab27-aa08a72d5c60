package com.glory.mes.wip.lot.run.trackmove;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.jface.wizard.IWizardPage;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;

public class TrackMoveWizard extends TrackOutWizard {

    public TrackMoveWizard() {
	}
	
	public TrackMoveWizard(TrackMoveContext context) {
		super(context);
	}

	@Override
	public boolean performFinish() {
		return true;
	}

	@Override
	public void invokeTrackOut() throws Exception {
		TrackMoveContext context = (TrackMoveContext)this.getContext();
		for (Lot lot : context.getOutLots()) {
			lot.setOperator1(context.getOperator1());
		}
		InContext inContext = context.getInContent();
		inContext.setLots(context.getOutLots());
		inContext.setAttributeValues(context.getLotAttributeValues());
		inContext.setmLots(context.getmLots());
		inContext.setOperator1(context.getOperator1());
//		ISpcProvider spcProvider = SpcProviderExtensionPoint.getSpcProvider();
//		if (spcProvider != null) {
//			moveContext.setSendSpc(true);
//		} 
		LotManager lotManager = Framework.getService(LotManager.class);
		OutContext outContext = lotManager.trackMove(inContext, Env.getSessionContext());
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll(outContext.getLots());
		lots.addAll(outContext.getReworkLots());
		context.setOutLots(lots);
	}
	
	@Override
	public IWizardPage getStartingPage() {
		return startPage;
	}

}
