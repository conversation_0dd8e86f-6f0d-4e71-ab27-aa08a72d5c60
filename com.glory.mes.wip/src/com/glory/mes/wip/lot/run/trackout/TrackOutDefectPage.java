package com.glory.mes.wip.lot.run.trackout;

import java.math.BigDecimal;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutDefectPage extends FlowWizardPage {

	protected TrackOutContext context;
	
	protected TrackOutDefectComponent defectComponent;
	protected TrackOutDefectQty defectQty;                   
				
	public TrackOutDefectPage() {
		super();
	}
	
	public TrackOutDefectPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		context = (TrackOutContext)((TrackOutWizard)this.getWizard()).getContext();
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		if (context.getInContent().getActions() != null) {
			List<LotAction> scrapLotActions = new ArrayList<LotAction>();
			for (LotAction lotAction : context.getInContent().getActions()) {
				if (LotAction.ACTIONTYPE_DEFECT.equals(lotAction.getActionType())) {
					scrapLotActions.add(lotAction);
				}
			}
			context.getInContent().getActions().removeAll(scrapLotActions);
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				//�������дȱ��������outMain�˻�ȱ���������ص���վ��ҳ
				if(trackOutLot.getScrapMainQty() != null) {
					if (trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
						trackOutLot.setOutMainQty(trackOutLot.getOutMainQty().add(trackOutLot.getScrapMainQty()));
					}
				}
			}
		}
		setErrorMessage("");
		return super.doPrevious();
	}
	
	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
//				if (!defectComponent.validate()) {
//					return "";
//				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_DEFECT.equals(lotAction.getActionType())) {
								scrapLotActions.add(lotAction);
							}
					}
					list.removeAll(scrapLotActions);
					list.addAll(defectComponent.getDefectLotActions());
					context.getInContent().setActions(list);
				} else {
					List<LotAction> list = new ArrayList<LotAction>();
					list.addAll(defectComponent.getDefectLotActions());
					context.getInContent().setActions(list);
				}
				
			} else {
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_DEFECT.equals(lotAction.getActionType())) {
								scrapLotActions.add(lotAction);
							}
					}
					list.removeAll(scrapLotActions);
					list.addAll(defectQty.getDefectLotActions());
					context.getInContent().setActions(list);
				} else {
					List<LotAction> list = new ArrayList<LotAction>();
					list.addAll(defectQty.getDefectLotActions());
					context.getInContent().setActions(list);
				}
			}
			if (context.isReworkFlag()) {
				return TrackOutDialog.REWORK_PAGE;
			} else {
				((TrackOutWizard)this.getWizard()).invokeTrackOut();
				return getDefaultDirect();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

		
	@Override
	public void createControl(Composite parent) {
		setTitle(Message.getString("wip.defect_code_editer"));
		List<Lot> lots = context.getLots();

		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		defectComponent = null;
		defectQty = null;
		List<Lot> scrapLots = new ArrayList<Lot>();
		for (Lot lot : lots) {
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				if (trackOutLot.getLot().getObjectRrn().equals(lot.getObjectRrn())) {
					if (context.isInputScrap()) {
						if (trackOutLot.getScrapMainQty() != null && trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
							scrapLots.add(lot);
						}
					} else {
						if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) < 0) {
							scrapLots.add(lot);
						}
					}
					break;
				}
			}
		}
		if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			defectComponent = new TrackOutDefectComponent(composite, scrapLots, context);
			defectComponent.createContent();
		} else if (QtyUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			defectQty = new TrackOutDefectQty(composite, scrapLots, context);
			defectQty.createContent();
		}
		setControl(composite);
	}

}
