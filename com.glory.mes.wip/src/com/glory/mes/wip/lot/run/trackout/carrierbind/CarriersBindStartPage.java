package com.glory.mes.wip.lot.run.trackout.carrierbind;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;

import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutLot;
import com.glory.mes.wip.lot.run.trackout.TrackOutStartPage;
import com.glory.mes.wip.model.Lot;

/**
 * ���ص�TrackOutStartPage���ı���תҳ��
 * <AUTHOR>
 *
 */
@Deprecated
public class CarriersBindStartPage extends TrackOutStartPage {
	
	public CarriersBindStartPage() {
		super();
	}
	
	public CarriersBindStartPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}
	
	@Override
	public String doNext() {
		String returnStr = "";
		try {
	 		if (!validate()) {
	 			return "";
	 		}
	 		if (attributeForm != null){
	 			//����Attribute��Ϣ
				context.setLotAttributeValues(attributeForm.getAttributeValues());	
			}
	 		
	 		List<TrackOutLot> trackOutLots = context.getTrackOutLots();
			List<Lot> outLots = new ArrayList<Lot>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				outLots.add(trackOutLot.getLot());
			}
			context.setTrackOutLots(trackOutLots);
			context.setOutLots(outLots);
			
			if (reworkAutoMerge != null) {
				if (reworkAutoMerge.getSelection()) {
					context.getInContent().setAutoMerge(true);
				} else {
					context.getInContent().setAutoMerge(false);
				}
			}
			//��¼Comments
			List<LotAction> actions = new ArrayList<LotAction>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				if (trackOutLot.getComment() != null && trackOutLot.getComment().trim().length() > 0) {
					LotAction lotAction = new LotAction();
					lotAction.setLotRrn(trackOutLot.getLot().getObjectRrn());
					lotAction.setActionComment(trackOutLot.getComment());
					actions.add(lotAction);
				}
			}
			context.getInContent().setActions(actions);
			
			if (context.isScrapFlag()) {
				returnStr = TrackOutDialog.SCRAP_PAGE;
			} else if (context.isReworkFlag()) {
				returnStr = TrackOutDialog.REWORK_PAGE;
			} else {
				// ��ԭ�治֮ͬ��
				// tw.invokeTrackOut();
				// returnStr = TrackOutDialog.LOT_LIST_PAGE;
				returnStr = getDefaultDirect();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return returnStr;
	}

}
