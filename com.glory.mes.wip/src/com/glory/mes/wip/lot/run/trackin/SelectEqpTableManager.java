package com.glory.mes.wip.lot.run.trackin;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.mes.wip.model.Lot;

public class SelectEqpTableManager extends ListTableManager{

	
	public SelectEqpTableManager(ADTable adTable) {
		super(adTable);
	}

	public SelectEqpTableManager(ADTable adTable, boolean checkFlag) {
		super(adTable, checkFlag);
	}
	
	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new SelectEqpItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
}
