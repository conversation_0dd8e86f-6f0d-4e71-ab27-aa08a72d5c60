package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.entitymanager.forms.ScorllFormComposite;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.defect.DefectQtyComposite;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutDefectQty extends ScorllFormComposite {

	protected TrackOutContext context;
	
	public TrackOutDefectQty(Composite parent, List<Lot> scrapLots, TrackOutContext context) {
		super(parent, scrapLots);
		this.context = context;
	}

	@Override
	public Composite createUnit(Composite com, Object obj) {
		Group group = new Group(com, SWT.NONE);
		GridLayout layout = new GridLayout(1, true);
		layout.numColumns = 1;
		layout.marginRight = 1;
		layout.marginLeft = 1;
		group.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.widthHint = 250;
		group.setLayoutData(gd);
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		Lot lot = (Lot) obj;
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}
		
		DefectQtyComposite form = new DefectQtyComposite(group,  SWT.NONE, lot, context.getStep().getDefectCodeSrc());
		return form;
	}
	
	public List<LotAction> getDefectLotActions() {
		List<LotAction> defectLotAction = new ArrayList<LotAction>();
		for (Composite unit : units) {
			DefectQtyComposite defectUnit = (DefectQtyComposite)unit;
			for (ProcessUnit processUnit : defectUnit.getDedectUnits()) {
				QtyUnit qtyUnit = (QtyUnit)processUnit;
				//DedectCode����
				LotAction lotDefectAction = new LotAction();
				lotDefectAction.setActionType(LotAction.ACTIONTYPE_DEFECT);
				lotDefectAction.setActionCode(qtyUnit.getActionCode());	
				List<ProcessUnit> qtyDefUnits = new ArrayList<ProcessUnit>();
				qtyDefUnits.add(qtyUnit);
				lotDefectAction.setActionUnits(qtyDefUnits);
				lotDefectAction.setLotRrn(defectUnit.getLot().getObjectRrn());
				//����ȱ����
				lotDefectAction.setActionCodeObject(qtyUnit.getActionCode());
				lotDefectAction.setActionCodeGroup(qtyUnit.getActionCode());
				defectLotAction.add(lotDefectAction);	
			}				
		}
		return defectLotAction;
	}
	
}
