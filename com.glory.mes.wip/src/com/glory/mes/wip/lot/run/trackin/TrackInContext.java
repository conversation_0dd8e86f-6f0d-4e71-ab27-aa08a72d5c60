package com.glory.mes.wip.lot.run.trackin;

import java.util.List;

import org.eclipse.ui.forms.IManagedForm;

import com.glory.mes.wip.his.ComponentUnitHis;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.model.Lot;

public class TrackInContext extends RunWizardContext {
	
	public static String TRACK_IN_BYLOT = "DefaultTrackIn";
	public static String TRACK_IN_BYEQP = "DefaultTrackIn";
	
	private IManagedForm mangedForm;
	private String trackInType;
	private String reticleId;
	
	/**
	 * ��������(ֻ��Batch����ʱ����)
	 * Batch����ʱֻ���Batch�е�һ��Lot
	 * ע��ֻ����Batch����ʱ���ܸ�ֵ
	 */
	private Lot measureLot;
	
	private boolean isMeasure = false;
	
	//����TrackIn����
	private Lot trackInLot;
	
	private List<ComponentUnitHis> componentUnitMaps; 
	
	public TrackInContext() {
	}
	
	public void setTrackInType(String trackInType) {
		this.trackInType = trackInType;
	}

	public String getTrackInType() {
		return trackInType;
	}

	public IManagedForm getMangedForm() {
		return mangedForm;
	}

	public void setMangedForm(IManagedForm mangedForm) {
		this.mangedForm = mangedForm;
	}
	
	public String getCategory() {
		return this.getTrackInType();
	}
	
	public List<ComponentUnitHis> getComponentUnitMaps() {
		return componentUnitMaps;
	}

	public void setComponentUnitMaps(List<ComponentUnitHis> componentUnitMaps) {
		this.componentUnitMaps = componentUnitMaps;
	}

	public Lot getTrackInLot() {
		return trackInLot;
	}

	public void setTrackInLot(Lot trackInLot) {
		this.trackInLot = trackInLot;
	}

	public String getReticleId() {
		return reticleId;
	}

	public void setReticleId(String reticleId) {
		this.reticleId = reticleId;
	}

	public Lot getMeasureLot() {
		return measureLot;
	}

	public void setMeasureLot(Lot measureLot) {
		this.measureLot = measureLot;
	}

	public boolean isMeasure() {
		return isMeasure;
	}

	public void setMeasure(boolean isMeasure) {
		this.isMeasure = isMeasure;
	}

}
