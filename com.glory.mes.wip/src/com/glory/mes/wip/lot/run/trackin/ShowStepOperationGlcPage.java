package com.glory.mes.wip.lot.run.trackin;

import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Operation;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.LotStepAttributeComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.operation.StepOperationDialog;
import com.glory.mes.wip.model.Lot;
import com.google.common.base.Objects;

public class ShowStepOperationGlcPage extends GlcFlowWizardPage {
	
	private static final Logger logger = Logger.getLogger(ShowStepOperationPage.class);
	
	public static final String FIELD_OPERATION = "operation";
	public static final String FIELD_FUTURENOTE = "futureNote";
	public static final String FIELD_LOTSTEP_ATTRIBUTE = "LotStepAttribute";
	
	public static final String TAB_LOTSTEP_ATTRIBUTE = "LotStepAttribute";
	public static final String TAB_FUTURENOTE = "FutureNote";

	protected ListTableManagerField operationField;
	protected TextField futureNoteField;
	protected CustomField equipmentInfoField;
	protected LotStepAttributeComposite attributeComposite;
	
	protected RunWizardContext context;
	protected Lot lot;
	protected List<Operation> operations;
	protected List<Node> nodes;
	protected List<FutureNote> futureNotes;
	protected List<FutureNote> procedurefutureNotes;
	protected List<StepAttribute> stepAttributes;
	
	public ShowStepOperationGlcPage() {
		super();
	}
	
	@Override
	protected boolean skipPageValidate() {
		try {
			RunWizardContext context = (RunWizardContext) ((TrackInWizard) getWizard()).getContext();
			LotManager lotManager = Framework.getService(LotManager.class);
			PrdManager prdManager = Framework.getService(PrdManager.class);
			lot = context.getLots().get(0);
			operations = lotManager.getLotCurrentOpeartion(lot);
			nodes = prdManager.getProcessFlowList(lot.getProcessInstanceRrn());
			futureNotes = lotManager.getLotFutureNote((StepState) nodes.get(nodes.size() - 1), lot);
			procedurefutureNotes = lotManager.getProcedureFutureNote((StepState) nodes.get(nodes.size() - 1));
			stepAttributes = prdManager.getStepAttribute(lot.getStepRrn(),StepAttribute.CATEGORY_TRACKIN);
			
			// ��û�в���ָʾ����ת����һҳ
			FlowWizard tiWizard = (FlowWizard) this.getWizard();
			if ((operations == null || operations.size() == 0)
					&& (stepAttributes == null || stepAttributes.size() == 0)
					&& (futureNotes == null || futureNotes.size() == 0)
					&& (procedurefutureNotes == null || procedurefutureNotes.size() == 0)) {
				tiWizard.getDialog().skipPressed();
				return true;
			}
		} catch (Exception e) {
			logger.error("ShowStepOperationForm : skipPageValidate()", e);
		}
		return super.skipPageValidate();
	}
	
	@Override
	protected ADTable changeADTable(ADTable adTable) {
		List<ADTab> tabs = adTable.getTabs();
		if (CollectionUtils.isEmpty(stepAttributes)) {
			Optional<ADTab> adTab = tabs.stream().filter(tab -> Objects.equal(tab.getName(), TAB_LOTSTEP_ATTRIBUTE)).findFirst();
			if (adTab.isPresent()) {
				tabs.remove(adTab.get());
			}
		}
		if (CollectionUtils.isEmpty(futureNotes) && CollectionUtils.isEmpty(procedurefutureNotes)) {
			Optional<ADTab> adTab = tabs.stream().filter(tab -> Objects.equal(tab.getName(), TAB_FUTURENOTE)).findFirst();
			if (adTab.isPresent()) {
				tabs.remove(adTab.get());
			}
		}
		adTable.setTabs(tabs);
		return adTable;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		setTitle(Message.getString(WipExceptionBundle.bundle.WipOperationTitle()));
		setMessage(Message.getString(WipExceptionBundle.bundle.WipOperationMessage()));
		setPageComplete(true);
		operationField = form.getFieldByControlId(FIELD_OPERATION, ListTableManagerField.class);
		futureNoteField = form.getFieldByControlId(FIELD_FUTURENOTE, TextField.class);
		equipmentInfoField = form.getFieldByControlId(FIELD_LOTSTEP_ATTRIBUTE, CustomField.class);
		if (equipmentInfoField != null) {
			attributeComposite = (LotStepAttributeComposite) equipmentInfoField.getCustomComposite();
		}
		
		operationField.getListTableManager().addDoubleClickListener(new IMouseAction() {
			
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				Operation operation = (Operation) operationField.getListTableManager().getSelectedObject();
				StepOperationDialog stepOperationDialog = new StepOperationDialog(getShell(), operation);
				stepOperationDialog.open();
			}
		});
		
		operationField.getListTableManager().setInput(operations);
		
		if (attributeComposite != null) {
			attributeComposite.setValue(stepAttributes);
			attributeComposite.setLot(lot);
			attributeComposite.refresh();
		} 
		
		//��ʾδ����ע
		StringBuffer textvalue = new StringBuffer();
		if (futureNoteField != null) {
			if (futureNotes != null && futureNotes.size() > 0) {
				textvalue.append(Message.getString(WipExceptionBundle.bundle.WipLotFutureNote()) + "----" + lot.getLotId() + "---------------------------------------\n");
				for (int n = 0; n < futureNotes.size(); n++) {
					textvalue.append((n + 1) + ": " + futureNotes.get(n).getNote() + "\n");
				}
			}
			
			if (procedurefutureNotes != null && procedurefutureNotes.size() > 0) {
				textvalue.append(Message.getString(WipExceptionBundle.bundle.WipProcedureFutureNote()) + "----" + lot.getLotId() + "---------------------------------------\n");
				for (int n = 0; n < procedurefutureNotes.size(); n++) {
					textvalue.append((n + 1) + ": " + procedurefutureNotes.get(n).getNote() + "\n");
				}
			}
			futureNoteField.getTextControl().setLayoutData(new GridData(GridData.FILL_BOTH));
			futureNoteField.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			futureNoteField.getTextControl().setText(textvalue.toString());
		}
	}

	@Override
	public String doNext() {
		RunWizardContext context = (RunWizardContext) ((TrackInWizard) getWizard()).getContext();
		if (attributeComposite != null) {
			boolean saveFlag = true;					
			if (attributeComposite.validate()) {
				saveFlag = false;
			}					
			if (saveFlag) {
				context.setLotAttributeValues((List)attributeComposite.getValue());	
			} else {
				return null;
			}			
		}
		Step step = context.getStep();
		if (step == null || step.getCapability() == null) {
			UI.showError(Message.getString(WipExceptionBundle.bundle.WipStepCapabilityIsNull()));
			return null;
		}
		return getDefaultDirect();
	}
	
	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		this.setErrorMessage(null);
		return super.doPrevious();
	}

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, true);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

}
