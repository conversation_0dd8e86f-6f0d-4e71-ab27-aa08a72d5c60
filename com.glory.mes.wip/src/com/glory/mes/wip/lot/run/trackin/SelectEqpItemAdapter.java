package com.glory.mes.wip.lot.run.trackin;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.model.Lot;

public class SelectEqpItemAdapter extends ListItemAdapter<Lot> {
	

	@Override
	public Color getBackground(Object element, String id) {
		try {
			Equipment equipment = (Equipment)element;
			SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
//			if (StringUtil.isEmpty(equipment.getTransEquipmentRecipe()) 
//					&& !MesCfMod.isNotUseEquipmentRecipe(equipment.getOrgRrn(), sysParameterManager)) {
//				if(MesCfMod.isDummyNotUseEquipmentRecipe(equipment.getOrgRrn(), sysParameterManager)) {
//					if(!"Y".equals(equipment.getAttribute5())) {
//						return new Color(Display.getCurrent(), 255, 111, 111);
//					}
//				} else {
//					return new Color(Display.getCurrent(), 255, 111, 111);
//				}
//			}
//			return new Color(Display.getCurrent(), 255, 111, 111);
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	return null;
	}

}
