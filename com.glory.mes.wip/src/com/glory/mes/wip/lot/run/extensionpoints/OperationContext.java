package com.glory.mes.wip.lot.run.extensionpoints;

import java.util.List;
import java.util.Map;

import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Maps;


public class OperationContext {
	public static int OK_ID = 0;
	public static int FAILED_ID = 1;
	
	private IEventBroker eventBroker;
	
	private int returnCode;

	private Object parentObject;

	private List<Lot> lots;
	
	private List<Equipment> equipments;
	
	private boolean ignoreStandardMethod = false;
	
	private Map<String, Object> datas;
	
	public IEventBroker getEventBroker() {
		return eventBroker;
	}

	public void setEventBroker(IEventBroker eventBroker) {
		this.eventBroker = eventBroker;
	}

	public int getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(int returnCode) {
		this.returnCode = returnCode;
	}
	
	public Object getParentObject() {
		return parentObject;
	}

	public void setParentObject(Object parentObject) {
		this.parentObject = parentObject;
	}

	public List<Lot> getLots() {
		return lots;
	}

	public void setLots(List<Lot> lots) {
		this.lots = lots;
	}

	public List<Equipment> getEquipments() {
		return equipments;
	}

	public void setEquipments(List<Equipment> equipments) {
		this.equipments = equipments;
	}

	public boolean isIgnoreStandardMethod() {
		return ignoreStandardMethod;
	}

	public void setIgnoreStandardMethod(boolean ignoreStandardMethod) {
		this.ignoreStandardMethod = ignoreStandardMethod;
	}
	
	public Object getData(String key) {
		if (datas != null) {
			return datas.get(key);
		}
		return null;
	}

	public void setData(String key, Object data) {
		if (datas == null) {
			datas = Maps.newHashMap();
		}
		datas.put(key, data);
	}
	
}
