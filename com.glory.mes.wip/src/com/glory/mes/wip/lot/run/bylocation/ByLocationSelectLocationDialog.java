package com.glory.mes.wip.lot.run.bylocation;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.Location;

public class ByLocationSelectLocationDialog extends GlcBaseDialog{

	protected EntityFormField entityFormField;
	
	private static final String FIELD_LOCATION = "location";

	private static final String FIELD_SUBLOCATION = "attribute1";

	private RefTableField locationField;
	private RefTableField subLocationField;
	
	public ByLocationSelectLocationDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		init();
	}
	
	@SuppressWarnings("unchecked")
	private void init() {
		try {
			locationField = form.getFieldByControlId(FIELD_LOCATION, RefTableField.class);
			subLocationField = form.getFieldByControlId(FIELD_SUBLOCATION, RefTableField.class);
			
			locationField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					RefTableField refTableField = (RefTableField) arg0;
					List<Location> locationList = (List<Location>) refTableField.getInput();
					if (arg1 != null) {
						locationList = locationList.stream().filter(location -> arg1.equals(location.getName())).collect(Collectors.toList());
						initSubLocationAdaptor(locationList.get(0).getObjectRrn());
					} else {
						subLocationField.setValue(null);
						subLocationField.setInput(new ArrayList<Location>());
					}
				}
			});
			
			MBASManager basManager = Framework.getService(MBASManager.class);
			String locationId = (String)locationField.getValue();
			Location location = basManager.getLocationById(Env.getOrgRrn(), locationId);
			if (location != null) {
				initSubLocationAdaptor(location.getObjectRrn());
			} else {
				subLocationField.setValue(null);
				subLocationField.setInput(new ArrayList<Location>());
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void initSubLocationAdaptor(Long locationObjRrn) {
		try {
			MBASManager basManager = Framework.getService(MBASManager.class);
			List<Location> storagelist = Framework.getService(ADManager.class).getEntityList(Env.getOrgRrn(),
					Location.class, Env.getMaxResult(), " parentLocationRrn = " + String.valueOf(locationObjRrn), null);
			String subLocationId = (String)subLocationField.getValue();
			Location subLocation = basManager.getLocationById(Env.getOrgRrn(), subLocationId);
			if (!storagelist.contains(subLocation)) {
				subLocationField.setValue(null);
			}
			subLocationField.setInput(storagelist);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	protected void cancelPressed() {
		super.cancelPressed();
	}

	
	@Override
	 protected Point getInitialSize() {
	    return new Point(600,400);
	 }
	
}
