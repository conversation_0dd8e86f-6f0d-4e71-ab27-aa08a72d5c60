package com.glory.mes.wip.lot.run.trackout;

import java.math.BigDecimal;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutScrapPage extends FlowWizardPage {

	protected TrackOutContext context;
	
	protected TrackOutScrapComponent scrapComponent;
	protected TrackOutScrapQty scrapQty;                   

	public TrackOutScrapPage() {
		super();
	}
	
	public TrackOutScrapPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		context = (TrackOutContext)((TrackOutWizard)this.getWizard()).getContext();
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		if (context.getInContent().getActions() != null) {
			List<LotAction> scrapLotActions = new ArrayList<LotAction>();
			for (LotAction lotAction : context.getInContent().getActions()) {
				if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
					scrapLotActions.add(lotAction);
				}
			}
			context.getInContent().getActions().removeAll(scrapLotActions);
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				//�������д����������outMain�˻ر����������ص���վ��ҳ
				if(trackOutLot.getScrapMainQty() != null) {
					if (trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
						trackOutLot.setOutMainQty(trackOutLot.getOutMainQty().add(trackOutLot.getScrapMainQty()));
					}
				}
			}
		}
		setErrorMessage("");
		return super.doPrevious();
	}
	
	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
				if (!scrapComponent.validate()) {
					return "";
				}
				if (!checkScrapQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							scrapLotActions.add(lotAction);
						}
					}
					list.removeAll(scrapLotActions);
					list.addAll(scrapComponent.getScrapLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapComponent.getScrapLotActions());
				}
				
			} else {
				if (!checkScrapQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							scrapLotActions.add(lotAction);
						}
					}
					list.removeAll(scrapLotActions);
					list.addAll(scrapQty.getScrapLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapQty.getScrapLotActions());
				}
			}
			if (context.isReworkFlag()) {
				return TrackOutDialog.REWORK_PAGE;
			} else {
				((TrackOutWizard)this.getWizard()).invokeTrackOut();
				return getDefaultDirect();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

	protected boolean checkScrapQty() {	
		List<TrackOutLot> trackOutLots = context.getTrackOutLots();		
		//�����������
		List<LotAction> scrapUnits;
		if (scrapComponent != null) {
			scrapUnits = scrapComponent.getScrapLotActions();
		} else {
			scrapUnits = scrapQty.getScrapLotActions();
		}
		
		for (TrackOutLot trackOutLot : trackOutLots) {
			List<ProcessUnit> units = new ArrayList<ProcessUnit>();
			for (LotAction lotAction : scrapUnits) {
				if (trackOutLot.getLot().getObjectRrn().equals(lotAction.getLotRrn())) {
					units.addAll(lotAction.getActionUnits());
				}				
			}
			
			BigDecimal scrapMainQty = BigDecimal.ZERO;
			BigDecimal scrapSubQty = null;
			if (trackOutLot.getSubQty() != null) {
				scrapSubQty = BigDecimal.ZERO;
			}
			for (ProcessUnit unit : units) {
				scrapMainQty = scrapMainQty.add(unit.getMainQty());//����ı�������2
				if (scrapSubQty != null) {
					scrapSubQty = scrapSubQty.add(unit.getSubQty() != null ? unit.getSubQty() : BigDecimal.ZERO);
				}
			}
			
			if (context.isInputScrap()) {
				if (trackOutLot.getScrapMainQty() != null) {
					if (scrapMainQty.compareTo(trackOutLot.getScrapMainQty()) !=  0) {
						setErrorMessage(trackOutLot.getLotId() + Message.getString("wip.trackout_scrap_qty") + trackOutLot.getScrapMainQty());
						return false;
					}
				}
			} else {
				BigDecimal qty = trackOutLot.getMainQty().subtract(trackOutLot.getOutMainQty());
				if (scrapMainQty.compareTo(qty) != 0) {
					setErrorMessage(trackOutLot.getLotId() + Message.getString("wip.trackout_scrap_qty") + qty);
					return false;
				}
			}
		}
		return true;
	}
		
	@Override
	public void createControl(Composite parent) {
		setTitle(Message.getString("wip.lot_scrap_operate"));
		List<Lot> lots = context.getLots();

		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		scrapComponent = null;
		scrapQty = null;
		List<Lot> scrapLots = new ArrayList<Lot>();
		for (Lot lot : lots) {
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				if (trackOutLot.getLot().getObjectRrn().equals(lot.getObjectRrn())) {
					if (context.isInputScrap()) {
						if (trackOutLot.getScrapMainQty() != null && trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
							scrapLots.add(lot);
						}
					} else {
						if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) < 0) {
							scrapLots.add(lot);
						}
					}
					break;
				}
			}
		}
		if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			scrapComponent = new TrackOutScrapComponent(composite, scrapLots, context);
			scrapComponent.createContent();
		} else if (QtyUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			scrapQty = new TrackOutScrapQty(composite, scrapLots, context);
			scrapQty.createContent();
		}
		setControl(composite);
	}

}
