package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.jface.wizard.IWizardPage;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;

public class TrackOutWizard extends RunWizard {

    public TrackOutWizard() {}
	
	public TrackOutWizard(TrackOutContext context) {
		super(context);
	}

	@Override
	public boolean performFinish() {
		return true;
	}

	public void invokeTrackOut() throws Exception {
		TrackOutContext context = (TrackOutContext)this.getContext();
		for (Lot lot : context.getOutLots()) {
			lot.setOperator1(context.getOperator1());
		}
		InContext inContext = context.getInContent();
		inContext.setLots(context.getOutLots());
		inContext.setAttributeValues(context.getLotAttributeValues());
		inContext.setmLots(context.getmLots());
		inContext.setOperator1(context.getOperator1());
		
		LotManager lotManager = Framework.getService(LotManager.class);
		OutContext outContext = lotManager.trackOut(inContext, Env.getSessionContext());
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll(outContext.getLots());
		for (Lot reworkLot : outContext.getReworkLots()) {
			if (!lots.contains(reworkLot)) {
				lots.add(reworkLot);
			}
		}
		context.setOutLots(lots);
	}
	
	@Override
	public IWizardPage getStartingPage() {
		return startPage;
	}

	public void setContext(TrackOutContext context) {
		this.context = context;
	}
	
	public TrackOutContext getContext() {
		return (TrackOutContext)context;
	}
}
