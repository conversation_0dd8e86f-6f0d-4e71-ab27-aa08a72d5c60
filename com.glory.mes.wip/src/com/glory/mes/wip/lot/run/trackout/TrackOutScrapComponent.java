package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.entitymanager.forms.ScorllFormComposite;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.scrap.ScrapComponentUnitForm;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class TrackOutScrapComponent extends ScorllFormComposite {

	TrackOutContext context;
	
	public TrackOutScrapComponent(Composite parent, List<Lot> scrapLots, TrackOutContext context) {
		super(parent, scrapLots);
		this.context = context;
	}

	@Override
	public Composite createUnit(Composite com, Object obj) {
		Group group = new Group(com, SWT.NONE);
		GridLayout gd = new GridLayout(1, true);
		gd.numColumns = 1;
		gd.marginRight = 1;
		gd.marginLeft = 1;
		group.setLayout(gd);
		group.setLayoutData(new GridData(GridData.FILL_BOTH));
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		Lot lot = (Lot) obj;
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}
		
		ScrapComponentUnitForm form = new ScrapComponentUnitForm(group, SWT.NONE, lot);
		
		return form;
	}
	
	public boolean validate() {
		for (Composite unit : units) {
			ScrapComponentUnitForm scrapUnit = (ScrapComponentUnitForm)unit;
			if (!scrapUnit.validate()) {
				return false;
			}
		}
		return true;
	}
	
	public List<LotAction> getScrapLotActions() {
		List<LotAction> scrapLotAction = new ArrayList<LotAction>();		
		for (Composite unit : units) {
			ScrapComponentUnitForm scrapUnit = (ScrapComponentUnitForm)unit;		
			Map<String, List<ProcessUnit>> scrapActionMap = new HashMap<String, List<ProcessUnit>>();
			for (ProcessUnit processUnit : scrapUnit.getScrapUnits()) {								
				ComponentUnit componentUnit = (ComponentUnit)processUnit;
				if (scrapActionMap.containsKey(componentUnit.getActionCode())) {
					List<ProcessUnit> list = scrapActionMap.get(componentUnit.getActionCode());
					list.add(componentUnit);
					scrapActionMap.put(componentUnit.getActionCode(), list);
				} else {
					List<ProcessUnit> list = new ArrayList<ProcessUnit>();
					list.add(componentUnit);
					scrapActionMap.put(componentUnit.getActionCode(), list);
				}
			}	
			
			for (String key : scrapActionMap.keySet()) {
				LotAction lotAction = new LotAction();				
				lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
				lotAction.setActionCode(key);	
				
				List<ProcessUnit> processUnits = scrapActionMap.get(key);
				String equipmentId = processUnits.get(0).getEquipmentId();
				if (StringUtil.isEmpty(equipmentId)) {
					equipmentId = scrapUnit.getLot().getEquipmentId();
				}
				lotAction.setActionUnits(processUnits);
				lotAction.setEquipmentId(equipmentId);
				
				lotAction.setLotRrn(scrapUnit.getLot().getObjectRrn());
				scrapLotAction.add(lotAction);
			}
		}			
		return scrapLotAction;
	}
	
}
