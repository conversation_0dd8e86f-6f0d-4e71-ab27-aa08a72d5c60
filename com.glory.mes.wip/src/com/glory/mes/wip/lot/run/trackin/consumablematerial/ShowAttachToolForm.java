package com.glory.mes.wip.lot.run.trackin.consumablematerial;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.client.ConsumableManager;
import com.glory.mes.mm.consumable.model.Tool;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;

public class ShowAttachToolForm implements IForm {

	private static final Logger logger = Logger.getLogger(ShowAttachToolForm.class);
	
	private static final String TABLE_NAME_TOOL = "WIPStepToolList";
	private static final String TABLE_NAME_TLOT = "WIPStepToolLotList";
	
	public ListTableManager toolManager;
	public ListTableManager tLotManager;
	
	protected FormToolkit toolkit;
	protected RunWizardContext context;
	
	public ShowAttachToolForm(Composite parent, int style, RunWizardContext context) {	
		this.context = context;
		createForm(parent);
		if (context != null) {
			loadFromObject();
	    }
	}
	
	public void createForm(Composite parent) {		
		try {	
			toolkit = new FormToolkit(Display.getCurrent());
			
			Group toolGroup = new Group(parent, SWT.BORDER);
			toolGroup.setText(Message.getString("wip.lot_step_tool_attach_info"));
			toolGroup.setBackground(new Color(null, 255, 255, 255));
			toolGroup.setLayout(new GridLayout(1, false));
			toolGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
				
			ADManager adManager = Framework.getService(ADManager.class);	
			ADTable adTable0 = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_TOOL);
			toolManager = new ListTableManager(adTable0);
			toolManager.setIndexFlag(true);
			toolManager.newViewer(toolGroup);
			
			ADTable adTable1 = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_TLOT);
			tLotManager = new ListTableManager(adTable1);
			tLotManager.setIndexFlag(true);
			tLotManager.newViewer(toolGroup);
		} catch (Exception e) {
			logger.error("ShowToolAttachForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean validate() {
		List<WorkOrderBomLine> toolBomLines = (List<WorkOrderBomLine>)(List<? extends Object>)toolManager.getInput();
		List<Tool> tools = (List<Tool>)(List<? extends Object>)tLotManager.getInput();
		for (WorkOrderBomLine toolBomLine : toolBomLines) {
			boolean flag = false;
			for (Tool tool : tools) {
				if (toolBomLine.getMaterialName().equals(tool.getMaterialName())) {
					flag = true;
					break;
				}
			}
			if (!flag) {
				UI.showError(Message.getString("mm.tool_not_bound"));
				return false;
			}
		}
		return true;
	}
	

	@Override
	public boolean saveToObject() {
		if (context != null) {
			if (!validate()){
				return false;
			}
			return true;
		}
		return false;
	}

	@Override
	public void loadFromObject() {
		try {
			List<WorkOrderBomLine> stepToolBomLines = new ArrayList<WorkOrderBomLine>();
			for (WorkOrderBomLine bomLine : context.getBomLines()) {
				if (BomLine.ITEMCATEGORY_TOOLS.equals(bomLine.getItemCategory())) {
					stepToolBomLines.add(bomLine);
				}
			}	
			toolManager.setInput(stepToolBomLines);
			
			List<Tool> allTools = new ArrayList<Tool>();
			ConsumableManager consumableManager = Framework.getService(ConsumableManager.class);
			for (Equipment eqp : context.getSelectEquipments()) {
				List<Tool> tools = consumableManager.getToolByEquipment(eqp.getObjectRrn(), null);
				if (tools != null && tools.size() > 0) {
					allTools.addAll(tools);
				}
			}
			tLotManager.setInput(allTools);
		} catch (Exception e) {
			logger.error("ShowToolAttachForm : loadFromObject", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public Object getObject() {
		return context;
	}

	@Override
	public void setObject(Object object) {
		this.context = (TrackInContext) object;
	}

	@Override
	public List<String> getCopyProperties() {
		return null;
	}

	@Override
	public void setEnabled(boolean enabled) {}

	@Override
	 public void dispose() {
        if (toolkit != null) {
            toolkit.dispose();
            toolkit = null;
        }
    } 
	   
	
}
