package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.entitymanager.forms.ScorllFormComposite;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.def.TransitionRework;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutReworkQty extends ScorllFormComposite {

		protected TrackOutContext context;
		
		public TrackOutReworkQty(Composite parent, List<Lot> reworkLots, TrackOutContext context) {
			super(parent, reworkLots);
			this.context = context;
		}

		public Composite createUnit(Composite com, Object obj) {
			Group group = new Group(com, SWT.NONE);
			GridLayout layout = new GridLayout(1, true);
			layout.numColumns = 1;
			layout.marginRight = 1;
			layout.marginLeft = 1;
			group.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			gd.widthHint = 250;
			group.setLayoutData(gd);
			group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
			
			Lot lot = (Lot) obj;
			if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
				group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
			} else {
				group.setText(lot.getLotId());
			}
			
			TrackOutReworkQtyComposite form = new TrackOutReworkQtyComposite(group, SWT.NONE, lot, context.getStep());
			return form;
		}
		
		public List<LotAction> getReworkLotActions() throws Exception {
			List<LotAction> reworkLotActions = new ArrayList<LotAction>();
			PrdManager prdManager = Framework.getService(PrdManager.class);
			
			for (Composite unit : units) {
				TrackOutReworkQtyComposite reworkComp = (TrackOutReworkQtyComposite)unit;				
				//��reworkTransition����
				Map<String, List<ProcessUnit>> reworkActionMap = new HashMap<String, List<ProcessUnit>>();
				
				for (ProcessUnit reworkUnit : reworkComp.getReworkUnits()) {
					QtyUnit qtyUnit = (QtyUnit)reworkUnit;
					String actionKey = qtyUnit.getActionCode() + ";" + (qtyUnit.getReworkTransition() == null ? "" : qtyUnit.getReworkTransition());
					
					if (reworkActionMap.containsKey(actionKey)) {
						List<ProcessUnit> list = reworkActionMap.get(actionKey);
						list.add(qtyUnit);
						reworkActionMap.put(actionKey, list);
					} else {
						List<ProcessUnit> list = new ArrayList<ProcessUnit>();
						list.add(qtyUnit);
						reworkActionMap.put(actionKey, list);
					}
				}	
				
				for (String key : reworkActionMap.keySet()) {
					LotAction lotAction = new LotAction();				
					lotAction.setActionType(LotAction.ACTIONTYPE_REWORK);
					
					String[] actionArray = key.split(";");
					lotAction.setActionCode(actionArray[0]);
					if (actionArray.length > 1) {
						lotAction.setReworkTransition(actionArray[1]);
					} 
					
					List<ProcessUnit> processUnits = reworkActionMap.get(key);
					String equipmentId = processUnits.get(0).getEquipmentId();
					if (StringUtil.isEmpty(equipmentId)) {
						equipmentId = reworkComp.getLot().getEquipmentId();
					}
					lotAction.setActionUnits(processUnits);
					lotAction.setEquipmentId(equipmentId);
					
					lotAction.setLotRrn(reworkComp.getLot().getObjectRrn());
					
					// �ж���ѡ��ķ����Ƿ�̬����
					if (!StringUtil.isEmpty(lotAction.getReworkTransition())) {
						StepState state = prdManager.getCurrentStepState(reworkComp.getLot().getProcessInstanceRrn(), false);
						Map<String, Transition> transMap = state.getLeavingTransitionsMap();
						TransitionRework transitionRework = (TransitionRework) transMap.get(lotAction.getReworkTransition());
						ReworkState reworkState = (ReworkState) transitionRework.getTo();
						// �Ƿ�̬����
						lotAction.setReworkProcedureByActionCode(reworkState.getIsDynamic());
					} 
					reworkLotActions.add(lotAction);
				}
			}
			
			return reworkLotActions;
		}
}