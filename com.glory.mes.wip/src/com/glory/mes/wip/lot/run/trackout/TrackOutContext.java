package com.glory.mes.wip.lot.run.trackout;

import java.util.ArrayList;
import java.util.List;

import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;

public class TrackOutContext extends RunWizardContext {

	public static String TRACK_OUT_BYLOT = "DefaultTrackOut";
	public static String TRACK_OUT_BYEQP = "DefaultTrackOut";
	
	protected InContext inContent = new InContext();
	
	private String trackOutType;
	
	//TrackOut������������ҪTrackOut������
	private List<Lot> outLots = new ArrayList<Lot>();

	//���ϱ��,�����������ֲ�ͬ��ʽ�ı���
	//false��ʾʹ��outQty��������,��outQtyС��ʵ�ʵ�Qty����Ϊ�Ǳ���
	//true��ʾʹ�ñ���ʱ������������������յı�������
	private boolean isInputScrap = false;

	private boolean scrapFlag = false;
	private boolean reworkFlag = false;
	
	private List<TrackOutLot> trackOutLots;
	
	public TrackOutContext() {
	}

	public TrackOutContext(TrackOutContext context) {
		this.setOperator1(context.getOperator1());
		this.setOperator2(context.getOperator2());
		this.setLots(context.getLots());
		this.setStep(context.getStep());
		this.setSelectEquipments(context.getSelectEquipments());
		this.setLotAttributeValues(context.getLotAttributeValues());
		this.setmLots(context.getmLots());
		this.setBomLines(context.getBomLines());
		
		this.trackOutType = context.getTrackOutType();
		this.scrapFlag = context.isScrapFlag();
		this.reworkFlag = context.isReworkFlag();

		this.inContent = context.getInContent();
		this.outLots = context.getOutLots();
		this.trackOutLots = context.getTrackOutLots();
	}
	
	public void setTrackOutType(String trackOutType) {
		this.trackOutType = trackOutType;
	}

	public String getTrackOutType() {
		return trackOutType;
	}

	public void setOutLots(List<Lot> outLots) {
		this.outLots = outLots;
	}

	public List<Lot> getOutLots() {
		return outLots;
	}

	public String getCategory() {
		return this.getTrackOutType();
	}

	public InContext getInContent() {
		return inContent;
	}

	public void setInContent(InContext inContent) {
		this.inContent = inContent;
	}
	
	public boolean isInputScrap() {
		return isInputScrap;
	}

	public void setInputScrap(boolean isInputScrap) {
		this.isInputScrap = isInputScrap;
	}

	public void setScrapFlag(boolean scrapFlag) {
		this.scrapFlag = scrapFlag;
	}

	public boolean isScrapFlag() {
		return scrapFlag;
	}

	public void setReworkFlag(boolean reworkFlag) {
		this.reworkFlag = reworkFlag;
	}

	public boolean isReworkFlag() {
		return reworkFlag;
	}

	public void setTrackOutLots(List<TrackOutLot> trackOutLots) {
		this.trackOutLots = trackOutLots;
	}

	public List<TrackOutLot> getTrackOutLots() {
		return trackOutLots;
	}
	
}
