package com.glory.mes.wip.lot.run.trackout.identify.auto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.internal.ExceptionHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class AutoIdentifyTrackOutForm extends EntityForm {

	private static final Logger logger = Logger.getLogger(EntityForm.class);

	private Lot lot;
	private List<ComponentUnit> componentUnitList = new ArrayList<ComponentUnit>();
	private ListTableManager tableManager;
	
	public AutoIdentifyTrackOutForm(Composite parent, int style, Object object,
			IMessageManager mmng) {
		super(parent, style, object, mmng);
		this.lot = (Lot)object;
		this.createForm();
	}
	
	@Override
	public void createForm() {
		try {
			super.createForm();
			Composite body = form.getBody();
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "WIPComponentUnitAuto");
			tableManager = new ListTableManager(adTable);
			tableManager.newViewer(body);
			

			LotManager lotManager = Framework.getService(LotManager.class);			
			lot = lotManager.getLotWithComponent(lot.getObjectRrn());
			if (lot.getSubProcessUnit() != null && lot.getSubProcessUnit().size() != 0) {
				for (ProcessUnit processUnit : lot.getSubProcessUnit()){
					componentUnitList.add((ComponentUnit)processUnit);
				}
			} else {
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				lot = componentManager.identityComponent(lot, Env.getSessionContext());
				lot = lotManager.getLotWithComponent(lot.getObjectRrn());
				for (ProcessUnit processUnit : lot.getSubProcessUnit()){
					componentUnitList.add((ComponentUnit)processUnit);
				}
			}
			tableManager.setInput(componentUnitList);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
	
	public List<ComponentUnit> getComponentUnitList() {
		return componentUnitList;
	}

}
