package com.glory.mes.wip.lot.run.trackthold;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Table;
import org.eclipse.ui.forms.widgets.Form;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutLot;
import com.glory.mes.wip.model.Lot;

public class TrackTHoldStartPage extends FlowWizardPage {

	public static String TABLE_NAME = "WIPTrackOutLot";
	
	public static String FIELD_REWORKMAINQTY = "reworkMainQty";
	
	protected CheckboxTableViewer viewer;
	
	protected Form form;
	protected FormToolkit toolkit;
	protected Table table;
	protected TrackTHoldWizard tw;
	protected TrackOutContext context;
	
	protected String tableName;
	protected TrackTHoldLotTable tableManager;
	protected Button btnReworkAutoMerge;
	
	public TrackTHoldStartPage() {
		super();
	}
	
	public TrackTHoldStartPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		try {
			tw = (TrackTHoldWizard) this.getWizard();
			context = (TrackOutContext)tw.getContext();
			toolkit = new FormToolkit(parent.getDisplay());
			
			Composite composite = toolkit.createComposite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			setControl(composite);
			
			Composite tableComp = toolkit.createComposite(composite, SWT.NONE);
			tableComp.setLayout(new GridLayout(1, true));
			tableComp.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			createLotListComposite(tableComp);
		
			//��ʾ��������
			Composite labCom = toolkit.createComposite(composite, SWT.NONE);
			labCom.setLayout(new GridLayout(1, true));
			labCom.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			if (context.isReworkFlag()) {
				btnReworkAutoMerge = toolkit.createButton(labCom, Message.getString("common.rework_auto_merger"), SWT.CHECK);
			}
			setTitle("TrackOut");
			setDescription(Message.getString("common.trackOut_first_title"));
		
		} catch(Exception e){
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	public void createLotListComposite(Composite parent) throws Exception {
		//�����ҪTrackOut������
		List<Lot> lots = new ArrayList<Lot>();
		if (context.getLots() != null && context.getLots().size() > 0) {
			lots.addAll(context.getLots());
		}
		
		List<TrackOutLot> trackOutLots = new ArrayList<TrackOutLot>();
		for (Lot lot : lots) {
			TrackOutLot trackOutLot = new TrackOutLot(lot);
			trackOutLot.setOutMainQty(trackOutLot.getMainQty());
			trackOutLots.add(trackOutLot);
		}
		
		ADManager adManager = Framework.getService(ADManager.class);
		if (tableName == null || tableName.trim().length() == 0) {
			tableName = TABLE_NAME;
		}
		ADTable adTable = adManager.getADTable(Env.getOrgRrn(), tableName);
		List<ADField> adFields = adTable.getFields();
		if (isNeedRework(trackOutLots)) {
			context.setReworkFlag(true);
			for (ADField adField : adFields) {
				if (FIELD_REWORKMAINQTY.equals(adField.getName())) {
					//��ʾRework��λ
					adField.setIsDisplay(true);
					adField.setIsMain(true);
					adField.setIsEditable(true);
					break;
				}
			}
		}
		
		tableManager = new TrackTHoldLotTable(adTable);
		tableManager.addStyle(SWT.CHECK);
		viewer = (CheckboxTableViewer)tableManager.createViewer(parent, toolkit, 50);
		tableManager.setInput(trackOutLots);
		
		//����Ĭ��ѡ����
		if (context.getOutLots() != null&&context.getLots()!=null) {
			List<TrackOutLot> selectedTrackOutLots = new ArrayList<TrackOutLot>();
			for (Lot outLot : context.getLots()) {
				for (TrackOutLot trackOutLot : trackOutLots) {
					if (trackOutLot.getLot().equals(outLot)) {
						selectedTrackOutLots.add(trackOutLot);
						break;
					}
				}
			}
			viewer.setCheckedElements(selectedTrackOutLots.toArray());
		}
		context.setTrackOutLots(trackOutLots);
	}
	
	public boolean isNeedRework(List<TrackOutLot> trackOutLots) throws Exception {
		boolean reworkFlag = false;
		PrdManager prdManager = Framework.getService(PrdManager.class);
		for(TrackOutLot trackOutLot : trackOutLots){
		   StepState state=	prdManager.getCurrentStepState(trackOutLot.getLot().getProcessInstanceRrn());
		   if (state != null && state.getIsTransitionRework()){
			   trackOutLot.setIsRework(true);
			   reworkFlag = true;
		   }
		}
		return reworkFlag;
	}
	
	@Override
	public String doNext() {
		String returnStr = "";
		try {
	 		if (!validate()) {
	 			return "";
	 		}
	 		
	 		List<TrackOutLot> trackOutLots = context.getTrackOutLots();
			List<Lot> outLots = new ArrayList<Lot>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				outLots.add(trackOutLot.getLot());
			}
			context.setTrackOutLots(trackOutLots);
			context.setOutLots(outLots);
			
			if (btnReworkAutoMerge != null) {
				if (btnReworkAutoMerge.getSelection()) {
					context.getInContent().setAutoMerge(true);
				} else {
					context.getInContent().setAutoMerge(false);
				}
			}
			//��¼Comments
			List<LotAction> actions = new ArrayList<LotAction>();
			for (TrackOutLot trackOutLot : trackOutLots) {
				if (trackOutLot.getComment() != null && trackOutLot.getComment().trim().length() > 0) {
					LotAction lotAction = new LotAction();
					lotAction.setLotRrn(trackOutLot.getLot().getObjectRrn());
					lotAction.setActionComment(trackOutLot.getComment());
					actions.add(lotAction);
				}
			}
			context.getInContent().setActions(actions);
			
			if (context.isScrapFlag()) {
				returnStr = TrackOutDialog.SCRAP_PAGE;
			} else if (context.isReworkFlag()) {
				returnStr = TrackOutDialog.REWORK_PAGE;
			} else {
				tw.invokeTrackOut();
				returnStr = TrackOutDialog.LOT_LIST_PAGE;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return returnStr;
	}

	public boolean validate() {
		List<TrackOutLot> trackOutLots = (List<TrackOutLot>) tableManager.getCheckedInput();
		if (trackOutLots == null || trackOutLots.size() == 0){
			setErrorMessage(Message.getString("wip.lot_select_alert"));
			return false;
		}
		context.setTrackOutLots(trackOutLots);
		
		//����ʵ����������Flag
		context.setReworkFlag(false);
		context.setScrapFlag(false);
		for (TrackOutLot trackOutLot : trackOutLots) {
			if ((trackOutLot.getOutMainQty() == null) && (trackOutLot.getReworkMainQty() == null)) {
				setErrorMessage(Message.getString("wip.lot_both_bull"));
				return false;
			}
			
			if (trackOutLot.getOutMainQty() != null) {
				if (trackOutLot.getOutMainQty().compareTo(BigDecimal.ZERO) < 0) {
					setErrorMessage(Message.getString("wip.qty_can_not_less_zero"));
					return false;
				}
				if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) > 0) {
					setErrorMessage(Message.getString("wip.lot_mainQty_bigger") + trackOutLot.getMainQty());
					return false;
				}
				if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) < 0) {
					//��վ����С����������,˵�������б���
					context.setScrapFlag(true);
				}
			}

			if (trackOutLot.getReworkMainQty() != null) {
				if (trackOutLot.getReworkMainQty().compareTo(BigDecimal.ZERO) < 0) {
					setErrorMessage(Message.getString("wip.qty_can_not_less_zero"));
					return false;
				}
				context.setReworkFlag(true);
				if (trackOutLot.getReworkMainQty().compareTo(trackOutLot.getOutMainQty()) > 0) {
					//������������С�ڳ�վ����
					setErrorMessage(Message.getString("wip.lot_rework_bigger") + trackOutLot.getMainQty());
					return false;
				}
			}
		}
		return true;
	}
	
	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getTableName() {
		return tableName;
	}


}