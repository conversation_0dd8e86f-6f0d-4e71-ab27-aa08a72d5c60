package com.glory.mes.wip.lot.run.operation;

import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Operation;

public class ShowStepOperationForm extends Form {
	
	protected static final String TABLE_STEPOPERATION = "WIPTrackInStepOperation";
	protected TableViewerManager tableViewerManager;
	
	public ShowStepOperationForm(Composite parent, int style, Object object) {
		super(parent, style, object);
		createForm();
    }

	@Override
    public void createForm(){
		toolkit = new FormToolkit(getDisplay());
		
        GridLayout layout = new GridLayout();
        layout.verticalSpacing = 0;
        layout.horizontalSpacing = 0;
        layout.marginWidth = 0;
        layout.marginHeight = 0;
        setLayout(new GridLayout(1, true));
         
        toolkit.setBackground(getBackground());
        form = toolkit.createScrolledForm(this);        
        form.setLayoutData(new GridData(GridData.FILL_BOTH));

        Composite body = getForm().getBody();
        layout = new GridLayout();
        layout.verticalSpacing = mVertSpacing;
        layout.horizontalSpacing = mHorizSpacing;
        layout.marginWidth = mMarginWidth;
        layout.marginHeight = mMarginHeight;
        layout.marginLeft = mLeftPadding;
        layout.marginRight = mRightPadding;
        layout.marginTop = mTopPadding;
        layout.marginBottom = mBottomPadding;
        body.setLayout(layout);
        
        try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_STEPOPERATION);
			
			tableViewerManager = new TableViewerManager(adTable);
			tableViewerManager.newViewer(body);
			
			tableViewerManager.addDoubleClickListener(new IMouseAction() {
				
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					Operation operation = (Operation) tableViewerManager.getSelectedObject();
					StepOperationDialog stepOperationDialog = new StepOperationDialog(getShell(), operation);
					stepOperationDialog.open();
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    }

	@Override
	public boolean validate() {
		return true;
	}	

	public void setValue(List<Operation> newOperations) {
		tableViewerManager.setInput(newOperations);
	}

	@Override
	public void addFields() {
		
	}
}
