package com.glory.mes.wip.lot.run.bylocation;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.EquipmentLine;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.custom.depend.MsgConsoleView;
import com.glory.mes.wip.lot.run.byeqp.sorting.SortingJobAction;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class ByLocationUtil {
	
	private static final Logger logger = Logger.getLogger(ByLocationUtil.class);
	
	public static List<Lot> getRtdLots(Equipment equipment) throws Exception {
		return getRtdLots(equipment, true);
	}
	/**
	 * ��ȡRtd�����б�
	 * @return
	 * @throws Exception
	 */
	public static List<Lot> getRtdLots(Equipment equipment, boolean showError) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		ADManager adManager = Framework.getService(ADManager.class);
		List<Lot> waittingLots = Lists.newArrayList();
		List<String> lotIds = null;
		try {
			String dispatchType = equipment.getIsBatch() ? "BATCH" : "LOT";
			List<ADRefList> refLists = adManager.getADRefList(0L, "ByEqpAction");
			if (CollectionUtils.isNotEmpty(refLists)) {
				for (ADRefList refList : refLists) {
					if (SortingJobAction.ACTION_NAME.equals(refList.getText())) {
						dispatchType = "SORTING";
						break;
					}
				}
			}
			
			lotIds = lotManager.getRtdQueue(equipment.getEquipmentId(), dispatchType, Env.getSessionContext());
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : getRtdLots() ", e);
			if (showError) {
				ByEqpConsole console = (ByEqpConsole) MsgConsoleView.getInstance();
				if (console != null) {
					console.error(Message.getString("wip.byeqp_disp_rule_not_found"));
				}
			}
			
			// ����һ���ǹ���û�У�����ʾwaittingLots
			waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), equipment.getObjectRrn(),
					LotStateMachine.STATE_WAIT, Env.getSessionContext());
		}
		
		if (CollectionUtils.isNotEmpty(lotIds)) {
			// ֻ����Waiting��������ȡ��Prepare�ų�
			waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), equipment.getObjectRrn(),
					LotStateMachine.STATE_WAIT, Env.getSessionContext());
			
			Map<String, Lot> waittingLotMap = waittingLots.stream().collect(Collectors.toMap(Lot::getLotId, l -> l));
			List<Lot> rtdLots = Lists.newArrayList();
			for (String lotId : lotIds) {
				if (waittingLotMap.containsKey(lotId)) {
					rtdLots.add(waittingLotMap.get(lotId));
				}
			}
			
			waittingLots = rtdLots;
		}
		
		return waittingLots;
	}
	
	protected static List<Lot> filterWaitingLots(List<Lot> lots, Equipment currentEqp) throws Exception {
		List<Lot> waitingLots = new ArrayList<Lot>();
		if (currentEqp != null && currentEqp.getObjectRrn() != null) {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			boolean isUseLine = MesCfMod.isUseLine(Env.getOrgRrn(), sysParamManager);

			List<String> lines = new ArrayList<String>();
			if (isUseLine) {

				ADManager manager = Framework.getService(ADManager.class);
				String whereClause = "equipmentRrn = " + currentEqp.getObjectRrn();
				List<EquipmentLine> lineEqps = manager.getEntityList(Env.getOrgRrn(), EquipmentLine.class,
						Integer.MAX_VALUE, whereClause, "");
				for (EquipmentLine lineEqp : lineEqps) {
					lines.add(lineEqp.getLineId());
				}

			}

			for (Lot lot : lots) {
				if (isUseLine) {
					if (!StringUtil.isEmpty(lot.getLineId()) && !lines.contains(lot.getLineId())) {
						// ������������߱����߱����豸�������߱���
						continue;
					}
				}
				
				if ((LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_DISP.equals(lot.getState()))
						&& !Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
					if (lot.getMessageList() != null && lot.getMessageList().size() > 0) {
						ByEqpConsole console = (ByEqpConsole) MsgConsoleView.getInstance();
						for (String msg : lot.getMessageList()) {
							console.warning(Message.formatString(msg));
						}
					}
					// ���������Ƶ����β�����
					if (!lot.getConstraintFlag()) {
						waitingLots.add(lot);
					}
				}
				
			}
//			boolean isByEqpQueryPpid = MesCfMod.isByEqpQueryPpid(Env.getOrgRrn(), sysParamManager);
//			if (isByEqpQueryPpid && CollectionUtils.isNotEmpty(waitingLots)) {
//				StopWatch stopWatch = new StopWatch();
//				stopWatch.start();
//				LotManager lotManager = Framework.getService(LotManager.class);
//				waitingLots = lotManager.calculatePPID(waitingLots, currentEqp, true, Env.getSessionContext());
//				stopWatch.split();
//				logger.info("calculatePPID Time :" + stopWatch.getSplitTime() + " Milliseconds");
//				stopWatch.stop();
//			}
		} else {
			for (Lot lot : lots) {
				if ((LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_DISP.equals(lot.getState()))
						&& !Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
					if (lot.getMessageList() != null && lot.getMessageList().size() > 0) {
						ByEqpConsole console = (ByEqpConsole) MsgConsoleView.getInstance();
						for (String msg : lot.getMessageList()) {
							console.warning(Message.formatString(msg));
						}
					}
					// ���������Ƶ����β�����
					if (!lot.getConstraintFlag()) {
						waitingLots.add(lot);
					}
				}
			}
			
		}
		return waitingLots;
	}

}
