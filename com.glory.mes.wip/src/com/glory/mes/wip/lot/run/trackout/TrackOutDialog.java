package com.glory.mes.wip.lot.run.trackout;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardDialog;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.mes.wip.lot.run.bylot.RunWizard;

public class TrackOutDialog extends FlowWizardDialog {
	
	private static int MIN_DIALOG_WIDTH = 700;
	private static int MIN_DIALOG_HEIGHT = 350;
	
	public static String START_PAGE = "startPage";	
	public static String SCRAP_PAGE = "scrapPage";
	public static String DEFECT_PAGE = "defectPage";
	public static String REWORK_PAGE = "reworkPage";
	public static String LOT_LIST_PAGE = "trackOutListPage";

	public TrackOutDialog(Shell parentShell, FlowWizard newWizard) {
		super(parentShell, newWizard);
	}
	
	@Override
	public int open() {
		RunWizard wizard = (RunWizard)getWizard(); 
		if (wizard.validateStepCategory()) {
			return super.open();
		} else {
			setReturnCode(CANCEL);
			close();
			return this.getReturnCode();
		}
	}
	
	
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("trackout-dialog"));
		return super.buildView(parent);
	}
	
	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.APPLICATION_MODAL | getDefaultOrientation());
	}
	
	@Override
	public void showPage(IWizardPage page) {
		super.showPage(page);
		FlowWizardPage fPage = ((FlowWizardPage)page);
		fPage.refresh();
	}
	
	protected void cancelPressed() {
		super.cancelPressed();
		if(LOT_LIST_PAGE.equals(getCurrentPage().getName())){
		     super.setReturnCode(Dialog.OK);
		}else{
			super.setReturnCode(Dialog.CANCEL);
		}
		close();		
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}

}