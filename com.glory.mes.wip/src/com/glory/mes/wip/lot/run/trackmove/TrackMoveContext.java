package com.glory.mes.wip.lot.run.trackmove;

import com.glory.mes.wip.lot.run.trackout.TrackOutContext;

public class TrackMoveContext extends TrackOutContext {
	
	public static String TRACK_MOVE_DEFAULT = "DefaultTrackMove";
	public static String TRACK_MOVE_MODAL = "ModuleTrackMove";
	
	private String trackMoveType;
	
	public void setTrackMoveType(String trackMoveType) {
		this.trackMoveType = trackMoveType;
	}

	public String getTrackMoveType() {
		return trackMoveType;
	}
	
	public String getCategory() {
		return this.getTrackMoveType();
	}

}
