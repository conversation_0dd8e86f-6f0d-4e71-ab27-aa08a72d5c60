package com.glory.mes.wip.lot.run.trackin.combineeqp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Capa;
import com.glory.mes.ras.eqp.CapaSub;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.subcapa.SubCapaEquipmentComponent;
import com.glory.mes.ras.eqp.subcapa.SubCapaEquipmentForm;

public class SubCapaCombineEquipmentForm extends SubCapaEquipmentForm {

	public SubCapaCombineEquipmentForm(Composite parent, List<CapaSub> subCapas) {
		super(parent, subCapas);
	}

	public void setUnitEquipment(Equipment parentEquipment) throws Exception {
		ADManager adManager = Framework.getService(ADManager.class);
		RASManager rasManager = Framework.getService(RASManager.class);
		
		for (Composite unit : units) {
			SubCapaEquipmentComponent subUnit = (SubCapaEquipmentComponent)unit;
			Capa capa = new Capa();
			capa.setObjectRrn(subUnit.getSubCapa().getSubCapaRrn());
			capa = (Capa) adManager.getEntity(capa);
			//��ÿ������豸,ֻ�����豸״̬,�����ǹ�������,�豸Ԥ������������
			List<Equipment> equipments = rasManager.getSubEquipmentCombineByParentAndSubCapa(parentEquipment.getObjectRrn(), capa.getName());		
			subUnit.setAvailableEquipment(equipments);
			if (subUnit.getTableManager().isCheckFlag()) {
				subUnit.getTableManager().getCheckedObject().addAll(equipments);
			} 
		}
	}
	
	public Map<CapaSub, List<Equipment>> getSubCapaEquipments() {
		Map<CapaSub, List<Equipment>> map = new HashMap<CapaSub, List<Equipment>>();
		for (Composite unit : units) {
			SubCapaEquipmentComponent subUnit = (SubCapaEquipmentComponent)unit;
			if (subUnit.getTableManager().isCheckFlag()) {
				map.put(subUnit.getSubCapa(), (List)subUnit.getTableManager().getCheckedObject());
			} else {
				map.put(subUnit.getSubCapa(), (List)subUnit.getTableManager().getInput());
			}
			
		}
		return map;
	}
	
}
