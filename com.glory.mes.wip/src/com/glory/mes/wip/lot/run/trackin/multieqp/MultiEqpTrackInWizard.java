package com.glory.mes.wip.lot.run.trackin.multieqp;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;
import com.google.common.collect.Lists;

public class MultiEqpTrackInWizard extends TrackInWizard {

	boolean isTrackInInputQty = true;

	@Override
	public boolean performFinish() {
		try {
			TrackInContext context = this.getContext();
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = context.getLots().get(0);
			lot.setOperator1(context.getOperator1());

			if (context.getStep().getIsRequireEqp() && context.getSelectEquipments() != null
					&& context.getSelectEquipments().size() > 0) {
				Equipment equipment = context.getSelectEquipments().get(0);
				if (lot.getEquipmentId() == null) {
					lot.setEquipmentId(equipment.getEquipmentId());
				}
			}

			InContext inContext = new InContext();
			inContext.setLots(Lists.newArrayList(lot));
			inContext.setAttributeValues(context.getLotAttributeValues());
			inContext.setEquipments(context.getSelectEquipments());
			inContext.setOperator1(context.getOperator1());
			inContext.setMultiEqp(true);

			lotManager.trackInPartialMultiEqp(inContext, lot.getSubProcessUnit(), Env.getSessionContext());
			UI.showInfo(Message.getString("wip.trackin_success"));
			context.setReturnCode(TrackInContext.OK_ID);
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		context.setReturnCode(TrackInContext.FAILED_ID);
		return false;
	}

	public boolean isTrackInInputQty() {
		return isTrackInInputQty;
	}

	public void setTrackInInputQty(boolean isTrackInInputQty) {
		this.isTrackInInputQty = isTrackInInputQty;
	}

}
