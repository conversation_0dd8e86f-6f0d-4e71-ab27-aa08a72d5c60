package com.glory.mes.wip.lot.run.trackout.identify.manual;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.track.model.OutContext;

public class ManualIdentifyTrackOutPage extends FlowWizardPage {

	protected FormToolkit toolkit;
	protected Color gray = new Color(null, 236, 233, 216);
	protected List<String> list = new ArrayList<String>();
	protected TrackOutWizard tw;
	protected TrackOutContext context;
	protected ManualIdentifyTrackOutForm inputWaferform;
	private List<ComponentUnit> componentUnitList;

	public ManualIdentifyTrackOutPage() {
		super();
	}

	public ManualIdentifyTrackOutPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public String doNext() {
		try {
			componentUnitList = (List<ComponentUnit>)inputWaferform.getComponentUnitList();
			for (ComponentUnit componentUnit : componentUnitList) {
				if (componentUnit.getComponentId() == null || componentUnit.getComponentId().trim().equals("")) {
					UI.showError(Message.getString("wip.identify.manual_null_error"));
					return null;
				}
			}
			for (int i = 0; i < componentUnitList.size(); i++) {
				for (int j = 0; j<componentUnitList.size(); j++) {
					if (i != j && componentUnitList.get(i).getComponentId().equals(componentUnitList.get(j).getComponentId())) {
						UI.showError(Message.getString("wip.lot.identify_component_repeat ") + componentUnitList.get(i).getComponentId());
						return null;
					}
				}
			}
			//���е�WaferId��������ȡ�
			ADManager entityManager = Framework.getService(ADManager.class);		
			List<ComponentUnit> componentUnitExistList = entityManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, Env.getMaxResult(), null, "position");
			if (componentUnitExistList != null && componentUnitExistList.size() != 0) {
				for (int i = 0; i<componentUnitExistList.size(); i++) {
					for (int j = 0; j<componentUnitList.size(); j++) {
						if (componentUnitExistList.get(i).getComponentId().equals(componentUnitList.get(j).getComponentId())) {
							UI.showError(Message.getString("wip.lot.identify_component_exist") + componentUnitList.get(j).getComponentId());
							return null;
						}
					}
				}
			}
			
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			InContext inContext = new InContext();
			Lot lot = context.getLots().get(0);
			lot.setSubProcessUnit((List<ProcessUnit>)(ArrayList)componentUnitList);
			inContext.setLots(context.getLots());
			inContext.setAttributeValues(context.getLotAttributeValues());	
			OutContext outContext = componentManager.trackOutIdentify(inContext, Env.getSessionContext());
			List<Lot> lots = new ArrayList<Lot>();
			lots.addAll(outContext.getLots());
			lots.addAll(outContext.getReworkLots());
			context.setOutLots(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return "";
		}
		return getDefaultDirect();
	}

	@Override
	public String doPrevious() {
		return null;
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return true;
	}

	@Override
	public void createControl(Composite parent) {
		tw = (TrackOutWizard) this.getWizard();
		context = tw.getContext();
		toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setBackground(gray);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite tabCom = toolkit.createComposite(composite, SWT.NONE);
		tabCom.setLayout(new GridLayout(1, true));
		tabCom.setLayoutData(new GridData(GridData.FILL_BOTH));
		List<Lot> lotList = context.getLots();
		inputWaferform = new ManualIdentifyTrackOutForm(tabCom, SWT.NONE, lotList.get(0), null);
		setControl(composite);
		setTitle("TrackOut");
		setDescription(Message.getString("common.trackOut_first_title"));
	}
}
