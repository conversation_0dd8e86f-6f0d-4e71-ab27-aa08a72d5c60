package com.glory.mes.wip.lot.run.trackin.consumablematerial;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;

public class ConsumeStepMaterialPage extends FlowWizardPage {

	private static final Logger logger = Logger.getLogger(ConsumeStepMaterialPage.class);
	
	public RunWizardContext context;
	public List<IForm> forms = new ArrayList<IForm>();
	
	@Override
	public void createControl(Composite parent) {
		context = (RunWizardContext)((RunWizard) getWizard()).getContext();
		
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			List<WorkOrderBomLine> bomLines = entityManager.getEntityList(Env.getOrgRrn(), WorkOrderBomLine.class, Env.getMaxResult(),
						"stepName = '" + context.getLots().get(0).getStepName() + "' and isCritical = 'Y' and isMain = 'N' and woId = '" + context.getLots().get(0).getWoId() + "'", "");
			if (bomLines == null || bomLines.size() == 0) {
				((FlowWizard) this.getWizard()).getDialog().skipPressed();
				return;
			}
			context.setBomLines(bomLines);
			Composite composite = new Composite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
			composite.setLayoutData(gd);
	
			createContent(composite, bomLines);
			setControl(composite);
			setPageTitle();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setPageTitle() {
		setTitle(Message.getString("wip.raw_material_and_tool_info"));
		setMessage(Message.getString("wip.select_raw_material_and_tool_info"));
	}
	
	public void createContent(Composite composite, List<WorkOrderBomLine> bomLines) {
		try {
			List<WorkOrderBomLine> stepMaterialBomLines = new ArrayList<WorkOrderBomLine>();
			List<WorkOrderBomLine> stepToolBomLines = new ArrayList<WorkOrderBomLine>();
			for (WorkOrderBomLine bomLine : bomLines) {
				if (BomLine.ITEMCATEGORY_TOOLS.equals(bomLine.getItemCategory())) {
					stepToolBomLines.add(bomLine);
				} else {
					stepMaterialBomLines.add(bomLine);
				}
			}
			if (stepToolBomLines.size() > 0) {
				ShowAttachToolForm showAttachToolForm = new ShowAttachToolForm(composite, SWT.NONE, context);
				forms.add(showAttachToolForm);
			}
			if (stepMaterialBomLines.size() > 0) {
				ConumableMatreialForm conumableMatreialForm = new ConumableMatreialForm(composite, SWT.NONE, context);
				forms.add(conumableMatreialForm);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	public String doNext() {
		try {
			boolean saveFlag = true;
			for (IForm detailForm : forms) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
					break;
				}
			}
			if (!saveFlag) {
				return null;	
			}
		} catch (Exception e) {
			logger.error("EntityForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return getDefaultDirect();
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		this.setErrorMessage(null);
		return super.doPrevious();
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return true;
	}
	
}
