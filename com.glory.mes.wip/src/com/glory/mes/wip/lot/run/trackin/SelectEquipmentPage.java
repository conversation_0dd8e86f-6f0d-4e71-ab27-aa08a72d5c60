package com.glory.mes.wip.lot.run.trackin;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.extensionpoints.TrackInCheckExtensionPoint;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotBatchJob;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;

public class SelectEquipmentPage extends FlowWizardPage {

	private static final Logger logger = Logger.getLogger(SelectEquipmentPage.class);
	
	protected static final String TABLE_EQUIPMENT = "WIPTrackInSelEqp";
    protected static final String FIELD_CONDITION = "equipmentRecipeCondition";
	
	protected IManagedForm managedForm;
	protected Lot lot;
	protected Step step;
	protected FormToolkit toolkit;
	protected EntityForm entityForm;	
	protected ListTableManager selectEqpTableManager;
	protected boolean canFlipNextPage=true;
	protected boolean isByEqpQueryPpid = true;

	public SelectEquipmentPage() {
		super();
	}
	
	public SelectEquipmentPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
		List<Lot> lots = context.getLots();
		lot = lots.get(0);
	}
	
	
	@Override
	public void createControl(Composite parent) {
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			step = context.getStep();
			if (step == null || step.getCapability() == null) {
				UI.showError(Message.getString("wip.step_capability_is_null"));
				context.setReturnCode(TrackInContext.FAILED_ID);
				if (this.getShell() != null && !this.getShell().isDisposed()) {
					this.getShell().dispose();
				}
				return;
			}
			//�ڰ��豸��ҵʱ�������豸
			List<Equipment> selectedEqps = context.getSelectEquipments();
			List<Equipment> availableEqps = null;
			
			//���û��ѡ���豸
			if (selectedEqps == null || selectedEqps.size() == 0) {
				LotManager manager = Framework.getService(LotManager.class);
			    availableEqps = manager.getAvailableEquipments(lot, Env.getSessionContext());
			    //��ҳ��ֻ��ʾ���豸
			    availableEqps =  availableEqps.stream().filter(p -> p.getParentEqpRrn() == null).collect(Collectors.toList());
			    orderByEqpId(availableEqps);
			} else {
				availableEqps = selectedEqps;
			}
			
			if (!step.getIsRequireEqp()) {
				//������Ǳ����豸����û�п����豸����ֱ����ת���¸�ҳ��
				if (availableEqps == null || availableEqps.size() == 0) {
					((FlowWizard) this.getWizard()).getDialog().skipPressed();
					return;
				}
				canFlipNextPage = false;
				((RunWizard)getWizard()).getDialog().updateButtons();
			} else {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				if (!isByEqpQueryPpid) {
					//����Ѿ����ú����豸��Ҳֱ����ת���¸�ҳ��
					if (selectedEqps != null && selectedEqps.size( )> 0) {
						((FlowWizard) this.getWizard()).getDialog().skipPressed();
						return;
					}	
				}		
				canFlipNextPage = false;
				((RunWizard)getWizard()).getDialog().updateButtons();
			}
			
			toolkit = new FormToolkit(Display.getCurrent());
			Composite composite = new Composite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
			composite.setLayoutData(gd);

			createContent(composite, availableEqps);
			setControl(composite);
			setPageTitle();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * ����EQP ID����
	 * @param availableEqps
	 */
	protected void orderByEqpId(List<Equipment> availableEqps) {
		Comparator<Equipment> comparator = new Comparator<Equipment>(){
			@Override
			public int compare(Equipment eqp1, Equipment eqp2) {
				return eqp1.getEquipmentId().compareTo(eqp2.getEquipmentId());
			}
		};
		Collections.sort(availableEqps,comparator);
	}

	protected void createContent(Composite composite, List<Equipment> availableEqps) {
		Composite parent = toolkit.createComposite(composite, SWT.NONE);

		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));

		Group tabGroup = new Group(parent, SWT.NONE);
		tabGroup.setText(Message.getString("wip.trackin_eqp_list"));
		tabGroup.setBackground(new Color(null, 255, 255, 255));
		tabGroup.setLayout(new GridLayout(1, true));
		tabGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			
			List<Equipment> showEqps =  availableEqps.stream().filter(p -> p.getIsAvailable() == true).collect(Collectors.toList());
			RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
			List<Lot> lots = context.getLots();
			if (isByEqpQueryPpid) {
				//У���豸�Ƿ���PPID,û������ʾ��ɫ
				for (Equipment equipment : showEqps) {
					 Lot formLot = ByEqpUtil.getEquipmentRecipeAndReticle(lots, true, false, false, equipment);
					if (!StringUtil.isEmpty(formLot.getEquipmentRecipe())) {
						equipment.setTransEquipmentRecipe(formLot.getEquipmentRecipe());
					}
					if ("Y".equals(formLot.getAttribute1())) {
						equipment.setAttribute5(formLot.getAttribute1());
					}
				}
			}
			
			ADManager adManager = Framework.getService(ADManager.class);					
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_EQUIPMENT);
			selectEqpTableManager = new SelectEqpTableManager(adTable);		
			selectEqpTableManager.newViewer(tabGroup);
			selectEqpTableManager.setInput(showEqps);
			selectEqpTableManager.getNatTable().setLayoutData(new GridData(GridData.FILL_BOTH));
			selectEqpTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					Object selectObj = event.getSelection();
					if (selectObj != null) {
						canFlipNextPage = true;
					}
					((RunWizard)getWizard()).getDialog().updateButtons();
				}
			});
			
			
			if (isByEqpQueryPpid) {
				Composite recipeReticleComp = toolkit.createComposite(composite, SWT.NONE);
				recipeReticleComp.setLayout(new GridLayout(4, false));
				recipeReticleComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
				
			    entityForm = new EntityForm(recipeReticleComp, SWT.NONE, new Lot(), getADTable1(), null);
			    entityForm.setLayout(new GridLayout());
				GridData gd = new GridData(GridData.FILL_BOTH);
                gd.heightHint = 120;
				entityForm.setLayoutData(gd);
				
                TextField conditionField = entityForm.getFieldByControlId(FIELD_CONDITION, TextField.class);
                conditionField.getTextControl().setVisible(false);
                conditionField.getLabelControl().setVisible(false);
                conditionField.addValueChangeListener(new IValueChangeListener() {

                    @Override
                    public void valueChanged(Object source, Object target) {
                        if (target != null) {
                            String targetValue = (String) target;
                            if (StringUtils.isNotEmpty(targetValue)) {
                                conditionField.getTextControl().setVisible(true);
                                conditionField.getLabelControl().setVisible(true);
                            } else {
                                conditionField.getTextControl().setVisible(false);
                                conditionField.getLabelControl().setVisible(false);
                            }
                        }
                    }
                });
				selectEqpTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
					@Override
					public void selectionChanged(SelectionChangedEvent event) {
						try {
							StructuredSelection selection = (StructuredSelection)event.getSelection();
							Equipment equipment = (Equipment) selection.getFirstElement();
							
							RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
							List<Lot> lots = context.getLots();
							
							boolean isNotUseEquipmentRecipe = isNotUseEquipmentRecipe(sysParamManager, lots, Arrays.asList(equipment));
							
							Lot formLot = ByEqpUtil.getEquipmentRecipeAndReticle(lots, true, true, !isNotUseEquipmentRecipe, equipment);
							entityForm.setObject(formLot);					
							entityForm.loadFromObject();
							entityForm.refresh();
						} catch (Exception e) {
							ExceptionHandlerManager.asyncHandleException(e);
						}
					}
				});
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		createInvaliableEqpGroup(parent, availableEqps);
	}

	//��ʾ�豸�������ã�������������ԭ���²���ʹ�õ���Щ�豸
	//����ʾ��ԭ��
	protected void createInvaliableEqpGroup(Composite parent, List<Equipment> availableEqps) {
		boolean hasInAvailableEqp = false;
		List<Equipment> inValiableEqp = new ArrayList<Equipment>();
		if (availableEqps!=null && availableEqps.size() > 0) {
			for (int i = 0; i < availableEqps.size(); i++) {
				Equipment equ = availableEqps.get(i);
				if (!equ.getIsAvailable()) {
					inValiableEqp.add(equ);
					hasInAvailableEqp = true;
				}
			}
		}
		
		if (!hasInAvailableEqp) {
			return;
		}
		
		ScrolledComposite sc = new ScrolledComposite(parent, SWT.BORDER | SWT.V_SCROLL);
		sc.setLayout(new GridLayout(10, false));
		GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
		gridData.heightHint = 250;
		sc.setLayoutData(gridData);
		sc.setExpandHorizontal(true);
		sc.setExpandVertical(true);
		sc.setMinWidth(300);
		if (availableEqps != null && availableEqps.size() > 0) {
			sc.setMinHeight(availableEqps.size() * 100);
		} else {
			sc.setMinHeight(500);
		}
		Composite topComposite = new Composite(sc, SWT.NONE);
		sc.setContent(topComposite);
		topComposite.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));// White color
		GridLayout gd = new GridLayout(1, true);
		topComposite.setLayout(gd);
		GridData data = new GridData(GridData.FILL_VERTICAL);
		topComposite.setLayoutData(data);
		
		for (Equipment equipment : inValiableEqp) {
			Label lab1 = toolkit.createLabel(topComposite, equipment.getEquipmentId() + ":");
			lab1.setForeground(Display.getCurrent().getSystemColor(SWT.COLOR_RED));
			lab1.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_MENU_ITEM));
			Label lab2 = toolkit.createLabel(topComposite, Message.formatString(equipment.getMessage()), SWT.READ_ONLY);
			lab2.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
		}
	}


	protected List<Equipment> getSelectedEquipments() throws Exception {
		List<Equipment> equipments = Lists.newArrayList();
		Equipment equipment = (Equipment) selectEqpTableManager.getSelectedObject();
		if (equipment != null) {
			equipments.add(equipment);
		}
		return equipments;
	}

	protected void setPageTitle() {
		setTitle(Message.getString("wip.selecteqp_title"));
		setMessage(Message.getString("wip.selecteqp_message"));
	}
	
	@Override
	public String doNext() {
		try {
			TrackInContext trackInContext = (TrackInContext)((RunWizard) getWizard()).getContext();
			List<Equipment> equipments = getSelectedEquipments();	
			
			String onlineEquipmentId = null;
			for (Equipment equipment : equipments) {
				if (Equipment.COMMUNICATION_STATE_ONLINE.equals(equipment.getCommunicationState())) {
					onlineEquipmentId = equipment.getEquipmentId();
					break;
				}
			}
			
			if (!StringUtil.isEmpty(onlineEquipmentId)) {
				if (!UI.showConfirm(String.format(Message.getString("wip.lot_track_manual_online_confirm"), onlineEquipmentId))) {
					return "";
				}
			}
			
			trackInContext.setSelectEquipments(equipments);
			
			//ѡ�豸����һ��ʱ�����йؼ���
			trackInContext = TrackInCheckExtensionPoint.executeTrackInCheck(trackInContext, TrackInCheckExtensionPoint.CHECK_POINT_SELECTEQP);
			if (TrackInContext.FAILED_ID == trackInContext.getCheckCode()) {
				return null;
			}
			
			if (equipments == null || equipments.size() == 0) {
				if (!trackInContext.getStep().getIsRequireEqp()) {
					return getDefaultDirect();
				}
				throw new ClientException("ras.not_select_equipment");
			}
				
			// �ж��豸�Ƿ����batch��ҵ
			if (trackInContext.getLots().size() > 1 && equipments.size() > 0) {
				// ͨ��batchjob����Ƿ�Ϊ����
				String batchId = trackInContext.getLots().get(0).getBatchId();
				if (!StringUtil.isEmpty(batchId)) {
					LotManager lotManager = Framework.getService(LotManager.class);
					List<LotBatchJob> batchJobs = lotManager.getBatchJobsByBatchId(Env.getOrgRrn(), batchId);
					if (CollectionUtils.isNotEmpty(batchJobs)) {
						List<Lot> bathcLots = lotManager.getLotsByBatch(Env.getOrgRrn(), batchId);
						//���MeasureLot 
						LotBatchJob batchjob = batchJobs.stream().filter(bjob-> LotBatchJob.MEASURE_FLAG_MEASURE.equals(bjob.getMeasureFlag())).findFirst().get();
						Lot measureLot = bathcLots.stream().filter(blot -> blot.getLotId().equals(batchjob.getLotId())).findFirst().get();
						trackInContext.setMeasureLot(measureLot);
						trackInContext.setMeasure(true);
						trackInContext.setLots(bathcLots);
					} else {
						for (Equipment equipment : equipments) {
							if (!equipment.getIsBatch()) {
								UI.showInfo(Message.getString("wip.trackin_cannot_batch"));
								return null;
							}
						}
					}
				}
			}
			
			// ����ȡ����Reticle��Logic Recipe
			if (entityForm != null) {
				Lot lotRecipeMask = (Lot) entityForm.getObject();
				String mask = DBUtil.toString(lotRecipeMask.getMask());
				String logicRecipe = DBUtil.toString(lotRecipeMask.getRecipeName());
				
				for (Lot lot : trackInContext.getLots()) {	
					LotManager lotManager = Framework.getService(LotManager.class);
					//У���豸��LogicRecipe��PPID�Ƿ�ƥ�䣬recipe�Ƿ�Ϊhold
					if(StringUtils.isNotEmpty(lotRecipeMask.getRecipeName()) && StringUtils.isNotEmpty(lotRecipeMask.getEquipmentRecipe())) {
//						SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
//						boolean isNotUseEquipmentRecipe = isNotUseEquipmentRecipe(sysParamManager, Arrays.asList(lot), equipments);
//						if (!isNotUseEquipmentRecipe) {
//							lotManager.validRecipeEquipment(Env.getOrgRrn(), equipments.get(0).getEquipmentId(), lotRecipeMask.getRecipeName(), lotRecipeMask.getEquipmentRecipe());
//						}
						lotManager.validRecipeEquipment(Env.getOrgRrn(), equipments.get(0).getEquipmentId(), lotRecipeMask.getRecipeName(), lotRecipeMask.getEquipmentRecipe());
					}		
					
					lot.setEquipmentMask(lotRecipeMask.getEquipmentMask());
					lot.setEquipmentRecipe(lotRecipeMask.getEquipmentRecipe());
					String lotMask =  DBUtil.toString(lot.getMask());
					String lotLogicRecipe = DBUtil.toString(lot.getRecipeName());
					// ������ֲ�һ�£���Ҫ����Աȷ���Ƿ�ʹ�õ����µ�Reticle��Recipe��ˢ�½������ܻ��һ�£�
					if (!Objects.equals(mask, lotMask)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_reticle"), lotMask, mask, lot.getLotId()))) {
							return null;
						}
					} else if (!Objects.equals(logicRecipe, lotLogicRecipe)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_recipe"), lotLogicRecipe, logicRecipe, lot.getLotId()))) {
							return null;
						}
					}
					
					// ��������Ҫ���¸�ֵ
					lot.setMask(mask);
					lot.setRecipeName(logicRecipe);
				}
			}
			
			if (trackInContext.getStep().getIsMultiEqp()) {
				if (equipments.size() == 1) {
					Equipment equipment = equipments.get(0);
					List<Lot> lots = trackInContext.getLots();
					for (Lot lot : lots) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
					return getDefaultDirect();
				} else if (equipments.size() > 1) {
					List<ProcessUnit> processUnits = new ArrayList<ProcessUnit>();
					for (Equipment e : equipments) {
						QtyUnit unit = new QtyUnit();
						unit.setEquipmentId(e.getEquipmentId());
						unit.setParentProcessUnit(lot);
						unit.setParentUnitRrn(lot.getObjectRrn());
						processUnits.add(unit);
					}
					lot.setSubUnitType(QtyUnit.getUnitType());
					lot.setSubProcessUnit(processUnits);
					return getDefaultDirect();
				}
			} else {
				Equipment equipment = equipments.get(0);
				if (equipment != null) {
					List<Lot> lots = trackInContext.getLots();
					for (Lot lot : lots) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
				}
			}
		} catch (Exception e) {
			logger.error("EntityForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
		return getDefaultDirect();
	}

	private boolean isNotUseEquipmentRecipe(SysParameterManager sysParameterManager, List<Lot> lots, List<Equipment> equipments) {
//		boolean isNotUseEquipmentRecipe = MesCfMod.isNotUseEquipmentRecipe(Env.getOrgRrn(), sysParameterManager);
//		try {
//			if(!isNotUseEquipmentRecipe) {
//				if(MesCfMod.isDummyNotUseEquipmentRecipe(Env.getOrgRrn(), sysParameterManager)) {
//					Boolean isDummy = false;
////					Boolean isFurnaceEqp = false;
//					LotManager lotManager = Framework.getService(LotManager.class);
////					RASManager rasManager = Framework.getService(RASManager.class);
//					for (Lot lot: lots) {
//						isDummy = lotManager.isDummy(Env.getOrgRrn(), lot.getLotType());
//						if(!isDummy) {
//							return false;
//						}
//					}
//					if (isDummy) {
////						for(Equipment equipment: equipments) {
////							isFurnaceEqp = rasManager.isFurnaceEqp(Env.getOrgRrn(), equipment.getEqpType());
////							if(!isFurnaceEqp) {
////								return false;
////							}
////						}
//						isNotUseEquipmentRecipe = true;
//					}
//				}
//			}
//		} catch (Exception e) {
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//		return isNotUseEquipmentRecipe;
		
		return true;
	}
	
	protected ADTable getADTable1() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable midTable = adManager.getADTable(Env.getOrgRrn(), "WIPLotMaskRecipe");
			return midTable;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;		
	}
	
	@Override
	public String doPrevious() {
		return null;
	}

	@Override
	public IWizardPage getNextPage() {
		return null;
	}

	@Override
	public String doSkip() {
		return this.getDefaultDirect();
	}

	@Override
	public boolean canFlipToNextPage() {
		return canFlipNextPage;
	}
	
}
