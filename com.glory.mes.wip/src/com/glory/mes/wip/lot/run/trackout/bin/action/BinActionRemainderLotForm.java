package com.glory.mes.wip.lot.run.trackout.bin.action;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.workflow.graph.def.Transition;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.model.Lot;

public class BinActionRemainderLotForm extends EntityForm {

	private static final Logger logger = Logger.getLogger(BinActionRemainderLotForm.class);
	
	private static final String FIELD_ACTION = "attribute2";
	private static final String FIELD_TRANSITION = "attribute3";
	
	public BinActionRemainderLotForm(Composite parent, int style, Object object, List<ADField> adfields, int gridY, IMessageManager mmng) {
		super(parent, style, object, adfields, gridY, mmng);
	}
	
	@Override
	public IField getField(ADField adField){
		if (FIELD_TRANSITION.equals(adField.getName())) {
			String name = adField.getName();
			String displayLabel = getLabelText(adField);
			int displayLength = adField.getDisplayLength() != null ? adField.getDisplayLength().intValue() : 32;
			try{
				ADManager entityManager = Framework.getService(ADManager.class);
				ADRefTable refTable = new ADRefTable();
				refTable.setObjectRrn(adField.getReftableRrn());
				refTable = (ADRefTable)entityManager.getEntity(refTable);
				if (refTable == null || refTable.getTableRrn() == null){
					return null;
				}
				ADTable adTable = entityManager.getADTable(refTable.getTableRrn());
				ListTableManager tableManager = new ListTableManager(adTable);
				
				PrdManager prdManager = Framework.getService(PrdManager.class);
				StepState state = prdManager.getCurrentStepState(((Lot)this.getObject()).getProcessInstanceRrn());
				List<Transition> reworkTransitions = prdManager.getReworkTransitions(state, true);
				tableManager.setInput(reworkTransitions);
			
				IField field = createRefTableFieldList(name, displayLabel, tableManager, refTable, displayLength);
				addField(name, field);
				return field;
			} catch (Exception e){
				logger.error("FTRemainderLotForm : Init tablelist", e);
			}
		}
		
		return super.getField(adField);
	}
	
	@Override
    public void loadFromObject() {
		super.loadFromObject();
		for (IField f : fields.values()){
			if (FIELD_ACTION.equals(((ADField)f.getADField()).getName())) {
				((RefTableField)f).setData(((RefTableField)f).getInput().get(0));
				break;
			}
		}
	}
}
