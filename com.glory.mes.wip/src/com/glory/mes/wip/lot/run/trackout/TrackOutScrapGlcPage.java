package com.glory.mes.wip.lot.run.trackout;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.custom.TrackOutScrapComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;

public class TrackOutScrapGlcPage extends GlcFlowWizardPage {

	protected TrackOutContext context;
	
	public static final String FIELD_SCRAPCOMPOSITE = "scrapComposite";

	protected CustomField scrapCompositeField;
	protected TrackOutScrapComposite scrapComposite;

	public TrackOutScrapGlcPage() {
		super();
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		setTitle(Message.getString(WipExceptionBundle.bundle.WipLotScrapOperate()));
		scrapCompositeField = form.getFieldByControlId(FIELD_SCRAPCOMPOSITE, CustomField.class);
		scrapComposite = (TrackOutScrapComposite) scrapCompositeField.getCustomComposite();
		
		List<Lot> lots = context.getLots();
		List<Lot> scrapLots = new ArrayList<Lot>();
		for (Lot lot : lots) {
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				if (trackOutLot.getLot().getObjectRrn().equals(lot.getObjectRrn())) {
					if (context.isInputScrap()) {
						if (trackOutLot.getScrapMainQty() != null && trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
							scrapLots.add(lot);
						}
					} else {
						if (trackOutLot.getOutMainQty().compareTo(trackOutLot.getMainQty()) < 0) {
							scrapLots.add(lot);
						}
					}
					break;
				}
			}
		}
		
		scrapComposite.setLots(scrapLots);
		scrapComposite.setStep(context.getStep());
		scrapComposite.refresh();
	}
	
	@Override
	public void setWizard(Wizard wizard) {
		this.wizard = wizard;
		context = (TrackOutContext)((TrackOutWizard)this.getWizard()).getContext();
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		if (context.getInContent().getActions() != null) {
			List<LotAction> scrapLotActions = new ArrayList<LotAction>();
			for (LotAction lotAction : context.getInContent().getActions()) {
				if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
					scrapLotActions.add(lotAction);
				}
			}
			context.getInContent().getActions().removeAll(scrapLotActions);
			for (TrackOutLot trackOutLot : context.getTrackOutLots()) {
				//�������д����������outMain�˻ر����������ص���վ��ҳ
				if(trackOutLot.getScrapMainQty() != null) {
					if (trackOutLot.getScrapMainQty().compareTo(BigDecimal.ZERO) > 0) {
						trackOutLot.setOutMainQty(trackOutLot.getOutMainQty().add(trackOutLot.getScrapMainQty()));
					}
				}
			}
		}
		setErrorMessage("");
		return super.doPrevious();
	}
	
	@Override
	public String doNext() {
		try {
			List<Lot> lots = context.getLots();
			if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
				if (!scrapComposite.validate()) {
					return "";
				}
				if (!checkScrapQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							scrapLotActions.add(lotAction);
						}
					}
					list.removeAll(scrapLotActions);
					list.addAll(scrapComposite.getScrapLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapComposite.getScrapLotActions());
				}
				
			} else {
				if (!checkScrapQty()) {
					return "";
				}
				if (context.getInContent().getActions() != null && context.getInContent().getActions().size() > 0) {
					List<LotAction> list = context.getInContent().getActions();
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (LotAction lotAction : list) {
						if (LotAction.ACTIONTYPE_SCRAP.equals(lotAction.getActionType())) {
							scrapLotActions.add(lotAction);
						}
					}
					list.removeAll(scrapLotActions);
					list.addAll(scrapComposite.getScrapLotActions());
					context.getInContent().setActions(list);
				} else {
					context.getInContent().setActions(scrapComposite.getScrapLotActions());
				}
			}
			if (context.isReworkFlag()) {
				return TrackOutDialog.REWORK_PAGE;
			} else {
				((TrackOutWizard)this.getWizard()).invokeTrackOut();
				return getDefaultDirect();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "";
	}

	protected boolean checkScrapQty() {	
		List<TrackOutLot> trackOutLots = context.getTrackOutLots();		
		//�����������
		List<LotAction> scrapUnits;
		if (scrapComposite != null) {
			scrapUnits = scrapComposite.getScrapLotActions();
		} else {
			scrapUnits = scrapComposite.getScrapLotActions();
		}
		
		for (TrackOutLot trackOutLot : trackOutLots) {
			List<ProcessUnit> units = new ArrayList<ProcessUnit>();
			for (LotAction lotAction : scrapUnits) {
				if (trackOutLot.getLot().getObjectRrn().equals(lotAction.getLotRrn())) {
					units.addAll(lotAction.getActionUnits());
				}				
			}
			
			BigDecimal scrapMainQty = BigDecimal.ZERO;
			BigDecimal scrapSubQty = null;
			if (trackOutLot.getSubQty() != null) {
				scrapSubQty = BigDecimal.ZERO;
			}
			for (ProcessUnit unit : units) {
				scrapMainQty = scrapMainQty.add(unit.getMainQty());//����ı�������2
				if (scrapSubQty != null) {
					scrapSubQty = scrapSubQty.add(unit.getSubQty() != null ? unit.getSubQty() : BigDecimal.ZERO);
				}
			}
			
			if (context.isInputScrap()) {
				if (trackOutLot.getScrapMainQty() != null) {
					if (scrapMainQty.compareTo(trackOutLot.getScrapMainQty()) !=  0) {
						setErrorMessage(trackOutLot.getLotId() + Message.getString(WipExceptionBundle.bundle.WipTrackOutScrapQty()) + trackOutLot.getScrapMainQty());
						return false;
					}
				}
			} else {
				BigDecimal qty = trackOutLot.getMainQty().subtract(trackOutLot.getOutMainQty());
				if (scrapMainQty.compareTo(qty) != 0) {
					setErrorMessage(trackOutLot.getLotId() + Message.getString(WipExceptionBundle.bundle.WipTrackOutScrapQty()) + qty);
					return false;
				}
			}
		}
		return true;
	}
		
}
