package com.glory.mes.wip.lot.run.trackout;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.GlcFlowWizardPage;
import com.glory.framework.core.exception.ExceptionBundle;

public class TrackOutLotListGlcPage extends GlcFlowWizardPage {

	protected FormToolkit toolkit;
	protected Composite parent;
	protected TrackOutWizard tw;
	protected TrackOutContext context;
	public static final String TABLE_NAME = "WIPTrackOutNextInfo";
	
	public static final String FIELD_LISTTABLE = "listTable";
	
	protected ListTableManagerField listTableField;

	public TrackOutLotListGlcPage() {
		super();
	}
	
	@Override
	public IWizardPage getNextPage() {
		return null;
	}

	@Override
	public IWizardPage getPreviousPage() {
		return null;
	}
	
	@Override
	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		listTableField = form.getFieldByControlId(FIELD_LISTTABLE, ListTableManagerField.class);
		
		tw = (TrackOutWizard) this.getWizard();	
		context = (TrackOutContext)tw.getContext();
		listTableField.getListTableManager().setInput(context.getOutLots());
		setTitle(Message.getString("wip.trackout_title"));
		setDescription(Message.getString("wip.lot_next_batch"));
		tw.getDialog().updateButtonName(IDialogConstants.NEXT_ID, Message.getString(ExceptionBundle.bundle.CommonComplete()));
		tw.getDialog().getSquareButton(IDialogConstants.CANCEL_ID).setEnabled(false);
	}
	
//	@Override
//	public void createControl(Composite parent) {
//		this.parent = parent;
//		tw = (TrackOutWizard) this.getWizard();		
//		context = (TrackOutContext)tw.getContext();
//		toolkit = new FormToolkit(parent.getDisplay());
//		/*Composite composite = toolkit.createComposite(parent, SWT.BORDER);
//		composite.setLayout(new GridLayout(1, true));
//		composite.setLayoutData(new GridData(GridData.FILL_BOTH));*/
//		
//		Composite tabCom = toolkit.createComposite(parent, SWT.NONE);
//		tabCom.setLayout(new GridLayout(1, true));
//		tabCom.setLayoutData(new GridData(GridData.FILL_BOTH));
//		try {
//			GridLayout layout = new GridLayout(1, false);
//			tabCom.setLayout(layout);
//			tabCom.setLayoutData(new GridData(GridData.FILL_BOTH));
//			ADManager adManager = Framework.getService(ADManager.class);
//			ADTable lotNext = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
//			ListTableManager tableManager = new TrackOutLotListTableManager(lotNext);
//			tableManager.newViewer(tabCom);
//			tableManager.setInput(context.getOutLots());
//			
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		setControl(parent);
//		setTitle(Message.getString("wip.trackout_title"));
//		setDescription(Message.getString("wip.lot_next_batch"));
//		tw.getDialog().updateButtonName(IDialogConstants.NEXT_ID, Message.getString(ExceptionBundle.bundle.CommonComplete()));
//		tw.getDialog().getSquareButton(IDialogConstants.CANCEL_ID).setEnabled(false);
//	}

	@Override
	public String doNext() {
		return getDefaultDirect();
	}

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	@Override
	public String doPrevious() {
		return "";
	}

}