package com.glory.mes.wip.lot.run.trackin.multieqp;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.SelectEquipmentPage;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

public class MultiEqpTrackInPage extends SelectEquipmentPage {

	protected static final String TABLE_EQUIPMENT = "WIPTrackInBatchEqp";
	protected static final String TABLE_LOT_MULTI = "WIPTrackInQtyUnit";

	protected static final String TABLE_SEL_EQUIPMENT = "WIPTrackInSelEqp";
	protected static final String TABLE_COMP_MULTI = "WIPTrackInCompMultiEqp";
	protected static final String TABLE_COMPONENT = "WIPTrackInSelComp";

	protected ListTableManager selectCompTableManager;

	private static final Logger logger = Logger.getLogger(MultiEqpTrackInPage.class);

	@Override
	public void createControl(Composite parent) {
		try {
			RunWizardContext context = (RunWizardContext) ((RunWizard) getWizard()).getContext();
			step = context.getStep();
			if (step == null || step.getCapability() == null) {
				UI.showError(Message.getString("wip.step_capability_is_null"));
				context.setReturnCode(TrackInContext.FAILED_ID);
				if (this.getShell() != null && !this.getShell().isDisposed()) {
					this.getShell().dispose();
				}
				return;
			}
			// �ڰ��豸��ҵʱ�������豸
			List<Equipment> selectedEqps = context.getSelectEquipments();
			List<Equipment> availableEqps = null;

			// ���û��ѡ���豸
			if (selectedEqps == null || selectedEqps.size() == 0) {
				LotManager manager = Framework.getService(LotManager.class);
				availableEqps = manager.getAvailableEquipments(lot, Env.getSessionContext());
				orderByEqpId(availableEqps);
			} else {
				availableEqps =	selectedEqps;
			}

			if (!step.getIsRequireEqp()) {
				// ������Ǳ����豸����û�п����豸����ֱ����ת���¸�ҳ��
				if (availableEqps == null || availableEqps.size() == 0) {
					((FlowWizard) this.getWizard()).getDialog().skipPressed();
					return;
				}
			} else {
				// ���豸���������ҳ��ѡ��
				canFlipNextPage = false;
			}

			toolkit = new FormToolkit(Display.getCurrent());
			Composite composite = new Composite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
			composite.setLayoutData(gd);

			createContent(composite, availableEqps);
			setControl(composite);
			setPageTitle();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected void createContent(Composite composite, List<Equipment> availableEqps) {
		try {
			Composite parent = toolkit.createComposite(composite, SWT.NONE);
			parent.setLayout(new GridLayout(1, true));
			parent.setLayoutData(new GridData(GridData.FILL_BOTH));

			if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
				createQtyContent(parent, toolkit);
			} else {
				createCompContent(parent, toolkit);
			}

			List<Equipment> showEqps = availableEqps.stream().filter(Equipment::getIsAvailable)
					.collect(Collectors.toList());
			// �Ƴ��ѽ�վ���豸
			showEqps.removeAll(getTrackenInEqp());
			selectEqpTableManager.setInput(showEqps);
			createInvaliableEqpGroup(parent, availableEqps);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void createCompContent(Composite parent, FormToolkit toolkit) throws Exception {
		Group tabGroup1 = new Group(parent, SWT.NONE);
		tabGroup1.setText(Message.getString("wip.multi_eqp_in_components"));
		tabGroup1.setBackground(new Color(null, 255, 255, 255));
		tabGroup1.setLayout(new GridLayout(1, true));
		GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
		gridData.heightHint = 150;
		tabGroup1.setLayoutData(gridData);

		ListTableManager tableManager = new ListTableManager(getAdTable(TABLE_COMP_MULTI));
		tableManager.setInput(getLotTrackedInComp());
		tableManager.newViewer(tabGroup1);

		Composite select = toolkit.createComposite(parent, SWT.NONE);
		select.setLayout(new GridLayout(2, true));
		select.setLayoutData(new GridData(GridData.FILL_BOTH));

		Group eqpGroup = new Group(select, SWT.NONE);
		eqpGroup.setText(Message.getString("wip.trackin_eqp_list"));
		eqpGroup.setBackground(new Color(null, 255, 255, 255));
		eqpGroup.setLayout(new GridLayout(1, false));
		eqpGroup.setLayoutData(new GridData(GridData.FILL_BOTH));

		selectEqpTableManager = new ListTableManager(getAdTable(TABLE_SEL_EQUIPMENT));
		selectEqpTableManager.newViewer(eqpGroup);
		selectEqpTableManager.addSelectionChangedListener(new ISelectionChangedListener() {

			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				selectCompTableManager.getCheckedObject().clear();
			}
		});

		Group compGroup = new Group(select, SWT.NONE);
		compGroup.setText(Message.getString("wip.multi_eqp_wait_components"));
		compGroup.setBackground(new Color(null, 255, 255, 255));
		compGroup.setLayout(new GridLayout(1, false));
		compGroup.setLayoutData(new GridData(GridData.FILL_BOTH));

		selectCompTableManager = new ListTableManager(getAdTable(TABLE_COMPONENT), true);
		selectCompTableManager.newViewer(compGroup);
		selectCompTableManager.setInput(getLotTrackInComp());
		selectCompTableManager.addICheckChangedListener(new ICheckChangedListener() {

			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				Equipment equipment = (Equipment) selectEqpTableManager.getSelectedObject();
				if (equipment == null) {
					setErrorMessage(Message.getString("wip.multi_eqp_select_eqp"));
					selectCompTableManager.getCheckedObject().clear();
					return;
				}

				for (Object object : eventObjects) {
					ComponentUnit unit = (ComponentUnit) object;
					if (checked) {
						unit.setEquipmentId(equipment.getEquipmentId());
						unit.setAttribute1(equipment);
					} else {
						unit.setEquipmentId(null);
						unit.setAttribute1(null);
					}
				}

				if (!canFlipNextPage) {
					if (step.getIsRequireEqp()) {
						canFlipNextPage = true;
					}
					((RunWizard) getWizard()).getDialog().updateButtons();
				}
			}
		});
	}

	private void createQtyContent(Composite parent, FormToolkit toolkit) throws Exception {
		Group tabGroup1 = new Group(parent, SWT.NONE);
		tabGroup1.setText(Message.getString("wip.multi_eqp_current_eqp"));
		tabGroup1.setBackground(new Color(null, 255, 255, 255));
		tabGroup1.setLayout(new GridLayout(1, true));
		GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
		gridData.heightHint = 150;
		tabGroup1.setLayoutData(gridData);

		ListTableManager tableManager = new ListTableManager(getAdTable(TABLE_LOT_MULTI));
		tableManager.setInput(getTrackenInUnits());
		tableManager.newViewer(tabGroup1);

		Group tabGroup = new Group(parent, SWT.NONE);
		tabGroup.setText(Message.getString("wip.trackin_eqp_list"));
		tabGroup.setBackground(new Color(null, 255, 255, 255));
		tabGroup.setLayout(new GridLayout(1, true));
		tabGroup.setLayoutData(new GridData(GridData.FILL_BOTH));

		ADTable adTable = getAdTable(TABLE_EQUIPMENT);
		if (isTrackInInputQty()) {
			selectEqpTableManager = new ListEditorTableManager(adTable, true);
		} else {
			selectEqpTableManager = new ListTableManager(adTable, true);
		}

		selectEqpTableManager.newViewer(tabGroup);
		if (isTrackInInputQty()) {
			((CheckBoxFixEditorTableManager) selectEqpTableManager.getTableManager())
					.addICheckChangedListener(new ICheckChangedListener() {

						@Override
						public void checkChanged(List<Object> eventObjects, boolean checked) {
							if (checked) {
								if (step.getIsRequireEqp()) {
									canFlipNextPage = true;
								}
								((RunWizard) getWizard()).getDialog().updateButtons();
							}
						}
					});
		} else {
			selectEqpTableManager.addICheckChangedListener(new ICheckChangedListener() {

				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if (checked) {
						if (step.getIsRequireEqp()) {
							canFlipNextPage = true;
						}
						((RunWizard) getWizard()).getDialog().updateButtons();
					}
				}
			});
		}
	}

	@Override
	public String doNext() {
		try {
			MultiEqpTrackInWizard wizard = (MultiEqpTrackInWizard) getWizard();
			wizard.setTrackInInputQty(isTrackInInputQty());

			RunWizardContext context = (RunWizardContext) wizard.getContext();
			List<Equipment> equipments = getSelectedEquipments();
			if (equipments == null || equipments.size() == 0) {
				if (!context.getStep().getIsRequireEqp()) {
					return getDefaultDirect();
				}
				throw new ClientException("wip.error.must_select_eqp");
			}
			context.setSelectEquipments(equipments);
			if (CollectionUtils.isNotEmpty(context.getSelectEquipments())) {
				List<ProcessUnit> processUnits = new ArrayList<ProcessUnit>();
				if (QtyUnit.getUnitType().equals(lot.getSubUnitType())) {
					if (isTrackInInputQty()) {
						// ������������Ƿ�����
						Optional<Equipment> f = equipments.stream().filter(e -> e.getAttribute1() == null
								|| BigDecimal.ZERO.compareTo(new BigDecimal(String.valueOf(e.getAttribute1()))) >= 0)
								.findAny();
						if (f.isPresent()) {
							throw new ClientException(Message.getString("wip.multi_eqp_lot_qty_error"));
						}
						// ������������Ƿ�С�ڵ�������ʣ��δ��վ����
						BigDecimal total = equipments.stream()
								.map(e -> new BigDecimal(String.valueOf(e.getAttribute1())))
								.reduce(BigDecimal.ZERO, BigDecimal::add);
						// �õ���ǰʣ������
						BigDecimal inQty = BigDecimal.ZERO;
						LotManager lotManager = Framework.getService(LotManager.class);
						List<ProcessUnit> oldProcessUnits = lotManager.getLotSubProcessUnits(lot);
						if (CollectionUtils.isNotEmpty(oldProcessUnits)) {
							inQty = oldProcessUnits.stream().map(ProcessUnit::getMainQty).reduce(BigDecimal.ZERO,
									BigDecimal::add);
						}

						BigDecimal lastQty = lot.getMainQty().subtract(inQty);
						if (total.compareTo(lastQty) > 0) {
							throw new ClientException(String.format(Message.getString("wip.multi_eqp_lot_qty_not_enough"), total, lastQty));
						}
					}

					for (Equipment e : equipments) {
						QtyUnit unit = new QtyUnit();
						unit.setEquipmentId(e.getEquipmentId());
						unit.setParentProcessUnit(lot);
						unit.setParentUnitRrn(lot.getObjectRrn());

						if (isTrackInInputQty()) {
							Object qty = e.getAttribute1();
							unit.setMainQty(new BigDecimal(String.valueOf(qty)));
						}
						processUnits.add(unit);
					}
				} else {
					List<?> objects = selectCompTableManager.getInput();
					// �õ��������豸�ŵ����
					List<ComponentUnit> units = Lists.newArrayList(objects).stream().map(o -> ((ComponentUnit) o))
							.filter(c -> c.getEquipmentId() != null).collect(Collectors.toList());
					processUnits.addAll(units);
				}

				lot.setEquipmentId(equipments.get(0).getEquipmentId());
				lot.setSubProcessUnit(processUnits);
				return getDefaultDirect();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("MultiEquipmentPage : doNext()", e);
		}
		return "";
	}

	@Override
	protected List<Equipment> getSelectedEquipments() throws Exception  {
		if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
			List<?> objects = selectCompTableManager.getInput();
			if (CollectionUtils.isEmpty(objects)) {
				return null;
			}

			Set<Equipment> selectEqps = Sets.newHashSet();
			for (Object object : objects) {
				ComponentUnit unit = (ComponentUnit) object;
				if (unit.getAttribute1() != null) {
					selectEqps.add((Equipment) unit.getAttribute1());
				}
			}
			return selectEqps.stream().collect(Collectors.toList());
		}
		
		List<Object> objects = selectEqpTableManager.getCheckedObject();
		if (CollectionUtils.isEmpty(objects)) {
			return null;
		} else {
			return objects.stream().map(o -> ((Equipment)o)).collect(Collectors.toList());
		}
	}

	private List<ComponentUnit> getLotTrackInComp() throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		Lot get = lotManager.getLotWithComponent(lot.getObjectRrn());
		List<ProcessUnit> processUnits = get.getSubProcessUnit();
		if (CollectionUtils.isNotEmpty(processUnits)) {
			List<ComponentUnit> componentUnits = processUnits.stream().filter(p -> p instanceof ComponentUnit)
					.map(p -> ((ComponentUnit) p))
					.filter(c -> c.getProcessState() == null && c.getEquipmentId() == null)
					.collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(componentUnits)) {
				// ����λ������
				componentUnits.sort(Comparator.comparing(ComponentUnit::getComponentId));
				return componentUnits;
			}
		}

		return Lists.newArrayList();
	}

	private List<ComponentUnit> getLotTrackedInComp() throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		Lot get = lotManager.getLotWithComponent(lot.getObjectRrn());
		List<ProcessUnit> processUnits = get.getSubProcessUnit();
		if (CollectionUtils.isNotEmpty(processUnits)) {
			List<ComponentUnit> componentUnits = processUnits.stream().filter(p -> p instanceof ComponentUnit)
					.map(p -> ((ComponentUnit) p)).filter(c -> c.getProcessState() != null)
					.collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(componentUnits)) {
				// ����ʱ�䡢�豸������
				componentUnits.sort(Comparator.comparing(ComponentUnit::getTrackInTime)
						.thenComparing(ComponentUnit::getComponentId));
				return componentUnits;
			}
		}

		return Lists.newArrayList();
	}
	
	private List<ProcessUnit> getTrackenInUnits() throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<ProcessUnit> units = lotManager.getLotSubProcessUnits(lot);
		return units.stream().filter(p -> !StringUtil.isEmpty(p.getProcessState())).collect(Collectors.toList());
	}

	private List<Equipment> getTrackenInEqp() throws Exception {
		Set<String> eqpIds = getTrackenInUnits().stream().filter(
				p -> !StringUtil.isEmpty(p.getEquipmentId())).map(ProcessUnit::getEquipmentId).collect(Collectors.toSet());
		List<Equipment> equipments = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(eqpIds)) {
			RASManager rasManager = Framework.getService(RASManager.class);
			for (String equipmentId : eqpIds) {
				equipments.add(rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentId));
			}
		}
		return equipments;
	}

	private ADTable getAdTable(String name) throws Exception {
		ADManager adManager = Framework.getService(ADManager.class);
		ADTable adTable = adManager.getADTable(Env.getOrgRrn(), name);
		return adTable;
	}

	protected boolean isTrackInInputQty() {
		return true;
	}

}
