package com.glory.mes.wip.lot.component.hisquery;

import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.export.NatExporter;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;
import org.jboss.logging.Logger;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotHistoryManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.his.ComponentUnitHis;
import com.glory.mes.wip.lot.componenteqp.hisquery.ComponentHisDialog;
import com.glory.mes.wip.model.Lot;

public class ComponentHisEditor extends GlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.component.hisquery.ComponentHisEditor";
	private static final Logger logger = Logger.getLogger(ComponentHisEditor.class);
	private static final String EVENT_ENTERPRESSED = "EnterPressed";
	private static final String CONTROL_EXPORT = "Export";
	private static final String CONTROL_REFRESH = "Refresh";

	protected ListTableManagerField listTableManager;
	protected NatTable natTable;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		subscribeAndExecute(eventBroker, form.getFullTopic(EVENT_ENTERPRESSED), this::enterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_EXPORT), this::export);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_REFRESH), this::refresh);
		listTableManager = form.getFieldByControlId("ComponentHisTable", ListTableManagerField.class);
		listTableManager.getListTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				doubleClick();
			}
		});
	}

	public void enterPressed(Object object) {
		try {
			Event event = (Event) object;
			ComponentUnitHis componentUnitHis = (ComponentUnitHis) event.getProperty(GlcEvent.PROPERTY_DATA);
			queryComponentUnitHis(componentUnitHis);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("ComponentHisEditor EnterPressed:", e);
		}
	}
	
	public void doubleClick() {
    	try {
    		ComponentUnitHis componentUnitHis = (ComponentUnitHis) listTableManager.getListTableManager().getSelectedObject();
    		ComponentHisDialog dialog = new ComponentHisDialog(null, null, eventBroker, componentUnitHis);
    		dialog.open();
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
			 logger.error("ComponentEqpHisEditor DoubleClick:", e);
		}
    }

	public void export(Object object) {
		try {
			this.natTable = this.listTableManager.getListTableManager().getNatTable();
			(new NatExporter(this.natTable.getShell())).exportSingleLayer(
					this.listTableManager.getListTableManager().getLayer(), this.natTable.getConfigRegistry());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public void refresh(Object object) {
		List<? extends Object> list = listTableManager.getListTableManager().getInput();
		ComponentUnitHis componentUnitHis = list != null && list.size() > 0 ? (ComponentUnitHis) list.get(0) : null;
		queryComponentUnitHis(componentUnitHis);
	}

	private void queryComponentUnitHis(ComponentUnitHis componentUnitHis) {
		try {
			if (componentUnitHis != null && componentUnitHis.getComponentId() != null) {
				LotHistoryManager lotHistoryManager = Framework.getService(LotHistoryManager.class);
				LotManager lotManager = Framework.getService(LotManager.class);
				List<ComponentUnitHis> componentUnitHisList = lotHistoryManager.getComponentHisByComponentId(
						Env.getOrgRrn(), componentUnitHis.getComponentId(), StringUtils.EMPTY);
				Lot subLot = new Lot();
				BeanUtils.copyProperties(subLot, componentUnitHis);
				List<Lot> lotList = lotManager.getParentUnitLots(subLot);
				componentUnitHisList.forEach(his -> {
					lotList.forEach(lot -> {
						if (lot.getObjectRrn().equals(his.getParentUnitRrn())) {
							his.setLotId(lot.getLotId());
						}
					});
				});
				listTableManager.getListTableManager().setInput(componentUnitHisList);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
