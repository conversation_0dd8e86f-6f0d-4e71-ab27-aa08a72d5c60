package com.glory.mes.wip.lot.unscrapnew;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.Form;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.part.PartFlowTreeField;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.action.LotUnScrapAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.newpart.NewPartForm;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;

public class UnScrapNewPartPage extends FlowWizardPage {

	protected PartFlowFormUnScrap form;
	private static final String TABLE_NAME = "WIPNewPart";
	protected static final Color GRAY = new Color(null, 255, 255, 255);
	protected Lot lot;
	protected UnScrapNewContext context;
	protected NewPartForm itemForm;
	protected Tree tree;

	public UnScrapNewPartPage() {
		super();
	}
	
	public UnScrapNewPartPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void refresh() {
		super.refresh();
		setErrorMessage(null);
		setMessage(null);
	}

	@Override
	public String doNext() {
		List<Node> nodes = new ArrayList<Node>();
		TreeItem[] treeItem = tree.getSelection();
		if (treeItem != null && treeItem.length > 0
				&& treeItem[0].getData() instanceof StepState) {
			if (treeItem[0].getParentItem() != null
					&& treeItem[0].getParentItem().getData() != null)
				nodes.add((Node) treeItem[0].getParentItem().getData());
			if (treeItem[0].getData() != null)
				nodes.add((Node) treeItem[0].getData());
			context.setNodeList(nodes);
			Lot lot = invokeUnScrapNewLot();
			context.setLot(lot);
			return UnScrapNewDialog.LOT_LIST_PAGE;
		} else {
			setErrorMessage(Message
					.getString("unscrap.unscrapnew_dialog_selectstartstep"));
			return null;
		}
	}

	public Lot invokeUnScrapNewLot() {
		try {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			
			context.getLot().setOperator1(context.getOperator());
			
			Lot lot = null;
			if (context.isUnScrapComponent()) {
				List<List<LotUnScrapAction>> unitActionsList = Lists.newArrayList();
				List<ComponentUnit> componentUnits = Lists.newArrayList();
				for (ProcessUnit processUnit : context.getUnits()) {
					ComponentUnit componentUnit = (ComponentUnit) processUnit;
					List<LotUnScrapAction> unitActions = Lists.newArrayList();
					for (LotScrap scrap : context.getLotScraps()) {
						if (scrap.getComponentId().equals(componentUnit.getComponentId())) {
							LotUnScrapAction action = new LotUnScrapAction();
							action.setActionCode(scrap.getUnScrapCode());
							action.setActionComment(scrap.getUnScrapComment());
							action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
							action.setLotScrap(scrap);
							
							unitActions.add(action);
						}
					}
					unitActionsList.add(unitActions);
					componentUnits.add(componentUnit);
				}
				
				// û����������Ĭ������Ƭ����
				boolean isQty = false;
				if (context.getLot().getSubQty() != null) {
					isQty = true;
				}
				
				lot = componentManager.unScrapComponentNewLot(context.getLot(), context.getNodeList(), 
						componentUnits, unitActionsList, isQty, true, null, Env.getSessionContext());
			} else {
				List<LotUnScrapAction> unScrapActions = Lists.newArrayList();
				for (LotScrap scrap : context.getLotScraps()) {
					LotUnScrapAction action = new LotUnScrapAction();
					action.setLotRrn(DBUtil.toLong(scrap.getAttribute1()));
					action.setActionCode(scrap.getUnScrapCode());
					action.setActionComment(scrap.getUnScrapComment());
					action.setActionType(LotAction.ACTIONTYPE_UNSCRAP);
					action.setLotScrap(scrap);
					
					unScrapActions.add(action);
				}
				
				LotManager lotManager = Framework.getService(LotManager.class);
				lot = lotManager.unScrapNewLot(context.getLot(), context.getNodeList(), 
						unScrapActions, context.getLotAction(), true, Env.getSessionContext());
			}
			return lot;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return new Lot();
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		setErrorMessage(null);
		setMessage(null);
		return UnScrapNewDialog.SCHEDULE_PAGE;
	}

	@Override
	public void createControl(Composite parent) {
		context = (UnScrapNewContext) ((UnScrapNewWizard) getWizard())
				.getContext();
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		IMessageManager mmng = new Form(parent, SWT.NONE).getMessageManager();
		setTitle(Message.getString("unscrap.unscrapnew_dialog_selectstep"));
		setDescription(Message
				.getString("unscrap.unscrapnew_dialog_selectstartstep"));

		Composite content = toolkit.createComposite(parent, SWT.NONE);
		content.setLayoutData(new GridData(700, 300));
		content.setLayout(new GridLayout(1, false));
		content.setBackground(GRAY);

		form = new PartFlowFormUnScrap(content, SWT.NONE, getADtable(), mmng);
		form.setContext(context);
//		form.setLayout(new FillLayout());
		form.setBackground(GRAY);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));
		setControl(content);
		treeItemSelect();
	}

	protected void treeItemSelect() {
		HashMap<String, IField> fields = form.getFields();
		Set<String> key = fields.keySet();
		PartFlowTreeField field = (PartFlowTreeField) fields.get(key.iterator()
				.next());
		tree = field.getTree();
		tree.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {}
		});
	}

	protected ADTable getADtable() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			return table;
		} catch (ClientException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new ADTable();
	}

}
