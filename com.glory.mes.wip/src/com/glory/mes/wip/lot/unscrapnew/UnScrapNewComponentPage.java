package com.glory.mes.wip.lot.unscrapnew;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class UnScrapNewComponentPage extends FlowWizardPage {
	
	private static final String TABLE_NAME = "WIPUnScrapComponentPosition";

	private FixEditorTableManager tableManager;

	private UnScrapNewContext context;
	
	public UnScrapNewComponentPage() {
		super();
	}
	
	public UnScrapNewComponentPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	protected void createTableViewer(Composite tc, FormToolkit toolkit) {
		tc.setLayoutData(new GridData(GridData.FILL_BOTH));
		tc.setLayout(new GridLayout(1, false));
		
		tableManager = new FixEditorTableManager(getTable());
		tableManager.newViewer(tc);
		// ��ID����
		List<ProcessUnit> units = context.getUnits();
		units = units.stream().map(p -> ((ComponentUnit)p))
				.sorted(Comparator.comparing(ComponentUnit::getComponentId)).collect(Collectors.toList());
		tableManager.setInput(units);
	}
	
	private ADTable getTable() {
		ADTable adTable = null;
    	try {
    		ADManager adManager = Framework.getService(ADManager.class);
    		adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	return adTable;
    }

	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}


	@Override
	public String doNext() {
		List<ProcessUnit> units = (List) tableManager.getInput();
		if (units.size() > 0) {
			List<ProcessUnit> processUnits = Lists.newArrayList(units);
			
			// ���һ��λ���Ƿ��Ӧ��Ƭ��
			Map<String, String> unitsMap = Maps.newHashMap();
			for (ProcessUnit processUnit : processUnits) {
				ComponentUnit unit = (ComponentUnit) processUnit;
				String position = unit.getPosition();
				if (unitsMap.containsKey(position)) {
					if (!unit.getComponentId().equals(unitsMap.get(position))) {
						UI.showInfo(String.format(Message.getString("wip.unscrap_position_repeat"), 
								unit.getComponentId(), unitsMap.get(position)));
						return "";
					}
				}
				
				unitsMap.put(position, unit.getComponentId());
			}
			
			context.setUnits(processUnits);
			return UnScrapNewDialog.SCHEDULE_PAGE;
		} else {
			UI.showInfo(Message.getString("wip.unscrap_no_units_selected"));
			return "";
		}
		
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		setErrorMessage(null);
		setMessage(null);
		
		return UnScrapNewDialog.START_PAGE;
	}

	@Override
	public void createControl(Composite parent) {
		context = (UnScrapNewContext) ((UnScrapNewWizard) getWizard()).getContext();
		
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		setTitle(Message.getString("unscrap.unscrapnew_dialog_scrap"));
		setDescription(Message.getString("wip.unscrap_enter_position"));

		Composite composite = new Composite(parent, SWT.NONE);
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
		tableContainer.setLayout(new GridLayout());
		tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
		createTableViewer(tableContainer, toolkit);
		setControl(composite);
	}

	@Override
	public boolean canFlipToNextPage() {
		return true;
	}

}
