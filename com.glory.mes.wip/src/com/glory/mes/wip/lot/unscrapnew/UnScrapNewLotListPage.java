package com.glory.mes.wip.lot.unscrapnew;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class UnScrapNewLotListPage extends FlowWizardPage {
	
	protected static final String TABLE_NAME = "WIPTrackOutNextInfo";

	protected FormToolkit toolkit;
	protected UnScrapNewWizard tw;
	protected UnScrapNewContext context;
	private ListTableManager tableManager;

	public UnScrapNewLotListPage() {
		super();
	}
	
	public UnScrapNewLotListPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public IWizardPage getNextPage() {
		return null;
	}

	@Override
	public IWizardPage getPreviousPage() {
		return null;
	}

	@Override
	public void refresh() {
		setErrorMessage(null);
		setMessage(null);
	}

	@Override
	public void createControl(Composite parent) {
		tw = (UnScrapNewWizard) this.getWizard();

		context = tw.getContext();
		toolkit = new FormToolkit(parent.getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		composite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		composite.setLayout(new GridLayout(1, true));
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));

		tableManager = new ListTableManager(getTable());
		tableManager.newViewer(composite);
		tableManager.setInput(Lists.newArrayList(context.getLot()));
		
		setControl(composite);
		setTitle(Message.getString("unscrap.unscrapnew_dialog_currentlotinfo"));
		setDescription(Message.getString("wip.lot_next_batch"));
		tw.getDialog().updateButtonName(IDialogConstants.CANCEL_ID, Message.getString(ExceptionBundle.bundle.CommonComplete()));
	}


	public void decorateButton(Button button) {
		button.setFont(JFaceResources.getDialogFont());
		GridData data = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		int widthHint = 92;
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = Math.max(widthHint, minSize.x);
		button.setLayoutData(data);
	}
	
	private ADTable getTable() {
		ADTable adTable = null;
    	try {
    		ADManager adManager = Framework.getService(ADManager.class);
    		adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	return adTable;
    }

	@Override
	public String doNext() {
		return "";
	}

	@Override
	public String doPrevious() {
		return "";
	}

}