package com.glory.mes.wip.lot.unscrapnew;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.part.PartFlowForm;
import com.glory.mes.prd.part.PartFlowTreeField;
import com.glory.mes.prd.viewer.ProcessFlowTreeManager;
import com.glory.mes.wip.model.Lot;

public class PartFlowFormUnScrap extends PartFlowForm {
	private static final Logger logger = Logger
			.getLogger(PartFlowFormUnScrap.class);
	protected UnScrapNewContext context;
	protected ProcessFlowTreeManager treeManager;

	public PartFlowFormUnScrap(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);

	}

	public PartFlowFormUnScrap(Composite parent, int style, ADTable table,
			IMessageManager mmng, UnScrapNewContext context) {
		this(parent, style, table, mmng);
		this.context = context;
	}

	@Override
	public void addFields() {
		try {
			treeManager = new ProcessFlowTreeManager();
			field = new PartFlowTreeField(FIELD_ID, "", treeManager);
			addField(FIELD_ID, field);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public boolean saveToObject() {
		return true;
	}

	@Override
	public void loadFromObject() {
		Lot lot = context.getLot();
		object = searchPart(Long.valueOf(lot.getPartRrn()));
		if (object != null) {
			field.setValue(object);
			refresh();
		}
	}

	protected Part searchPart(Long partRrn) {
		try {
			if (partRrn != null) {
				Part part = new Part();
				part.setObjectRrn(partRrn);
				ADManager entityManager = Framework.getService(ADManager.class);
				return (Part) entityManager.getEntity(part);
			}
		} catch (Exception e) {
			logger
					.error("PartFlowFormUnScrap searchPart: part with id "+partRrn+" isn' t exsited!");
		}
		return null;
	}

	public void setContext(UnScrapNewContext context) {
		this.context = context;
		loadFromObject();
	}

	public ProcessFlowTreeManager getTreeManager() {
		return treeManager;
	}

}
