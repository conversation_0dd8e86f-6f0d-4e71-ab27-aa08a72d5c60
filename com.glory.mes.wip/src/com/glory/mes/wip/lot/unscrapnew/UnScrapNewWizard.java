package com.glory.mes.wip.lot.unscrapnew;

import org.eclipse.jface.wizard.IWizardContainer;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;

public class UnScrapNewWizard extends FlowWizard {

	private UnScrapNewContext context;

	public UnScrapNewWizard() {	
	}
	
	public UnScrapNewWizard(UnScrapNewContext context) {
		super(UnScrapNewContext.UN_SCRAP_NEW);
		this.context = context;
	}

	@Override
	public boolean performFinish() {
		return true;
	}

	/*@Override
	public boolean performCancel() {
		IWizardContainer container = getContainer();
		if (container.getCurrentPage().equals(
				getPage(UnScrapNewDialog.LOT_LIST_PAGE)))
			return true;
		if (UI.showConfirm(Message.getString("wip.lot_cancle")))
			return true;
		else
			return false;
	}*/

	public void setContext(UnScrapNewContext context) {
		this.context = context;
	}

	public UnScrapNewContext getContext() {
		return context;
	}

}
