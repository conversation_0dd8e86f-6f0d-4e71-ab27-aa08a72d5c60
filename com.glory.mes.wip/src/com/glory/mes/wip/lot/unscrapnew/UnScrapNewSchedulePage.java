package com.glory.mes.wip.lot.unscrapnew;

import java.math.BigDecimal;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.wip.model.Lot;

public class UnScrapNewSchedulePage extends FlowWizardPage {
	
	protected IMessageManager mmng;
	protected UnScrapNewScheduleForm form;
	protected UnScrapNewContext context;

	public UnScrapNewSchedulePage() {
		super();
	}
	
	public UnScrapNewSchedulePage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public String doNext() {
		try {
			getMessageManager().setAutoUpdate(true);
			getMessageManager().removeAllMessages();
			if (form.getObject() != null) {
				boolean saveFlag = true;
				if (!form.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					Lot lotNew = (Lot) form.getObject();
					lotNew.setOrgRrn(Env.getOrgRrn());
					
					if (context.isUnScrapComponent()) {
						lotNew.setSubUnitType(Lot.UNIT_TYPE_COMPONENT);
					} else {
						lotNew.setSubUnitType(Lot.UNIT_TYPE_QTY);
					}
					
					PrdManager prdManager = Framework.getService(PrdManager.class);
					Part part = null;
					if (lotNew.getPartVersion() != null) {
						part = prdManager.getPartById(
								Env.getOrgRrn(), lotNew.getPartName(), lotNew.getPartVersion());
					} else {
						part = prdManager.getActivePart(Env.getOrgRrn(), lotNew.getPartName(), false);
					}
					lotNew.setPartName(part.getName());
					lotNew.setPartRrn(part.getObjectRrn());
					lotNew.setPartType(part.getMaterialType());
					lotNew.setPartVersion(part.getVersion());
					// ��ʼ�����������������������ں�̨����
					lotNew.setMainQty(BigDecimal.ZERO);
					if (context.isUnScrapSubQty()) {
						lotNew.setSubQty(BigDecimal.ZERO);
					}
					
					context.setLot(lotNew);
					return UnScrapNewDialog.NEW_PART_PAGE;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			getMessageManager().setAutoUpdate(false);
		}
		return "";
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		setErrorMessage(null);
		setMessage(null);
		
		boolean isComponent = context.getLotScraps().get(0).getComponentRrn() != null;
		
		if (isComponent) {
			return UnScrapNewDialog.COMP_UNIT_PAGE;
		} else {
			return UnScrapNewDialog.START_PAGE;
		}
	}

	@Override
	public void createControl(Composite parent) {
		mmng = getMessageManager();
		
		context = (UnScrapNewContext) ((UnScrapNewWizard) getWizard())
				.getContext();
		
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		setTitle(Message.getString("unscrap.unscrapnew_dialog_createnewLot"));
		setDescription(Message.getString("unscrap.unscrapnew_dialog_inputlotdetail"));

		Composite content = toolkit.createComposite(parent, SWT.NONE);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setLayout(new GridLayout(1, false));
		content.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		form = new UnScrapNewScheduleForm(content, SWT.BORDER, new Lot(), mmng);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));
		setControl(content);
	}

	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	public IMessageManager getMessageManager() {
		if (this.mmng != null) {
			if (this.mmng instanceof FMessageManager) {
				return mmng;
			}
		}
		return new FMessageManager();
	}

}
