package com.glory.mes.wip.lot.unscrapnew;

import java.util.ArrayList;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.wip.model.Lot;

public class UnScrapNewScheduleForm extends EntityForm {

	private static final String TABLE_NAME = "WIPUnScrapNew";

	public UnScrapNewScheduleForm(Composite parent, int style, Object object, IMessageManager mmng) {
		super(parent, style, object, mmng);
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ADTab adTab = adManager.getADTab(table.getTabs().get(0).getObjectRrn());
			ADField lotId = null;
			ADField mainQty = null;
			for (ADField field : adTab.getFields()) {
				if (this.allADfields == null)
					this.allADfields = new ArrayList<ADField>();
				if ("planStartDate".equals(field.getName())) {
					field.setIsEditable(true);
				}
				if ("mainQty".equals(field.getName())) {
					mainQty = field;
				}
				if ("lotId".equals(field.getName())) {
					lotId = field;
				}
				this.allADfields.add(field);
			}
			adTab.getFields().remove(mainQty);
			adTab.getFields().remove(lotId);
			this.tab = adTab;
			createForm();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void addFields() {
		super.addFields();
		SearchField searchField = (SearchField) getFields().get("woId");
		searchField.addValueChangeListener(new IValueChangeListener() {
			
			@Override
			public void valueChanged(Object sender, Object newValue) {
				WorkOrder workOrder = (WorkOrder) searchField.getData();
				Lot lot = (Lot) getObject();
				lot.setWoId(workOrder.getDocId());
				lot.setPlanStartDate(workOrder.getPlanStartDate());
				lot.setPlanEndDate(workOrder.getPlanEndDate());
				lot.setRequireDate(workOrder.getRequireDate());
				lot.setPriority(workOrder.getPriority());
				lot.setPartName(workOrder.getPartName());
				lot.setPartVersion(workOrder.getPartVersion());
				lot.setCustomerCode(workOrder.getCustomerCode());
				lot.setCustomerOrder(workOrder.getCustomerOrder());
				lot.setCustomerPartId(workOrder.getCustomerPartId());
				
				setObject(lot);
				loadFromObject();
			}
		});
	}

}
