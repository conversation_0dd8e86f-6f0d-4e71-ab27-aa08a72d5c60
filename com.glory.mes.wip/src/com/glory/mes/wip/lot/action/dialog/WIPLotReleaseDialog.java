package com.glory.mes.wip.lot.action.dialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADQuery;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.framework.security.model.ADUserGroup;
import com.glory.mes.wip.Activator;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.google.common.collect.Lists;

public class WIPLotReleaseDialog extends LotActionDialog {
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String ADFORM_NAME = "WIPLotActionReleaseDialog";
	
	private static final String FIELD_LOT_HOLD_LIST = "lotHoldList";
	private static final String FIELD_RELEASE_ACTION = "releaseAction";
	private static final String FIELD_IS_FORBIDDEN_FLAG = "isForbiddenFlag";
	
	private ListTableManagerField lotHoldListField;
	private EntityFormField releaseActionEntityForm;
	protected List<String> currentUserGroups;
	protected List<Lot> lots;
	protected Event event;
	
	public WIPLotReleaseDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	public WIPLotReleaseDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, Event event) {
		super(ADFORM_NAME, authority, eventBroker);
		this.lots = Lists.newArrayList(lot);
        this.event = event;
        setLotList(Lists.newArrayList(lot));
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotHoldListField = form.getFieldByControlId(FIELD_LOT_HOLD_LIST, ListTableManagerField.class);
		releaseActionEntityForm = form.getFieldByControlId(FIELD_RELEASE_ACTION, EntityFormField.class);
		releaseActionEntityForm.setValue(new LotAction());
		releaseActionEntityForm.refresh();
		
		BooleanField forbiddenField = releaseActionEntityForm.getFieldByControlId(FIELD_IS_FORBIDDEN_FLAG, BooleanField.class);
		if (forbiddenField!= null && !Activator.isRTDAvailable()) {
			forbiddenField.getCheckboxControl().dispose();
			forbiddenField.getLabelControl().dispose();
		}
		
		initLot();
	}
	
	@Override
	public void initLot() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			if(CollectionUtils.isNotEmpty(getLotList())) {
				lots = getLotList();
			}
			
			if (CollectionUtils.isNotEmpty(lots)) {
				StringBuffer whereClause = new StringBuffer(" lotId in (");
				for (int i = 0; i < lots.size(); i++) {
					if (i + 1 == lots.size()) {
						whereClause.append("'" + lots.get(i).getLotId() + "')");
					} else {
						whereClause.append("'" + lots.get(i).getLotId() + "',");	
					}
				}
				List<ADQuery> querys = adManager.getEntityList(0L, ADQuery.class, 1, " name = 'BeachHoldList'", "");
				if (querys.size() != 0) {
					ADQuery query = (ADQuery)querys.get(0);
					String queryText = query.getQueryText();
					List<Map> currentList = adManager.getEntityMapListByQueryText(queryText, getParameterMap(), 0, Env.getMaxResult(), 
							                                                      whereClause.toString(), "");
					if (currentList != null) {
						lotHoldListField.getListTableManager().setInput(currentList);
					}
				}					
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			if (!releaseActionEntityForm.validate()) {
				return;
			}
			LotAction action = (LotAction) releaseActionEntityForm.getValue();
			List<Object> objects = lotHoldListField.getListTableManager().getCheckedObject();

			StringBuffer whereClause = new StringBuffer(" objectRrn in (");
			if (CollectionUtils.isNotEmpty(objects)) {
				for (int i = 0; i < objects.size(); i++) {
					Map<String, Object> selectObjMap = (Map<String, Object>) objects.get(i);
					if (!selectObjMap.containsKey("HOLDRRN")) {
						UI.showError(Message.getString("wip.release_not_null"));
						return;
					}
					if (i + 1 == objects.size()) {
						whereClause.append(selectObjMap.get("HOLDRRN") + ")");
					} else {
						whereClause.append(selectObjMap.get("HOLDRRN") + ",");
					}
				}
				List<LotHold> lotHold = adManager.getEntityList(Env.getOrgRrn(), LotHold.class, Integer.MAX_VALUE, whereClause.toString(), "");
				lotManager.releaseLots(lotHold, action, Env.getSessionContext());
				UI.showError(Message.getString("wip.release_successed"));
				super.okPressed();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void intUserGroup() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADUser User = new ADUser();
			User.setObjectRrn(Env.getUserRrn());
			User.setOrgRrn(Env.getOrgRrn());
			ADUser currUser = (ADUser) adManager.getEntity(User);
			List<ADUserGroup> userGroups = currUser.getUserGroups();
			userGroups = userGroups.stream().filter(ls -> ls.getOrgRrn().equals(Env.getOrgRrn())) .collect(Collectors.toList());
			currentUserGroups = new ArrayList<String>();
			for (int i = 0; i < userGroups.size(); i++) {
				currentUserGroups.add(userGroups.get(i).getName());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public Map<String, Object> getParameterMap() {
		intUserGroup();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("holdOwner", currentUserGroups);
		paramMap.put("orgRrn", Env.getOrgRrn());
		return paramMap;
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			for(Lot lot : getLotList()) {
				if (!Lot.HOLDSTATE_ON.equalsIgnoreCase(lot.getHoldState())) {
					UI.showError(lot.getLotId() + Message.getString("wip.lot_holdstate_not_allow"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}
