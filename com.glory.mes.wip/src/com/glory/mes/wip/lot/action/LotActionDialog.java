package com.glory.mes.wip.lot.action;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.widgets.IPreAuthFunc;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateModel;

public abstract class LotActionDialog extends GlcBaseDialog implements IPreAuthFunc {
	
	private List<Lot> lotList;
	public String eventId;
	public IPreAuthFunc preAuthFunc;
	private String operator;
	
	public LotActionDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}
	
	public abstract void initLot();
	
	public void initADFromName () throws Exception{};
	
	public abstract boolean isSupportMulitLot();
	
	public List<Lot> getLotList() {
		return lotList;
	}

	public void setLotList(List<Lot> lotList) {
		this.lotList = lotList;
	}
	
	public boolean validate() {
		if (CollectionUtils.isEmpty(lotList)) {
			UI.showError(Message.getString("wip.not_select_lot"));
			return false;
		}
		if (!isSupportMulitLot()) {
			if (lotList.size() > 1) {
				UI.showError(Message.getString("wip.not_support_multi_lot"));
				return false;
			}
		}
		return true;
	}
	
	/**
	 * ����Ϊ�¼�����LotStateModel,���û�������򷵻�null,��ʱ��Ҫ����¼���Ĭ�Ϲ���
	 * ����Ϊÿ���¼����ö���LotStateModel,ֻҪ����һ������Ϳ���ִ�д��¼�(Button����)
	 * LotStateModel��鷽ʽΪ���ε�ǰ״̬��Hold״̬,��LotStateModelһ��
	 */
	public Boolean checkLotStateModel(List<Lot> lots) {
		try {
			if (!StringUtil.isEmpty(eventId)) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotStateModel> models = adManager.getEntityList(Env.getOrgRrn(), LotStateModel.class, Integer.MAX_VALUE, " eventId = '" + eventId + "'", "");
				if (models.isEmpty()) {
					return null;
				}
				
				for (Lot lot : lots) {
					boolean flag = false;
					for (LotStateModel model : models) {
						if (!StringUtil.isEmpty(model.getHoldState()) && !model.getHoldState().equals(lot.getHoldState())) {
							//���μ�鲻ͨ��,���������һ����
							continue;
						}
						if (!StringUtil.isEmpty(model.getLotState()) && !model.getLotState().equals(lot.getState())) {
							//���μ�鲻ͨ��,���������һ����
							continue;
						}
						flag = true;
						break;
					}
					if (!flag) {
						UI.showError(Message.getString("wip.lot_state_not_allow"));
						return false;
					}
				}	
				return true;
			} else {
				return null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}
	
}
