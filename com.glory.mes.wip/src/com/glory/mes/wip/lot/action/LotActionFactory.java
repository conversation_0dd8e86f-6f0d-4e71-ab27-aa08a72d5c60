package com.glory.mes.wip.lot.action;

import java.lang.reflect.Constructor;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.security.client.SecurityManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.wip.lot.action.dialog.WIPLotBankInDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotBankOutDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotHoldDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotInfoModifyDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotMergeDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotParameterDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotReleaseDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotScrapDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotShipDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotSplitDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotTerminateDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotUnScrapDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotUnShipDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotUnTerminateDialog;
import com.google.common.collect.Lists;

public class LotActionFactory {
	
	private static final Logger logger = Logger.getLogger(LotActionFactory.class);

	public static final String ACTION_SCRAP = "LotActionScrap";
	public static final String ACTION_SCRAP_CANCEL = "LotActionScrapCancel";
	public static final String ACTION_SPLIT = "LotActionSplit";
	public static final String ACTION_MERGE = "LotActionMerge";
	public static final String ACTION_HOLD = "LotActionHold";
	public static final String ACTION_RELEASE = "LotActionRelease";
	public static final String ACTION_RUN_HOLD = "LotActionRunHold";
	public static final String ACTION_RUN_RELEASE = "LotActionRunRelease";
	public static final String ACTION_TERMINATE = "LotActionTerminate";
	public static final String ACTION_TERMINATE_CANCEL = "LotActionTerminateCancel";
	public static final String ACTION_BANK_IN = "LotActionBankIn";
	public static final String ACTION_BANK_OUT = "LotActionBankOut";
	public static final String ACTION_SHIP = "LotActionShip";
	public static final String ACTION_SHIP_CANCEL = "LotActionShipCancel";
	public static final String ACTION_LOT_INFO_MODIFY = "LotActionLotInfoModify";
	public static final String ACTION_LOT_PARAMETERS_MODIFY = "LotActionLotParametersModify";
	public static final String ACTION_LOT_COMMENTS_MODIFY = "LotActionLotCommentsModify";
	public static final String ACTION_LOT_WO_CHANGE = "LotActionLotWOChange";
	public static final String ACTION_LOT_PRODUCT_CHANGE = "LotActionLotProductChange";
	
	private static Map<Long, List<ADAuthority>> defaultMenuActionMap = new ConcurrentHashMap<Long, List<ADAuthority>>();
	private static Map<String, String> dialogActions = new ConcurrentHashMap<String, String>();
	
	static {
		dialogActions.put(ACTION_SCRAP, WIPLotScrapDialog.class.getName());
		dialogActions.put(ACTION_SCRAP_CANCEL, WIPLotUnScrapDialog.class.getName());
		dialogActions.put(ACTION_SPLIT, WIPLotSplitDialog.class.getName());
		dialogActions.put(ACTION_MERGE, WIPLotMergeDialog.class.getName());
		dialogActions.put(ACTION_HOLD, WIPLotHoldDialog.class.getName());
		dialogActions.put(ACTION_RELEASE, WIPLotReleaseDialog.class.getName());
		dialogActions.put(ACTION_TERMINATE, WIPLotTerminateDialog.class.getName());
		dialogActions.put(ACTION_TERMINATE_CANCEL, WIPLotUnTerminateDialog.class.getName());
		dialogActions.put(ACTION_BANK_IN, WIPLotBankInDialog.class.getName());
		dialogActions.put(ACTION_BANK_OUT, WIPLotBankOutDialog.class.getName());
		dialogActions.put(ACTION_SHIP, WIPLotShipDialog.class.getName());
		dialogActions.put(ACTION_SHIP_CANCEL, WIPLotUnShipDialog.class.getName());
		dialogActions.put(ACTION_LOT_INFO_MODIFY, WIPLotInfoModifyDialog.class.getName());
		dialogActions.put(ACTION_LOT_PARAMETERS_MODIFY, WIPLotParameterDialog.class.getName());
		dialogActions.put(ACTION_RUN_HOLD, WIPLotHoldDialog.class.getName());
		dialogActions.put(ACTION_RUN_RELEASE, WIPLotReleaseDialog.class.getName());
		
		initDefaultMenu();
	}
	
	public static void initDefaultMenu() {
		Set<String> defalutActions = dialogActions.keySet();
		
		try {
			List<ADAuthority> adAuthorities = Lists.newArrayList();
			//��ѯ������Ȩ�޵Ĳ˵���������ť��
			SecurityManager securityManager = Framework.getService(SecurityManager.class);
			Set<ADAuthority> allAuthoritys = securityManager.getUserAuthority(Env.getOrgRrn(), Env.getUserRrn(), ADAuthority.AUTHORITY_CATEGORY_MES);
			if (CollectionUtils.isNotEmpty(allAuthoritys)) {
				for (ADAuthority authority : allAuthoritys) {
					//ɸ����ť
					if (ADAuthority.AUTHORITY_TYPE_BUTTON.equals(authority.getAuthorityType())) {
						for (String defalutAction : defalutActions) {
							//ɸ��������defalutAction��β�İ�ť. ��ť�������Ṧ���� + ��ť��
							if (authority.getName().endsWith(defalutAction)) {
								adAuthorities.add(authority);
								break;
							}
						}
					}
				}
			}
			for (ADAuthority authority : adAuthorities) {			
				if (defaultMenuActionMap.containsKey(authority.getParentRrn())) {
					List<ADAuthority> defaultMenuActions = defaultMenuActionMap.get(authority.getParentRrn());
					defaultMenuActions.add(authority);
					defaultMenuActionMap.put(authority.getParentRrn(), defaultMenuActions);
				} else {
					List<ADAuthority> defaultMenuActions = Lists.newArrayList();
					defaultMenuActions.add(authority);	
					defaultMenuActionMap.put(authority.getParentRrn(), defaultMenuActions);
				}				
			}
		} catch (Exception e) {
			logger.error("initDefaultMenu() failed !", e);
		}
	}
	
	public static List<LotMenuAction> getLotMenuActionByAuthName(String authorityName) {
		List<LotMenuAction> defaultMenuActions = Lists.newArrayList();
		try {		
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADAuthority> parentAuthoritys = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + authorityName + "'", "");
			if (CollectionUtils.isNotEmpty(parentAuthoritys)) {
				List<ADAuthority> subAuthoritys = defaultMenuActionMap.get(parentAuthoritys.get(0).getObjectRrn());
				if (CollectionUtils.isNotEmpty(subAuthoritys)) {
					Collections.sort(subAuthoritys, new Comparator<ADAuthority>() {
						@Override
						public int compare(ADAuthority o1, ADAuthority o2) {
							Long seqNo1 = o1.getSeqNo() != null ? o1.getSeqNo() : 0;
							Long seqNo2 = o2.getSeqNo() != null ? o2.getSeqNo() : 0;
							return seqNo1.compareTo(seqNo2);
						}						
					});
					
					for (ADAuthority subAuthority : subAuthoritys) {
						LotMenuAction action = new LotMenuAction(subAuthority);
						defaultMenuActions.add(action);
					}
				}
			}		
		} catch (Exception e) {
			logger.error("getLotMenuActionByAuthName() failed !", e);
		}
		return defaultMenuActions;
	}
	
	public static LotActionDialog createDialog(String authority, IEventBroker eventBroker) throws Exception {
		authority = authority.substring(authority.lastIndexOf(".") + 1);
		String className = dialogActions.get(authority);
		Class<?> clazz = Class.forName(className);
		Class[] parameterTypes = new Class[] { String.class, String.class, IEventBroker.class };
		Object[] parameters = new Object[] { "", authority, eventBroker };
		Constructor constructor = clazz.getConstructor(parameterTypes);
		
		Object dialog = constructor.newInstance(parameters);
		if (!(dialog instanceof LotActionDialog)) {
			UI.showError("Only support LotActionDialog.");
		} 
		return (LotActionDialog)dialog;
	}

}
