package com.glory.mes.wip.lot.action;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.widgets.AuthorityAction;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.wip.custom.LotListComposite;

public class LotMenuAction extends AuthorityAction {

	private LotListComposite parent;
	private LotActionDialog dialog;
	
	public LotMenuAction(ADAuthority authority) {
		super("", IAction.AS_UNSPECIFIED);
		this.setAuthorityKey(authority.getName());

		String label = (String)I18nUtil.getI18nMessage(authority, "label");
		this.setText(label);
		ImageDescriptor image = ImageDescriptor.createFromImage(SWTResourceCache.getImage(authority.getImage()));
		this.setImageDescriptor(image);
		
		//����Ȩ��
		setEnabled(false);
		for (String authorityKey : Env.getAuthority()) {
			if (authorityKey.equalsIgnoreCase(authority.getName())) {
				setEnabled(true);
			}
		}
	}
	
	public LotMenuAction(String authority, String label, String image) {
		super(label, IAction.AS_UNSPECIFIED);
		this.setAuthorityKey(authority);

		ImageDescriptor imageDesc = ImageDescriptor.createFromImage(SWTResourceCache.getImage(image));
		this.setImageDescriptor(imageDesc);
	}
	
	@Override
	public void run()  {
		try {
			dialog = LotActionFactory.createDialog(this.getAuthorityKey(), parent.getEventBroker());
			dialog.setLotList(parent.getSelectedLots());
			dialog.initADFromName();
			setPreAuth(dialog);
			super.run();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void open() {
		try {
			if (dialog == null) {
				dialog = LotActionFactory.createDialog(this.getAuthorityKey(), parent.getEventBroker());
			} 
			dialog.setOperator(this.getOperator());
			if (Dialog.OK == dialog.open()) {
				if (parent != null) {
					parent.refresh();
					if (parent.getRefreshObject() != null) {
						parent.getRefreshObject().refresh();
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public LotListComposite getParent() {
		return parent;
	}

	public void setParent(LotListComposite parent) {
		this.parent = parent;
	}
	
}
