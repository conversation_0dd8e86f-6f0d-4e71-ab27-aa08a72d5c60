package com.glory.mes.wip.lot.action.dialog;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class WIPLotHoldDialog extends LotActionDialog {
	
	private static final String ADFORM_NAME = "WIPLotActionHoldDialog";
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String FIELD_LOTLIST = "lotList";
	private static final String FIELD_HOLDACTION = "holdAction";
	
	private ListTableManagerField lotListField;
	private EntityFormField holdActionEntityForm;
	protected List<Lot> lots;
	protected Event event;
	
	public WIPLotHoldDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	public WIPLotHoldDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, Event event) {
		super(ADFORM_NAME, authority, eventBroker);
		setLotList(Lists.newArrayList(lot));
		this.lots = Lists.newArrayList(lot);
        this.event = event;
        this.eventId = LotStateMachine.TRANS_HOLDLOT;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotListField = form.getFieldByControlId(FIELD_LOTLIST, ListTableManagerField.class);
		holdActionEntityForm = form.getFieldByControlId(FIELD_HOLDACTION, EntityFormField.class);
		holdActionEntityForm.setValue(new LotAction());
		holdActionEntityForm.refresh();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getLotList())) {
			lots = getLotList();
		}
		lotListField.getListTableManager().setInput(lots);
	}
	
	@Override
	protected void okPressed() {
		try {
			if (!holdActionEntityForm.validate()) {
				return;
			}
			LotAction action = (LotAction) holdActionEntityForm.getValue();
			
			List<Object> objects = (List<Object>) lotListField.getListTableManager().getInput();
			List<LotHold> lotHold = new ArrayList<LotHold>();
			if(CollectionUtils.isNotEmpty(objects)) {
				for(Object object : objects) {
					Lot lot = (Lot) object;
					LotHold multihold = new LotHold();
					multihold.setHoldCode(action.getActionCode());
					multihold.setHoldReason(action.getActionReason());
					multihold.setHoldOwner(action.getActionOperator());
					multihold.setHoldComment(action.getActionComment());
					multihold.setLotRrn(lot.getObjectRrn());
					lotHold.add(multihold);
				}
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
		    lotManager.holdLots(lotHold, Env.getSessionContext());
		    UI.showInfo(Message.getString("common.hold_successed"));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			Boolean result = checkLotStateModel(getLotList());
			if (result == null) {
				for (Lot lot : getLotList()) {							
					if (!(LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_RUN.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_TRACKOUT.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_DISP.equalsIgnoreCase(lot.getState()))) {
						UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
						return false;
					}			
				}
			} else {
				return result;
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
}
