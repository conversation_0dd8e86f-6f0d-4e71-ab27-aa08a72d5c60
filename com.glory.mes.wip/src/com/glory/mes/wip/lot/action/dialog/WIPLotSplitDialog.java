package com.glory.mes.wip.lot.action.dialog;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureMerge;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotSplitDialog extends LotActionDialog {

    private static final Logger logger = Logger.getLogger(WIPLotSplitDialog.class);

    private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
    public static final String BUTTON_NAME_REMOVE = "remove"; 
   	public static final String BUTTON_NAME_ADD = "add"; 
   	public static final String BUTTON_NAME_MERGE = "merge"; 
    
    private static final String FIELD_SPLIT_COMPONENT = "splitComponentList";
    private static final String FIELD_SPLIT_INFO = "splitInfo";
    private static final String FIELD_CHILD_LIST = "childList";
    private static final String FIELD_CHILD_INFO = "childInfo";
    private static final String FIELD_BYQTY = "byQty";
    private static final String FIELD_MAINQTY = "mainQty";
    private static final String FIELD_SUBQTY = "subQty";
    private static final String FIELD_CHILD_MAINQTY = "childMainQty";
    private static final String FIELD_CHILD_SUBQTY = "childSubQty";
    private static final String FIELD_STEP_NAME = "stepName";
    
    protected EntityFormField childInfoField, splitInfoField;
	protected ListTableManagerField childListField;
	protected ListTableManagerField splitComponentListTableManagerField;
	protected RefTableField stepNameField;
	protected TextField byQtyField, mainQtyField, subQtyField, childMainQtyField, childSubQtyField;
	protected List<Lot> lotQtys = new ArrayList<Lot>();
	protected BigDecimal splitedMainQty = BigDecimal.ZERO, splitedSubQty = BigDecimal.ZERO;
	protected BigDecimal inputMainQty, inputSubQty, addMainQty, addSubQty;
	private Consumer<WIPLotSplitDialog> closeAdaptor;
	
	public static final String AD_FROM_NAME_SPLITQTY = "WIPLotActionSplitQtyDialog";
	public static final String AD_FROM_NAME_SPLITCOMP = "WIPLotActionSplitCompDialog";
	
	protected Lot lot;
	protected Event event;
	
    
    public WIPLotSplitDialog(String adFormName, String authority, IEventBroker eventBroker){
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
		this.eventId = LotStateMachine.TRANS_SPLITLOT;
	}
    
    public WIPLotSplitDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, Event event) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
        this.event = event;
        this.setBlockOnOpen(false);
        this.eventId = LotStateMachine.TRANS_SPLITLOT;
        setLotList(Lists.newArrayList(lot));
	}
    
	@Override
	public void initADFromName() {
		if (CollectionUtils.isNotEmpty(getLotList())) {
			String adFormName = AD_FROM_NAME_SPLITQTY;
			if (ComponentUnit.class.getSimpleName().equals(getLotList().get(0).getSubUnitType())) {
				adFormName = AD_FROM_NAME_SPLITCOMP;
			}
			this.adFormName = adFormName;
		}
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {
        	initLot();//��ȡ���β�ѯ��ѡ��������Ϣ
        	ADManager adManager = Framework.getService(ADManager.class);
    		LotManager lotManager = Framework.getService(LotManager.class);
    		//�ж���qty���ͷ�������Component����
        	if(adFormName.equals(AD_FROM_NAME_SPLITQTY)) {//QTY����
        		childListField = form.getFieldByControlId(FIELD_CHILD_LIST, ListTableManagerField.class);
            	childInfoField = form.getFieldByControlId(FIELD_CHILD_INFO, EntityFormField.class);
            	splitInfoField = form.getFieldByControlId(FIELD_SPLIT_INFO, EntityFormField.class);
            	splitInfoField.setValue(new LotAction());
            	splitInfoField.refresh();
            	
            	byQtyField = childInfoField.getFieldByControlId(FIELD_BYQTY, TextField.class);	
            	mainQtyField = childInfoField.getFieldByControlId(FIELD_MAINQTY, TextField.class);	
            	subQtyField = childInfoField.getFieldByControlId(FIELD_SUBQTY, TextField.class);	
            	childMainQtyField = childInfoField.getFieldByControlId(FIELD_CHILD_MAINQTY, TextField.class);	
            	childSubQtyField = childInfoField.getFieldByControlId(FIELD_CHILD_SUBQTY, TextField.class);	
            	
            	subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_REMOVE), this::deleteAdapter);
            	subscribeAndExecute(eventBroker, byQtyField.getFullTopic(BUTTON_NAME_ADD), this::byQtyAddAdapter);   
            	subscribeAndExecute(eventBroker, subQtyField.getFullTopic(BUTTON_NAME_ADD), this::subQtyAddAdapter);
            	subscribeAndExecute(eventBroker, childSubQtyField.getFullTopic(BUTTON_NAME_ADD), this::childSubQtyAddAdapter);
        	}else {//Component����
        		splitComponentListTableManagerField = form.getFieldByControlId(FIELD_SPLIT_COMPONENT, ListTableManagerField.class);
            	lot = lotManager.getLotWithComponent(lot.getObjectRrn());
            	if(lot.getSubProcessUnit() != null) {
            		splitComponentListTableManagerField.getListTableManager().setInput(lot.getSubProcessUnit());
            	}
            	
            	splitInfoField = form.getFieldByControlId(FIELD_SPLIT_INFO, EntityFormField.class);
            	splitInfoField.setValue(new LotAction());
            	splitInfoField.refresh();
			}
        	
        	stepNameField = splitInfoField.getFieldByControlId(FIELD_STEP_NAME, RefTableField.class);	//��ȡδ������
        	if (lot.getObjectRrn() != null) {
        		//д��δ������������λ������Ϣ
        		List<StepState> stepStates = new ArrayList<StepState>();
 			    StepState stepState = new StepState();
 			    stepStates.add(stepState);
 			    stepStates.addAll(lotManager.getFutureSteps(lot));
 			    stepNameField.setInput(stepStates);
        		
				// ȡ��δ������
			    List<FutureMerge> futureMerges = adManager.getEntityList(Env.getOrgRrn(), FutureMerge.class, 1, 
			    		"parentLotRrn = '" + lot.getObjectRrn() + "'", "");
			    if (futureMerges != null && futureMerges.size() > 0) {
			    	FutureMerge futureMerge = futureMerges.get(0);
			    	stepNameField.setValue(futureMerge.getStepName());
			    } else {
			    	stepNameField.setValue(null);
			    }
			}
        	stepNameField.refresh();
        } catch (Exception e) {
            logger.error("WIPLotSplitDialog : Init tablelist", e);
        }
	}
	
	@Override
	protected void okPressed() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			if (!splitInfoField.validate()) {
				return;
			}
			if (adFormName.equals(AD_FROM_NAME_SPLITQTY)) {//QTY�����ֳ����������Ϣ
				if(CollectionUtils.isNotEmpty(lotQtys)) {
					List<Lot> children = new ArrayList<Lot>();
					for (Lot lq : lotQtys) {
						Lot lt = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
						Lot child = (Lot) lt.clone();
						child.setLotId(null);
						child.setMainQty(lq.getMainQty());
						child.setSubQty(lq.getSubQty());
						child.setOperator1(Env.getUserName());
						child.setLotId(null);
						children.add(child);
					}
					lot.setOperator1(getOperator());

					List<StepState> stepStates = stepNameField.getInput();
					StepState stepState = null;
					if (stepStates.size() > 0) {
						stepState = (StepState) stepNameField.getData();
					}
					LotAction lotAction = (LotAction) splitInfoField.getValue();
					lotAction = new LotAction();
					lotAction.setFutureStepState(stepState);

					lotManager.splitLot(lot, children, lotAction, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.split_success"));
				}else {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				}
			} else {//Component��������ѡ��Ҫ�ֳ���Ƭд��
				List<Object> selectObjs = splitComponentListTableManagerField.getListTableManager().getCheckedObject();
                if(CollectionUtils.isNotEmpty(selectObjs)) {
                	List<StepState> stepStates = stepNameField.getInput();
    				StepState stepState = null;
    				if (stepStates.size() > 0) {
    					stepState = (StepState) stepNameField.getData();
    				}
    				LotAction lotAction = (LotAction) splitInfoField.getValue();
    				lotAction = new LotAction();
    				lotAction.setFutureStepState(stepState);

    				List<ComponentUnit> splitComponents = new ArrayList<ComponentUnit>();
    				for (Object obj : selectObjs) {
    					ComponentUnit comp = (ComponentUnit) obj;
    					splitComponents.add(comp);
    				}
    				if (splitComponents.size() == 0) {
    					UI.showWarning(String.format(Message.getString("common.please_select"),
    							ComponentUnit.class.getSimpleName()));
    					return;
    				}

    				lot = lotManager.getLotByLotId(Env.getOrgRrn(), lot.getLotId());
    				lot.setOperator1(getOperator());
    				// У��Ƭ�Ƿ��ڸ�ĸ����
    				List<ProcessUnit> processUnits = lotManager.getLotWithComponent(lot.getObjectRrn()).getSubProcessUnit();
    				List<ComponentUnit> componentUnits = new ArrayList<>();
    				componentUnits = processUnits.stream().map(processUnit -> ((ComponentUnit) processUnit))
    						.collect(Collectors.toList());
    				for (ComponentUnit sComponentUnit : splitComponents) {
    					if (!componentUnits.contains(sComponentUnit)) {
    						UI.showInfo(sComponentUnit.getComponentId() + Message.getString("wip.component_not_in_lot"));
    					}
    				}

    				lotManager.splitLotComponent(lot, splitComponents, lotAction, Env.getSessionContext());
    				UI.showInfo(Message.getString("wip.split_success"));
                }else {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				}
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	// �Ƴ������б�������ť
	protected void deleteAdapter(Object object) {
		try {
			List<Object> selectObjs = childListField.getListTableManager().getCheckedObject();
			childListField.getListTableManager().getInput().removeAll(selectObjs);
			List<Lot> lots = new ArrayList<Lot>();
			if (selectObjs != null && selectObjs.size() > 0) {
				for (Object obj : selectObjs) {
					lots.add((Lot) obj);
				}
			}
			lotQtys.removeAll(lots);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// �������ְ�ť
	protected void byQtyAddAdapter(Object object) {
		try {
			if (!"".equals(byQtyField.getText().trim())) {
				int splitNum = Integer.parseInt(byQtyField.getText());
				if (!valid(splitNum)) {
					return;
				}
				BigDecimal mCount = BigDecimal.ZERO, sCount = BigDecimal.ZERO;
				BigDecimal mResidue = BigDecimal.ZERO, sResidue = BigDecimal.ZERO;
				if (lot.getMainQty() != null) {
					BigDecimal mainQty = lot.getMainQty();
					mCount = computeCount(mainQty, splitNum, 1);
					mResidue = getResidue(mainQty, BigDecimal.valueOf(splitNum));
					if (mCount.compareTo(BigDecimal.ZERO) == 0 && mainQty.compareTo(BigDecimal.valueOf(splitNum)) < 0) {
						mCount = BigDecimal.ONE;
					}
				}
				if (lot.getSubQty() != null && lot.getSubQty().compareTo(BigDecimal.ZERO) != 0) {
					BigDecimal subQty = lot.getSubQty();
					sCount = computeCount(subQty, splitNum, 2);
					sResidue = getResidue(subQty, sCount);
					if (sCount.compareTo(BigDecimal.ZERO) == 0 && subQty.compareTo(BigDecimal.valueOf(splitNum)) < 0) {
						sCount = BigDecimal.ONE;
					}
				}
				if (lotQtys == null || lotQtys.size() != 0) {
					lotQtys = new ArrayList<Lot>();
				}
				int totalMainQty = 0, totalSubQty = 0;
				for (int i = 0; i < splitNum; i++) {
					Lot lq = new Lot();
					if (mCount.compareTo(BigDecimal.ONE) == 0) {
						if (totalMainQty < lot.getMainQty().doubleValue()) {
							lq.setMainQty(BigDecimal.ONE);
							totalMainQty++;
						}
					} else if (mCount.compareTo(BigDecimal.ZERO) != 0) {
						lq.setMainQty(mCount);
					}

					if (sCount.compareTo(BigDecimal.ONE) == 0) {
						if (totalSubQty < lot.getSubQty().doubleValue()) {
							lq.setSubQty(BigDecimal.ONE);
							totalSubQty++;
						}
					} else if (sCount.compareTo(BigDecimal.ZERO) != 0) {
						lq.setSubQty(sCount);
					}
					lotQtys.add(lq);
				}
				if (mResidue.compareTo(BigDecimal.ZERO) > 0 || sResidue.compareTo(BigDecimal.ZERO) > 0) {
					Lot lq = null;
					if (lotQtys.size() > 0) {
						lq = lotQtys.get(lotQtys.size() - 1);
					} else {
						lq = new Lot();
						lq.setMainQty(BigDecimal.ZERO);
						lq.setSubQty(BigDecimal.ZERO);
						lotQtys.add(lq);
					}
					if (lot.getMainQty() != null && mResidue.compareTo(BigDecimal.ZERO) > 0) {
						if (totalMainQty < lot.getMainQty().doubleValue()) {
							lq.setMainQty(lq.getMainQty().add(mResidue));
						}
					}
					if (lot.getSubQty() != null && sResidue.compareTo(BigDecimal.ZERO) > 0) {
						if (totalSubQty < lot.getSubQty().doubleValue()) {
							lq.setSubQty(lq.getSubQty().add(sResidue));
						}
					}
				}
				childListField.getListTableManager().setInput(lotQtys);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// ����������ť
	protected void subQtyAddAdapter(Object object) {
		try {
			if (isLessThanLotQtys()) {
				Lot lotQty = new Lot();
				if (lot.getMainQty() != null && inputMainQty.compareTo(BigDecimal.ZERO) > 0) {
					lotQty.setMainQty(inputMainQty);
				}
				if (lot.getSubQty() != null && inputSubQty.compareTo(BigDecimal.ZERO) > 0) {
					lotQty.setSubQty(inputSubQty);
				}
				if (lotQty.getMainQty() == null && lotQty.getSubQty() == null)
					return;
				if (lotQtys != null) {
					lotQtys.add(lotQty);
					childListField.getListTableManager().setInput(lotQtys);
				}
				mainQtyField.setText("");
				subQtyField.setText("");
			}
			inputMainQty = inputSubQty = BigDecimal.ZERO;
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// ����������ť
	protected void childSubQtyAddAdapter(Object object) {
		try {
			if (validateQty()) {
				int mNum = 0, sNum = 0;
				BigDecimal byMainQtyCount = BigDecimal.ZERO, bySubQtyCount = BigDecimal.ZERO;
				BigDecimal mResidue = BigDecimal.ZERO, sResidue = BigDecimal.ZERO;
				if (childMainQtyField.getText() != null && !"".equals(childMainQtyField.getText().trim())) {
					if (lot.getMainQty() != null) {
						BigDecimal mainQty = lot.getMainQty();
						byMainQtyCount = new BigDecimal(childMainQtyField.getText());
						mNum = computeCount(mainQty, byMainQtyCount.intValue(), 1).intValue();
						mResidue = getResidue(mainQty, byMainQtyCount);
					}
				}
				if (childSubQtyField.getText() != null && !"".equals(childSubQtyField.getText().trim())) {
					bySubQtyCount = new BigDecimal(childSubQtyField.getText());
					if (lot.getSubQty() != null) {
						BigDecimal subQty = lot.getSubQty();
						sNum = computeCount(subQty, bySubQtyCount.intValue(), 2).intValue();
						sResidue = getResidue(subQty, bySubQtyCount);
					}
				}
				splitLot(byMainQtyCount, bySubQtyCount, mNum, sNum, mResidue, sResidue);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// ����������Ч��
	private boolean valid(int splitNum) {
		if (lot.getMainQty() != null && lot.getSubQty() == null) {
			if (lot.getMainQty().doubleValue() < splitNum)
				return false;
		} else if (lot.getMainQty() == null && lot.getSubQty() != null) {
			if (lot.getSubQty().doubleValue() < splitNum)
				return false;
		} else if (lot.getMainQty() != null && lot.getSubQty() != null) {
			if (lot.getMainQty().doubleValue() < splitNum && lot.getSubQty().doubleValue() < splitNum)
				return false;
		}
		return true;
	}

	private BigDecimal computeCount(BigDecimal qty, int num, int index) {
		BigDecimal count = qty.divide(BigDecimal.valueOf(num), 3);
		switch (index) {
		case 1:
			splitedMainQty = count;
			break;
		case 2:
			splitedSubQty = count;
			break;
		}
		return count;
	}

	// ����dividend ���� divisor������
	private BigDecimal getResidue(BigDecimal dividend, BigDecimal divisor) {
		return dividend.remainder(divisor);
	}

	public boolean isLessThanLotQtys() {
		computeAddQty();
		inputMainQty = inputSubQty = BigDecimal.ZERO;
		if (!"".equals(mainQtyField.getText().trim())) {
			inputMainQty = new BigDecimal(mainQtyField.getText());
		}
		if (!"".equals(subQtyField.getText().trim())) {
			inputSubQty = new BigDecimal(subQtyField.getText());
		}
		if (inputMainQty.compareTo(BigDecimal.ZERO) == 0 && inputSubQty.compareTo(BigDecimal.ZERO) == 0) {
			return false;
		}
		if (lot.getMainQty() != null) {
			if (lot.getMainQty().compareTo(inputMainQty.add(addMainQty)) < 0) {
				UI.showError(Message.getString("wip.split_morethan_main"));
				mainQtyField.setText("");
				return false;
			}
		}
		if (lot.getSubQty() != null) {
			if (lot.getSubQty().compareTo(inputSubQty.add(addSubQty)) < 0) {
				UI.showError(Message.getString("wip.split_morethan_sub"));
				subQtyField.setText("");
				return false;
			}
		}
		return true;
	}

	public void computeAddQty() {
		addMainQty = BigDecimal.ZERO;
		addSubQty = BigDecimal.ZERO;
		if (lotQtys != null) {
			for (Lot lq : lotQtys) {
				if (lq.getMainQty() != null) {
					addMainQty = addMainQty.add(lq.getMainQty());
				}
				if (lq.getSubQty() != null) {
					addSubQty = addSubQty.add(lq.getSubQty());
				}
			}
		}
	}

	// ������������Ч��
	protected boolean validateQty() {
		if (childMainQtyField.getText() != null && !"".equals(childMainQtyField.getText().trim())) {
			if (lot.getMainQty() == null
					|| Integer.parseInt(childMainQtyField.getText()) > lot.getMainQty().intValue()) {
				UI.showError(Message.getString("wip.split_morethan_main"));
				childMainQtyField.setText("");
				return false;
			}
		}
		if (childSubQtyField.getText() != null && !"".equals(childSubQtyField.getText().trim())) {
			if (lot.getSubQty() == null || Integer.parseInt(childSubQtyField.getText()) > lot.getSubQty().intValue()) {
				UI.showError(Message.getString("wip.split_morethan_sub"));
				childSubQtyField.setText("");
				return false;
			}
		}
		return true;
	}

	// ���������ֳ�����
	private void splitLot(BigDecimal byMainQtyCount, BigDecimal bySubQtyCount, int mNum, int sNum, BigDecimal mResidue,
			BigDecimal sResidue) {
		if (byMainQtyCount.compareTo(BigDecimal.ZERO) != 0 && bySubQtyCount.compareTo(BigDecimal.ZERO) == 0) {
			split(byMainQtyCount, mNum, mResidue, 1);
			childListField.getListTableManager().setInput(lotQtys);
		} else if (byMainQtyCount.compareTo(BigDecimal.ZERO) == 0 && bySubQtyCount.compareTo(BigDecimal.ZERO) != 0) {
			split(bySubQtyCount, sNum, sResidue, 2);
			childListField.getListTableManager().setInput(lotQtys);
		} else if (byMainQtyCount.compareTo(BigDecimal.ZERO) != 0 && bySubQtyCount.compareTo(BigDecimal.ZERO) != 0) {
			if (mNum <= sNum && mResidue.compareTo(BigDecimal.ZERO) == 0) {
				addLots(byMainQtyCount, bySubQtyCount, mNum - 1, true);
				Lot lq = new Lot();
				lq.setMainQty(byMainQtyCount);
				BigDecimal remnant = lot.getSubQty().subtract(bySubQtyCount.multiply(BigDecimal.valueOf((mNum - 1))));
				lq.setSubQty(remnant);
				lotQtys.add(lq);
			} else if (mNum <= sNum && mResidue.compareTo(BigDecimal.ZERO) != 0) {
				addLots(byMainQtyCount, bySubQtyCount, mNum, true);
				Lot lq = new Lot();
				lq.setMainQty(mResidue);
				BigDecimal remnant = lot.getSubQty().subtract(bySubQtyCount.multiply(BigDecimal.valueOf(mNum)));
				lq.setSubQty(remnant);
				lotQtys.add(lq);
			} else if (mNum > sNum && sResidue.compareTo(BigDecimal.ZERO) == 0) {
				int dv = mNum - sNum;
				addLots(byMainQtyCount, bySubQtyCount, sNum, true);
				addLots(byMainQtyCount, BigDecimal.ZERO, dv, false);
				if (mResidue.compareTo(BigDecimal.ZERO) != 0) {
					Lot lq = new Lot();
					lq.setMainQty(mResidue);
					lotQtys.add(lq);
				}
			} else if (mNum > sNum && sResidue.compareTo(BigDecimal.ZERO) != 0) {
				int dv = mNum - sNum;
				addLots(byMainQtyCount, bySubQtyCount, sNum, true);
				{
					Lot lq = new Lot();
					lq.setMainQty(byMainQtyCount);
					lq.setSubQty(sResidue);
					lotQtys.add(lq);
				}
				addLots(byMainQtyCount, BigDecimal.ZERO, dv - 1, false);
				if (mResidue.compareTo(BigDecimal.ZERO) != 0) {
					Lot lq = new Lot();
					lq.setMainQty(mResidue);
					lotQtys.add(lq);
				}
			}
			childListField.getListTableManager().setInput(lotQtys);
		}
	}

	// ����num��Lot, �������residue != 0, ��residue���뵽���һ��Lot��, Ȼ�󽫲�����Lot����Table��
	private void split(BigDecimal byQtyCount, int num, BigDecimal residue, int index) {
		if (lotQtys == null || lotQtys.size() != 0) {
			lotQtys = new ArrayList<Lot>();
		}
		for (int i = 0; i < num; i++) {
			Lot lq = new Lot();
			switch (index) {
			case 1:
				lq.setMainQty(byQtyCount);
				break;
			case 2:
				lq.setSubQty(byQtyCount);
			}
			lotQtys.add(lq);
		}
		if (residue.compareTo(BigDecimal.ZERO) > 0) {
			Lot lq = new Lot();
			switch (index) {
			case 1:
				lq.setMainQty(residue);
				break;
			case 2:
				lq.setSubQty(residue);
			}
			lotQtys.add(lq);
		}
		childListField.getListTableManager().setInput(lotQtys);
	}

	// ����num��Lot, Ȼ�󽫲�����Lot����Table��
	private void addLots(BigDecimal byMainQtyCount, BigDecimal bySubQtyCount, int num, boolean isClear) {
		if (isClear) {
			if (lotQtys == null || lotQtys.size() != 0) {
				lotQtys = new ArrayList<Lot>();
			}
		}
		for (int i = 0; i < num; i++) {
			Lot lq = new Lot();
			if (byMainQtyCount.compareTo(BigDecimal.ZERO) != 0) {
				lq.setMainQty(byMainQtyCount);
			}
			if (bySubQtyCount.compareTo(BigDecimal.ZERO) != 0) {
				lq.setSubQty(bySubQtyCount);
			}
			lotQtys.add(lq);
		}
	}
	
	@Override
	public void initLot() {
		if(CollectionUtils.isNotEmpty(getLotList())) {
			lot = getLotList().get(0);
		}
	}

	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			Boolean result = checkLotStateModel(getLotList());
			if (result == null) {
				for(Lot lot : getLotList()) {
					if (!(LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState()) 
							|| LotStateMachine.STATE_RUN.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState()))) { 
						UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
						return false;
					}
				}
			} else {
				return result;
			}
		} else {
			return flag;
		} 
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}
	
	public Consumer<WIPLotSplitDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<WIPLotSplitDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}
}
