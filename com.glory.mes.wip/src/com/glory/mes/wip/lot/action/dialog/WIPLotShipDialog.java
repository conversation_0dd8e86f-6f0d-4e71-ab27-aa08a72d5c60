package com.glory.mes.wip.lot.action.dialog;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class WIPLotShipDialog extends LotActionDialog{
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String ADFORM_NAME = "WIPLotActionShipDialog";
	
	private static final String FIELD_LOTSHIPTABLE = "lotShipTable";
	private static final String FIELD_LOTSHIPCODE = "lotShipCode";
	
	private static final String FIELD_ACTIONCODE = "actionCode";
	private static final String FIELD_COMMENT = "actionComment";

	private ListTableManagerField lotShipListTable;
	private EntityFormField lotShipWarehouseEntityForm;
	private RefTableField actionCodeField;
	private TextField commentField;
	
	private ListTableManager lotShipTableManager;

	public WIPLotShipDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotShipListTable = form.getFieldByControlId(FIELD_LOTSHIPTABLE, ListTableManagerField.class);
		lotShipWarehouseEntityForm = form.getFieldByControlId(FIELD_LOTSHIPCODE, EntityFormField.class);
		actionCodeField = lotShipWarehouseEntityForm.getFieldByControlId(FIELD_ACTIONCODE, RefTableField.class);
		commentField = lotShipWarehouseEntityForm.getFieldByControlId(FIELD_COMMENT, TextField.class);	
		
		lotShipTableManager = lotShipListTable.getListTableManager();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		List<Lot> lots = getLotList();
		lotShipTableManager.setInput(lots);
	}
	
	@Override
	protected void okPressed() {
		try {
			if (!lotShipWarehouseEntityForm.validate()) {
				UI.showInfo(Message.getString("wip.select_ship_code"));
				return;
			}
			LotAction action = new LotAction();
			action.setActionCode(actionCodeField.getText());
			action.setActionComment(commentField.getText());
			
			List<Lot> batchObject = (List<Lot>) lotShipTableManager.getInput();
			List<Lot> batchLots = new ArrayList<Lot>();

			if (batchObject.size() > 0) {
				LotManager lotManager = Framework.getService(LotManager.class);
				for (int i = 0; i < batchObject.size(); i++) {
					Lot lot = batchObject.get(i);
					lot.setOperator1(getOperator());
					batchLots.add(lot);
				}
				lotManager.shipLot(batchLots, action, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.ship_success"));
			} else {
				UI.showError(Message.getString("wip.ship_error_nolot_toship"));
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			for(Lot lot : getLotList()) {
				if (!LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState())) {
					UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
					return false;
				}
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
