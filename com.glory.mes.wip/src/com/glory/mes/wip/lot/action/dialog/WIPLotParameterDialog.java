package com.glory.mes.wip.lot.action.dialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotParameterDialog extends LotActionDialog{

    private static final Logger logger = Logger.getLogger(WIPLotParameterDialog.class);
    
    private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
    private static final String AD_FROM = "WIPLotActionParameterDialog";

    private static final String FIELD_PARMETER   = "parameterList";
    private static final String FIELD_PARAMETER_INFO = "parameterInfo";
    protected EntityFormField lotChangeParameterField;
    protected ListTableManagerField parameterListTableManagerField;
    protected List<Lot> lots;
	protected Event event;
	protected Map<String, Object> paramMap;
	private Consumer<WIPLotParameterDialog> closeAdaptor;
	
	public WIPLotParameterDialog(String adFormName, String authority, IEventBroker eventBroker){
		super(AD_FROM, authority, eventBroker);
	}
    
    public WIPLotParameterDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, Event event){
		super(adFormName, authority, eventBroker);
		setLotList(Lists.newArrayList(lot));
        this.lots = Lists.newArrayList(lot);
        this.event = event;
        this.setBlockOnOpen(false);
	}
    
	@Override
	public void initLot() {
		try {
			if(CollectionUtils.isNotEmpty(getLotList())) {
				lots = getLotList();
			}
			PrdManager prdManager = Framework.getService(PrdManager.class);
			paramMap = prdManager.getCurrentParameter(lots.get(0).getProcessInstanceRrn());
			
			List<Lot> lotParameters = new ArrayList<Lot>();
			if(paramMap !=null && paramMap.size() > 0) {
				for (String key : paramMap.keySet()) {
					Lot lotParameter = (Lot)lots.get(0).clone();
					lotParameter.setAttribute1(key);
					lotParameter.setAttribute3(String.valueOf(paramMap.get(key)));
					lotParameters.add(lotParameter);
				}
			}
			
			parameterListTableManagerField.getListTableManager().setInput(lotParameters);
		} catch (Exception e) {
            logger.error("WIPLotParameterDialog : Init tablelist", e);
        }
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
		parameterListTableManagerField = form.getFieldByControlId(FIELD_PARMETER, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, parameterListTableManagerField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChangedAdaptor);
		
		lotChangeParameterField = form.getFieldByControlId(FIELD_PARAMETER_INFO, EntityFormField.class);
		lotChangeParameterField.setValue(new Lot());
		lotChangeParameterField.refresh();
		
		initLot();
	}
	
	
	private void selectionChangedAdaptor(Object obj) {
		Event event = (Event) obj; 
		Lot lotParameter = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
		lotChangeParameterField.setValue(lotParameter);
		lotChangeParameterField.refresh();
	}
	
	@Override
	protected void okPressed() {	
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
            if (!lotChangeParameterField.validate()) {
                return;
            }
            Map<Long, Object> oldValues = new HashMap<Long, Object>();
            boolean flag = false;
            Lot changeLot = (Lot)lotChangeParameterField.getValue();
 			if (paramMap.containsKey(changeLot.getAttribute1())) {
 				flag = false;
 				oldValues.put(changeLot.getObjectRrn(), paramMap.get(changeLot.getAttribute1()));
 			} else {
 				flag = true;
 			}
 			lots.get(0).setOperator1(getOperator());
 			lotManager.changeParameter(Lists.newArrayList(lots.get(0)), changeLot.getAttribute1().toString(), changeLot.getAttribute3(), oldValues, flag, Env.getSessionContext());
            UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<WIPLotParameterDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<WIPLotParameterDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}

}
