package com.glory.mes.wip.lot.action.dialog;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.HistoryUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotScrapDialog extends LotActionDialog {

	private static final Logger logger = Logger.getLogger(WIPLotScrapDialog.class);
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	public static final String AD_FROM_NAME_SCRAPQTY = "WIPLotActionScrapQtyDialog";
	public static final String AD_FROM_NAME_SCRAPCOMP = "WIPLotActionScrapComponentDialog";
	
	public static final String BUTTON_NAME_REMOVE = "remove";
	public static final String BUTTON_NAME_ADD = "add";
	public static final String BUTTON_NAME_MERGE = "merge";

	private static final String FIELD_SCRAP_COMPONENT = "componentList";
	private static final String FIELD_SCRAP_ACTION = "scrapAction";
	private static final String FIELD_LOT_SCRAP = "lotScrapList";
	private static final String FIELD_SCRAP_CODE = "actionCode";
	private static final String FIELD_MAINQTY = "mainQty";
	private static final String FIELD_SUBQTY = "subQty";
	private static final String FIELD_SCRAP_COMMENT = "actionComment";

	protected EntityFormField scrapCompActionField, scrapActionField;
	protected ListTableManagerField lotScrapListField, componentListField;
	protected RefTableField scrapCodeField;
	protected TextField mainQtyField, subQtyField, scrapCommentField;
	protected CheckBoxTableViewerManager checkBoxTableViewerManager;
	private Consumer<WIPLotScrapDialog> closeAdaptor;

	protected Lot lot;
	protected List<LotScrap> lotScraps = new ArrayList<LotScrap>();
	protected String scrapCode;
	protected BigDecimal inputMainQty, inputSubQty, addMainQty, addSubQty, splitedMainQty, splitedSubQty;
	public static final String DEFAULT_SCRAP_TABLE = "ScrapCode";

	public WIPLotScrapDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
		this.eventId = LotStateMachine.TRANS_SCRAPLOT;
	}

	@Override
	public void initADFromName() {
		if (CollectionUtils.isNotEmpty(getLotList())) {
			String adFormName = AD_FROM_NAME_SCRAPQTY;
			if (ComponentUnit.class.getSimpleName().equals(getLotList().get(0).getSubUnitType())) {
				adFormName = AD_FROM_NAME_SCRAPCOMP;
			}
			this.adFormName = adFormName;
		}
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		try {
			initLot();// ��ȡ���β�ѯ��ѡ��������Ϣ
			LotManager lotManager = Framework.getService(LotManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			// �ж���qty���ͻ���Component
			if (adFormName.equals(AD_FROM_NAME_SCRAPQTY)) {//QTY
				lotScrapListField = form.getFieldByControlId(FIELD_LOT_SCRAP, ListTableManagerField.class);

				scrapActionField = form.getFieldByControlId(FIELD_SCRAP_ACTION, EntityFormField.class);
				scrapActionField.setValue(new LotAction());
				scrapActionField.refresh();
				mainQtyField = scrapActionField.getFieldByControlId(FIELD_MAINQTY, TextField.class);
				subQtyField = scrapActionField.getFieldByControlId(FIELD_SUBQTY, TextField.class);
				scrapCommentField = scrapActionField.getFieldByControlId(FIELD_SCRAP_COMMENT, TextField.class);

				subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_REMOVE), this::deleteAdapter);
				subscribeAndExecute(eventBroker, subQtyField.getFullTopic(BUTTON_NAME_ADD), this::addAdapter);
			} else {//Component
				componentListField = form.getFieldByControlId(FIELD_SCRAP_COMPONENT, ListTableManagerField.class);
				ListTableManager listTableManager = componentListField.getListTableManager();
				checkBoxTableViewerManager = (CheckBoxTableViewerManager) listTableManager.getTableManager();
				checkBoxTableViewerManager.addICheckChangedListener(checkChangedListener);
				lot = lotManager.getLotWithComponent(lot.getObjectRrn());
				List<ProcessUnit> subProcessUnit = lot.getSubProcessUnit();
				if (CollectionUtils.isNotEmpty(subProcessUnit)) {
					for (int i = subProcessUnit.size() - 1; i >= 0; i--) {
						ComponentUnit componentUnit = (ComponentUnit) subProcessUnit.get(i);
						if (LotStateMachine.STATE_SCRAP.equals(componentUnit.getState())) {
							subProcessUnit.remove(i);
						}
					}
					listTableManager.setInput(subProcessUnit);
				}

				scrapActionField = form.getFieldByControlId(FIELD_SCRAP_ACTION, EntityFormField.class);
				scrapActionField.setValue(new LotAction());
				scrapActionField.refresh();
			}

			scrapCodeField = scrapActionField.getFieldByControlId(FIELD_SCRAP_CODE, RefTableField.class);
			// ���ݹ�����ñ�����
			 
			scrapCode = getScrapCode();

			List<ADURefList> adURefLists = adManager.getADURefList(Env.getOrgRrn(), scrapCode);
			scrapCodeField.setInput(adURefLists);
			scrapCodeField.refresh();
		} catch (Exception e) {
			logger.error("WIPLotScrapDialog : Init tablelist", e);
		}
	}
	
	@Override
	protected void okPressed() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			if (adFormName.equals(AD_FROM_NAME_SCRAPQTY)) {// QTY�����ֳ����������Ϣ
				if (CollectionUtils.isNotEmpty(lotScraps)) {
					lot.setOperator1(Env.getSessionContext().getTransUser());
					Map<String, List<ProcessUnit>> lotActionsMap = new HashMap<String, List<ProcessUnit>>();
					for (LotScrap unit : lotScraps) {
						QtyUnit qtyUnit = new QtyUnit();
						qtyUnit.setActionCode(unit.getActionCode());
						qtyUnit.setMainQty(unit.getMainQty());
						qtyUnit.setSubQty(unit.getSubQty());
						if (lotActionsMap.containsKey(qtyUnit.getActionCode())) {
							List<ProcessUnit> units = lotActionsMap.get(qtyUnit.getActionCode());
							units.add(qtyUnit);
							lotActionsMap.put(qtyUnit.getActionCode(), units);
						} else {
							List<ProcessUnit> units = new ArrayList<ProcessUnit>();
							units.add(qtyUnit);
							lotActionsMap.put(qtyUnit.getActionCode(), units);
						}
					}

					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					for (String key : lotActionsMap.keySet()) {
						LotAction lotAction = (LotAction) scrapActionField.getValue();
						lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
						lotAction.setLotRrn(lot.getObjectRrn());
						lotAction.setActionCode(key);
						lotAction.setActionUnits(lotActionsMap.get(key));
						if (StringUtil.isEmpty(lotAction.getActionComment())) {
							UI.showInfo(Message.getString("wip.abort_comments_null"));
							return;
						}
						scrapLotActions.add(lotAction);
					}
					LotAction lotAction = (LotAction) scrapActionField.getValue();
					if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
						return;
					}
					lotManager.scrapLot(lot, scrapLotActions, lotAction, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.scraplot_success"));
				} else {
					UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
					return;
				}
			} else {// Component����
				if (scrapActionField.validate()) {
					List<ComponentUnit> scrapUnits = new ArrayList<ComponentUnit>();
					List<Object> elements = componentListField.getListTableManager().getCheckedObject();
					for (int i = 0; i < elements.size(); i++) {
						ComponentUnit comp = (ComponentUnit) elements.get(i);
						scrapUnits.add(comp);
					}
					if (scrapUnits.size() == 0) {
						UI.showWarning(String.format(Message.getString("common.please_select"), ComponentUnit.class.getSimpleName()));
						return;
					}
					List<LotAction> scrapLotActions = new ArrayList<LotAction>();
					BigDecimal scrapQty = BigDecimal.ZERO;
					Map<String, LotAction> componentUnitActionMap = Maps.newHashMap();
					for (ComponentUnit componentUnit : scrapUnits) {
						LotAction lotAction = new LotAction();
						lotAction.setActionCode(((LotAction) scrapActionField.getValue()).getActionCode());
						lotAction.setActionReason(((LotAction) scrapActionField.getValue()).getActionReason());
						lotAction.setActionComment(((LotAction) scrapActionField.getValue()).getActionComment());
						lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
						lotAction.setLotRrn(lot.getObjectRrn());
						lotAction.setActionUnits(Lists.newArrayList(componentUnit));
						String reasonSector = DBUtil.toString(lotAction.getActionReason());
						String comment = (lotAction.getActionComment() + HistoryUtil.buildHisComment("Reason sector", reasonSector));
						lotAction.setActionComment(comment);
						if (StringUtil.isEmpty(lotAction.getActionComment())) {
							UI.showError(Message.getString("wip.abort_comments_null"));
							return;
						}
						if (StringUtil.isEmpty(lotAction.getActionReason())) {
							UI.showError(Message.getString("wip.abort_dept_null"));
							return;
						}
						scrapQty = scrapQty.add(componentUnit.getMainQty());
						scrapLotActions.add(lotAction);
						componentUnitActionMap.put(componentUnit.getComponentId(), lotAction);
					}
					LotAction lotAction = (LotAction) scrapActionField.getValue();
					if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
						return;
					}
					lotManager.scrapLot(lot, scrapLotActions, lotAction, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.scraplot_success"));
				}
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * Ƭ���Ϲ�ѡ�¼�
	 */
	ICheckChangedListener checkChangedListener = new ICheckChangedListener() {
		@Override
		public void checkChanged(List<Object> eventObjects, boolean checked) {
			try {
				if (CollectionUtils.isEmpty(eventObjects)) {
					return;
				}
				if (scrapActionField.validate()) {
					if (checked) {
						for (Object object : eventObjects) {
							ComponentUnit compositeUnit = (ComponentUnit) object;
							compositeUnit.setActionCode(((LotAction) scrapActionField.getValue()).getActionCode());
						}
					}
				} else {
					for (Object object : eventObjects) {
						checkBoxTableViewerManager.unCheckObject(object);
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	};

	/**
	 * ��ñ�����
	 */
	protected String getScrapCode() {
		try {
			if (lot != null && lot.getObjectRrn() != null && lot.getStepRrn() != null) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Step step = new Step();
				step.setObjectRrn(lot.getStepRrn());
				step = (Step) prdManager.getSimpleProcessDefinition(step);
				scrapCode = step.getScrapCodeSrc();
			} else {
				scrapCode = DEFAULT_SCRAP_TABLE;
			}
			if (scrapCode == null || scrapCode.trim().length() == 0) {
				scrapCode = DEFAULT_SCRAP_TABLE;
			}
		} catch (Exception e) {
			logger.error("ScrapLotDialog : initComoContent() ", e);
		}
		return scrapCode;
	}

	/**
	 * �Ƴ������б�������ť
	 */
	protected void deleteAdapter(Object object) {
		List<Object> selectObjs = lotScrapListField.getListTableManager().getCheckedObject();
		lotScrapListField.getListTableManager().getInput().removeAll(selectObjs);
		List<LotScrap> scraps = new ArrayList<LotScrap>();
		if (selectObjs != null && selectObjs.size() > 0) {
			for (Object obj : selectObjs) {
				scraps.add((LotScrap) obj);
			}
		}
		lotScraps.removeAll(scraps);
	}

	/**
	 * ��ӱ���
	 */
	protected void addAdapter(Object object) {
		if (scrapActionField.validate()) {
			if (isLessThanLotQtys()) {
				scrapCode = ((LotAction) scrapActionField.getValue()).getActionCode().toString();
				LotScrap sh = new LotScrap();
				sh.setActionCode(scrapCode);
				sh.setMainQty(inputMainQty);
				sh.setSubQty(inputSubQty);
				sh.setActionComment(((LotAction) scrapActionField.getValue()).getActionComment());
				lotScraps.add(sh);
				lotScrapListField.getListTableManager().setInput(lotScraps);
				;
				scrapActionField.setValue(new LotAction());
			}
		}
		inputMainQty = inputSubQty = BigDecimal.ZERO;
	}

	/**
	 * ��ӱ���,����Ч��
	 */
	public boolean isLessThanLotQtys() {
		computeAddQty();
		inputMainQty = BigDecimal.ZERO;
		inputSubQty = BigDecimal.ZERO;

		if ("0".equals(mainQtyField.getText().trim()) && "".equals(subQtyField.getText().trim())) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}

		if ("".equals(mainQtyField.getText().trim()) && "0".equals(subQtyField.getText().trim())) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}

		if (lot.getMainQty() != null && !"".equals(mainQtyField.getText().trim())) {
			inputMainQty = new BigDecimal(mainQtyField.getText());
			if (inputMainQty.compareTo(BigDecimal.ZERO) < 0) {
				UI.showError(Message.getString("wip.scrap_check_qty"));
				return false;
			}
			if (lot.getMainQty().compareTo(inputMainQty.add(addMainQty)) < 0) {
				UI.showError(Message.getString("wip.scrap_more_than_main_qty"));
				mainQtyField.setText("");
				mainQtyField.setValue(null);
				mainQtyField.refresh();
				return false;
			}
		}

		if (lot.getSubQty() != null && !"".equals(subQtyField.getText().trim())) {
			inputSubQty = new BigDecimal(subQtyField.getText());
			if (inputSubQty.compareTo(BigDecimal.ZERO) < 0) {
				UI.showError(Message.getString("wip.scrap_check_qty"));
				return false;
			}
			if (lot.getSubQty().compareTo(inputSubQty.add(addSubQty)) < 0) {
				UI.showError(Message.getString("wip.scrap_more_than_subqty"));
				subQtyField.setText("");
				subQtyField.setValue(null);
				subQtyField.refresh();
				return false;
			}
		}

		if (inputMainQty.compareTo(BigDecimal.ZERO) == 0 && inputSubQty.compareTo(BigDecimal.ZERO) == 0) {
			UI.showError(Message.getString("wip.scrap_check_qty"));
			return false;
		}
		return true;
	}

	public void computeAddQty() {
		addMainQty = BigDecimal.ZERO;
		addSubQty = BigDecimal.ZERO;

		if (lotScraps != null) {
			for (LotScrap sl : lotScraps) {
				if (sl.getMainQty() != null) {
					addMainQty = addMainQty.add(sl.getMainQty());
				}
				if (sl.getSubQty() != null) {
					addSubQty = addSubQty.add(sl.getSubQty());
				}
			}
		}
	}

	@Override
	public void initLot() {
		if (CollectionUtils.isNotEmpty(getLotList())) {
			lot = getLotList().get(0);
		}
	}

	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			Boolean result = checkLotStateModel(getLotList());
			if (result == null) {
				for(Lot lot : getLotList()) {
					if (!(LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState()) 
							|| LotStateMachine.STATE_RUN.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState()))) { 
						UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
						return false;
					}
				}
			} else {
				return result;
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return false;
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<WIPLotScrapDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<WIPLotScrapDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}
}
