package com.glory.mes.wip.lot.action.dialog;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class WIPLotTerminateDialog extends LotActionDialog{
	
	private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 400;
	
	private static final String ADFORM_NAME = "WIPLotActionTerminateDialog";
	
	private static final String FIELD_LOTTERMINATE = "lotTermainateTable";
	private static final String FIELD_LOTACTIONCODE = "lotTermainateCode";
	private static final String FIELD_ACTIONCODE = "actionCode";
	private static final String FIELD_COMMENT = "actionComment";

	private ListTableManagerField lotTerminateListTable;
	private EntityFormField lotActionCodeEntityForm;
	private RefTableField actionCodeField;
	private TextField commentField;
	
	private ListTableManager lotTerminateTableManager;
	
	public WIPLotTerminateDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(ADFORM_NAME, authority, eventBroker);
		this.eventId = LotStateMachine.TRANS_TERMLOT;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		lotTerminateListTable = form.getFieldByControlId(FIELD_LOTTERMINATE, ListTableManagerField.class);
		lotActionCodeEntityForm = form.getFieldByControlId(FIELD_LOTACTIONCODE, EntityFormField.class);
		actionCodeField = lotActionCodeEntityForm.getFieldByControlId(FIELD_ACTIONCODE, RefTableField.class);
		commentField = lotActionCodeEntityForm.getFieldByControlId(FIELD_COMMENT, TextField.class);
		
		
		lotTerminateTableManager = lotTerminateListTable.getListTableManager();
		
		initLot();
	}
	
	@Override
	public void initLot() {
		List<Lot> lots = getLotList();
		lotTerminateTableManager.setInput(lots);
	}

	@Override
	protected void okPressed() {
		try {
			if (!lotActionCodeEntityForm.validate()) {
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			LotAction action = new LotAction();
			action.setActionCode(actionCodeField.getText());
			action.setActionComment(commentField.getText());
			
			List<Lot> batchObject = (List<Lot>) lotTerminateTableManager.getInput();
			List<Lot> terminateLots = new ArrayList<Lot>();
			if (batchObject.size() > 0) {
				for (int i = 0; i < batchObject.size(); i++) {
					Lot lot = batchObject.get(i);
					if(LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_SCHD.equalsIgnoreCase(lot.getState())
							|| LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState())) {
						lot.setOperator1(getOperator());
						terminateLots.add(lot);
					} 
				}
				lotManager.terminate(terminateLots, action, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.terminate_success"));// ������ʾ��
			}
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public boolean validate() {
		boolean flag = super.validate();
		if (flag) {
			Boolean result = checkLotStateModel(getLotList());
			if (result == null) {
				for(Lot lot : getLotList()) {
					if (!LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState())
							&& !LotStateMachine.STATE_SCHD.equalsIgnoreCase(lot.getState())
							&& !LotStateMachine.STATE_FIN.equalsIgnoreCase(lot.getState())) {
						UI.showError(lot.getLotId() + Message.getString("wip.lot_state_not_allow"));
						return false;
					}
				}
			} else {
				return result;
			}
		} else {
			return flag;
		}
		return true;
	}
	
	@Override
	public boolean isSupportMulitLot() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
