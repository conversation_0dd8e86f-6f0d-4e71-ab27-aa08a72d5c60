package com.glory.mes.wip.lot.changeparam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeParamViewDialog  extends BaseTitleDialog {
	private static final Logger logger = Logger.getLogger(ChangeParamViewDialog.class);
	private static int MIN_DIALOG_WIDTH = 600;
	private static int MIN_DIALOG_HEIGHT = 300;
	private ListTableManager tableManager;
	private List<Lot> lots;
	private Lot parameterLot;
	private ChangeParamQuerySection section;
	private String action;
	
	public ChangeParamViewDialog(Shell parent, Lot parameterLot, List<Lot> lots, ChangeParamQuerySection section, String action) {
        super(parent);
        this.lots = lots;
        this.parameterLot = parameterLot;
        this.section = section;
        this.action = action;
    }
	
	@Override
    protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("operation-dialog"));
        setTitle(Message.getString("wip.changeparam_titile"));
        setMessage(Message.getString("wip.changeparam_info"));
       
        FormToolkit toolkit = new FormToolkit(parent.getDisplay());	
        Composite tableCom = toolkit.createComposite(parent, SWT.NULL);
    	tableCom.setLayout( new GridLayout(4, false));
    	tableCom.setLayoutData(new GridData(GridData.FILL_BOTH));
    	try {
 			ADManager adManager = Framework.getService(ADManager.class);
 			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "WIPLotParamView");
 			tableManager = new ListTableManager(adTable, false);
 			tableManager.newViewer(tableCom);
 			
 			PrdManager prdManager = Framework.getService(PrdManager.class);
 			
 			for (Lot lot : lots) {
 				lot.setAttribute1(parameterLot.getAttribute1());//������
 				lot.setAttribute3(parameterLot.getAttribute3());//����ֵ
 				
 				Map<String, Object> params = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
 				
 				String paramName = lot.getAttribute1().toString();
 				
 				if (params != null && params.size() > 0 && params.containsKey(paramName)) {
 					lot.setAttribute2(params.get(paramName));//ԭʼֵ
 				}
			}
 			
 			tableManager.setInput(lots);
 		} catch (Exception e) {
 			e.printStackTrace();
 		}
        return parent;
	}
	
	@Override
	protected void okPressed() {
		try {
			Map<Long, Object> oldValues = new HashMap<Long, Object>();
			String paramName = null;
			Object paramValue = null;
 			for (Lot lot : lots) {
 				if (paramName == null) {
 					paramName = lot.getAttribute1().toString();
 				}
 				if (paramValue == null) {
 					paramValue = lot.getAttribute3();
 				}
 				//��¼�޸�ǰ�Ĳ���ֵ
				if (lot.getAttribute2() != null) {
					oldValues.put(lot.getObjectRrn(), lot.getAttribute2());
				}
					
 				//����ʱ�����ÿ�
 				lot.setAttribute1(null);
 				lot.setAttribute2(null);
 				lot.setAttribute3(null);
			}
 			LotManager lotManager = Framework.getService(LotManager.class);
 			boolean flag = false;
 			String operator = Env.getUserName();
 			if (ChangeParamQuerySection.ACTION_CHANGECURRENTPARAM.equals(action)) {
 				flag = false;
 				operator = (String) section.itemChangeCurrentParam.getData(LotAction.ACTION_TYPE_OPERATOR);
 			} else if (ChangeParamQuerySection.ACTION_CHANGELOTPARAM.equals(action)) {
 				flag = true;
 				operator = (String) section.itemChangeLotParam.getData(LotAction.ACTION_TYPE_OPERATOR);
 			}
 			SessionContext sc = Env.getSessionContext();
 			sc.setUserName(operator);
 			
 			List<Lot> resultLots = lotManager.changeParameter(lots, paramName, paramValue, oldValues, flag, sc);
 			
 			section.getTableManager().getCheckedObject().clear();
 			for (Lot lot : resultLots) {
 				section.refreshUpdate(lot);
 				section.getTableManager().setCheckedObject(lot);
			}
 			
 			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
		} catch (Exception e) {
			logger.error("Error at ChangeParamViewDialog okPressed() : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		super.okPressed();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
	
}
