package com.glory.mes.wip.lot.changeparam;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeParamQuerySection extends QueryEntityListSection implements IRefresh {
	
	private static final Logger logger = Logger.getLogger(ChangeParamQuerySection.class);
	
	String whereClause;

	public static final String KEY_CURRENT_PARAM = "currentParam";
	
	public static final String KEY_LOT_PARAM = "lotParam";
	
	protected AuthorityToolItem itemChangeCurrentParam;
	protected AuthorityToolItem itemChangeLotParam;
	
	public static final String ACTION_CHANGECURRENTPARAM = "CHANGECURRENTPARAM";
	public static final String ACTION_CHANGELOTPARAM = "CHANGELOTPARAM";
	
	public ChangeParamQuerySection(ListTableManager tableManager) {
		super(tableManager);
	}
		
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChangeCurrentParam(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemChangeLotParam(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemChangeCurrentParam(ToolBar tBar) {
		itemChangeCurrentParam = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_CURRENT_PARAM);
		itemChangeCurrentParam.setAuthEventAdaptor(this::changeCurrentParamAdapter);
		itemChangeCurrentParam.setText(Message.getString("wip.current_param"));
		itemChangeCurrentParam.setImage(SWTResourceCache.getImage("edit"));
//		itemChangeCurrentParam.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				changeAdapter(ACTION_CHANGECURRENTPARAM);
//			}
//		});
	}

	protected void createToolItemChangeLotParam(ToolBar tBar) {
		itemChangeLotParam = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_CURRENT_PARAM);
		itemChangeLotParam.setAuthEventAdaptor(this::changeLotParamAdapter);
		itemChangeLotParam.setText(Message.getString("wip.lot_param"));
		itemChangeLotParam.setImage(SWTResourceCache.getImage("edit"));
//		itemChangeLotParam.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				changeAdapter(ACTION_CHANGELOTPARAM);
//			}
//		});
	}
	
	protected void changeCurrentParamAdapter(SelectionEvent event) {
		try {
			List<Object> objects = getCheckedObject();
			if (objects == null || objects.size() == 0) {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "WIPLotParamEdit1");
			
			ChangeParamEditDialog dialog = new ChangeParamEditDialog(adTable, new Lot(), this, ACTION_CHANGECURRENTPARAM);
			
			List<Lot> lots = new ArrayList<Lot>();
			for (Object object : objects) {
				Lot lot = (Lot) object;
				lots.add(lot);
			}
			dialog.setLots(lots);
			dialog.open();
		} catch (Exception e) {
			logger.error("ChangeParamQuerySection : changeAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void changeLotParamAdapter(SelectionEvent event) {
		try {
			List<Object> objects = getCheckedObject();
			if (objects == null || objects.size() == 0) {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "WIPLotParamEdit1");
			
			ChangeParamEditDialog dialog = new ChangeParamEditDialog(adTable, new Lot(), this, ACTION_CHANGELOTPARAM);
			
			List<Lot> lots = new ArrayList<Lot>();
			for (Object object : objects) {
				Lot lot = (Lot) object;
				lots.add(lot);
			}
			dialog.setLots(lots);
			dialog.open();
		} catch (Exception e) {
			logger.error("ChangeParamQuerySection : changeAdapter()",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
