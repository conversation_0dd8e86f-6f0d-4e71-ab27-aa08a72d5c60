package com.glory.mes.wip.lot.changeparam;


import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;

public class ChangeParamEditDialog extends EntityDialog {
	private static final Logger logger = Logger.getLogger(ChangeParamEditDialog.class);
	private List<Lot> lots;
	private ChangeParamQuerySection section;
	private String action;
	
	public ChangeParamEditDialog(ADTable table, ADBase adObject, ChangeParamQuerySection section, String action) {
		super(table, adObject);
		this.section = section;
		this.action = action;
	}

	
	@Override
	protected boolean saveAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					return true;
				}
			}
		} catch (Exception e) {
			logger.error("Error at EntityDialog saveAdapter() : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return false;
	}
	
	@Override
	protected void buttonPressed(int buttonId) {
		if (IDialogConstants.OK_ID == buttonId) {
			if(saveAdapter()) {
				okPressed();
				
				ChangeParamViewDialog dialog = new ChangeParamViewDialog(UI.getActiveShell(), (Lot) getAdObject(), lots, section, action);
				dialog.open();
			} else {
				return;
			}
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed();
		}
	}

	public List<Lot> getLots() {
		return lots;
	}


	public void setLots(List<Lot> lots) {
		this.lots = lots;
	}

}
