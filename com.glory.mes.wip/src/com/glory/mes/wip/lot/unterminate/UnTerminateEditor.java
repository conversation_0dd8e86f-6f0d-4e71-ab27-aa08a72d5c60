package com.glory.mes.wip.lot.unterminate;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.Message;

public class UnTerminateEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.unterminate.UnTerminateEditor";

	private UnTerminateSection section;

	@Inject
	protected ESelectionService selectionService;
	
	@Inject
	protected MPart mPart;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		createSection(adTable);
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);
		//form.setLayoutData(new GridData(GridData.FILL_BOTH));
		ManagedForm mform = new ManagedForm(toolkit, form);
		
		Composite body = form.getBody();
		configureBody(body);
		section.createContents(mform, body);
		
//		mPart.setLabel(Message.getString("wip.unterminate_editortitle"));
	}
	
	protected void createSection(ADTable adTable) {
		section = new UnTerminateSection(adTable);
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
}
