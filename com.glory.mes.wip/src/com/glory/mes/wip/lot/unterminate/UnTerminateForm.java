package com.glory.mes.wip.lot.unterminate;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.model.Lot;

public class UnTerminateForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(UnTerminateForm.class);
	private Lot lot;
	private IField fieldUnTerminateCode;
	private static final String UNTERMINATECODE = "UnTerminateCode";
	private static final String UNTERMINATECODE_ID = "UnTerminateCode";
	private IField fieldComment;
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "comment";
	private LotAction lotAction = new LotAction();

	public UnTerminateForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public UnTerminateForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public UnTerminateForm(Composite parent, int style, Object object,
			ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public UnTerminateForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {

			fieldUnTerminateCode = createUserRefList(UNTERMINATECODE_ID, Message.getString("wip.unterminate_code") + "*", UNTERMINATECODE, false);			
	
			fieldComment = createText(COMMENT_ID, Message.getString("wip.comment"), "", 32);

			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			addField(UNTERMINATECODE, fieldUnTerminateCode);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("UnTerminateForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField)
						&& !f.equals(fieldUnTerminateCode)
						&& !f.equals(fieldComment)) {
					Object o = PropertyUtil.getPropertyForIField(object, f
							.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			lot = (Lot) object;
			lotAction.setActionCode((String) fieldUnTerminateCode.getValue());
			lotAction.setActionComment((String) fieldComment.getValue());
			return true;
		}
		return false;
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			fieldUnTerminateCode.setValue("");
			fieldUnTerminateCode.refresh();
			((TextField) fieldComment).setText("");
		} catch (Exception e) {
			logger.error("UnTerminateForm : refresh()", e);
		}
	}

	public LotAction getLotAction() {
		return lotAction;
	}

	public Lot getLot() {
		return lot;
	}

	@Override
	public boolean validate() {
		boolean validFlag = super.validate();
		if (fieldUnTerminateCode.getValue() == null
				|| "".equals(String.valueOf(fieldUnTerminateCode.getValue())
						.trim())) {
			validFlag = false;
			mmng.addMessage(UNTERMINATECODE_ID, String.format(Message
					.getString("common.ismandatry"), UNTERMINATECODE_ID), null,
					IMessageProvider.ERROR,
					fieldUnTerminateCode.getControls()[1]);
		}
		return validFlag;
	}
}
