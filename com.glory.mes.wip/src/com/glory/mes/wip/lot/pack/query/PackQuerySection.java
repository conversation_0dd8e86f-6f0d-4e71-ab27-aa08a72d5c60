package com.glory.mes.wip.lot.pack.query;

import java.util.List;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.mm.PackageObjectDetail;
import com.google.common.collect.Lists;

public class PackQuerySection extends QueryEntityListSection implements IRefresh {
	
	public String packageHierarchyName;
	
	public PackQuerySection(ListTableManager tableManager, String packageHierarchyName) {
		super(tableManager);
		this.packageHierarchyName = packageHierarchyName;
	}
	
	protected void createSectionDesc(Section section) {}
	
	protected QueryForm createQueryFrom(final IManagedForm form, Composite queryComp) {
		QueryForm queryForm = super.createQueryFrom(managedForm, queryComp);
		queryForm.setObject(new PackageObjectDetail());
		return queryForm;
	}
	
	@Override
	protected void queryAdapter() {
		managedForm.getMessageManager().removeAllMessages();
		if (!getQueryForm().saveToObject()){
			return;
		}
		refresh();
	}
	
	public void refresh() {
		try {
			List<PackageObjectDetail> details = Lists.newArrayList();
			PackageObjectDetail lot = (PackageObjectDetail) getQueryForm().getObject();	
			String level1Id = (String) lot.getObjectId1();
			String level2Id = (String) lot.getObjectId2();
			String level3Id = (String) lot.getObjectId3();
			String level0Id = (String) lot.getObjectId0();
			
			String queryId = null;
			int count = 0;
			int packLevel = 0;
			if (!StringUtil.isEmpty(level0Id)) {
				packLevel = 0;
				count++;
				queryId = level0Id;
			}
			if (!StringUtil.isEmpty(level1Id)) {
				packLevel = 1;
				count++;
				queryId = level1Id;
			}
			if (!StringUtil.isEmpty(level2Id)) {
				packLevel = 2;
				count++;
				queryId = level2Id;
			}
			if (!StringUtil.isEmpty(level3Id)) {
				packLevel = 3;
				count++;
				queryId = level3Id;
			}
			
			if (StringUtil.isEmpty(queryId)) {
				UI.showInfo(Message.getString("wip.pack_query_need_id"));
				return;
			}
			
			if (count > 1) {
				UI.showInfo(Message.getString("wip.pack_query_only_one_id"));
				return;
			}
					
			MLotManager mLotManager = Framework.getService(MLotManager.class);
			details = mLotManager.getPackageObjectDetailsById(Env.getOrgRrn(), packageHierarchyName, packLevel, queryId, null);
			tableManager.setInput(details);
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	public String getPackageHierarchyName() {
		return packageHierarchyName;
	}

	public void setPackageHierarchyName(String packageHierarchyName) {
		this.packageHierarchyName = packageHierarchyName;
	}
	
}
