package com.glory.mes.wip.lot.pack.action;

import java.math.BigDecimal;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.pack.splitunpack.SplitUnPackManagerEditor;
import com.glory.mes.wip.model.Lot;

public class PackedActionManagerEditor extends SplitUnPackManagerEditor{
	
	private static final Logger logger = Logger.getLogger(PackedActionManagerEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.pack.action.PackedActionManagerEditor";

	@Override
	protected void createFormAction(GlcForm form) {
		ADEditor adEditor = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR));
		packageTypeName = adEditor.getPARAM3();
		packageLayer = adEditor.getPARAM5();
		
		packDetailField = form.getFieldByControlId(FIELD_PACKDETAIL, GlcFormField.class);
		packLotField = form.getFieldByControlId(FIELD_PACKLOT, GlcFormField.class);
		lotInfoField = packDetailField.getFieldByControlId(FIELD_LOTINFO, CustomField.class);
		packDetailTableField = packDetailField.getFieldByControlId(FIELD_PACKDETAILTABLE, ListTableManagerField.class);
		lotInfoField1 = packLotField.getFieldByControlId(FIELD_LOTINFO, CustomField.class);
		packLotTableField = packLotField.getFieldByControlId(FIELD_PACKLOTTABLE, ListTableManagerField.class);
		
		boxIdEnterPressComposite = (EnterPressComposite) lotInfoField.getCustomComposite();
		lotIdEnterPressComposite = (EnterPressComposite) lotInfoField1.getCustomComposite();
		
		EntityForm sourceLotEntityForm = (EntityForm) packDetailTableField.getSlotForm();
		EntityForm packedLotEntityForm = (EntityForm) packLotTableField.getSlotForm();
		
		mainQtyField = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		totalQtyField = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		
		packMainQtyField = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		packTotalQtyField = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_BOXENTERPRESS), this::boxIdEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTENTERPRESS), this::lotIdEnterPressed);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNPACKING), this::unpackingAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PACKUNPACK), this::packUnpackAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ADD), this::addAdapter);
		
		SquareButton packTotalQtyButton = (SquareButton) packTotalQtyField.getButtonByControl(BUTTON_CLEAR);
		packTotalQtyButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				clearAdapter();
			}
		});
	}
	
	@Override
	protected void unpackingAdapter(Object object) {
		try {
			Lot packedLot = getPackedLot();
			if (packedLot == null) {
				UI.showInfo(Message.getString("mm.pack_unpack_lot_is_null"));
				return;
			}
			
			boolean confirmUnPack = UI.showConfirm(Message.getString("mm.pack_unpack_confirm"));
			if (confirmUnPack) {
				// �������
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				carrierLotManager.unpackLot(packedLot, Env.getSessionContext());
				
				//���
				clearForm();
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipUnPacked()));
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	@Override
	protected void packUnpackAdapter(Object object) {
		try {
			Lot packedLot = getPackedLot();
			if (packedLot == null) {
				UI.showInfo(Message.getString("mm.pack_unpack_lot_is_null"));
				return;
			}
			
			List<Lot> checkedPackLotList = getCheckedPackLotList();
			if (checkedPackLotList.size() == 0) {
                UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			boolean confirm = UI.showConfirm(Message.getString("mm.pack_unpack_confirm"));
			if (confirm) {
				// ���ֲ������
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				carrierLotManager.unpackLot(packedLot, checkedPackLotList, Env.getSessionContext());
				
				removeLots(checkedPackLotList);
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipUnPacked()));
			} 
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	@Override
	protected void addAdapter(Object object) {
		try {
			Lot packedLot = getPackedLot();
			if (packedLot == null) {
				UI.showInfo(Message.getString("mm.pack_unpack_lot_is_null"));
				return;
			}
			
			// ��ȡѡ�����ϸ�������浽List
			List<Lot> lotList = getLotList();
			if (lotList.size() == 0) {
				UI.showInfo(Message.getString("mm.packing_add_mlot_is_null"));
			} else {
				for(Lot lot : lotList) {
					if(BigDecimal.ZERO.compareTo(lot.getMainQty()) == 0) {
						UI.showInfo(Message.getString("mm.packing_add_mlotqty_is_zero"));
						return;
					}
				}
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				packedLot = carrierLotManager.addPackLot(packedLot, lotList, this.getPackageType().getName(), Env.getSessionContext());
				
				refresh();
				sourceClearForm();
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipAddPacked()));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
		}
	}	
}
