package com.glory.mes.wip.lot.pack;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.SquareButton;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.application.event.AppEvent;
import com.glory.framework.base.application.event.AppEventManager;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.mm.client.PackManager;
import com.glory.mes.mm.model.PackageRule;
import com.glory.mes.mm.model.PackageType;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.MergeManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class PackManagerEditor extends GlcEditor { 
	
	private static final Logger logger = Logger.getLogger(PackManagerEditor.class);

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.pack.PackManagerEditor";

	protected static final String FIELD_ENTERPRESS = "packDetail-EnterPressed";
	protected static final String FIELD_PACKDETAIL = "packDetail";
	protected static final String FIELD_PACKLOT = "packLot";
	protected static final String FIELD_REJECTLOT = "rejectLot";
	protected static final String FIELD_LOTINFO = "lotInfo";
	protected static final String FIELD_PACKDETAILTABLE = "packDetailTable";
	protected static final String FIELD_PACKLOTTABLE = "packLotTable";
	protected static final String FIELD_REJECTLOTTABLE = "rejectLotTable";

	protected static final String BUTTON_PACKING = "packing";
	protected static final String BUTTON_UNPACKING = "unpacking";
	protected static final String BUTTON_PRINT = "print";
	protected static final String BUTTON_CLEAR = "clear";
	
	public static final String FIELD_MAINQTY = "mainQty";
	public static final String FIELD_TOTALQTY = "totalQty";
	
	protected GlcFormField packDetailField;
	protected GlcFormField packLotField;
	protected GlcFormField rejectLotField;
	protected CustomField lotInfoField;
	protected ListTableManagerField sourceLotTableField;
	protected ListTableManagerField packLotTableField;
	protected ListTableManagerField rejectLotTableField;
	protected EnterPressComposite lotIdEnterPressComposite;
	
	protected TextField sourceCountTxt, sourceTotalTxt, packLotCountTxt, packLotTotalTxt, rejectCountTxt, rejectTotalTxt;
	
	protected String packageTypeName;
	protected String packageLayer;
	protected PackageType packageType;
	protected Lot parentLot;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		ADEditor adEditor = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR));
		packageTypeName = adEditor.getPARAM3();
		packageLayer = adEditor.getPARAM5();
		
		packDetailField = form.getFieldByControlId(FIELD_PACKDETAIL, GlcFormField.class);
		packLotField = form.getFieldByControlId(FIELD_PACKLOT, GlcFormField.class);
		rejectLotField = form.getFieldByControlId(FIELD_REJECTLOT, GlcFormField.class);
		lotInfoField = packDetailField.getFieldByControlId(FIELD_LOTINFO, CustomField.class);
		sourceLotTableField = packDetailField.getFieldByControlId(FIELD_PACKDETAILTABLE, ListTableManagerField.class);
		packLotTableField = packLotField.getFieldByControlId(FIELD_PACKLOTTABLE, ListTableManagerField.class);
		rejectLotTableField = rejectLotField.getFieldByControlId(FIELD_REJECTLOTTABLE, ListTableManagerField.class);
		
		lotIdEnterPressComposite = (EnterPressComposite) lotInfoField.getCustomComposite();
		
		EntityForm sourceLotEntityForm = (EntityForm) sourceLotTableField.getSlotForm();
		EntityForm packedLotEntityForm = (EntityForm) packLotTableField.getSlotForm();
		EntityForm rejectLotEntityForm = (EntityForm) rejectLotTableField.getSlotForm();
		
		sourceCountTxt = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		sourceTotalTxt = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
				
		packLotCountTxt = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		packLotTotalTxt = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		
		rejectCountTxt = (TextField) rejectLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		rejectTotalTxt = (TextField) rejectLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_ENTERPRESS), this::lotIdEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PACKING), this::packingAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNPACKING), this::unpackingAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PRINT), this::printAdapter);
		
		SquareButton sourceTotalButton = (SquareButton) sourceTotalTxt.getButtonByControl(BUTTON_CLEAR);
		sourceTotalButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				sourceClearAdapter();
			}
		});
		
		SquareButton packTotalButton = (SquareButton) packLotTotalTxt.getButtonByControl(BUTTON_CLEAR);
		packTotalButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				packLotClearAdapter();
			}
		});
		
		SquareButton rejectTotalButton = (SquareButton) rejectTotalTxt.getButtonByControl(BUTTON_CLEAR);
		rejectTotalButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				rejectLotclearAdapter();
			}
		});
	}

	protected void packingAdapter(Object object) {
		try {
			//�����װ��ť����װ����Lot
			List<Lot> lots = new ArrayList<Lot>();
			lots.addAll((List<Lot>)getLotList());
			if (lots.size() == 0) {
				UI.showWarning(Message.getString("error.no_lot_input"));
				return;
			}
			packLot(lots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void unpackingAdapter(Object object) {
		try {
			List<Lot> checkedPackLotList = getCheckedPackLotList();
			if (checkedPackLotList.size() == 0) {
                UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			boolean confirmUnPack = UI.showConfirm(Message.getString("mm.pack_unpack_confirm"));
			if (confirmUnPack) {			
				unPackLot(checkedPackLotList);
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}

	protected void printAdapter(Object object) {
        try {
        	List<Lot> checkedList = getCheckedPackLotList();
			if (checkedList.size() == 0) {
				UI.showError(Message.getString("mm.please_select_records"));
				return;
			}
        	
			AppEvent appEvent = new AppEvent();
			appEvent.setEventParam1("PRINT"); //actionType
			appEvent.setEventParam2("LotList"); //objectType
			appEvent.setEventParam3(checkedList); //object
			AppEventManager.postEvent(AppEvent.EVENT_ID_PRINTLABEL, appEvent);
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
	}

	protected void lotIdEnterPressed(Object object) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Event event = (Event) object;
			ADBase adBase = (ADBase) event.getProperty(GlcEvent.PROPERTY_DATA);
			Lot currentLot = lotManager.getLot(adBase.getObjectRrn());
			if (currentLot == null) {
				UI.showWarning(Message.getString("wip.lot_not_found"));
				lotIdEnterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));	
				lotIdEnterPressComposite.getTxtLot().selectAll();
				return;
			} 
			if(BigDecimal.ZERO.compareTo(currentLot.getMainQty()) == 0){
				UI.showWarning(Message.getString("wip.lot_qty_must_more_than.zero"));
				lotIdEnterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				lotIdEnterPressComposite.getTxtLot().selectAll();
				return;
			} 
			if (currentLot.getParentLotRrn() != null && LotStateMachine.STATE_PACKED.equals(currentLot.getState())) {
				UI.showWarning(Message.getString("mm.pack_lot_is_packed"));
				lotIdEnterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				lotIdEnterPressComposite.getTxtLot().selectAll();
				return;
			}
			
			if (getPackageType() != null) {
				PackageType newPackageType = getPackageType(currentLot);
				if (!getPackageType().getName().equals(newPackageType.getName())) {
					UI.showWarning(Message.getString("wip.lotId_packagin_are_inconsistent"));
					return;
				}
			}
			
			if (parentLot == null) {
				PackageType newPackageType = getPackageType(currentLot);
				if (newPackageType == null) {
					return;
				}
				if (!StringUtil.isEmpty(newPackageType.getSourceMainMatType())) { //Դ��װ����δά��ʱ����������
					if (!newPackageType.getSourceMainMatType().equals(currentLot.getMainMatType())) {
						UI.showWarning(Message.getString("mm.pack_mainmattype_not_allow"));
						return;
					}
				}
				
				if (!StringUtil.isEmpty(newPackageType.getSourceLotState())) {
					if (!currentLot.getState().equals(newPackageType.getSourceLotState())) {
						UI.showWarning(Message.getString("error.lot_state_not_allow"));
						return;
					}
				}

				if (newPackageType.getPackMainMatType().equals(currentLot.getMainMatType())) {
					UI.showWarning(Message.getString("mm.packed"));
					return;
				}

				setPackageType(newPackageType);
				parentLot = currentLot;
			}																	
			addSource(currentLot);
			lotIdEnterPressComposite.getTxtLot().setText("");	
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}

	protected void sourceClearAdapter() {
		try {
			List<Object> checkedList = sourceLotTableField.getListTableManager().getCheckedObject();
			List<Lot> clearLots = new ArrayList<>();
			
			BigDecimal sourceMainQty =  BigDecimal.ZERO;
			for (Object object1 : checkedList) {
				if (object1 instanceof Lot) {
					Lot clearLot = (Lot)object1;
					clearLots.add(clearLot);
					sourceMainQty = clearLot.getMainQty().add(sourceMainQty);
				}
			}
			
			List<Lot> sourceLots = new ArrayList<Lot>();
			sourceLots.addAll((List<Lot>) sourceLotTableField.getListTableManager().getInput());
			sourceLots.removeAll(clearLots);
			if (clearLots.contains(parentLot)) {
				if (sourceLots.size() <= 0) {
					parentLot = null;
				} else {
					parentLot = sourceLots.get(0);
				}
			}
			
			BigDecimal sourceMainTotalQty = BigDecimal.ZERO;
			if (!sourceTotalTxt.getText().trim().isEmpty()) {
				sourceMainTotalQty = new BigDecimal(sourceTotalTxt.getText());
			}
			sourceMainTotalQty = sourceMainTotalQty.subtract(sourceMainQty);
						
			sourceCountTxt.setText(String.valueOf(sourceLots.size()));
			sourceTotalTxt.setText(sourceMainTotalQty.toString());
			sourceLotTableField.getListTableManager().setInput(sourceLots);
			
			if (sourceLotTableField.getListTableManager().getInput().size() == sourceLotTableField.getListTableManager().getCheckedObject().size()) {
				setPackageType(null);
				parentLot = null;
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	
	}

	protected void packLotClearAdapter() {
		List<Object> checkedList = packLotTableField.getListTableManager().getCheckedObject();
		List<Lot> clearLots = new ArrayList<>();
		
		BigDecimal packMainQty =  BigDecimal.ZERO;
		for (Object object1 : checkedList) {
			if (object1 instanceof Lot) {
				Lot clearLot = (Lot)object1;
				clearLots.add(clearLot);
				packMainQty = clearLot.getMainQty().add(packMainQty);
			}
		}
		
		List<Lot> packLots = new ArrayList<Lot>();
		packLots.addAll((List<Lot>) packLotTableField.getListTableManager().getInput());
		packLots.removeAll(clearLots);
		
		// �����ĺϼ�
		BigDecimal packMainTotalQty = BigDecimal.ZERO;
		if (!packLotTotalTxt.getText().trim().isEmpty()) {
			packMainTotalQty = new BigDecimal(packLotTotalTxt.getText());
		}
		packMainTotalQty = packMainTotalQty.subtract(packMainQty);

		packLotCountTxt.setText(String.valueOf(packLots.size()));
		packLotTotalTxt.setText(packMainTotalQty.toString());
		packLotTableField.getListTableManager().setInput(packLots);
	}

	protected void rejectLotclearAdapter() {
		rejectCountTxt.setText("");
		rejectTotalTxt.setText("");
		rejectLotTableField.getListTableManager().setInput(new ArrayList<Lot>());
	}
	
	protected void packLot(List<Lot> lots) {
		try {
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			Lot packLot = carrierLotManager.packLot(lots, "", getPackageType().getName(), Env.getSessionContext());
			
			List<Lot> packLots = new ArrayList<Lot>();
			packLots.add(packLot);
			
			clearForm();				
			refreshPackForm(packLots);
			UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipPackedLot()));
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	protected void unPackLot(List<Lot> lots) {
		try {
			// �������
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);					
			for (Lot packLot : lots) {
				if (packLot instanceof Lot) {
					carrierLotManager.unpackLot(packLot, Env.getSessionContext());
				}
			}

			// ������ͳ��
			List<Lot> packLots = new ArrayList<Lot>();
			packLots.addAll((List<Lot>) getPackLotList());
			packLots.removeAll(lots);
			refreshForm(packLots);
			UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipUnPacked()));
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	protected void addSource(ADBase adBase) {
		try {
			if (adBase instanceof Lot) {
				//δ��װ��lot
				List<Lot> lots = new ArrayList<Lot>();
				lots.addAll((List<Lot>) sourceLotTableField.getListTableManager().getInput());
				
				PackManager packManager = Framework.getService(PackManager.class);
				MergeManager mergeManager  = Framework.getService(MergeManager.class);
				// table �����lot
				Lot lot = (Lot) adBase;
				PackageRule packageRule = packManager.getPackageRule(Env.getOrgRrn(), lot, PackageType.OBJECT_TYPE_LOT, getPackageType().getName(), null);
				if (lots != null && lots.size() > 0) {
					// �жϽ�����û���Ѵ��ڵ�lot
					for (Lot oldLot : lots) {
						if (oldLot.getLotId().equals(lot.getLotId())) {
							return;
						}
					}
					
					if (!PackageType.OBJECT_TYPE_LOT.equals(getPackageType().getObjectType())) {
						UI.showError(Message.getString("mm.pack_object_type_mlot"));
						return;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceMainMatType())
							&& !getPackageType().getSourceMainMatType().equals(lot.getMainMatType())) {
						UI.showError(Message.getString("mm.pack_mainmattype_not_allow"));
						return;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceSubMatType())
							&& !getPackageType().getSourceSubMatType().equals(lot.getSubMatType())) {
						UI.showError(Message.getString("mm.pack_submattype_not_allow"));
						return;
					}
					if (!StringUtil.isEmpty(packageRule.getMergeRuleName())) {
						// ����װ��������,�����ͨ�������rejectSourceTableManager
						List<Lot> childLots = new ArrayList<>();
						childLots.add(lot);
					
						MergeRuleResult pckResult = mergeManager.checkLotPackRule(parentLot, childLots, packageRule.getMergeRuleName(), false);

						if (!pckResult.isSuccess()) {
							refreshRejectForm(childLots);
							UI.showError(Message.getString("mm.pack_mergerule_not_allow"));
							return;
						} else {
							lots.add(lot);
						}
					}
				} else {
					lots.add(lot);
				}
				
				//�ۼ�lot��ĺϼ�
				BigDecimal mainTotalQty = BigDecimal.ZERO;
				if (!sourceTotalTxt.getText().trim().isEmpty()) {
					mainTotalQty = new BigDecimal(sourceTotalTxt.getText());
				}
				BigDecimal mainQty = lot.getMainQty();
				mainTotalQty = mainQty.add(mainTotalQty);
				
				BigDecimal countTotalQty = new BigDecimal(lots.size());
	
				// ���������,����sourceTableManager�ۼ�SourceCount��SourceMainQty
				sourceCountTxt.setText(countTotalQty.toString());
				sourceTotalTxt.setText(mainTotalQty.toString());
				sourceLotTableField.getListTableManager().setInput(lots);
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	protected void refreshPackForm(List<Lot> lots) {
		try {
			// �Ѱ�װ��lot
			List<Lot> packLots = new ArrayList<Lot>();
			packLots.addAll((List<Lot>)getPackLotList());

			// ���ӵ�packTableManager��
			packLots.addAll(lots);

			refreshForm(packLots);
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	protected List<Lot> getPackLotList() {
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll((List<Lot>) packLotTableField.getListTableManager().getInput());
		return lots;
	}
	
	protected List<Lot> getLotList() {
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll((List<Lot>) sourceLotTableField.getListTableManager().getInput());
		return lots;
	}
	
	protected void refreshForm(List<Lot> packLots) {
		BigDecimal packMainTotalQty = BigDecimal.ZERO;

		// �ۼ�lot��������ͺϼ�
		for (Lot packedLot : packLots) {
			packMainTotalQty = packMainTotalQty.add(packedLot.getMainQty());
		}

		packLotCountTxt.setText(String.valueOf(packLots.size()));
		packLotTotalTxt.setText(packMainTotalQty.toString());
		packLotTableField.getListTableManager().setInput(packLots);
	}

	protected PackageType getPackageType() {
		return packageType;
	}

	protected void setPackageType(PackageType packageType) {
		this.packageType = packageType;
	}
	
	protected PackageType getPackageType(Lot lot) throws Exception {
		PackManager packManager = Framework.getService(PackManager.class);
		PackageType newPackageType = packManager.getPackageType(Env.getOrgRrn(), lot, PackageType.OBJECT_TYPE_LOT, packageLayer, null);
		if (newPackageType == null) {
			if (StringUtil.isEmpty(packageTypeName)) {
				logger.warn("Package type name is null, please set adeditor param2 as package type name.");
				UI.showWarning(Message.getString("mm.pack_package_type_not_exist"));				
			} else {
				newPackageType = packManager.getPackageType(Env.getOrgRrn(), packageTypeName, PackageType.OBJECT_TYPE_LOT);
				if (newPackageType == null) {
					logger.warn("Package type " + packageTypeName + "is not found");
					UI.showWarning(Message.getString("mm.pack_package_type_not_exist"));					
				}
			}
		}
		return newPackageType;
	}
	
	protected void clearForm() {
		sourceCountTxt.setText("0");
		sourceTotalTxt.setText("0");
		sourceLotTableField.getListTableManager().setInput(new ArrayList<Object>());
		parentLot = null;
	}
	
	protected List<Lot> getCheckedPackLotList() {
		List<Object> checkedList = packLotTableField.getListTableManager().getCheckedObject();
		if (checkedList.size() > 0) {
			List<Lot> lstPackLot = new ArrayList<Lot>();
			for (Object obj : checkedList) {
				lstPackLot.add((Lot)obj);
			}
			return lstPackLot;
		} else {
			return new ArrayList<Lot>();
		}
	}
	
	protected void refreshRejectForm(List<Lot> lots) {
		try {
			List<Lot> lotList = new ArrayList<Lot>();
			lotList.addAll((List<Lot>) rejectLotTableField.getListTableManager().getInput());

			LotManager lotManager = Framework.getService(LotManager.class);

			// ��ȡ���ε�ǰ��Ϣ
			for (Lot lot : lots) {
				boolean bLotExisted = false;
				Lot tmpLot = lotManager.getLot(lot.getObjectRrn());
				for (Lot lt : lotList) {
					if (tmpLot.getLotId().equals(lt.getLotId())) {
						lt.setMainQty(tmpLot.getMainQty());
						bLotExisted = true;
						break;
					}
				}
				if (!bLotExisted) {
					lotList.add(tmpLot);
				}
			}

			// ȥ������Ϊ0��β������
			List<Lot> retLotList = new ArrayList<Lot>();
			for (Lot lt : lotList) {
				if (lt.getMainQty().compareTo(BigDecimal.ZERO) > 0) {
					retLotList.add(lt);
				}
			}

			/**
			 * ͳ��
			 */
			BigDecimal totalQty = BigDecimal.ZERO;

			// �ۼ�lot��������ͺϼ�
			for (Lot lt : retLotList) {
				totalQty = totalQty.add(lt.getMainQty());
			}

			rejectCountTxt.setText(String.valueOf(retLotList.size()));
			rejectTotalTxt.setText(totalQty.toString());
			rejectLotTableField.getListTableManager().setInput(retLotList);
		} catch (ClientException e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}