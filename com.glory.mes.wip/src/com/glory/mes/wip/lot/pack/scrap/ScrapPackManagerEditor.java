package com.glory.mes.wip.lot.pack.scrap;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.application.event.AppEvent;
import com.glory.framework.base.application.event.AppEventManager;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.google.common.collect.Lists;

public class ScrapPackManagerEditor extends GlcEditor { 

	private static final Logger logger = Logger.getLogger(ScrapPackManagerEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.pack.scrap.ScrapPackManagerEditor";

	public static final String FIELD_LOTSCRAPQUERY = "lotScrapQuery";
	public static final String FIELD_PACKID = "packId";

	public static final String BUTTON_PACK = "pack";
	public static final String BUTTON_UNPACK = "unpack";
	public static final String BUTTON_PRINT = "print";
	public static final String BUTTON_CLEAR = "clear";

	protected QueryFormField lotScrapQueryField;
	protected ListTableManagerField packIdField;
	protected TextField txtPackLot, txtPackDate;
	
	private String packageType;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotScrapQueryField = form.getFieldByControlId(FIELD_LOTSCRAPQUERY, QueryFormField.class);
		packIdField = form.getFieldByControlId(FIELD_PACKID, ListTableManagerField.class);
		EntityForm entityForm  = (EntityForm)packIdField.getSlotForm();
		txtPackLot = entityForm.getFieldByControlId("packId", TextField.class);
		txtPackDate = entityForm.getFieldByControlId("objectId1", TextField.class);

		subscribeAndExecute(eventBroker, lotScrapQueryField.getFullTopic(GlcEvent.EVENT_QUERY), this::lotScrapQueryQuery);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PACK), this::packAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNPACK), this::unpackAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PRINT), this::printAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
		
		txtPackLot.getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				txtPackLot.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
					String packId = txtPackLot.getText();
					txtPackLot.setText(packId.toUpperCase());
					Lot packedLot = searchLot(packId.toUpperCase());
					if (packedLot == null) {
						txtPackLot.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						return;
					}
					txtPackLot.getTextControl().selectAll();
					
					List<LotScrap> lotScraps = searchLotScrapByParentLotRrn(packedLot.getObjectRrn());
					if (lotScraps != null) {	
						for(LotScrap lotScrap : lotScraps) {
						    Lot lot = searchLot(lotScrap.getLotId());
						    if (lot != null) {
						        lotScrap.setAttribute1(lot.getWoId());
						        lotScrap.setAttribute2(lot.getCustomerCode());
						        lotScrap.setAttribute3(lot.getCustomerOrder());
						        lotScrap.setAttribute4(lot.getCustomerLotId());
						        lotScrap.setAttribute5(lot.getCustomerPartId());
						    }
						}
						packIdField.getListTableManager().setInput(lotScraps);
						packIdField.getListTableManager().refresh();		
						SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						txtPackDate.setText(sdf.format(packedLot.getCreated()).toString());
					} else {
						packIdField.getListTableManager().setInput(new ArrayList<LotScrap>());
						packIdField.getListTableManager().refresh();		
					}
					break;
				}
			}
		
		});
		txtPackLot.getTextControl().addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {}
			
			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				tLotId.setText(tLotId.getText().toUpperCase());
			}
		});
		
		ADEditor adEditor = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR));
		packageType = adEditor.getPARAM3();
	}

	private void lotScrapQueryQuery(Object object) {
		refresh();
	}
	
	private void packAdapter(Object object) {
		CheckBoxTableViewerManager t = 	((CheckBoxTableViewerManager)lotScrapQueryField.getQueryForm().getTableManager());
		try {
			List<Object> selectLotScraps = t.getCheckedObject(); 
			if (selectLotScraps.size() == 0) {
				UI.showError(Message.getString("mm.please_select_records"));
				return;
			}
			
			Set<String> actionCodeSet = new HashSet<String>();
			List<LotScrap> lotScraps = new ArrayList<LotScrap>();
			for (Object selectLotScrap : selectLotScraps) {
				LotScrap sbd = (LotScrap) selectLotScrap;
				lotScraps.add(sbd);
				actionCodeSet.add(sbd.getActionCode());
			}
			
			if (actionCodeSet.size() > 1) {
				if (!UI.showConfirm(Message.getString("wip.pack_choice_multi_scrapcode"))) {
					return;
				}
			}
			
			if (lotScraps != null && lotScraps.size() > 0) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				Lot lot = carrierLotManager.packScrapLot(lotScraps, txtPackLot.getText(), packageType, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.pack_scrap_cuccess") + Message.getString("wip.pack_scrap_new_lot")  + lot.getLotId());
				txtPackLot.setText(lot.getLotId().toUpperCase());
				Lot packedLot = searchLot(lot.getLotId().toUpperCase());
				txtPackLot.getTextControl().selectAll();
				SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				txtPackDate.setText(sdf.format(packedLot.getCreated()).toString());
				List<LotScrap> allLotScraps = (List<LotScrap>)(List)this.lotScrapQueryField.getQueryForm().getTableManager().getInput();
				allLotScraps.removeAll(lotScraps);
				List<LotScrap> packlotScraps = searchLotScrapByParentLotRrn(packedLot.getObjectRrn());
				for(LotScrap lotScrap : packlotScraps) {
				    Lot packlot = searchLot(lotScrap.getLotId());
				    if (packlot != null) {
				        lotScrap.setAttribute1(packlot.getWoId());
				        lotScrap.setAttribute2(packlot.getCustomerCode());
				    }
				}
				List<LotScrap> scraps = Lists.newArrayList();
				scraps.addAll(allLotScraps);
				this.lotScrapQueryField.getQueryForm().getTableManager().setInput(scraps);
				this.lotScrapQueryField.getQueryForm().getTableManager().refresh();
				this.packIdField.getListTableManager().setInput(packlotScraps);
			}
		} catch (Exception e) {
			lotScrapQueryField.getQueryForm().getTableManager().refresh();
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void unpackAdapter(Object object) {
		try {
			List<Object> selectLotScraps = packIdField.getListTableManager().getCheckedObject(); 
			if (selectLotScraps.size() == 0) {
				UI.showError(Message.getString("mm.please_select_records"));
				return;
			}
			
			List<LotScrap> lotScraps = new ArrayList<LotScrap>();
			for (Object selectLotScrap : selectLotScraps) {
				LotScrap sbd = (LotScrap) selectLotScrap;
				lotScraps.add(sbd);			
			}			
			
			if (lotScraps != null && lotScraps.size() > 0) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				Lot packedLot = searchLot(txtPackLot.getText().toUpperCase());
				packedLot = carrierLotManager.unPackScrapLot(packedLot, lotScraps, packageType, Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.unpack_scrap_cuccess"));
				List<LotScrap> allLotScraps = (List<LotScrap>)(List)packIdField.getListTableManager().getInput();
				allLotScraps.removeAll(lotScraps);
				packIdField.getListTableManager().setInput(allLotScraps);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void printAdapter(Object object) {
        try {
        	String txtPackLots = txtPackLot.getText();
        	if (txtPackLots != null && txtPackLots.length() != 0) {
        		LotManager lotManager = Framework.getService(LotManager.class);
        		Lot sourceLot = lotManager.getLotByLotId(Env.getOrgRrn(),txtPackLots.toUpperCase());
    			if (sourceLot != null) {
    				AppEvent appEvent = new AppEvent();
					appEvent.setEventParam1("PRINT"); //actionType
					appEvent.setEventParam2("LotList"); //objectType
					appEvent.setEventParam3(sourceLot); //object
					AppEventManager.postEvent(AppEvent.EVENT_ID_PRINTLABEL, appEvent);
    			} else {
    				UI.showWarning(Message.getString("wip.lot_not_found") +
    						Message.getString("mm.package_id"));
    				return;
    			}
        	} else {
        		UI.showWarning(Message.getString("mm.package_type_object_has_no_choice") +
						Message.getString("mm.package_id"));
				return;
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

	private void clearAdapter(Object object) {
		txtPackLot.setText("");
		txtPackDate.setText("");
		lotScrapQueryField.getQueryForm().getTableManager().setInput(Lists.newArrayList());
		packIdField.getListTableManager().setInput(Lists.newArrayList());
	
	}

	public void refresh() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			String whereClause = " 1 = 1 " + lotScrapQueryField.getQueryForm().getQueryForm().createWhereClause();
			whereClause = StringUtil.relpaceWildcardCondition(whereClause);
			if (lotScrapQueryField.getQueryForm().getTableManager().getADTable() != null && this.lotScrapQueryField.getQueryForm().getTableManager().getADTable().getWhereClause() != null
					&& this.lotScrapQueryField.getQueryForm().getTableManager().getADTable().getWhereClause().trim().length() > 0) {
				whereClause = whereClause != null && whereClause.trim().length() > 0
						? "( " + this.lotScrapQueryField.getQueryForm().getTableManager().getADTable().getWhereClause() + ") AND ( " + whereClause + ")"
						: "( " + this.lotScrapQueryField.getQueryForm().getTableManager().getADTable().getWhereClause() + ")";
			} 
			List<LotScrap> currentList = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class, Env.getMaxResult(), whereClause, "");
			
			for(LotScrap lotScrap : currentList) {
			    Lot lot = searchLot(lotScrap.getLotId());
			    if (lot != null) {
			        lotScrap.setAttribute1(lot.getWoId());
			        lotScrap.setAttribute2(lot.getCustomerCode());
			    }
			}
			lotScrapQueryField.getQueryForm().getTableManager().setInput(currentList);
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public List<LotScrap> searchLotScrapByParentLotRrn(Long parentLotRrn) {
		try {			
			ADManager adManager = Framework.getService(ADManager.class);	
			List<LotScrap> lotScraps = adManager.getEntityList(Env.getOrgRrn(), LotScrap.class,
					Integer.MAX_VALUE, " parentLotRrn = " + parentLotRrn + " AND parentLotRrn is not null ", null);
			return lotScraps;
		} catch (Exception e) {
			logger.warn("ScrapPkgSection searchLotScrapByParentLotRrn(): Lot isn' t exsited!");
		}
		return null;
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			return lot;
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
}