package com.glory.mes.wip.lot.pack.query;

import java.util.List;

import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.mm.PackageObjectDetail;
import com.google.common.collect.Lists;

public class PackQueryManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.pack.query.PackQueryManagerEditor";

	public static final String FIELD_PACKQUERY = "packQuery";
	public static final String FIELD_INFO = "info";

	public static final String BUTTON_QUERY = "query";
	public static final String BUTTON_REFRESH = "refresh";

	protected ListTableManagerField infoField;
	protected EntityForm entityForm;
	protected TextField textField0, textField1;
	
	public String packageHierarchyName;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		infoField = form.getFieldByControlId(FIELD_INFO, ListTableManagerField.class);
		entityForm  = (EntityForm)infoField.getSlotForm();
		
		textField0 = entityForm.getFieldByControlId("objectId0", TextField.class);
		textField1 = entityForm.getFieldByControlId("objectId1", TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_QUERY), this::queryAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		SquareButton sourceTotalButton = (SquareButton) textField1.getButtonByControl("query");
		sourceTotalButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				packQueryQuery();
			}
		});
		ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);
		packageHierarchyName = adEditor.getPARAM3();
	}

	private void packQueryQuery() {
//		if (!entityForm.saveToObject()){
//			return;
//		}
		refreshAdapter(null);
	}

	
	private void queryAdapter(Object object) {
		refreshAdapter(object);
	}

	private void refreshAdapter(Object object) {
		try {
			List<PackageObjectDetail> details = Lists.newArrayList();
			
			PackageObjectDetail lot = (PackageObjectDetail) entityForm.getObject();	
			String level1Id = (String) textField1.getText();
//			String level2Id = (String) lot.getObjectId2();
//			String level3Id = (String) lot.getObjectId3();
			String level0Id = (String) textField0.getText();
			
			String queryId = null;
			int count = 0;
			int packLevel = 0;
			if (!StringUtil.isEmpty(level0Id)) {
				packLevel = 0;
				count++;
				queryId = level0Id;
			}
			if (!StringUtil.isEmpty(level1Id)) {
				packLevel = 1;
				count++;
				queryId = level1Id;
			}
//			if (!StringUtil.isEmpty(level2Id)) {
//				packLevel = 2;
//				count++;
//				queryId = level2Id;
//			}
//			if (!StringUtil.isEmpty(level3Id)) {
//				packLevel = 3;
//				count++;
//				queryId = level3Id;
//			}
			
			if (StringUtil.isEmpty(queryId)) {
				UI.showInfo(Message.getString("wip.pack_query_need_id"));
				return;
			}
			
			if (count > 1) {
				UI.showInfo(Message.getString("wip.pack_query_only_one_id"));
				return;
			}
					
			MLotManager mLotManager = Framework.getService(MLotManager.class);
			details = mLotManager.getPackageObjectDetailsById(Env.getOrgRrn(), packageHierarchyName, packLevel, queryId, null);
			infoField.getListTableManager().setInput(details);
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

}