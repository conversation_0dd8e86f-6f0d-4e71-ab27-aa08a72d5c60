package com.glory.mes.wip.lot.pack.relationquery;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.model.PackageDetail;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

/**
 * 
 * �ȱ�������ʹ��PackQuery����
 *
 */
@Deprecated
public class PackRelationQuerySection extends QueryEntityListSection {
	
	public PackRelationQuerySection(ListTableManager tableManager) {
		super(tableManager);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	@Override
	protected void queryAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (!getQueryForm().validate()){
				return;
			}
			
			String lotId = "";
			
			LinkedHashMap<String, IField> fields = getQueryForm().getFields();
	        for(IField f : fields.values()) {
	        	ADField adField = (ADField)f.getADField();
	        	if ("lotId".equals(adField.getName())) {
	        		lotId = f.getValue().toString();
	        		break;
	        	}
	        }

	        if (StringUtil.isEmpty(lotId)) {
	        	return;
	        }
	        
	        //���lot
	        LotManager lotManager = Framework.getService(LotManager.class);
	        Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
	        if (lot == null) {
	        	UI.showWarning(Message.getString("wip.lot_is_not_exist"));
	        	return;
	        }

        	List<Lot> lots = new ArrayList<Lot>();
        	
	        //���ýӿ�
        	CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
        	List<PackageDetail> packDetails = carrierLotManager.getParentPackageDetails(lot.getObjectRrn());
        	if (packDetails.size() > 0) {
	        	//������֯
	        	for (PackageDetail pd : packDetails) {
	        		if (PackageDetail.OBJECT_TYPE_LOT.equals(pd.getObjectType()) && pd.getSourceObject() != null) {
	        			Lot lt = (Lot)pd.getSourceObject();
	        			lt.setAttribute1(pd.getPackLotId()); //��װ���� -> wip.attribute1
	        			lt.setEndMainQty(pd.getPackMainQty()); //��װ������ -> wip.endMainQty
	        			lt.setAttribute2(pd.getPackageType()); //��װ���� -> wip.attribute2
	        			lt.setMainQty(pd.getSourceMainQty()); //Դ������ -> wip.mainQty
	        			lots.add(lt);
					}        		
	        	}
        	}
        	this.getTableManager().setInput(lots);
        	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
