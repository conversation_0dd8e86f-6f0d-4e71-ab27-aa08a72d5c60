package com.glory.mes.wip.lot.pack.splitpack;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.SquareButton;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.custom.EnterPressComposite;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.mm.client.PackManager;
import com.glory.mes.mm.model.PackageRule;
import com.glory.mes.mm.model.PackageType;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.MergeManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.lot.pack.PackManagerEditor;
import com.glory.mes.wip.model.Lot;

public class SplitPackManagerEditor extends PackManagerEditor{

	private static final Logger logger = Logger.getLogger(SplitPackManagerEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.pack.splitpack.SplitPackManagerEditor";

	protected static final String FIELD_PACKQTY = "packQty";
	protected static final String BUTTON_REJECTPACK = "rejectPack";
	
	protected TextField sourcePackQtyTxt;
	protected PackageRule packageRule;

	@Override
	protected void createFormAction(GlcForm form) {
		ADEditor adEditor = ((ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR));
		packageTypeName = adEditor.getPARAM3();
		packageLayer = adEditor.getPARAM5();
		
		packDetailField = form.getFieldByControlId(FIELD_PACKDETAIL, GlcFormField.class);
		packLotField = form.getFieldByControlId(FIELD_PACKLOT, GlcFormField.class);
		rejectLotField = form.getFieldByControlId(FIELD_REJECTLOT, GlcFormField.class);
		lotInfoField = packDetailField.getFieldByControlId(FIELD_LOTINFO, CustomField.class);
		sourceLotTableField = packDetailField.getFieldByControlId(FIELD_PACKDETAILTABLE, ListTableManagerField.class);
		packLotTableField = packLotField.getFieldByControlId(FIELD_PACKLOTTABLE, ListTableManagerField.class);
		rejectLotTableField = rejectLotField.getFieldByControlId(FIELD_REJECTLOTTABLE, ListTableManagerField.class);
		
		lotIdEnterPressComposite = (EnterPressComposite) lotInfoField.getCustomComposite();
		
		EntityForm sourceLotEntityForm = (EntityForm) sourceLotTableField.getSlotForm();
		EntityForm packedLotEntityForm = (EntityForm) packLotTableField.getSlotForm();
		EntityForm rejectLotEntityForm = (EntityForm) rejectLotTableField.getSlotForm();
		
		sourceCountTxt = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		sourceTotalTxt = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		sourcePackQtyTxt = (TextField) sourceLotEntityForm.getFieldByControlId(FIELD_PACKQTY, TextField.class);
		
		packLotCountTxt = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		packLotTotalTxt = (TextField) packedLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		
		rejectCountTxt = (TextField) rejectLotEntityForm.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		rejectTotalTxt = (TextField) rejectLotEntityForm.getFieldByControlId(FIELD_TOTALQTY, TextField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_ENTERPRESS), this::lotIdEnterPressed);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PACKING), this::packingAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNPACKING), this::unpackingAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PRINT), this::printAdapter);
		
		SquareButton sourceTotalButton = (SquareButton) sourcePackQtyTxt.getButtonByControl(BUTTON_CLEAR);
		sourceTotalButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				sourceClearAdapter();
			}
		});
		
		SquareButton packTotalButton = (SquareButton) packLotTotalTxt.getButtonByControl(BUTTON_CLEAR);
		packTotalButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				packLotClearAdapter();
			}
		});
		
		SquareButton rejectTotalButton = (SquareButton) rejectTotalTxt.getButtonByControl(BUTTON_CLEAR);
		rejectTotalButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				rejectLotclearAdapter();
			}
		});
		
		subscribeAndExecute(eventBroker, rejectLotField.getFullTopic(BUTTON_REJECTPACK), this::rejectPackAdapter);
		
	}

	private void rejectPackAdapter(Object object) {
		try {
			List<Object> checkedList = rejectLotTableField.getListTableManager().getCheckedObject();
			if (checkedList.size() == 0) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			List<Lot> lstSourceLot = new ArrayList<Lot>();
			for (Object obj : checkedList) {
				lstSourceLot.add((Lot)obj);
			}

			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			PackageType newPackageType = getPackageType(lstSourceLot.get(0));
			if(newPackageType == null) {
				UI.showWarning(Message.getString("mm.pack_package_type_not_exist"));
				return;
			}
			List<Lot> lstCurrPackedLot = carrierLotManager.packLotMany(lstSourceLot, newPackageType.getName(), Env.getSessionContext());

			//ˢ���Ѱ�װ��Ϣ
			refreshPackForm(lstCurrPackedLot);
			//ˢ��β��
			refreshRejectForm(lstSourceLot);
			if (CollectionUtils.isNotEmpty(lstCurrPackedLot)) {
				UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipRejectPacked()));
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	
	}

	@Override
	protected void lotIdEnterPressed(Object object) {
		try {
			Event event = (Event) object;
			ADBase adBase = (ADBase) event.getProperty(GlcEvent.PROPERTY_DATA);
			Lot currentLot = (Lot) adBase;
			if (currentLot == null) {
				lotIdEnterPressComposite.getTxtLot().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				UI.showWarning(Message.getString("wip.lot_not_found"));
				return;
			} 
			if(BigDecimal.ZERO.compareTo(currentLot.getMainQty()) == 0){
				UI.showWarning(Message.getString("wip.lot_qty_must_more_than.zero"));
				return;
			} 
			
			if(getPackageType() != null) {
				PackageType newPackageType = getPackageType(currentLot);
				if(!getPackageType().getName().equals(newPackageType.getName())) {
					UI.showWarning(Message.getString("wip.lotId_packagin_are_inconsistent"));
					return;
				}
			}
			
			if (parentLot == null) {
				PackageType newPackageType = getPackageType(currentLot);
				if (newPackageType == null) {
					return;
				}
				if (!StringUtil.isEmpty(newPackageType.getSourceMainMatType())) { // Դ��װ����δά��ʱ����������
					if (!newPackageType.getSourceMainMatType().equals(currentLot.getMainMatType())) {
						UI.showWarning(Message.getString("mm.pack_mainmattype_not_allow"));
						return;
					}
				}
				
				if (!StringUtil.isEmpty(newPackageType.getSourceLotState())) {
					if (!currentLot.getState().equals(newPackageType.getSourceLotState())) {
						UI.showWarning(Message.getString("error.lot_state_not_allow"));
						return;
					}
				}

				if(newPackageType.getPackMainMatType() != null) {
					if (newPackageType.getPackMainMatType().equals(currentLot.getMainMatType())) {
						UI.showWarning(Message.getString("mm.packed"));
						return;
					}
				}

				setPackageType(newPackageType);
				parentLot = currentLot;
			}
			addSource(currentLot);
			lotIdEnterPressComposite.getTxtLot().getTextControl().setText("");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}

	@Override
	protected void sourceClearAdapter() {
		try {
			List<Object> checkedList = sourceLotTableField.getListTableManager().getCheckedObject();
			List<Lot> clearLots = new ArrayList<>();
			if (checkedList.size() == 0) {
                UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			BigDecimal sourceMainQty =  BigDecimal.ZERO;
			for (Object object1 : checkedList) {
				if (object1 instanceof Lot) {
					Lot clearLot = (Lot)object1;
					clearLots.add(clearLot);
					sourceMainQty = clearLot.getMainQty().add(sourceMainQty);
				}
			}
			
			List<Lot> sourceLots = new ArrayList<Lot>();
			sourceLots.addAll((List<Lot>) sourceLotTableField.getListTableManager().getInput());
			sourceLots.removeAll(clearLots);
			if (clearLots.contains(parentLot)) {
				if (sourceLots.size() <= 0) {
					parentLot = null;
				} else {
					parentLot = sourceLots.get(0);
				}
			}
			
			BigDecimal sourceMainTotalQty = BigDecimal.ZERO;
			if (!sourceTotalTxt.getText().trim().isEmpty()) {
				sourceMainTotalQty = new BigDecimal(sourceTotalTxt.getText());
			}
			sourceMainTotalQty = sourceMainTotalQty.subtract(sourceMainQty);
			BigDecimal packQty = sourceMainTotalQty.divide(packageRule.getSourceMainQty() != null ? packageRule.getSourceMainQty():BigDecimal.ZERO, 1, BigDecimal.ROUND_DOWN);
				
			sourceCountTxt.setText(String.valueOf(sourceLots.size()));
			sourceTotalTxt.setText(sourceMainTotalQty.toString());
			sourcePackQtyTxt.setText(packQty.toString());
			sourceLotTableField.getListTableManager().setInput(sourceLots);
			
			if (sourceLotTableField.getListTableManager().getInput().size() == sourceLotTableField.getListTableManager().getCheckedObject().size()) {
				setPackageType(null);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	
	}

	@Override
	protected void rejectLotclearAdapter() {
		List<Object> checkedList = rejectLotTableField.getListTableManager().getCheckedObject();
		List<Lot> clearLots = new ArrayList<Lot>();
		
		BigDecimal clearTotalQty =  BigDecimal.ZERO;
		for (Object object1 : checkedList) {
			if (object1 instanceof Lot) {
				Lot clearLot = (Lot)object1;
				clearLots.add(clearLot);
				clearTotalQty = clearLot.getMainQty().add(clearTotalQty);
			}
		}
		
		List<Lot> lots = new ArrayList<Lot>();
		lots.addAll((List<Lot>) rejectLotTableField.getListTableManager().getInput());
		lots.removeAll(clearLots);
		
		// �����ĺϼ�
		BigDecimal totalQty = BigDecimal.ZERO;
		if (!rejectTotalTxt.getText().trim().isEmpty()) {
			totalQty = new BigDecimal(rejectTotalTxt.getText());
		}
		totalQty = totalQty.subtract(clearTotalQty);

		rejectCountTxt.setText(String.valueOf(lots.size()));
		rejectTotalTxt.setText(totalQty.toString());
		rejectLotTableField.getListTableManager().setInput(lots);
	}
	
	@Override
	protected void packLot(List<Lot> lots) {
		try {
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			List<Lot> lstCurrPackedLot = carrierLotManager.packLotMany(lots, this.getPackageType(), Env.getSessionContext());
			// ���sourceTableManager��rejectSourceTableManager���������
			clearForm();
			// �����װ����
			this.setPackageType(null);
			refreshPackForm(lstCurrPackedLot);
			//ˢ��β��
			refreshRejectForm(lots);		
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	@Override
	protected void unPackLot(List<Lot> lots) {
		try {
			List<Lot> checkedPackLotList = getCheckedPackLotList();
			if (checkedPackLotList.size() == 0) {
                UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			List<Lot> unpackedSourceLots = new ArrayList<>();

			// �������
			CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			BigDecimal packMainQty = BigDecimal.ZERO;
			for (Lot packLot : checkedPackLotList) {
				if (packLot instanceof Lot) {
					//packLots.add(packLot);
					List<Lot> tmpLots = carrierLotManager.unpackLotMany(packLot, Env.getSessionContext());
					packMainQty = packLot.getMainQty().add(packMainQty);
					
					if (unpackedSourceLots.size() == 0) {
						unpackedSourceLots.addAll(tmpLots);
					} else {
						for (Lot lt : tmpLots) {
							List<Lot> any = unpackedSourceLots.stream().filter(p -> !lt.getLotId().equals(p.getLotId())).collect(Collectors.toList());
							if (any.size() > 0) {
								unpackedSourceLots.add(lt);
							}
						}
					}
				}
			}

			// ������ͳ��
			List<Lot> packLots = new ArrayList<Lot>();
			packLots.addAll((List<Lot>) getPackLotList());
			packLots.removeAll(checkedPackLotList);
			refreshForm(packLots);
			
			//ˢ��β��
			refreshRejectForm(unpackedSourceLots);
			UI.showInfo(Message.getString(WipExceptionBundle.bundle.WipUnPacked()));
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	@Override
	protected void addSource(ADBase adBase) {
		try {
			if (adBase instanceof Lot) {
				//δ��װ��lot
				List<Lot> lots = new ArrayList<Lot>();
				lots.addAll((List<Lot>) sourceLotTableField.getListTableManager().getInput());
				
				PackManager packManager = Framework.getService(PackManager.class);
				MergeManager mergeManager  = Framework.getService(MergeManager.class);
				// table �����lot
				Lot lot = (Lot) adBase;
				packageRule = packManager.getPackageRule(Env.getOrgRrn(), lot, PackageType.OBJECT_TYPE_LOT, getPackageType().getName(), null);
				if (lots != null && lots.size() > 0) {
					// �жϽ�����û���Ѵ��ڵ�lot
					for (Lot oldLot : lots) {
						if (oldLot.getLotId().equals(lot.getLotId())) {
							return;
						}
					}
					
					if (!PackageType.OBJECT_TYPE_LOT.equals(getPackageType().getObjectType())) {
						UI.showError(Message.getString("mm.pack_object_type_mlot"));
						return;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceMainMatType())
							&& !getPackageType().getSourceMainMatType().equals(lot.getMainMatType())) {
						UI.showError(Message.getString("mm.pack_mainmattype_not_allow"));
						return;
					}
					if (!StringUtil.isEmpty(getPackageType().getSourceSubMatType())
							&& !getPackageType().getSourceSubMatType().equals(lot.getSubMatType())) {
						UI.showError(Message.getString("mm.pack_submattype_not_allow"));
						return;
					}
					if (!StringUtil.isEmpty(packageRule.getMergeRuleName())) {
						// ����װ��������,�����ͨ�������rejectSourceTableManager
						List<Lot> childLots = new ArrayList<>();
						childLots.add(lot);
					
						MergeRuleResult pckResult = mergeManager.checkLotPackRule(parentLot, childLots, packageRule.getMergeRuleName(), false);

						if (!pckResult.isSuccess()) {
							refreshRejectForm(childLots);
							UI.showError(Message.getString("mm.pack_mergerule_not_allow"));
							return;
						} else {
							lots.add(lot);
						}
					}
				} else {
					lots.add(lot);
				}
				
				//�ۼ�lot��ĺϼ�
				BigDecimal mainTotalQty = BigDecimal.ZERO;
				if (!sourceTotalTxt.getText().trim().isEmpty()) {
					mainTotalQty = new BigDecimal(sourceTotalTxt.getText());
				}
				BigDecimal mainQty = lot.getMainQty();
				mainTotalQty = mainQty.add(mainTotalQty);
				
				BigDecimal countTotalQty = new BigDecimal(lots.size());
				BigDecimal packQty = mainTotalQty.divide(packageRule.getSourceMainQty(), 1, BigDecimal.ROUND_DOWN);
	
				// ���������,����sourceTableManager�ۼ�SourceCount��SourceMainQty
				sourceCountTxt.setText(countTotalQty.toString());
				sourceTotalTxt.setText(mainTotalQty.toString());
				sourcePackQtyTxt.setText(packQty.toString());
				sourceLotTableField.getListTableManager().setInput(lots);
			}
		} catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
	         return;
		}
	}
	
	@Override
	protected void clearForm() {
		sourceCountTxt.setText("0");
		sourceTotalTxt.setText("0");
		sourcePackQtyTxt.setText("0");
		sourceLotTableField.getListTableManager().setInput(new ArrayList<Object>());
		parentLot = null;
	}
}
