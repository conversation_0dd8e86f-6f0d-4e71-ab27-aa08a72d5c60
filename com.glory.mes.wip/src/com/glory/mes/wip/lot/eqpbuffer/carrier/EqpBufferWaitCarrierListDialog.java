package com.glory.mes.wip.lot.eqpbuffer.carrier;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotEquipmentUnit;
import com.glory.mes.wip.model.LotStateMachine;

public class EqpBufferWaitCarrierListDialog extends GlcBaseDialog {

	public static final String CONTROL_FIELD_LOTEQUIPMENTUNITLIST = "lotEquipmentUnitList";
	
	public ListTableManagerField lotEquipmentUnitTableManager;
	
	protected Equipment mianEquipment;
	protected String bufferId;
	
	public EqpBufferWaitCarrierListDialog(String adFormName, String authority, IEventBroker eventBroker, 
			Equipment mianEquipment, String bufferId) {
		super(adFormName, authority, eventBroker);
		this.mianEquipment = mianEquipment;	
		this.bufferId = bufferId;
	}
	
	protected void createFormAction(GlcForm form) {		
		lotEquipmentUnitTableManager = form.getFieldByControlId(CONTROL_FIELD_LOTEQUIPMENTUNITLIST, ListTableManagerField.class);
		
		init();
	}
	
	public Object getSelectedObject(){
		return lotEquipmentUnitTableManager.getListTableManager().getSelectedObject();
	}
	
	@Override
	protected void okPressed() {
		LotEquipmentUnit selectLot = (LotEquipmentUnit) getSelectedObject();	
		if (selectLot == null) {
			UI.showError(Message.getString("common.select_durable"));
			return;
		}
		super.okPressed();
	}

	private void init() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			
			List<Lot> dispLots = lotManager.getLotsByEqp(Env.getOrgRrn(), mianEquipment.getObjectRrn(),
					LotStateMachine.STATE_DISP, Env.getSessionContext());
			List<Lot> waitLots = lotManager.getLotsByEqp(Env.getOrgRrn(), mianEquipment.getObjectRrn(),
					LotStateMachine.STATE_WAIT, Env.getSessionContext());
			dispLots.addAll(waitLots);
			
			if (CollectionUtils.isNotEmpty(dispLots)) {
				List<LotEquipmentUnit> lotEquipmentUnits = lotManager.getLotEquipmentUnits(Env.getOrgRrn(), bufferId);
				if (lotEquipmentUnits != null && lotEquipmentUnits.size() > 0) {
					 List<Lot> existLots = new ArrayList<Lot>();
					 for (Lot waitLot : dispLots) {
						 for (LotEquipmentUnit lotEquipmentUnit : lotEquipmentUnits) {
							if (!StringUtil.isEmpty(waitLot.getDurable()) && waitLot.getDurable().equals(lotEquipmentUnit.getDurableId())) {
								existLots.add(waitLot);
								break;
							}
						 } 
					 }
					 dispLots.removeAll(existLots);
				}
			}	
			Map<String, List<Lot>> durableLotMap = null;
			if (CollectionUtils.isNotEmpty(dispLots)) {
				durableLotMap = dispLots.stream().filter(l -> !StringUtil.isEmpty(l.getDurable()))
						.collect(Collectors.groupingBy(Lot::getDurable));
			}
			
			List<LotEquipmentUnit> lotEquipmentUnits = new ArrayList<LotEquipmentUnit>();
			if (MapUtils.isNotEmpty(durableLotMap)) {
				for (String durableId : durableLotMap.keySet()) {
					LotEquipmentUnit lotEquipmentUnit = new LotEquipmentUnit();
					lotEquipmentUnit.setMainEquipmentId(mianEquipment.getEquipmentId());
					lotEquipmentUnit.setUnitId(bufferId);
					lotEquipmentUnit.setDurableId(durableId);
					if (MapUtils.isNotEmpty(durableLotMap) && durableLotMap.containsKey(durableId)) {
						lotEquipmentUnit.setLotIdList(durableLotMap.get(durableId).stream().map(Lot::getLotId).collect(Collectors.joining(";")));
					}
					
					lotEquipmentUnits.add(lotEquipmentUnit);
				}
			}
			
			lotEquipmentUnitTableManager.setValue(lotEquipmentUnits);
			lotEquipmentUnitTableManager.refresh();
		 } catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
			 return;
	    }
	}
	
}
