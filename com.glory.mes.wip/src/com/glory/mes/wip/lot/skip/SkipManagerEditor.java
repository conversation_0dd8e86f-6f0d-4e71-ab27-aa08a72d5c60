package com.glory.mes.wip.lot.skip;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.AuthorityToolItem;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.LotStateModel;

public class SkipManagerEditor extends GlcEditor { 
	
	private static final Logger logger = Logger.getLogger(SkipManagerEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.skip.SkipManagerEditor";

	protected static final String CONTROL_LOTID_ENTERPRESSED = "skipInfo-lotId-EnterPressed";
	protected static final String CONTROL_CARRIERID_ENTERPRESSED = "skipInfo-carrierId-EnterPressed";
	
	private static final String FIELD_SKIPINFO = "skipInfo";
	private static final String FIELD_PROCEDUREINFO = "procedureInfo";
	private static final String FIELD_COMMENT = "comment";

	private static final String BUTTON_SKIP = "skip";
	private static final String BUTTON_SPECIALSKIP = "specialSkip";
	private static final String BUTTON_REFRESH = "refresh";
	
	private static final String EVENT_ID = LotStateMachine.TRANS_SKIPLOT;

	protected CustomField skipInfoField;
	protected CustomField procedureInfoField;
	protected EntityFormField commentField;
	protected TextField commentField1;
	protected CarrierLotCustomComposite carrierLotCustomComposite;
	protected FlowCustomComposite flowCustomComposite;
	
	protected AuthorityToolItem itemSkip;
	protected AuthorityToolItem itemSpecialSkip;
	
	public HeaderText txtLotId, txtCarrierId;
	protected Boolean isCaseSensitive;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		//ע��Ĭ�ϻس��¼�
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_LOTID_ENTERPRESSED));
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED));
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_LOTID_ENTERPRESSED), this::lotIdEnterpressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED), this::carrierIdEnterpressed);
		
		skipInfoField = form.getFieldByControlId(FIELD_SKIPINFO, CustomField.class);
		procedureInfoField = form.getFieldByControlId(FIELD_PROCEDUREINFO, CustomField.class);
		commentField = form.getFieldByControlId(FIELD_COMMENT, EntityFormField.class);
		commentField1 = commentField.getFieldByControlId(FIELD_COMMENT, TextField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SKIP), this::skipAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SPECIALSKIP), this::specialSkipAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		carrierLotCustomComposite = (CarrierLotCustomComposite) skipInfoField.getCustomComposite();
		flowCustomComposite = (FlowCustomComposite) procedureInfoField.getCustomComposite();
		
		txtLotId = carrierLotCustomComposite.getTxtLotId();
		txtCarrierId = carrierLotCustomComposite.getTxtCarrierId();
		
		itemSkip = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_SKIP);
		itemSpecialSkip = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_SPECIALSKIP);
		itemSkip.setEnabled(false);
	}

	protected void lotIdEnterpressed(Object obj) {
		String lotId = txtLotId.getText();
		if (!StringUtil.isEmpty(lotId)) {
			if (!isLotIdCaseSensitive()) {
				lotId = lotId.toUpperCase();
			}
			getLotByLotId(lotId);
		}
	}
	
	protected void carrierIdEnterpressed(Object obj) {
		String carrierId = txtCarrierId.getText();
		if (!StringUtil.isEmpty(carrierId)) {
			getLotsByCarrierId(carrierId);
		}
	}
	
	public void getLotByLotId(String lotId) {
		try {
			Lot lot = searchLot(lotId);
			if (lot != null) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.add(lot);
				
				carrierLotCustomComposite.getLotTableManager().setInput(lots);
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				txtLotId.setText(lotId);
				// Ĭ��ȫѡ
				if (carrierLotCustomComposite.isCheckFlag()) {
					carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
				}
				carrierLotCustomComposite.getLotTableManager().refresh();
				flowCustomComposite.loadFlowTreeByLot(lot);
				refresh();
				carrierLotCustomComposite.loadComponentUnits(lot);
				if(!StringUtil.isEmpty(lot.getDurable())) {
					txtCarrierId.setText(lot.getDurable());
				}else {
					txtCarrierId.setText("");
				}	
				txtLotId.focusing();
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.setText(lotId);
				flowCustomComposite.loadFlowTreeByLot(null);
				
				carrierLotCustomComposite.getLotTableManager().setSelection(new StructuredSelection(new Object[] {new Lot()}));
				
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtLotId.warning();
			}
			refreshAdapter(lot);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isNotEmpty(lots)) {
					// Load Current Step
					Lot lotInfo = searchLot(lots.get(0).getLotId());
					
					carrierLotCustomComposite.getLotTableManager().setInput(lots);
					// Ĭ��ȫѡ
					if (carrierLotCustomComposite.isCheckFlag()) {
						for (Lot lot : lots) {
							carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
						}
					}
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					carrierLotCustomComposite.getLotTableManager().refresh();
					
					if (lots != null && lots.size() > 0) {
						flowCustomComposite.loadFlowTreeByLot(lotInfo);
						refresh();
					}
					
					carrierLotCustomComposite.loadComponentUnits(lots);	
					txtLotId.setText(lots.get(0).getLotId());
					txtCarrierId.focusing();
					refreshAdapter(lotInfo);
				} else {
					carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
					carrierLotCustomComposite.getLotTableManager().refresh();
				}
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				flowCustomComposite.loadFlowTreeByLot(null);
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtCarrierId.warning();
			}
			
			if (carrierLotCustomComposite.getLotDetailsForm() != null) {
				carrierLotCustomComposite.getLotDetailsForm().setObject(new Lot());
				carrierLotCustomComposite.getLotDetailsForm().loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public Lot searchLot(String lotId) {
		try {
			//���߱�������
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	private void skipAdapter(Object object) {
		skip(false);
	}
	
	private void specialSkipAdapter(Object object) {
		skip(true);
	}
	
	private void skip(Boolean isSpecial) {
		try {
			if (commentField.validate()) {
				Lot lot = LotProviderEntry.getLot(txtLotId.getText());
				if (lot != null) {
					LotManager lotManager = Framework.getService(LotManager.class);
					
					Step currentStep = null;
					if (lot.getProcessInstanceRrn() > 0 || lot.getProcessInstanceRrn() != null) {
						PrdManager prdManager = Framework.getService(PrdManager.class);
						StepState step = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
						currentStep = step.getUsedStep();
						
						//�������Timer,Timer���ᱻ���
						List<? extends FutureTimerInstance> lits = lotManager.getLotFutureTimerInstanceByEnd(lot, step, null);
						if (lits.size() > 0) {
							if (!UI.showConfirm(Message.getString("wip.skip_timerwillnull"))) {
								return;
							}
						}
					}
					
					String operator = Env.getUserName();
					if (isSpecial) {
						if (itemSpecialSkip.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
							operator = (String) itemSpecialSkip.getData(LotAction.ACTION_TYPE_OPERATOR);
						}
					} else {
						if (itemSkip.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
							operator = (String) itemSkip.getData(LotAction.ACTION_TYPE_OPERATOR);
						}
					}
					lot.setOperator1(operator);
					
					LotAction action = new LotAction();
					action.setActionComment(commentField1.getText());
					
					if (isSpecial) {
						if (currentStep != null && currentStep.getNoSkip()) {
							if (!UI.showConfirm(Message.getString("lot.confirm_skip_no_skip_step"))) {
								return;
							}
						}
						lot = lotManager.skipLot(lot, action, true, Env.getSessionContext());
					} else {
						if (!UI.showConfirm(Message.getString("common.whether_to_continue"))) {
							return;
						}
						lot = lotManager.skipLot(lot, action, Env.getSessionContext());
					}
					UI.showInfo(Message.getString("wip.skip_successed"));// ������ʾ��
					flowCustomComposite.loadFlowTreeByLot(lot);
					refresh();
					refreshAdapter(lot);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}


	private void refreshAdapter(Object object) {
		try {
			Lot lot = new Lot();
			if (object instanceof Lot) {
				lot = (Lot) object;
			} else {
				lot = LotProviderEntry.getLot(txtLotId.getText());
			}
			statusChanged(lot);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh() {
		if (txtLotId != null) {
			txtLotId.selectAll();
		}
		if (txtCarrierId != null) {
			txtCarrierId.selectAll();
		}
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
	public void statusChanged(Lot lot) {
		if (lot != null) {
			Boolean result = checkLotStateModel(lot);
			if (result == null) {
				//δִ�м�飬����ʹ��Ĭ�ϵļ��
				if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(lot.getState())) {
					itemSkip.setEnabled(true);
					itemSpecialSkip.setEnabled(true);
				} else {
					itemSkip.setEnabled(false);
					itemSpecialSkip.setEnabled(false);
				}
			} else if (result) {
				itemSkip.setEnabled(true);
				itemSpecialSkip.setEnabled(true);
			} else {
				itemSkip.setEnabled(false);
				itemSpecialSkip.setEnabled(false);
			}
		} else {
			itemSkip.setEnabled(false);
			itemSpecialSkip.setEnabled(false);
		}
	}
	
	/**
	 * ����Ϊ�¼�����LotStateModel,���û�������򷵻�null,��ʱ��Ҫ����¼���Ĭ�Ϲ���
	 * ����Ϊÿ���¼����ö���LotStateModel,ֻҪ����һ������Ϳ���ִ�д��¼�(Button����)
	 * LotStateModel��鷽ʽΪ���ε�ǰ״̬��Hold״̬,��LotStateModelһ��
	 */
	public Boolean checkLotStateModel(Lot lot) {
		try {
			if (!StringUtil.isEmpty(EVENT_ID)) {
				ADManager adManager = Framework.getService(ADManager.class);
				List<LotStateModel> models = adManager.getEntityList(Env.getOrgRrn(), LotStateModel.class, Integer.MAX_VALUE, " eventId = '" + EVENT_ID + "'", "");
				if (models.isEmpty()) {
					return null;
				}
				
				boolean isAllFail = true;
				for (LotStateModel model : models) {
					if (!StringUtil.isEmpty(model.getHoldState()) && !model.getHoldState().equals(lot.getHoldState())) {
						//���μ�鲻ͨ��,���������һ����
						continue;
					}
					if (!StringUtil.isEmpty(model.getLotState()) && !model.getLotState().equals(lot.getState())) {
						//���μ�鲻ͨ��,���������һ����
						continue;
					}
					isAllFail = false;
					break;
				}
				if(!isAllFail) {
					return true;
				} else {
					return false;
				}
			} else {
				return null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}

}