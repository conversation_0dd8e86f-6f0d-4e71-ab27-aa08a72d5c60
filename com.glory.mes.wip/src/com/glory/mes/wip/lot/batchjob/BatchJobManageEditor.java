package com.glory.mes.wip.lot.batchjob;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.osgi.service.event.Event;

import com.glory.common.fel.common.StringUtils;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.LotBatchJob;

public class BatchJobManageEditor extends GlcEditor{
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.batchjob.BatchJobManageEditor";

	private QueryFormField queryList; 
	private static final String FIELD_TABLE = "queryList";

	@Override
	protected void createFormAction(GlcForm form) { 
		super.createFormAction(form);
		queryList = form.getFieldByControlId(FIELD_TABLE, QueryFormField.class);
		queryList.unsubscribeDefaultEvent(GlcEvent.EVENT_SELECTION_CHANGED);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_TABLE,GlcEvent.EVENT_TABLE_CHECK), this::checkAdaptor);
		subscribeAndExecute(eventBroker,  form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdaptor);
	}
	
	@SuppressWarnings("unchecked")
	private void checkAdaptor(Object obj) {
		Event evt = (Event)obj;
		CheckBoxTableViewerManager checkboxTable = (CheckBoxTableViewerManager)queryList.getQueryForm().getTableManager();
		Object[] eventObjs = (Object[])evt.getProperty(GlcEvent.PROPERTY_DATA);
		List<LotBatchJob> allBatchJobs = (List<LotBatchJob>)queryList.getQueryForm().getTableManager().getInput();
		List<LotBatchJob> groupBatchJobs = new ArrayList<>(); 
		LotBatchJob currentJob = ((List<LotBatchJob>)eventObjs[1]).get(0);
		
		if ( CollectionUtils.isNotEmpty(allBatchJobs)) { 
			groupBatchJobs = allBatchJobs.stream().filter (it -> StringUtils.equals(it.getResumeGroup(),
					currentJob.getResumeGroup()) && it.getObjectRrn() != currentJob.getObjectRrn()).collect(Collectors.toList());
		}
		if(Boolean.valueOf(eventObjs[0].toString())) {
			for (LotBatchJob lotBatchJob : groupBatchJobs) {
				checkboxTable.checkObject(lotBatchJob);
			}
		}else {
			for (LotBatchJob lotBatchJob : groupBatchJobs) {
				checkboxTable.unCheckObject(lotBatchJob);
			} 
		}
	} 
	
	private void deleteAdaptor(Object obj) {
		List<Object> checkedBatchJobs =  queryList.getCheckedObjects();
		if(checkedBatchJobs != null) {
			if(UI.showConfirm(Message.getString("wip.batch_job_delete_confirm"))){
				LotManager lotManager;
				try {
					lotManager = Framework.getService(LotManager.class);
					lotManager.removeBatchJobs(checkedBatchJobs.stream().map(it -> (LotBatchJob)it).collect(Collectors.toList())
							,Env.getSessionContext());
					queryList.getQueryForm().refresh();
				} catch (Exception e) { 
					ExceptionHandlerManager.asyncHandleException(e); 
				}
				
			}
		}
	}
}
