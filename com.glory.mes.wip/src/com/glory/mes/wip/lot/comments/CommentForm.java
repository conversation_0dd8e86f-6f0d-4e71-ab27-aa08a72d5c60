package com.glory.mes.wip.lot.comments;


import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.wip.model.Lot;

public class CommentForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(CommentForm.class);


	private IField fieldComment;
//	private static final String LOTPRO = "LotPriority";
//	private static final String LOTPRO_ID = "priority";
	private static final String COMMENT = "Comment";
	private static final String COMMENT_ID = "lotComment";
	public String comment;
	public CommentForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	public CommentForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public CommentForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public CommentForm(Composite parent, int style, Object object, ADTable table,
			IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}	
	
	@Override
	public void addFields() {
		super.addFields();
		try {	
			
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", "");
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
//			fieldLotPro = createUserRefList(LOTPRO_ID, LOTPRO, LOTPRO, true);
			fieldComment = createTextArea(COMMENT_ID, Message.getString("wip.comment"), "", 340);
			ADField adField = new ADField();
			adField.setIsSameline(true);
			fieldComment.setADField(adField);
			
//			addField(LOTPRO,fieldLotPro);
			addField(COMMENT, fieldComment);
		} catch (Exception e) {
			logger.error("LotProForm : Init listItem", e);
		}
	}

	@Override
	public void loadFromObject() {
		super.loadFromObject();
		
		fieldComment.setEnabled(true);
	}

//	@Override
//	public void setEnabled() {
//		if (object != null && object instanceof ADBase) {
//			ADBase base = (ADBase) object;
//			for (IField f : fields.values()) {
//				ADField adField = adFields.get(f.getId());
//				if (adField != null && !adField.getIsEditable()) {
//					if (base.getObjectRrn() == null || base.getObjectRrn() == 0) {
//						f.setEnabled(true);
//					} else {
//						f.setEnabled(false);
//					}
//				}
//			}
//			fieldComment.setEnabled(true);
//		}
//	}



	@Override
	public boolean saveToObject() {
		if (object != null) {
			Lot lot = (Lot)object;
			PropertyUtil.setProperty(lot, fieldComment.getId(),
					fieldComment.getValue());
			return true;
		}
		return false;
	}

	@Override
	public boolean validate() {
		boolean validFlag = true;
		return validFlag;
	}

}

