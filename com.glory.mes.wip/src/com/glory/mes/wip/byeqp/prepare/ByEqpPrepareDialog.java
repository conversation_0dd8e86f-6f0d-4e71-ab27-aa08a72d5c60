package com.glory.mes.wip.byeqp.prepare;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.edc.client.EDCManager;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.ConstraintManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotPrepareManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public abstract class ByEqpPrepareDialog extends GlcBaseDialog {

	private static final Logger logger = Logger.getLogger(ByEqpPrepareDialog.class);
	
	private Consumer<ByEqpPrepareDialog> closeAdaptor;
	
	public ADManager adManager;
	public RASManager rasManager;
	public EDCManager edcManager;
	public SysParameterManager sysParamManager;
	public ConstraintManager constraintManager;
	public LotManager lotManager;
	public LotPrepareManager prepareManager;
	
	/**
	 * ��ǰ�豸
	 */
	public Equipment currentEqp;
	
	public boolean isCheckBatchControl = true;
	
	public ByEqpPrepareDialog() {
		super();
	}
	
	public ByEqpPrepareDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
	}
	
	protected void createFormAction(GlcForm form) {				
		try {
			adManager = Framework.getService(ADManager.class);
			sysParamManager = Framework.getService(SysParameterManager.class);
			rasManager = Framework.getService(RASManager.class);
			edcManager = Framework.getService(EDCManager.class);
			constraintManager = Framework.getService(ConstraintManager.class);
			lotManager = Framework.getService(LotManager.class);
			prepareManager = Framework.getService(LotPrepareManager.class);				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected abstract List<Lot> getBatchLotList();
	
	protected abstract String getJobId();
	
	/**
	 * ����LotJobPrepare
	 * @param object
	 */
	protected boolean createJobAdapter(Object object) {
		try {		
			List<Lot> checkedBatchLots = getBatchLotList();		
			if (CollectionUtils.isNotEmpty(checkedBatchLots)) {
				// �������²�ѯ���µ�PPID��Reticle		
				for (Lot lot : checkedBatchLots) {	
					// ����ȡ����Reticle��Logic Recipe
					List<Lot> checkList = Lists.newArrayList(lot);
					if (!StringUtil.isEmpty(lot.getBatchId())) {
						checkList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
					}
					Lot lotRecipeMask = ByEqpUtil.getEquipmentRecipeAndReticle(checkList, currentEqp);
					String mask = DBUtil.toString(lotRecipeMask.getMask());
					String logicRecipe = DBUtil.toString(lotRecipeMask.getRecipeName());
					
					
					//У���豸��LogicRecipe��PPID�Ƿ�ƥ�䣬recipe�Ƿ�Ϊhold
					if(!StringUtil.isEmpty(lotRecipeMask.getRecipeName())) {
						lotManager.validRecipeEquipment(Env.getOrgRrn(), currentEqp.getEquipmentId(), lotRecipeMask.getRecipeName(), lotRecipeMask.getEquipmentRecipe());
					}		
					
					lot.setEquipmentMask(lotRecipeMask.getEquipmentMask());
					lot.setEquipmentRecipe(lotRecipeMask.getEquipmentRecipe());
					String lotMask =  DBUtil.toString(lot.getMask());
					String lotLogicRecipe = DBUtil.toString(lot.getRecipeName());
					// ������ֲ�һ�£���Ҫ����Աȷ���Ƿ�ʹ�õ����µ�Reticle��Recipe��ˢ�½������ܻ��һ�£�
					if (!Objects.equals(mask, lotMask)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_reticle"), lotMask, mask, lot.getLotId()))) {
							return false;
						}
					}
					if (!Objects.equals(logicRecipe, lotLogicRecipe)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_recipe"), lotLogicRecipe, logicRecipe, lot.getLotId()))) {
							return false;
						}
					}
					
					// ��������Ҫ���¸�ֵ
					lot.setMask(mask);
					lot.setRecipeName(logicRecipe);
				}
				
				String jobId = getJobId();
				//���JobID�Ƿ��ظ�
				if (!StringUtil.isEmpty(jobId)) {
					List<LotPrepare> prepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), jobId, false);
					if (CollectionUtils.isNotEmpty(prepares)) {
						UI.showError(Message.getString("wip.lot_prepare_jobid_exist"));
						return false;
					}
				}
				
				//��IsNotCheckBatchControl���ش�ʱ����̨����У��EquipmentBatchControl���˹�Prepare��ʱ���Ϊǰ̨У�飬У�鲻ͨ����������û���ѡ���Ƿ�ǿ��PrePare
				Boolean isCreateLotPrepare = true;
		        if (isCheckBatchControl) {
		        	try {
		        		ConstraintManager constraintManager = Framework.getService(ConstraintManager.class);
		        		constraintManager.checkEquipmentCapacity(Env.getOrgRrn(), Arrays.asList(currentEqp), checkedBatchLots, true);
					} catch (ClientParameterException e) {
						String message = String.format(Message.getString(e.getErrorCode()), e.getParameters()[0], e.getParameters()[1])
								+ Message.getString("wip_whether_to_continue_creating");
						if (UI.showConfirm(message)) {
							isCreateLotPrepare = true;
						} else {
							isCreateLotPrepare = false;
						}
					}
		        }
		        
		        if (isCreateLotPrepare) {
		        	prepareManager.createLotJobPrepare(currentEqp.getEquipmentId(), jobId, checkedBatchLots, Env.getSessionContext());
		        	UI.showInfo(Message.getString("wip.byeqp_create_prepare_job_success"));
		        	
		        	refreshAdapter(object);
		        	return true;
		        }
			}
			return false;
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : createJobAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
			return false;
		}
	}
	
	protected abstract List<Object> getCheckedLotPrepareList();
	
	/**
	 * ȡ��LotJobPrepare
	 * @param object
	 */
	protected void cancelJobAdapter(Object object) {
		try {
			List<Object> checkedLotPrepares = getCheckedLotPrepareList();
			if (CollectionUtils.isNotEmpty(checkedLotPrepares)) {
				List<LotPrepare> lotPrepares = checkedLotPrepares.stream().map(o -> ((LotPrepare) o)).collect(Collectors.toList());
				
				List<String> jobIds = lotPrepares.stream().map(LotPrepare::getJobId).distinct().collect(Collectors.toList());
				String jobIdsStr = jobIds.stream().collect(Collectors.joining(","));
				if (UI.showConfirm(String.format(Message.getString("wip.byeqp_sure_cancel_jobs"), jobIdsStr))) {
					prepareManager.cancelLotJobPrepare(currentEqp.getEquipmentId(), jobIds, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : cancelJobAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	protected void refreshAdapter(Object object) {
		
	}
	
	protected abstract String getChangeJobId();
	
	/**
	 * �޸�LotJobPrepare��
	 * @param object
	 */
	protected void changeJobIdAdapter(Object object) {
		try {
			List<Object> checkedLotPrepares = getCheckedLotPrepareList();
			if (CollectionUtils.isNotEmpty(checkedLotPrepares)) {
				// ���ȼ��ֻ��ͬʱ�޸�һ��Job
				Set<String> modifyJobIds = checkedLotPrepares.stream().map(o -> ((LotPrepare)o).getJobId()).collect(Collectors.toSet());
				if (modifyJobIds.size() > 1) {
					UI.showInfo(Message.getString("wip.lot_prepare_jobid_modify_one"));
					return;
				}
							
				String jobId = getChangeJobId();
				// ����޸ĵ���������Ƿ��Ѿ�����
				if (!StringUtil.isEmpty(jobId)) {
					List<LotPrepare> prepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), jobId, false);
					if (CollectionUtils.isNotEmpty(prepares)) {
						UI.showError(Message.getString("wip.lot_prepare_jobid_exist"));
						return;
					}
				} else {
					UI.showInfo(Message.getString("wip.lot_prepare_jobid_modify_input"));
					return;
				}
				
				List<LotPrepare> lotPrepares = checkedLotPrepares.stream().map(o -> ((LotPrepare) o)).collect(Collectors.toList());			
				prepareManager.changePrepareId(lotPrepares, jobId, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				
				refreshAdapter(object);
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : changeJobIdAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<ByEqpPrepareDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<ByEqpPrepareDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {	
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonClose()), false, null);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
	}

	public Equipment getCurrentEqp() {
		return currentEqp;
	}

	public void setCurrentEqp(Equipment currentEqp) {
		this.currentEqp = currentEqp;
	}

	public boolean isCheckBatchControl() {
		return isCheckBatchControl;
	}

	public void setCheckBatchControl(boolean isCheckBatchControl) {
		this.isCheckBatchControl = isCheckBatchControl;
	}
	
}
