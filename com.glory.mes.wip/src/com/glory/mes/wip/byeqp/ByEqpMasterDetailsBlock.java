package com.glory.mes.wip.byeqp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcMasterDetailsBlock;
import com.glory.framework.base.entitymanager.glc.GlcPageProvider;
import com.glory.mes.wip.byeqp.extensionpoint.ByEqpExtensionPoint;
import com.glory.mes.wip.byeqp.extensionpoint.IByEqpPage;
import com.google.common.collect.Maps;

public class ByEqpMasterDetailsBlock extends GlcMasterDetailsBlock {
	
	protected Set<String> eqpProcessModeSet;
	
	protected List<IByEqpPage> detailsPages = new ArrayList<IByEqpPage>();
	
	public ByEqpMasterDetailsBlock(ADTab masterTab) {
		super(masterTab, null);
	}
	
	@Override
	protected void registerPages(DetailsPart detailsPart) {
		detailsPages.clear();
		eqpProcessModeSet = ByEqpExtensionPoint.getInstance().getRegistry().keySet();
		for (String eqpProcessMode :  eqpProcessModeSet) {
			IByEqpPage page = ByEqpExtensionPoint.getInstance().getPage(eqpProcessMode);
			page.setAuthority(glcForm.getAuthority());
			page.setEventBroker(glcForm.getEventBroker());
			page.setRootForm(glcForm);
			detailsPart.registerPage(eqpProcessMode, page);
			detailsPages.add(page);
		}
		
		detailsPart.setPageProvider(new GlcPageProvider());
		
		if (eqpProcessModeSet.size() >= 1) {
			//Ĭ����ʾ
			Map<String, Object> eventData = Maps.newHashMap();
			eventData.put(GlcEvent.PROPERTY_PAGE_KEY, ByEqpPage.DEFAULT_PROCESS_MODE);	
			glcForm.postEvent(null, GlcEvent.EVENT_GLC_MDFIRESECTION, eventData);
		}
	}
	
	//�����¼�
	public void preDestory() {
		if (detailsPages.size() > 0) {
			for (IByEqpPage page : detailsPages) {
				if (page instanceof ByEqpPage) {
					((ByEqpPage)page).preDestory();
				}
			}
		}
	}

}
