package com.glory.mes.wip.byeqp.extensionpoint;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

public class ByEqpExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(ByEqpExtensionPoint.class);
	
	private static final ByEqpExtensionPoint instance = new ByEqpExtensionPoint();
	
	private static Map<String, IConfigurationElement> registry = new HashMap<String, IConfigurationElement>();

	public final static String X_POINT = "com.glory.mes.wip.byeqp";
    
    public final static String E_MANAGER = "manager";
    public final static String A_EQP_PROCESSMODE = "eqpProcessMode";
    public final static String A_FORM_NAME = "formName";
    public final static String A_CLASS = "class";
    
	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			try {
				IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
				for (int j = 0; j < configElements.length; j++) {
					if (E_MANAGER.equals(configElements[j].getName())) {
						String eqpProcessMode = configElements[j].getAttribute(A_EQP_PROCESSMODE);
						registry.put(eqpProcessMode, configElements[j]);

					}
				}
			} catch (Exception e) {
				logger.error("ByEqpExtensionPoint : init ", e);
			}
		}			
	}
    
    public static ByEqpExtensionPoint getInstance() {
    	return instance;
    }

    public IByEqpPage getPage(String eqpProcessMode) {
    	try {
	    	if (registry.containsKey(eqpProcessMode)) {
	    		IByEqpPage page = (IByEqpPage)registry.get(eqpProcessMode).createExecutableExtension(A_CLASS);
	    		page.setAdFormName(registry.get(eqpProcessMode).getAttribute(A_FORM_NAME));
	    		return page;
	    	}
    	} catch (Exception e) {
			logger.error("ByEqpExtensionPoint : init ", e);
		}
    	return null;
    }
    
    public static IConfigurationElement getRegistry(String eqpProcessMode) {
		return registry.get(eqpProcessMode);
	}
    
    public static Map<String, IConfigurationElement> getRegistry() {
		return registry;
	}

	public static void setRegistry(Map<String, IConfigurationElement> registry) {
		ByEqpExtensionPoint.registry = registry;
	}
	
}
