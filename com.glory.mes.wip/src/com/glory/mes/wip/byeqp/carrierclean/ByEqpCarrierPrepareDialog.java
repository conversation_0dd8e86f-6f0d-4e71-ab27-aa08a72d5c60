package com.glory.mes.wip.byeqp.carrierclean;

import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.CarrierPrepare;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.framework.core.exception.ExceptionBundle;

public class ByEqpCarrierPrepareDialog extends GlcBaseDialog {
	
	private static final Logger logger = Logger.getLogger(ByEqpCarrierPrepareDialog.class);
	
	public static final String FIELD_LEFTFROM = "leftFrom";
	protected static final String FIELD_CARRIERLIST = "carrierList";
	protected static final String FIELD_PREPARELIST = "prepareList";
	
	public static final String FIELD_JOBID = "jobId";
	public static final String FIELD_COMPONENTID = "componentId";
	
	private static final String BUTTON_PREPARE_CREATE = "preparecreate";
	private static final String BUTTON_PREPARE_DELETE = "preparedelete";
	private static final String BUTTON_PREPARE_MODIFY = "preparemodify";
	
	private ListTableManagerField carrierListField;
	private ListTableManagerField prepareField;
	private TextField leftJobIdField , rightJobIdField;
	private Text leftJobIdText, rightJobIdText;
	
	private ByEqpCarrierCleanPage byEqpCarrierCleanPage;
	
	private Equipment equipment;
	
	private Consumer<ByEqpCarrierPrepareDialog> closeAdaptor;
	
	public ByEqpCarrierPrepareDialog(String adFormName, String authority, IEventBroker eventBroker, ByEqpCarrierCleanPage byEqpCarrierCleanPage) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
		this.byEqpCarrierCleanPage = byEqpCarrierCleanPage;
	}
	
	protected void createFormAction(GlcForm form) {
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_PREPARE_CREATE), this::createAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PREPARE_DELETE), this::removeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PREPARE_MODIFY), this::modifyAdapter);
		
		carrierListField = form.getFieldByControlId(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_CARRIERLIST, ListTableManagerField.class);
		prepareField = form.getFieldByControlId(FIELD_PREPARELIST, ListTableManagerField.class);
		leftJobIdField = form.getFieldByControlId(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_JOBID, TextField.class);
		rightJobIdField = form.getFieldByControlId(FIELD_JOBID, TextField.class);
		
		leftJobIdText = leftJobIdField.getTextControl();
		rightJobIdText = rightJobIdField.getTextControl();
		GridData gText = new GridData();
		gText.widthHint = 316;
		leftJobIdText.setLayoutData(gText);
		rightJobIdText.setLayoutData(gText);
		
		this.equipment = (Equipment) propValues.get("leftFrom-equipmentInfo");
	}

	private void createAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			ListTableManager tableManager = carrierListField.getListTableManager();
			List<Object> objs = tableManager.getCheckedObject();
			
			if (CollectionUtils.isNotEmpty(objs)) {
				// �������²�ѯ���µ�PPID��Reticle
				List<Carrier> carriers = objs.stream().map(o -> ((Carrier)o)).collect(Collectors.toList());
				
				DurableManager dureableManager = Framework.getService(DurableManager.class);
				String jobId = leftJobIdField.getText();
				// ���JobID�Ƿ��ظ�
				if (!StringUtil.isEmpty(jobId)) {
					if (!ValidatorFactory.isValid(DataType.INTEGER, leftJobIdField.getText())) {
						ADField adField = (ADField) leftJobIdField.getADField();
						form.getMessageManager().addMessage(adField.getName() + "common.isvalid", 
								String.format(Message.getString("common.isvalid"), I18nUtil.getI18nMessage(adField, "label"), DataType.INTEGER), null,
								MsgType.MSG_ERROR.getIndex(), leftJobIdField.getControls()[leftJobIdField.getControls().length - 1]);
						return;
					}
					List<CarrierPrepare> carrierPrepares = dureableManager.getCarrierPrepareJobs(Env.getOrgRrn(), equipment.getEquipmentId(), null, jobId, true);
					if (CollectionUtils.isNotEmpty(carrierPrepares)) {
						UI.showError(Message.getString("wip.lot_prepare_jobid_exist"));
						return;
					}
				}
				
				dureableManager.createCarrierJobPrepare(equipment.getEquipmentId(), jobId, carriers, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.byeqp_create_prepare_job_success"));
				updateList();
				leftJobIdField.setValue(null);
				leftJobIdField.refresh();
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : createAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	private void removeAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			List<Object> objs = prepareField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isNotEmpty(objs)) {
				DurableManager dureableManager = Framework.getService(DurableManager.class);
				List<CarrierPrepare> carrierPrepares = objs.stream().map(o -> ((CarrierPrepare) o)).collect(Collectors.toList());
				
				List<String> jobIds = carrierPrepares.stream().map(CarrierPrepare::getJobId).distinct().collect(Collectors.toList());
				String jobIdsStr = jobIds.stream().collect(Collectors.joining(","));
				if (UI.showConfirm(String.format(Message.getString("wip.byeqp_sure_cancel_jobs"), jobIdsStr))) {
					dureableManager.cancelCarrierJobPrepare(equipment.getEquipmentId(), jobIds, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					updateList();
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : removeAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	public void updateList() throws Exception {
		DurableManager dureableManager = Framework.getService(DurableManager.class);
		List<CarrierPrepare> carrierPrepars = dureableManager.getCarrierPrepareJobs(
				Env.getOrgRrn(), equipment.getEquipmentId(), null, null, true);
		prepareField.setValue(carrierPrepars);
		prepareField.refresh();
		
		// ˢ�������б�������֤�б��е����������µ�
		ListTableManager tableManager = carrierListField.getListTableManager();
		
		List<Carrier> waitingCarriers = byEqpCarrierCleanPage.getCarrierSpecMainMatType(equipment);
		waitingCarriers = byEqpCarrierCleanPage.getCarrierState(waitingCarriers, 2);
		
		tableManager.setInput(waitingCarriers);
	}
	
	private void modifyAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			List<Object> objs = prepareField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isNotEmpty(objs)) {
				// ���ȼ��ֻ��ͬʱ�޸�һ��Job
				Set<String> modifyJobIds = objs.stream().map(o -> ((CarrierPrepare)o).getJobId()).collect(Collectors.toSet());
				if (modifyJobIds.size() > 1) {
					UI.showInfo(Message.getString("wip.lot_prepare_jobid_modify_one"));
					return;
				}
				
				DurableManager dureableManager = Framework.getService(DurableManager.class);
				
				TextField rightJobIdField = form.getFieldByControlId(FIELD_JOBID, TextField.class);
				if (!ValidatorFactory.isValid(DataType.INTEGER, rightJobIdField.getText())) {
					ADField adField = (ADField) rightJobIdField.getADField();
					form.getMessageManager().addMessage(adField.getName() + "common.isvalid", 
							String.format(Message.getString("common.isvalid"), I18nUtil.getI18nMessage(adField, "label"), DataType.INTEGER), null,
							MsgType.MSG_ERROR.getIndex(), rightJobIdField.getControls()[rightJobIdField.getControls().length - 1]);
					return;
				}
				String jobId = rightJobIdField.getText();
				// ����޸ĵ���������Ƿ��Ѿ�����
				if (!StringUtil.isEmpty(jobId)) {
					List<CarrierPrepare> carrierPrepares = dureableManager
							.getCarrierPrepareJobs(Env.getOrgRrn(), equipment.getEquipmentId(), null, jobId, false);
					if (CollectionUtils.isNotEmpty(carrierPrepares)) {
						UI.showError(Message.getString("wip.lot_prepare_jobid_exist"));
						return;
					}
				} else {
					UI.showInfo(Message.getString("wip.lot_prepare_jobid_modify_input"));
					return;
				}
				
				List<CarrierPrepare> carrierPrepares = objs.stream().map(o -> ((CarrierPrepare) o)).collect(Collectors.toList());
				dureableManager.changePrepareId(carrierPrepares, jobId, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				updateList();
				rightJobIdField.setValue(null);
				rightJobIdField.refresh();
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : modifyAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {	
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonClose()), false, null);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

	}
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<ByEqpCarrierPrepareDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<ByEqpCarrierPrepareDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}
	
}
