package com.glory.mes.wip.byeqp;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.GlcMasterDetailsBlock;
import com.glory.framework.core.util.StringUtil;

public class ByEqpForm extends GlcForm {

	public ByEqpForm(ADForm adForm, ADTable adTable) {
		super(adForm, adTable);
	}
	
	public void createForm(Composite parent, FormToolkit toolkit) {
		mmng = getMessageManager();
		if (ADForm.TYPE_MDBLOCK.equals(adForm.getFormType())) {
			//��Master-Detail��ʽ��ʾ,���������ѡ������ʾ�ұߵĲ�ͬҳ��
			//��һ��TabΪMaster/���涼ΪDetail
			List<ADTab> tabs = adTable.getTabs();
			if (tabs.size() < 1) {
				return;
			}
			block = new ByEqpMasterDetailsBlock(tabs.get(0));
			((GlcMasterDetailsBlock)block).setGlcForm(this);
			((GlcMasterDetailsBlock)block).setButtons(adForm.getButtons());
			ScrolledForm form = toolkit.createScrolledForm(parent);
			form.setLayout(new GridLayout(1, true));
			form.setLayoutData(new GridData(GridData.FILL_BOTH));
			ManagedForm mform = new ManagedForm(toolkit, form);
			block.createContent(mform);  
			
			if (adForm.getIntWeights() != null) {
				((GlcMasterDetailsBlock)block).setWeights(adForm.getIntWeights());
			}		
			if (!StringUtil.isEmpty(adForm.getOrientation())) {
				if (ADForm.ORIENTATION_VERTICAL.equals(adForm.getOrientation())) {
					((GlcMasterDetailsBlock)block).setOrientation(SWT.VERTICAL);
	    		} else {
	    			((GlcMasterDetailsBlock)block).setOrientation(SWT.HORIZONTAL);
	    		}
			}		
		} else {
			super.createForm(parent, toolkit);
		}
	}
	

}
