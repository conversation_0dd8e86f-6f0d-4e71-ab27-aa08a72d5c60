package com.glory.mes.wip.byeqp;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.state.RasState;
import com.glory.mes.wip.Activator;
import com.glory.mes.wip.byeqp.prepare.ByEqpPrepareDefaultDialog;
import com.glory.mes.wip.byeqp.prepare.ByEqpPrepareDialog;
import com.glory.mes.wip.custom.ByEqpEqpTreeCustomComposite;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.lot.action.LotActionFactory;
import com.glory.mes.wip.lot.action.LotMenuAction;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class ByEqpDefaultPage extends ByEqpPage implements IRefresh {

	private static final Logger logger = Logger.getLogger(ByEqpDefaultPage.class);
	
	public static final String BYEQP_PREPARE_DIALOG_FORM_NAME = "WIPLotByEqpPrepareDialog";
	
	public static final String FIELD_LOTFORM = "lotForm";
	public static final String FIELD_DISPATCHFLAG = "dispatchFlag";
	public static final String FIELD_EQUIPMENT_ID = "equipmentId";
	public static final String FIELD_CARRIER_ID = "carrierId";
	public static final String FIELD_LOT_ID = "lotId";
	public static final String FIELD_RUNNINGLOTS = "runningLots";
	public static final String FIELD_WAITTINGLOTS = "waittingLots";

	public static final String BUTTON_PREPARE = "prepare";
	public static final String BUTTON_TRACKIN = "trackin";
	public static final String BUTTON_WAITREFRESH = "inrefresh";
	public static final String BUTTON_DOCP = "docp";
	public static final String BUTTON_TRACKOUT = "trackout";
	public static final String BUTTON_ABORT = "abort";
	public static final String BUTTON_RUNREFRESH = "runrefresh";
		
	protected BooleanField dispatchField;
	protected TextField fieldEqp;
	protected TextField fieldLot;
	protected TextField fieldCarrier;
	protected CustomField fieldRunning;
	protected CustomField fieldWaitting;
	
	protected ToolItem itemPrepare;
	protected ToolItem itemRunRefresh;
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		dispatchField = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_DISPATCHFLAG, BooleanField.class);
		if (dispatchField != null) {
			dispatchField.addValueChangeListener(new IValueChangeListener() {			
				@Override
				public void valueChanged(Object sender, Object newValue) {
					waitRefreshAdapter(null);
				}
			});
		}
		
		fieldEqp = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_EQUIPMENT_ID, TextField.class);
		fieldLot = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_LOT_ID, TextField.class);
		fieldCarrier = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_CARRIER_ID, TextField.class);			
		fieldRunning = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_RUNNINGLOTS, CustomField.class);
		fieldWaitting = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGLOTS, CustomField.class);		
		
		LotListComposite fieldRunningListComposite = (LotListComposite) fieldRunning.getCustomComposite();
		LotListComposite fieldWaittingListComposite = (LotListComposite) fieldWaitting.getCustomComposite();
		//�����Ҽ��˵�
		List<LotMenuAction> actions = LotActionFactory.getLotMenuActionByAuthName(this.getAuthority());
		for (LotMenuAction action : actions) {
			if (!action.getAuthorityKey().endsWith(LotActionFactory.ACTION_SHIP) && !action.getAuthorityKey().endsWith(LotActionFactory.ACTION_SHIP_CANCEL)) {
				fieldWaittingListComposite.addCustomMenuAction(action);
			}
			if (action.getAuthorityKey().endsWith(LotActionFactory.ACTION_RUN_HOLD) || action.getAuthorityKey().endsWith(LotActionFactory.ACTION_RUN_RELEASE)) {
				fieldRunningListComposite.addCustomMenuAction(action);
			}
		}
		fieldWaittingListComposite.initMenu();
		fieldWaittingListComposite.setRefreshObject(this);
		fieldRunningListComposite.initMenu();
		fieldRunningListComposite.setRefreshObject(this);
	    
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_EQUIPMENT_ID, GlcEvent.EVENT_ENTERPRESSED), this::equipmentEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_LOT_ID, GlcEvent.EVENT_ENTERPRESSED), this::lotEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_CARRIER_ID, GlcEvent.EVENT_ENTERPRESSED), this::carrierEnterPressed);
		subscribeAndExecute(eventBroker, fieldWaitting.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::waitingLotSelectionAdaptor);	
		subscribeAndExecute(eventBroker, fieldRunning.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::runningLotSelectionAdaptor);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_PREPARE), this::prepareAdapter);						
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_TRACKIN), this::trackInAdapter);					
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_WAITREFRESH), this::waitRefreshAdapter);				
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_DOCP), this::docpAdapter);				
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_TRACKOUT), this::trackOutAdapter);		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_ABORT), this::abortAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_RUNREFRESH), this::runRefreshAdapter);
				
		itemPrepare = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_PREPARE);
		itemTrackIn = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_TRACKIN);
		itemDcop = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_DOCP);
		itemTrackOut = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_TRACKOUT);
		itemAbort = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_ABORT);
		itemRunRefresh = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_RUNREFRESH);
		
		itemPrepare.setEnabled(false);
		itemTrackIn.setEnabled(false);
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);	
		itemAbort.setEnabled(false);	
		
		addKeyListener();
	}
	
	protected void addKeyListener() {
		LotListComposite waitComposite = (LotListComposite) fieldWaitting.getCustomComposite();
		waitComposite.getTableManager().getNatTable().addListener(SWT.KeyDown, new Listener() {
			@Override
			public void handleEvent(org.eclipse.swt.widgets.Event event) {
				switch (event.keyCode) {
				case SWT.F3:
					if (itemTrackIn != null && itemTrackIn.isEnabled()) {
						trackInAdapter(null);
					}
					break;
				case SWT.F8:
					waitRefreshAdapter(null);
					break;
				}
			}
			
		});
		
		LotListComposite runComposite = (LotListComposite) fieldRunning.getCustomComposite();
		runComposite.getTableManager().getNatTable().addListener(SWT.KeyDown, new Listener() {
			public void handleEvent(org.eclipse.swt.widgets.Event e) {
				switch (e.keyCode) {
				case SWT.F4:
					if (itemDcop != null && itemDcop.isEnabled()) {
						docpAdapter(null);
					}
					break;
				case SWT.F5:
					if (itemTrackOut != null && itemTrackOut.isEnabled()) {
						trackOutAdapter(null);
					}
					break;
				case SWT.F6:
					if (itemAbort != null && itemAbort.isEnabled()) {
						abortAdapter(null);
					}
					break;
				case SWT.F8:
					if (itemRunRefresh != null && itemRunRefresh.isEnabled()) {
						runRefreshAdapter(null);
					}
					break;
				}
			}
		});
		
		// ͨ��ˢ�½���ȼ�ʧЧ����
		waitComposite.getTableManager().getNatTable().refresh();
		runComposite.getTableManager().getNatTable().refresh();
	}
	
	/**
	 * ����豸ѡ����Ӧ�¼�
	 * @param object
	 */
	protected void eqpSelectionChanged(Object object) {
		try {
			Event event = (Event) object;		
			//��ȡѡ�е��豸
			Equipment equipment = (Equipment) event.getProperty(GlcEvent.PROPERTY_DATA);	
			if (equipment != null) {
				RasState state = rasManager.getState(Env.getOrgRrn(), equipment.getState());
				equipment.setIsAvailable(state.getIsAvailable());
				currentEqp = equipment;
				
				if (itemPrepare != null && !itemPrepare.isDisposed()) {
					itemPrepare.setEnabled(true);
				}
				
				fieldEqp.setText(equipment.getEquipmentId());
				fieldLot.setText(null);
				fieldCarrier.setText(null);
				
				refreshAdapter();		
				
				//����̨��ʾ�����õ��豸
				if (!state.getIsAvailable()) {
					if (console != null && !console.isDisposed()) {
						String message = "";
						message += "[" + currentEqp.getEquipmentId() + "];";
						message += Message.getString("byeqp.runninglot_eqpisnotavailable") + currentEqp.getState();
						console.error(message);
					}
				}
				//����̨��ʾHold������
				if (console != null && !console.isDisposed() && CollectionUtils.isNotEmpty(getHoldLots())) {
					String message = "";			
					message += Message.getString("wip.lot_current_state_is_hold");
					for (Lot slot : this.getHoldLots() ) {
						message += "[" + slot.getLotId() + "];";
					}
					console.info(message);
				}
			} else {
				if (itemPrepare != null && !itemPrepare.isDisposed()) {
					itemPrepare.setEnabled(false);
				}
				clear();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * waiting����ѡ���¼�
	 * @param object
	 */
	protected void waitingLotSelectionAdaptor(Object object) {
		try {
			Event event = (Event) object;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lot != null) {
				lotStatusChanged(lot.getState(), lot.getHoldState());				
				if (console != null && currentEqp != null) {
					ByEqpUtil.noticeEquipmentRecipeAndReticle(lot, currentEqp, console);
				}
			} else {
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			}
			
			ListTableManager tableManager = ((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
			List<Object> objects = tableManager.getCheckedObject();
			List<Lot> lots = objects.stream().map(o -> (Lot)o).collect(Collectors.toList());
			Optional<Lot> f = lots.stream().filter(r -> Lot.HOLDSTATE_ON.equals(r.getHoldState())).findFirst();
			if (f.isPresent()) {
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			} else {
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(true);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	/**
	 * running����ѡ���¼�
	 * @param object
	 */
	protected void runningLotSelectionAdaptor(Object object) {
		Event event = (Event) object;
		Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
		if (lot != null) {
			lotStatusChanged(lot.getState(), lot.getHoldState());
		} else {
			itemDcop.setEnabled(false);
			itemTrackOut.setEnabled(false);
			itemAbort.setEnabled(false);
		}
	}

	/**
	 * �豸�ؼ��س��¼�
	 * @param obj
	 */
	protected void equipmentEnterPressed(Object object) {
		try {
			Event event = (Event) object;
			String equipmentId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentId, false);
			if (equipment != null) {
				ByEqpEqpTreeCustomComposite composite = ((ByEqpEqpTreeCustomComposite)eqpTreeField.getCustomComposite());
				composite.doSelection(equipment);
			} else {
				currentEqp = null;
				clear();
				if (console != null && !console.isDisposed()) {
					console.error(Message.getString("ras.equipment_no_found"));
				}
			}
		} catch (Exception e) {
			currentEqp = null;
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	/**
	 * �ؾ߿ؼ��س��¼�
	 * @param obj
	 */
	protected void carrierEnterPressed(Object object) {
		try {
			if (currentEqp == null) {
				if (console != null) {
					console.error(Message.getString("ras.equipment_select_eqp"));
				}
				return;
			}
			
			Event event = (Event) object;
			String carrierId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);	
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);		
			if (carrier != null) {			
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);		
				if (lots != null && lots.size() > 0) {	
					Lot lot = lots.get(0);
					processLot(lot);
				} else {
					if (console != null) {
						console.error(Message.getString("wip.carrier_mainlot_is_not_found"));
					}
				}
			} else {
				if (console != null) {
					console.error(Message.getString("mm.durable_not_exist"));
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpManagerDefault : carrierEnterPressed() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	/**
	 * ���οؼ��س��¼�
	 * @param obj
	 */
	protected void lotEnterPressed(Object object) {
		try {
			if (currentEqp == null) {
				if (console != null) {
					console.error(Message.getString("ras.equipment_select_eqp"));
				}
				return;
			}
			
			Event event = (Event) object;
			String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			if (lot != null) {
				processLot(lot);
			} else {
				if (console != null) {
					console.error(Message.getString("wip.lot_is_not_exist"));
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpManagerDefault : lotEnterPressed() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	/**
	 * ���δ���
	 * @param lot
	 * @throws Exception
	 */
	protected void processLot(Lot lot) throws Exception {
		ListTableManager tableManager = ((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
		// �������б����ҵ�������
		List<? extends Object> inputLots = tableManager.getInput();
		Optional<? extends Object> f = inputLots.stream().filter(
				o -> ((Lot)o).getLotId().equals(lot.getLotId())).findFirst();
		if (!f.isPresent()) {
			if (console != null) {
				console.error(Message.getString("wip.byeqp_scan_lot_not_exist"));
			}
		} else {
			// �ҵ�����
			Lot findLot = (Lot) f.get();
			List<Lot> lots = null;
			if (LotStateMachine.STATE_DISP.equals(lot.getState())) {	
				// ����Prepare����
				lots = prepareManager.getPrepareGroupLots(currentEqp.getEquipmentId(), findLot.getLotId(), null, Env.getSessionContext());
			} else if (!StringUtil.isEmpty(lot.getBatchId())) {
				// ����batch����
				lots = inputLots.stream().filter(o -> lot.getBatchId().equals(((Lot)o).getBatchId()))
						.map(o -> ((Lot)o)).collect(Collectors.toList());
			} else {
				// ��������
				lots = Lists.newArrayList(findLot);
			}
			// ѡ��
			tableManager.setSelection(new StructuredSelection(lots.toArray()));
			// Check
			fieldWaitting.postEvent(fieldWaitting.getFullTopic(LotListComposite.EVENT_SETCHECK), lots);
		}
	}
	
	/**
	 * ��ҵ׼��
	 * @param obj
	 */	
	protected void prepareAdapter(Object object) {
		if (currentEqp != null && currentEqp.getIsAvailable()) {
			try {				
				// ��ҵ׼��Dialog
				ByEqpPrepareDefaultDialog dialog = new ByEqpPrepareDefaultDialog(BYEQP_PREPARE_DIALOG_FORM_NAME, this.getAuthority(), this.getEventBroker());
				dialog.setRTDAvailable(isRTDAvailable());
				Map<String, Object> propValues = Maps.newHashMap();
				// ��ҵ׼����¼		
				List<LotPrepare> lotPrepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), null, true);
				propValues.put("prepareList", lotPrepares);
				// �豸��Ϣ
				propValues.put("leftFrom-equipmentInfo", currentEqp);
				
				// ��������ҵ׼��������
				List<Lot> waittingLots = Lists.newArrayList();
				if (isRTDAvailable()) {
					waittingLots = ByEqpUtil.getRtdLots(currentEqp, false);
				} else {
					waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(), LotStateMachine.STATE_WAIT, Env.getSessionContext());
				}		
				waittingLots = ByEqpUtil.filterWaitingLots(waittingLots, currentEqp);
				propValues.put("leftFrom-lotList", waittingLots);
				dialog.setPropValues(propValues);
				dialog.setCloseAdaptor(new Consumer<ByEqpPrepareDialog>() {				
					@Override
					public void accept(ByEqpPrepareDialog t) {
						waitRefreshAdapter(object);
					}
				});
				dialog.open();
			} catch (Exception e) {
				logger.error("Error at ByEqpManagerDefault : prepareAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	
	/**
	 * ��ȡ����Ҫ��վ����
	 * @return
	 */
	public List<Lot> getTrackInLotList() {
		LotListComposite composite = (LotListComposite) fieldWaitting .getCustomComposite();
		return (List<Lot>)(List)composite.getCheckedObjects();
	}
	
	/**
	 * ��ȡѡ��Ҫ��վ������
	 * @return
	 */
	public Lot getSelectedRunningLot() {
		LotListComposite composite = (LotListComposite) fieldRunning.getCustomComposite();
		return (Lot) composite.getSelectedObject();
	}
	
	/**
	 * Allˢ��
	 */
	protected void refreshAdapter() {
		waitRefreshAdapter(null);
		runRefreshAdapter(null);
	}
	
	/**
	 * �ȴ�������Ϣˢ��
	 */
	protected void waitRefreshAdapter(Object object) {
		try {
			if (currentEqp != null && currentEqp.getObjectRrn() != null) {
				List<Lot> dispLots = prepareManager.getSortedLotsByEquipmentId(Env.getOrgRrn(), currentEqp.getEquipmentId());
				
				List<Lot> waittingLots = Lists.newArrayList();
				if (isRTDAvailable()) {
					waittingLots = ByEqpUtil.getRtdLots(currentEqp);
				} else {
					waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(),
							LotStateMachine.STATE_WAIT, Env.getSessionContext());
				}
				waittingLots.removeAll(dispLots);
				dispLots.addAll(waittingLots);
				
				//ɸѡ��Hold������
				this.setHoldLots(filterHoldLots(dispLots));
				
//				List<Lot> allLots = ByEqpUtil.filterWaitingLots(dispLots, currentEqp);
				fieldWaitting.setValue(dispLots);
				fieldWaitting.refresh();
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ����������Ϣˢ��
	 */
	protected void runRefreshAdapter(Object object) {
		try {
			if (currentEqp != null && currentEqp.getObjectRrn() != null) {
				List<Lot> runningLots = lotManager.getRunningLotsByEqp(Env.getOrgRrn(), currentEqp.getEquipmentId(), false, false);
				fieldRunning.setValue(runningLots);
				fieldRunning.refresh();											
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ���ҳ����λ��Ϣ
	 */
	protected void clear() {
		fieldWaitting.setValue(Lists.newArrayList());
		fieldWaitting.refresh();
		fieldRunning.setValue(Lists.newArrayList());
		fieldRunning.refresh();
		
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);
		if (itemTrackIn != null) {
			itemTrackIn.setEnabled(false);
		}	
		itemAbort.setEnabled(false);
		itemPrepare.setEnabled(false);
		
		ListTableManager runningTableManager = 
				((LotListComposite)fieldRunning.getCustomComposite()).getTableManager();
		ListTableManager waittingTableManager = 
				((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
		runningTableManager.getTableManager().setSelectedObject(null);
		waittingTableManager.getTableManager().setSelectedObject(null);
		
		fieldEqp.setText(null);
		fieldLot.setText(null);
		fieldCarrier.setText(null);
	}
	
	/**
	 * �Ƿ�ʵʱ�ɹ�
	 * @return
	 */
	protected boolean isRTDAvailable() {
		if (dispatchField != null) {
			if (!dispatchField.getCheckboxControl().isDisposed()) {
				if (dispatchField.isChecked()) {
					return Activator.isRTDAvailable();
				}
			}
		}
		return false;
	}

	@Override
	public void refresh() {
		waitRefreshAdapter(null);
		runRefreshAdapter(null);
	}
	
	@Override
	public void setWhereClause(String whereClause) {}

	@Override
	public String getWhereClause() {
		return null;
	}

	@Override
	public boolean isUseParam() {
		return false;
	}

	@Override
	public Map<String, Object> getParameterMap() {
		return null;
	}
}
