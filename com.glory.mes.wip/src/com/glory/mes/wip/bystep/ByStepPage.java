package com.glory.mes.wip.bystep;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.edc.EdcEntry;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcSetCurrentComponentDialog;
import com.glory.edc.collection.EdcSetCurrentDialog;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTecn;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.GlcPage;
import com.glory.framework.base.ui.extensionpoints.WizardPageExtensionPoint;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.bystep.extensionpoint.IByStepPage;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotPrepareManager;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.lot.run.abort.AbortDialog;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveDialog;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveWizard;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalDialog;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalWizard;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.google.common.collect.Sets;

public abstract class ByStepPage extends GlcPage implements IByStepPage {

	private static final Logger logger = Logger.getLogger(ByStepPage.class);
	
	public static final String DEFAULT_STEP_USE_CATEGORY = "Default";
	
	public static final String FIELD_STEPTREE = "stepTree";
	
	/**
	 * ��ʾ����̨
	 */
	public ByEqpConsole console;
	
	/**
	 * �����豸�ؼ�
	 */
	public CustomField stepTreeField;
	
	protected ToolItem itemTrackMove;
	protected ToolItem itemTrackIn;
	protected ToolItem itemDcop;
	protected ToolItem itemTrackOut;
	protected ToolItem itemAbort;
	
	public ADManager adManager;
	public RASManager rasManager;
	public PrdManager prdManager;
	public EDCManager edcManager;
	public DurableManager durableManager;
	public CarrierLotManager carrierLotManager;
	public LotManager lotManager;
	public LotPrepareManager prepareManager;
	
	/**
	 * Editor�е�GlcForm
	 */
	public GlcForm rootForm;
	
	/**
	 * ��ǰ�豸
	 */
	public Equipment currentEquipment;
	
	/**
	 * ��ǰ����
	 */
	public Step currentStep;
	
	
	protected void createFormAction(GlcForm form) {		
		//��ȡ��ߵ������豸�б�
		stepTreeField = rootForm.getFieldByControlId(FIELD_STEPTREE, CustomField.class);	
				
		//��ȡ��ߵ������豸����¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::stepSelectionChanged);	
		
		try {
			adManager = Framework.getService(ADManager.class);
			rasManager = Framework.getService(RASManager.class);
			prdManager = Framework.getService(PrdManager.class);
			edcManager = Framework.getService(EDCManager.class);
			durableManager = Framework.getService(DurableManager.class);
			carrierLotManager = Framework.getService(CarrierLotManager.class);
			lotManager = Framework.getService(LotManager.class);
			prepareManager = Framework.getService(LotPrepareManager.class);				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected abstract void stepSelectionChanged(Object object);
	
	/**
	 * ����״̬�仯���ư�ť����
	 * @param state ����״̬
	 * @param holdState ����Hold״̬
	 */
	protected void lotStatusChanged(String state, String holdState) {
		if (Lot.HOLDSTATE_ON.equals(holdState)) {
			if (itemTrackMove != null) {
				itemTrackMove.setEnabled(false);
			}
			
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
			if (itemDcop != null) {
				itemDcop.setEnabled(false);
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(false);
			}			
			if (LotStateMachine.STATE_RUN.equals(state)) {
				if (itemTrackOut != null) {
					itemTrackOut.setEnabled(true);
				}
				if (itemAbort != null) {
					itemAbort.setEnabled(true);
				}
			}
		} else if (LotStateMachine.STATE_WAIT.equals(state) 
				|| LotStateMachine.STATE_DISP.equals(state)) {
			
			Step step = getCurrentStep();
			if (step.getIsMoveNext()) {
				if (itemTrackMove != null) {
					itemTrackMove.setEnabled(true);
				}
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			} else {
				if (itemTrackMove != null) {
					itemTrackMove.setEnabled(false);
				}
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(true);
				}
			}
			
			if (itemDcop != null) {
				itemDcop.setEnabled(false);
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(false);
			}
		} else if (LotStateMachine.STATE_RUN.equals(state)) {
			if (itemTrackMove != null) {
				itemTrackMove.setEnabled(false);
			}	
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
			if (itemDcop != null) {
				List<Lot> lots = getTrackLotList();
				boolean docpFlag = false;
				if (lots != null && lots.size() > 0) {
					List<EdcSetCurrent> currents = getEdcSetCurrent(lots.get(0));
					if (currents != null && currents.size() > 0) {	
						docpFlag = true;
					}
				}
				
				itemDcop.setEnabled(docpFlag);	
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(true);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(true);
			}
		} else {
			if (itemTrackMove != null) {
				itemTrackMove.setEnabled(false);
			}
			
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
			if (itemDcop != null) {
				itemDcop.setEnabled(false);
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(false);
			}
		}
	}
		
	/**
	 * ��ȡ����Ҫ��վ����
	 * @return
	 */
	protected abstract List<Lot> getTrackLotList();
	
	/**
	 * ���ι�վ
	 * @param object
	 */
	protected void trackMoveAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			
			Event event = (Event) object;
			String operator1 = Env.getUserName();
			if (event != null && event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			
			TrackMoveContext context = new TrackMoveContext();
			context.setTrackMoveType(TrackMoveContext.TRACK_MOVE_DEFAULT);
			
			List<Lot> lots =  getTrackLotList();
			//�������
			if (!checkBasicConstraint(lots)) {
				return;
			}
			
			context.setLots(lots);
			context.setOperator1(operator1);
			
			List<Equipment> equipments = new ArrayList<Equipment>(); 
			Equipment eqp = getCurrentEquipment();//ȡ��ѡ�е��豸
			if(eqp != null) {
				equipments.add(eqp);
				for (Lot lot : lots) {
					lot.setEquipmentId(eqp.getEquipmentId());
				}
			}	
			context.setSelectEquipments(equipments);
			
			Step step = new Step();
			step.setObjectRrn(currentStep.getObjectRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setEquipments(equipments);
			inContext = setInContext(inContext);
			
			//�����ҵ׼������
			if (!checkPrepareConstraint(inContext)) {
				return;
			}
			
			//����վ����
			if (!checkPreTrackInConstraint(inContext)) {
				return;
			}
			
			//�����������
			if (!checkOtherConstraint(inContext)) {
				return;
			}

			context.setOperator1(operator1);
			TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(step.getMoveNextWizard());
			wizard.setContext(context);
			
			if (wizard instanceof TrackMoveWizard) {
				TrackMoveDialog dialog = new TrackMoveDialog(UI.getActiveShell(), wizard);
				int result = dialog.open();
				if (result == Dialog.OK || result == TrackMoveDialog.FIN) {
					refreshAdapter(object);
					UI.showInfo(Message.getString("wip.trackmove_success"));
				}
			} else if (wizard instanceof TrackMoveModalWizard) {
				//�Թ̶����ڵ���ʽ��վ
				((TrackMoveContext)((TrackMoveModalWizard)wizard).getContext()).setTrackMoveType(TrackMoveContext.TRACK_MOVE_MODAL);
				TrackMoveModalDialog dialog = new TrackMoveModalDialog(UI.getActiveShell(), wizard);
				dialog.open();
				refreshAdapter(object);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ������飬�Ƿ�ѡ���豸���Ƿ�ѡ�����Σ��豸�Ƿ���õ�
	 * @param lots
	 * @return
	 */
	public boolean checkBasicConstraint(List<Lot> lots) {
		//�Ƿ�ѡ�˽�վ����
		if (CollectionUtils.isEmpty(lots)) {
			if (console != null) {
				console.error(Message.getString("wip_not_select_lot"));
			} else {
				UI.showError(Message.getString("wip_not_select_lot"));
			}
			return false;
		}		
		
		//�豸�Ƿ���Ч
		if (currentEquipment != null) {
			if (!currentEquipment.getIsAvailable()) {
				if (console != null) {
					console.error(Message.getString("error.eqp_state_not_allow"));
				} else {
					UI.showError(Message.getString("error.eqp_state_not_allow"));
				}
				return false;
			}
			
			//����豸Batch
			if (!currentEquipment.getIsBatch() && lots.size() > 1) {
				if (console != null) {
					console.error(Message.getString("wip.trackin_cannot_batch"));
				} else {
					UI.showError(Message.getString("wip.trackin_cannot_batch"));
				}
				return false;
			}
			return true;
		}	
		
		return true;
	}
	
	/**
	 * ��������һЩ��鲻��
	 * @param inContext
	 * @return
	 */
	public InContext setInContext(InContext inContext) {
		return inContext;
	}
	
	/**
	 * �����ҵ׼������
	 * @param inContext
	 * @return
	 */
	public boolean checkPrepareConstraint(InContext inContext) {
		if (currentEquipment != null) {
			List<LotPrepare> prepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), currentEquipment.getEquipmentId(), null, false);
			if (CollectionUtils.isNotEmpty(prepares)) {
				// ���ȼ���Ƿ�����С����ҵ������
				Map<String, List<LotPrepare>> prepareMap = prepares.stream().collect(Collectors.groupingBy(LotPrepare::getJobId));
				
				Optional<String> f = prepareMap.keySet().stream().min(Comparator.comparingLong(Long::valueOf));
				if (f.isPresent()) {
					List<LotPrepare> nextPrepareBatch = prepareMap.get(f.get());
					boolean matchFlag = false;
					// ����Ƿ�ƥ�����Σ�ֻҪƥ����䵽һ�����κž�ͨ��
					for (LotPrepare lotPrepare : nextPrepareBatch) {
						boolean thisMatch = false;
						for (Lot lot : inContext.getLots()) {
							if (lotPrepare.getLotId().equals(lot.getLotId())) {
								thisMatch = true;
								break;
							}
						}
						
						matchFlag = matchFlag || thisMatch;
					}
					
					if (!matchFlag) {
						if (!UI.showConfirm(Message.getString("wip.byeqp_prepare_rule_check"))) {
							return false;
						}
					}
				}
				
				// ����Prepare��վ����
				Set<String> jobIds = Sets.newHashSet();
				int i = 0;
				for (LotPrepare lotPrepare : prepares) {
					for (Lot lot : inContext.getLots()) {
						if (lotPrepare.getLotId().equals(lot.getLotId())) {
							jobIds.add(lotPrepare.getJobId());
							i++;
							break;
						}
					}
				}
				
				if (jobIds.size() > 1) {
					// ����������ҵ׼������ͬʱ��վ
					if (console != null) {
						console.error(Message.getString("wip.byeqp_prepare_job_multi_error"));
					} else {
						UI.showError(Message.getString("wip.byeqp_prepare_job_multi_error"));
					}
					return false;
				} else if (jobIds.size() == 1 && i != inContext.getLots().size()) {
					// ��������ҵ׼����������������������ͬʱ��վ
					if (console != null) {
						console.error(Message.getString("wip.byeqp_prepare_mix"));
					} else {
						UI.showError(Message.getString("wip.byeqp_prepare_mix"));
					}
					return false;
				} else {
					// û��Job��������
				}
			}
		}
		return true;
	}
	
	/**
	 * ����վǰ��һЩ���ƣ�д�ں�̨�У����繤��Ȩ�ޣ�����״̬���������ƣ��������Ƶ�
	 * @param inContext
	 * @return
	 */
	public boolean checkPreTrackInConstraint(InContext inContext) {
		ChainContext wipContext = lotManager.checkTrackInConstraint(inContext, Env.getSessionContext());
		if (wipContext.getReturnMessage() != null && wipContext.getReturnMessage().trim().length() > 0) {
			if (console != null) {
				if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
					console.error(Message.formatString(wipContext.getReturnMessage()));
				} else {
					console.info(Message.formatString(wipContext.getReturnMessage()));
				}
			} else {
				if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
					UI.showError(Message.formatString(wipContext.getReturnMessage()));
				} else {
					UI.showInfo(Message.formatString(wipContext.getReturnMessage()));
				}						
			}
		}
		if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
			refreshAdapter(null);
			return false;
		}
		return true;
	}
	
	/**
	 * ����������ƣ���ͬ�Ĺ�������ͬ�豸��һЩ���������ƣ���д�÷����ɿ��ƻ�
	 * @param inContext
	 * @return
	 */
	public boolean checkOtherConstraint(InContext inContext) {
		return true;
	}
	
	/**
	 * ��վ
	 * @param object
	 */
	protected void trackInAdapter(Object object) {	
		try {
			form.getMessageManager().removeAllMessages();
			
			Event event = (Event) object;
			String operator1 = Env.getUserName();
			if (event != null && event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			
			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYLOT);
			
			List<Lot> lots =  getTrackLotList();
			//�������
			if (!checkBasicConstraint(lots)) {
				return;
			}
			
			List<Equipment> equipments = new ArrayList<Equipment>(); 
			Equipment eqp = getCurrentEquipment();//ȡ��ѡ�е��豸
			if(eqp != null) {
				equipments.add(eqp);
				for (Lot lot : lots) {
					lot.setEquipmentId(eqp.getEquipmentId());
				}
			}	
			context.setSelectEquipments(equipments);			
			context.setLots(lots);
			context.setOperator1(operator1);
			
			Step step = new Step();
			step.setObjectRrn(currentStep.getObjectRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);	
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setEquipments(equipments);
			inContext = setInContext(inContext);
			
			//�����ҵ׼������
			if (!checkPrepareConstraint(inContext)) {
				return;
			}
			
			//����վ����
			if (!checkPreTrackInConstraint(inContext)) {
				return;
			}
			
			//�����������
			if (!checkOtherConstraint(inContext)) {
				return;
			}
			
			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry().get(step.getTrackInFlow());
			if (wizard instanceof TrackInWizard) {
				context.setOperator1(operator1);
				((TrackInWizard) wizard).setContext(context);
				((TrackInWizard) wizard).setEventBroker(eventBroker);
				((TrackInWizard) wizard).setAuthority(form.getAuthority());
			}

			TrackInDialog dialog = new TrackInDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackInDialog.FIN) {
				refreshAdapter(object);
			}
			
		} catch (Exception e) {
			logger.error("Error at ByStepPage : trackInAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}
	
	protected List<EdcSetCurrent> getEdcSetCurrent(Lot lot) {
		try {		
			return edcManager.getItemSetCurrents(Env.getOrgRrn(), lot.getBatchId(), lot.getObjectRrn(), null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	/**
	 * �����ռ�
	 * @param object
	 */
	protected void docpAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			
			Event event = (Event) object;
			String operator1 = Env.getUserName();
			if (event != null && event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}

			List<Lot> lots =  getTrackLotList();
			if (lots == null || lots.size() == 0) {
				UI.showWarning(Message.getString("wip_not_select_lot"));
				return;
			}
			
			Lot lot = lots.get(0);
			
			List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
			if (currents != null && currents.size() > 0) {				
				if (currents.size() == 1 
						&& !EdcSetCurrent.FLAG_DONE.equals(currents.get(0).getEdcFlag())
						&& !EdcSetCurrent.FLAG_PASS.equals(currents.get(0).getEdcFlag())){
					//�����жϣ����Batch�ɼ�ʱ�����ͬBatch��EDCTECN������Ƿ�ѡ��������
					if(!checkBatchEdcTecn(lot)) {
						return;
					}
					
					//��ֻ��һ��EDCʱ����δ���ʱ
					int result = EdcEntry.open(EdcData.EDCFROM_LOT, currents.get(0), null, lot);
					if (result == Dialog.OK) {
						refreshAdapter(object);
					}
				} else {
					boolean flag = false;
					for (EdcSetCurrent current : currents) {
						if (!StringUtil.isEmpty(current.getComponentUnitId())) {
							flag = true;
							break;
						}
					}
					EdcSetCurrentDialog edcSetCurrentDialog = null;
					if (flag) {
						edcSetCurrentDialog = new EdcSetCurrentComponentDialog(UI.getActiveShell(), lot, currents);					
					} else {
						edcSetCurrentDialog = new EdcSetCurrentDialog(UI.getActiveShell(), lot, currents);	
					}
					edcSetCurrentDialog.open();
					if (edcSetCurrentDialog.getReturnCode() == Dialog.OK) {
						refreshAdapter(object);
					}
				}
			} else {
				UI.showWarning(Message.getString("edc.alert_message"), Message.getString("edc.alert_message_title"));
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPage : docpAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ���Batch EdcTecn�����Batch�ɼ�ʱ���ͬBatch��EDCTECN������Ƿ�ѡ��������
	 * @param lot
	 * @return
	 */
	protected boolean checkBatchEdcTecn(Lot lot) {
		try {
			if(!StringUtil.isEmpty(lot.getBatchId())) {
				List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			
				StringBuffer sqlBuffer = new StringBuffer(" status = 'Active' and lotId in (");
				for(Lot batchLot : lots) {
					sqlBuffer.append("'" + batchLot.getLotId() + "',");
				}
				String sql = sqlBuffer.substring(0, sqlBuffer.length() - 1) + ") and stepName = '" + lot.getStepName() + "' ";
				List<EdcTecn> edcTecns = adManager.getEntityList(Env.getOrgRrn(), EdcTecn.class, Integer.MAX_VALUE, sql, null);
				if(edcTecns != null && !edcTecns.isEmpty()) {
					List<EdcTecn> carrLotEdcs = edcTecns.stream().filter(p -> p.getLotId().equals(lot.getLotId())).collect(Collectors.toList());
					if(carrLotEdcs.isEmpty()) {
						UI.showWarning(Message.getString("edc.please_select_other_lot_with_the_same_batch"));
						return false;
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPage : checkBatchEdcTecn() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}
	
	/**
	 * �������λ�ȡ����Run����
	 * @param lot
	 * @return
	 * @throws Exception
	 */
	protected List<Lot> getRunningLotList(Lot lot) throws Exception {
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}
	
	/**
	 * ��վ
	 * @param object
	 */
	protected void trackOutAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			
			Event event = (Event) object;
			String operator1 = Env.getUserName();
			if (event != null && event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}	
			
			TrackOutContext context = new TrackOutContext();
			context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);

			List<Lot> lots =  getTrackLotList();
			if (lots == null || lots.size() == 0) {
				UI.showWarning(Message.getString("wip_not_select_lot"));
				return;
			}
			
			lots = getRunningLotList(lots.get(0));
			context.setLots(lots);
			context.setOperator1(operator1);
			
			Step step = new Step();
			step.setObjectRrn(currentStep.getObjectRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
					
			context.setOperator1(operator1);
			TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(step.getTrackOutFlow());
			wizard.setContext(context);

			TrackOutDialog dialog = new TrackOutDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackOutDialog.FIN) {
				refreshAdapter(object);

				if (((TrackOutWizard) wizard).getContext().getOutLots().size() > 0) {
					Lot trackOutLot = ((TrackOutWizard) wizard).getContext().getOutLots().get(0);
					if (!LotStateMachine.STATE_RUN.equals(trackOutLot.getState())) {
						UI.showInfo(Message.getString("wip.trackout_success"));
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByStepPage : trackOutAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}	
	}
	
	/**
	 * ȡ����վ
	 * @param object
	 */
	protected void abortAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			
			Event event = (Event) object;
			String operator1 = Env.getUserName();
			if (event != null && event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
						
			InContext context = new InContext();

			List<Lot> lots =  getTrackLotList();
			if (lots == null || lots.size() == 0) {
				UI.showWarning(Message.getString("wip_not_select_lot"));
				return;
			}
			
			lots = getRunningLotList(lots.get(0));
			context.setLots(lots);
			context.setOperator1(operator1);

			Step step = new Step();
			step.setObjectRrn(currentStep.getObjectRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setCurrentStep(step);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkAbortConstraint(context, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			AbortWizard wizard = (AbortWizard)WizardPageExtensionPoint.getWizardRegistry().get(step.getAbortWizard());
			wizard.setContext(context);

			AbortDialog dialog = new AbortDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == AbortDialog.FIN) {
				refreshAdapter(object);
			}
		} catch (Exception e) {
			logger.error("Error at ByStepPage : abortAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ˢ��
	 */
	protected void refreshAdapter(Object object) {}

	
	public Equipment getCurrentEquipment() {
		return currentEquipment;
	}

	public void setCurrentEquipment(Equipment currentEquipment) {
		this.currentEquipment = currentEquipment;
	}

	public Step getCurrentStep() {
		return currentStep;
	}

	public void setCurrentStep(Step currentStep) {
		this.currentStep = currentStep;
	}

	public GlcForm getRootForm() {
		return rootForm;
	}

	public void setRootForm(GlcForm rootForm) {
		this.rootForm = rootForm;
	}
	
}
