package com.glory.mes.wip.bystep;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.TableLegend;
import com.glory.framework.base.ui.nattable.color.ICellBackgroundFunc;
import com.glory.framework.base.ui.nattable.color.ICellForegroundFunc;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.custom.ByStepEquipmentLedCustomComposite;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class ByStepDefaultPage extends ByStepPage implements IRefresh {

	private static final Logger logger = Logger.getLogger(ByStepDefaultPage.class);
	
	public static final String BYEQP_PREPARE_DIALOG_FORM_NAME = "WIPLotByEqpPrepareDialog";
	
	public static final String FIELD_LOTFORM = "lotForm";

	public static final String FIELD_EQUIPMENT_ID = "equipmentId";
	public static final String FIELD_LOT_ID = "lotId";
	public static final String FIELD_TRACKLOTS = "trackLots";
	public static final String FIELD_EQUIPMENT_LED = "equipmentLed";

	public static final String BUTTON_TRACKMOVE = "trackmove";
	public static final String BUTTON_TRACKIN = "trackin";
	public static final String BUTTON_DCOP = "dcop";
	public static final String BUTTON_TRACKOUT = "trackout";
	public static final String BUTTON_ABORT = "abort";
	public static final String BUTTON_REFRESH = "refresh";
		
	protected TextField fieldEqp;
	protected TextField fieldLot;
	protected CustomField fieldTrackLots;
	protected CustomField fieldEqpLed;
	protected ByStepEquipmentLedCustomComposite stepEquipmentLedCustomComposite;
	protected LotListComposite lotListComposite;
	protected ToolItem itemTrackMove;
	protected ToolItem itemRefresh;
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		fieldEqp = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_EQUIPMENT_ID, TextField.class);
		fieldLot = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_LOT_ID, TextField.class);
		
		fieldTrackLots = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  FIELD_TRACKLOTS, CustomField.class);
		lotListComposite = (LotListComposite)fieldTrackLots.getCustomComposite();
		lotListComposite.setRefreshObject(this);
		
		fieldEqpLed = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_EQUIPMENT_LED, CustomField.class);		
		stepEquipmentLedCustomComposite = (ByStepEquipmentLedCustomComposite)fieldEqpLed.getCustomComposite();
	
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_EQUIPMENT_ID, GlcEvent.EVENT_ENTERPRESSED), this::equipmentEnterPressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_LOT_ID, GlcEvent.EVENT_ENTERPRESSED), this::lotEnterPressed);

		subscribeAndExecute(eventBroker, fieldTrackLots.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionAdaptor);	
		subscribeAndExecute(eventBroker, fieldEqpLed.getFullTopic(GlcEvent.EVENT_CLICK), this::equipmentLedClickAdaptor);	
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_TRACKMOVE), this::trackMoveAdapter);						
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_TRACKIN), this::trackInAdapter);								
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_DCOP), this::docpAdapter);				
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_TRACKOUT), this::trackOutAdapter);		
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_ABORT), this::abortAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_REFRESH), this::refreshAdapter);
				
		itemTrackMove = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_TRACKMOVE);
		itemTrackIn = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_TRACKIN);
		itemDcop = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_DCOP);
		itemTrackOut = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_TRACKOUT);
		itemAbort = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_ABORT);
		itemRefresh = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_REFRESH);
		
		itemTrackMove.setEnabled(false);
		itemTrackIn.setEnabled(false);
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);	
		itemAbort.setEnabled(false);	
		//������ɫͼ��
		initColor();
		addKeyListener();
	}
	
	private void initColor() {
	    try {
	        List<TableLegend> legends = Lists.newArrayList();
	        TableLegend legend = new TableLegend();
            legend.setColor(new Color(Display.getCurrent(), 0, 255, 0));
            legend.setLegendText(Message.getString("wip.run_lot"));
            legends.add(legend);	        
	        //���¼���ͼ��
            legends.addAll(lotListComposite.getTableLegends());
	        lotListComposite.setTableLegends(legends);
	        // ���ӱ���ɫ��ɫ����
	        lotListComposite.addBackgroundFunc(new HoldLotColor());
	        //����ǰ��ɫ��ɫ����
	        lotListComposite.addForegroundFunc(new HoldLotColor());
	    } catch (Exception e) {
	        ExceptionHandlerManager.asyncHandleException(e);
	    }
	}
	
	private class HoldLotColor implements ICellBackgroundFunc, ICellForegroundFunc {
		@Override
		public Color getForegroundColor(Object element, String id) {
			return null;
		}

		@Override
		public Color getBackgroundColor(Object element, String id) {
			if ("cstate".equals(id)) {
				Lot lot = (Lot) element;
				if (Lot.PROCESS_STATE_RUN.equals(lot.getState())) {
					return SWTResourceCache.getColor(SWTResourceCache.COLOR_GREEN);
				} else if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
					return SWTResourceCache.getColor(SWTResourceCache.COLOR_RED);
				}
			}
			return null;
		}
	}
	
	protected void addKeyListener() {

	}
	
	/**
	 * ����豸ѡ����Ӧ�¼�
	 * @param object
	 */
	protected void stepSelectionChanged(Object object) {
		try {
			Event event = (Event) object;		
			//��ȡѡ�е��豸
			Step step = (Step) event.getProperty(GlcEvent.PROPERTY_DATA);	
			if (step != null) {
				currentStep = step;
				currentEquipment = null;
				refresh();				
			} else {
				clear();		
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ����豸ѡ����Ӧ�¼�
	 * @param object
	 */
	protected void equipmentLedClickAdaptor(Object object) {
		try {
			Event event = (Event) object;		
			//��ȡѡ�е��豸
			Equipment equipment = (Equipment) event.getProperty(GlcEvent.PROPERTY_DATA);	
			if (equipment != null) {
				currentEquipment = equipment;
				fieldEqp.setValue(equipment.getEquipmentId());
				fieldEqp.refresh();
				stepEquipmentLedCustomComposite.equipmentChanged(equipment);
			} else {
				currentEquipment = null;
				fieldEqp.setValue(null);
				fieldEqp.refresh();	
				stepEquipmentLedCustomComposite.equipmentChanged(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ����������Ϣˢ��
	 */
	public void refresh() {
		try {
			if (currentStep != null && currentStep.getObjectRrn() != null) {			
				List<Lot> lots = lotManager.getLotsByStep(Env.getOrgRrn(), currentStep.getName(), Env.getSessionContext());
				fieldTrackLots.setValue(lots);
				fieldTrackLots.refresh();		
				
				stepEquipmentLedCustomComposite.stepChanged(currentStep);	
			} else {
				fieldTrackLots.setValue(null);
				fieldTrackLots.refresh();	
				stepEquipmentLedCustomComposite.stepChanged(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ����ѡ���¼�
	 * @param object
	 */
	protected void selectionAdaptor(Object object) {
		try {
			Event event = (Event) object;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lot != null) {
				fieldLot.setValue(lot.getLotId());
				fieldLot.refresh();
				
				lotStatusChanged(lot.getState(), lot.getHoldState());
				
				if (LotStateMachine.STATE_WAIT.equals(lot.getState()) 
						|| LotStateMachine.STATE_DISP.equals(lot.getState())) {
					ListTableManager tableManager = lotListComposite.getTableManager();
					List<Object> objects = tableManager.getCheckedObject();
					List<Lot> lots = objects.stream().map(o -> (Lot)o).collect(Collectors.toList());
					Optional<Lot> f = lots.stream().filter(r -> Lot.HOLDSTATE_ON.equals(r.getHoldState())).findFirst();
					if (f.isPresent()) {
						if (itemTrackIn != null) {
							itemTrackIn.setEnabled(false);
						}
					} else {
						if (itemTrackIn != null) {
							itemTrackIn.setEnabled(true);
						}
					}
				}
			} else {
				fieldLot.setValue("");
				fieldLot.refresh();
				
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			}						
		} catch (Exception e) {
			logger.error("Error at ByStepDefaultPage : selectionAdaptor() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * �豸�ؼ��س��¼�
	 * @param obj
	 */
	protected void equipmentEnterPressed(Object object) {
		try {
			Event event = (Event) object;
			String equipmentId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentId, false);
			if (equipment != null) {
				currentEquipment = equipment;
				stepEquipmentLedCustomComposite.equipmentChanged(equipment);
			} else {
				currentEquipment = null;
				stepEquipmentLedCustomComposite.equipmentChanged(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	/**
	 * ���οؼ��س��¼�
	 * @param obj
	 */
	protected void lotEnterPressed(Object object) {
		try {
			if (currentStep != null) {
				Event event = (Event) object;
				String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
				Lot lot = LotProviderEntry.getLotByLine(lotId);
				if (lot != null) {		
					if (currentStep.getIsBatch()) {
						if (lotListComposite.getTableManager().getInput().contains(lot)) {
							lotListComposite.getTableManager().setCheckedObject(lot);
						}
					} else {
						if (lotListComposite.getTableManager().getInput().contains(lot)) {
							lotListComposite.getTableManager().setSelection(new StructuredSelection(new Object[] {lot}));
						} else {
							lotListComposite.getTableManager().setSelection(null);
						}
					}			
				}
			}			
		} catch (Exception e) {
			logger.error("Error at ByStepDefaultPage : lotEnterPressed() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	/**
	 * ����������Ϣˢ��
	 */
	protected void refreshAdapter(Object object) {
		try {
			refresh();
			
			fieldEqp.setText(null);
			fieldLot.setText(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ���ҳ����λ��Ϣ
	 */
	protected void clear() {
		fieldEqp.setText(null);
		fieldLot.setText(null);
		
		fieldTrackLots.setValue(null);
		fieldTrackLots.refresh();
		
		stepEquipmentLedCustomComposite.stepChanged(null);
	}

	/**
	 * ����վ����
	 */
	@Override
	protected List<Lot> getTrackLotList() {
		if (lotListComposite.getTableManager().isCheckFlag()) {
			return (List<Lot>)(List)lotListComposite.getTableManager().getCheckedObject();
		} else {
			List<Lot> lots = Lists.newArrayList();
			Lot lot = (Lot) lotListComposite.getSelectedObject();
			lots.add(lot);
			return lots;
		}
	}
	
	@Override
	public void setWhereClause(String whereClause) {}

	@Override
	public String getWhereClause() {
		return null;
	}

	@Override
	public boolean isUseParam() {
		return false;
	}

	@Override
	public Map<String, Object> getParameterMap() {
		return null;
	}
	
}
