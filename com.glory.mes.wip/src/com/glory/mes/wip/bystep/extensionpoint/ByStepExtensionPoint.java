package com.glory.mes.wip.bystep.extensionpoint;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

public class ByStepExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(ByStepExtensionPoint.class);
	
	private static final ByStepExtensionPoint instance = new ByStepExtensionPoint();
	
	private static Map<String, IConfigurationElement> registry = new HashMap<String, IConfigurationElement>();

	public final static String X_POINT = "com.glory.mes.wip.bystep";
    
    public final static String E_MANAGER = "manager";
    public final static String A_NAME = "name";
    public final static String A_CLASS = "class";
    public final static String A_STEP_USECATOGORY = "stepUseCategory";
    public final static String A_FORM_NAME = "formName";
    
	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			try {
				IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
				for (int j = 0; j < configElements.length; j++) {
					if (E_MANAGER.equals(configElements[j].getName())) {
						String name = configElements[j].getAttribute(A_NAME);
						String stepUseCategory = configElements[j].getAttribute(A_STEP_USECATOGORY);

						registry.put(stepUseCategory, configElements[j]);

					}
				}
			} catch (Exception e) {
				logger.error("ByEqpExtensionPoint : init ", e);
			}
		}			
	}
    
    public static ByStepExtensionPoint getInstance() {
    	return instance;
    }

    public IByStepPage getPage(String stepUseCategory) {
    	try {
	    	if (registry.containsKey(stepUseCategory)) {
	    		IByStepPage page = (IByStepPage)registry.get(stepUseCategory).createExecutableExtension(A_CLASS);
	    		page.setAdFormName(registry.get(stepUseCategory).getAttribute(A_FORM_NAME));
	    		return page;
	    	}
    	} catch (Exception e) {
			logger.error("ByStepExtensionPoint : getPage ", e);
		}
    	return null;
    }
    
    public static IConfigurationElement getRegistry(String stepUseCategory) {
		return registry.get(stepUseCategory);
	}
    
    public static Map<String, IConfigurationElement> getRegistry() {
		return registry;
	}

	public static void setRegistry(Map<String, IConfigurationElement> registry) {
		ByStepExtensionPoint.registry = registry;
	}
	
}
