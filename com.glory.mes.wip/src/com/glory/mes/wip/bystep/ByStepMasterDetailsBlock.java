package com.glory.mes.wip.bystep;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcMasterDetailsBlock;
import com.glory.framework.base.entitymanager.glc.GlcPageProvider;
import com.glory.mes.wip.bystep.extensionpoint.ByStepExtensionPoint;
import com.glory.mes.wip.bystep.extensionpoint.IByStepPage;
import com.google.common.collect.Maps;

public class ByStepMasterDetailsBlock extends GlcMasterDetailsBlock {
	
	protected Set<String> stepUseCategorySet;
	
	protected List<IByStepPage> detailsPages = new ArrayList<IByStepPage>();
	
	public ByStepMasterDetailsBlock(ADTab masterTab) {
		super(masterTab, null);
	}
	
	@Override
	protected void registerPages(DetailsPart detailsPart) {
		detailsPages.clear();
		stepUseCategorySet = ByStepExtensionPoint.getInstance().getRegistry().keySet();
		for (String stepUseCategory :  stepUseCategorySet) {
			IByStepPage page = ByStepExtensionPoint.getInstance().getPage(stepUseCategory);
			page.setAuthority(glcForm.getAuthority());
			page.setEventBroker(glcForm.getEventBroker());
			page.setRootForm(glcForm);
			detailsPart.registerPage(stepUseCategory, page);
			detailsPages.add(page);
		}
		
		detailsPart.setPageProvider(new GlcPageProvider());
		
		if (stepUseCategorySet.size() >= 1) {
			//Ĭ����ʾ
			Map<String, Object> eventData = Maps.newHashMap();
			eventData.put(GlcEvent.PROPERTY_PAGE_KEY, ByStepPage.DEFAULT_STEP_USE_CATEGORY);	
			glcForm.postEvent(null, GlcEvent.EVENT_GLC_MDFIRESECTION, eventData);
		}
	}
	
	//�����¼�
	public void preDestory() {
		if (detailsPages.size() > 0) {
			for (IByStepPage page : detailsPages) {
				if (page instanceof ByStepPage) {
					((ByStepPage)page).preDestory();
				}
			}
		}
	}

}
