package com.glory.mes.wip.batch;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;

public class BatchLotEditComposite extends BatchLotComposite {

	private static final Logger logger = Logger.getLogger(BatchLotEditComposite.class);
			
	private static final String TABLE_NAME = "WIPBatchSplitMergeManager";
										   
	public BatchLotEditComposite(Composite parent, int style, boolean checkFlag, boolean showLotFlag) {
		super(parent, style, checkFlag, showLotFlag);
	}

	public void createPartControl() {
		try {
			this.setLayout(new GridLayout(1, false));
			this.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite carrierComposite = new Composite(this, SWT.NONE);
			int gridY = 2;
			if (showLotFlag) {
				gridY += 2;
			}

			carrierComposite.setLayout(new GridLayout(gridY, false));

			Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
			if (StringUtil.isEmpty(lblBatch)) {
				lblCarrierId.setText(Message.getString("common.batch_id"));
			} else {
				lblCarrierId.setText(lblBatch);
			}
			lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

			txtBatchId = new HeaderText(carrierComposite);
			txtBatchId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtBatchId.setTextLimit(32);

			txtBatchId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// �س��¼�
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String batchId = ((Text) event.widget).getText();
						List<Lot> queryLots = getLotsByBatchIdOrLotId(null, batchId);
						if (CollectionUtils.isEmpty(queryLots)) {
							txtBatchId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							txtBatchId.warning();
						} else {
							lotTableManager.setInput(queryLots);
							txtBatchId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
							txtBatchId.focusing();
							txtBatchId.selectAll();
						}
						
						if(txtLotId != null) {
							txtLotId.setText("");
						}
						lotTableManager.refresh();
					}
				}
			});
			if (showLotFlag) {
				Label lblLotId = new Label(carrierComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtLotId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-lot"));
				txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtLotId.setTextLimit(64);

				txtLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// �س��¼�
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText();
							List<Lot> queryLots = getLotsByBatchIdOrLotId(lotId, null);
							
							if (CollectionUtils.isEmpty(queryLots)) {
								txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
								txtLotId.warning();
							} else {
								lotTableManager.setInput(queryLots);
								txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
								txtLotId.focusing();
								txtLotId.selectAll();
							}
							
							if(txtBatchId != null) {
								txtBatchId.setText("");
							}
							lotTableManager.refresh();
						}
					}
				});
			}

//			Composite lotComposite = new Composite(this, SWT.V_SCROLL | SWT.H_SCROLL);
//			lotComposite.setLayout(new GridLayout(1, false));
//
//			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
//			gridData.heightHint = tableHeigthHint;
//			lotComposite.setLayoutData(gridData);
//			lotComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));

			Composite resultComp = new Composite(this, SWT.NONE);
			GridLayout layout = new GridLayout(); 
	        layout.verticalSpacing = 0;
	        layout.marginHeight = 0;
	        resultComp.setLayout(layout);
	        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
	        
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotTableManager = new ListEditorTableManager(lotTable, checkFlag);
			lotTableManager.setAutoSizeFlag(true);
			lotTableManager.newViewer(resultComp);
			lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					StructuredSelection selection = (StructuredSelection) event.getSelection();
					Lot lot = (Lot) selection.getFirstElement();

				}
			});

		} catch (Exception e) {
			logger.error("BatchLotComposite createPartControl error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
//	@Override
//	public void getLotsByBatchId(String batchId) {
//		try {
//			LotManager lotManager = Framework.getService(LotManager.class);
//			List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), batchId);
//			lotTableManager.setInput(lots);
//			txtBatchId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
//			lotTableManager.refresh();
//
//		} catch (Exception e) {
//			e.printStackTrace();
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
//	
//	@Override
//	public void getBatchLotsByLotId(String lotId) {
//		try {
//			Lot lot = LotProviderEntry.getLot(lotId);
//			if (lot == null) {
//				UI.showError(Message.getString(""));
//				return;
//			} 
//			
//			List<Lot> lots = new ArrayList<Lot>();
//			if (!StringUtil.isEmpty(lot.getBatchId())) {
//				LotManager lotManager = Framework.getService(LotManager.class);
//				lots = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
//			} else {
//				UI.showInfo(Message.getString("pp.lot_is_not_batch_lot"));
//				return; 
//			}
//			
//			List<Lot> lotViews = new ArrayList<Lot>();
//			for (Lot batchLot : lots) {
//				if (!"COM".equals(batchLot.getComClass())) {
//					lotViews.add(batchLot);
//				}
//			}
//			
//			lotTableManager.setInput(lotViews);
//
//			txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
//			lotTableManager.refresh();
//
//			lotTableManager.setSelection(new StructuredSelection(new Object[] { lot }));
//		} catch (Exception e) {
//			e.printStackTrace();
//			ExceptionHandlerManager.asyncHandleException(e);
//		}
//	}
	
	@Override
	public List<Lot> getLotsByBatchIdOrLotId(String lotId, String batchId) {
		List<Lot> queryLots = super.getLotsByBatchIdOrLotId(lotId, batchId);
		// ��ʾ��batch��
		List<Lot> batchLots = new ArrayList<Lot>();
		for (Lot lot : queryLots) {
			if (!StringUtil.isEmpty(lot.getBatchId())) {
				batchLots.add(lot);
			}
		}
		return batchLots;
	}
	
}
