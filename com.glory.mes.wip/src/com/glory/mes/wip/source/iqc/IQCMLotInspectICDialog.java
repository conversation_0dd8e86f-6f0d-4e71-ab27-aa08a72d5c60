package com.glory.mes.wip.source.iqc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.KeyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotIQC;
import com.glory.mes.mm.lot.model.MLotIQCDetail;
import com.glory.framework.core.exception.ExceptionBundle;

public class IQCMLotInspectICDialog extends BaseTitleDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
    private static int MIN_DIALOG_HEIGHT = 500;
			
	public Text testQtyText;
	public Text badQtyText;
	public Text badYieldText;
	
	public ListTableManager tableManager;
	public XCombo comboBadCode;
	public XCombo comboActionCode;
	public Text iqcBadQtyText;
	
	public ADManager entityManager;
	
	protected MLot mlot;
	public List<MLotIQC> iqcDatas;
	
	public IQCMLotInspectICDialog(MLot mlot) {
		super();
		this.mlot = mlot;
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setText(Message.getString("mm.iqc_samp_inspect"));
	}
	
	@Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }
	
	protected void createFormContent(Composite parent) {	
		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		Composite content = new Composite(parent, SWT.NONE);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		try {
			entityManager = (ADManager)Framework.getService(ADManager.class);
			iqcDatas = entityManager.getEntityList(Env.getOrgRrn(), MLotIQC.class, Env.getMaxResult(), "mLotRrn = " + mlot.getObjectRrn(), "");
			
			Composite groupContent = new Composite(content, SWT.BORDER);
			groupContent.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			
			GridLayout layout = new GridLayout(1, false);
			GridData gd = new GridData(GridData.FILL_BOTH);
			groupContent.setLayout(layout);				
			groupContent.setLayoutData(gd);
			
			Group group1 = new Group(groupContent, SWT.NONE);
			group1.setText(Message.getString("mm.iqc_lot_info"));
			group1.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			GridLayout layoutGroup1 = new GridLayout(6, false);
			group1.setLayout(layoutGroup1);
			group1.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
			GridData gridDataLabel = new GridData(GridData.FILL_HORIZONTAL);		
			gridDataLabel.minimumWidth = 20;
			GridData gridDataText = new GridData(GridData.FILL_HORIZONTAL);		
			gridDataText.minimumWidth = 115;
			
			Label labelLotId = toolkit.createLabel(group1, Message.getString("mm.mlot_id"), SWT.NONE);
			labelLotId.setLayoutData(gridDataLabel);
			Text lotIdText = toolkit.createText(group1, "");
			lotIdText.setLayoutData(gridDataText);
			lotIdText.setEnabled(false);
			lotIdText.setText(mlot.getmLotId() != null ? mlot.getmLotId() : "");
			
			Label matTypeLabel = toolkit.createLabel(group1, Message.getString("mm.iqc_lot_mattype"), SWT.NONE);
			matTypeLabel.setLayoutData(gridDataLabel);
			Text matTypeText = toolkit.createText(group1, "");
			matTypeText.setLayoutData(gridDataText);
			matTypeText.setEnabled(false);			
			matTypeText.setText(mlot.getMaterialType() != null ? mlot.getMaterialType() : "");
			
			Label testQtyLabel = toolkit.createLabel(group1, Message.getString("mm.iqc_test_qty"), SWT.NONE);
			testQtyLabel.setLayoutData(gridDataLabel);
			testQtyText = toolkit.createText(group1, "");
			testQtyText.setLayoutData(gridDataText);
			if (iqcDatas != null && iqcDatas.size() > 0) {
				testQtyText.setText(iqcDatas.get(0).getLotSampQty() != null ? iqcDatas.get(0).getLotSampQty().toString() : "");
			} else {
				testQtyText.setText(mlot.getMainQty().toString());
			}
			testQtyText.addKeyListener(new KeyListener(){
				@Override
				public void keyPressed(KeyEvent e) {
					if (testQtyText.getText().length() == 1) {
						badYieldText.setText("");
					}
				}
				@Override
				public void keyReleased(KeyEvent e) {
					if (badQtyText.getText() != null && !"".equals(badQtyText.getText())) {						
						badYieldText.setText(String.format("%.6f", Double.valueOf(badQtyText.getText())/Double.valueOf(testQtyText.getText())));						
					} else {
						badYieldText.setText("");
					}
				}								
			});
			testQtyText.addVerifyListener(new VerifyListener() {
				public void verifyText(VerifyEvent e) {
					nameCheckAdapter(e, testQtyText);
				}
			});
			
			Label badQtyLabel = toolkit.createLabel(group1, Message.getString("mm.iqc_bad_qty"), SWT.NONE);
			badQtyLabel.setLayoutData(gridDataLabel);
			badQtyText = toolkit.createText(group1, "");
			badQtyText.setLayoutData(gridDataText);
			badQtyText.setEnabled(false);
			if (iqcDatas != null && iqcDatas.size() > 0) {
				badQtyText.setText(iqcDatas.get(0).getBadQty() != null ? iqcDatas.get(0).getBadQty().toString() : "");
			} else {
				badQtyText.setText("0");
			}
			
			Label badYieldLabel = toolkit.createLabel(group1, Message.getString("mm.iqc_bad_yield"), SWT.NONE);
			badYieldLabel.setLayoutData(gridDataLabel);
			badYieldText = toolkit.createText(group1, "");
			badYieldText.setLayoutData(gridDataText);
			badYieldText.setEnabled(false);
			if (iqcDatas != null && iqcDatas.size() > 0 && iqcDatas.get(0).getLotSampQty() != null
					&& iqcDatas.get(0).getBadQty() != null) {
				badYieldText.setText(String.format("%.4f",
						Double.valueOf(iqcDatas.get(0).getBadQty().toString())/Double.valueOf(iqcDatas.get(0).getLotSampQty().toString())));
			}
						
			Group group2 = new Group(groupContent, SWT.NONE);
			group2.setText(Message.getString("mm.iqc_bad_judge"));
			group2.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			GridLayout layoutGroup2 = new GridLayout(1, false);
			gd = new GridData(GridData.FILL_BOTH);
			group2.setLayout(layoutGroup2);
			group2.setLayoutData(gd);
			
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "MMMLotIQCICDetail");
			tableManager = new ListTableManager(adTable, true);
			tableManager.newViewer(group2);
			
			if (iqcDatas != null && iqcDatas.size() > 0) {
				List<MLotIQCDetail> lines = entityManager.getEntityList(Env.getOrgRrn(), MLotIQCDetail.class, Env.getMaxResult()," iqcRrn = " + iqcDatas.get(0).getObjectRrn(), "");
				tableManager.setInput(lines);
			}
			
			Composite tableComposite = toolkit.createComposite(group2);
			GridLayout layoutList = new GridLayout(10, false);
			tableComposite.setLayout(layoutList);
			GridData gdList = new GridData(GridData.FILL_BOTH);	
			gdList.horizontalAlignment = SWT.RIGHT;
			gdList.grabExcessHorizontalSpace = true;		
			tableComposite.setLayoutData(gdList);
			
			toolkit.createLabel(tableComposite, Message.getString("mm.iqc_bad_code"), SWT.NONE);							
    		adTable = entityManager.getADTable(Env.getOrgRrn(), "ADURefListView");   		
    		ListTableManager adTableManager = new ListTableManager(adTable);
			String whereClause = " referenceName = 'IQCBadCode'";
			List<ADBase> list = entityManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(),
					Env.getMaxResult(), whereClause, null);
			adTableManager.setInput(list);	
			ADRefTable refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setKeyField("key");
			refTable.setTextField("text");	
			comboBadCode = new XCombo(tableComposite, adTableManager, refTable.getKeyField(), refTable.getTextField(), SWT.NONE, true, false);   	
			comboBadCode.setLayoutData(gridDataText);			
			
			toolkit.createLabel(tableComposite, Message.getString("mm.iqc_bad_qty"), SWT.NONE);
			iqcBadQtyText = toolkit.createText(tableComposite, "");
			iqcBadQtyText.setLayoutData(gridDataText);
			iqcBadQtyText.addVerifyListener(new VerifyListener() {
				public void verifyText(VerifyEvent e) {
					nameCheckAdapter(e, testQtyText);
				}
			});
			
			toolkit.createLabel(tableComposite, Message.getString("mm.iqc_action_code"), SWT.NONE);						
    		adTable = entityManager.getADTable(Env.getOrgRrn(), "ADURefListView");   		
    		adTableManager = new ListTableManager(adTable);
			whereClause = " referenceName = 'IQCActionCode'";
			list = entityManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(),
					Env.getMaxResult(), whereClause, null);
			adTableManager.setInput(list);	
			refTable = new ADRefTable();
			refTable.setTableRrn(adTable.getObjectRrn());
			refTable.setKeyField("key");
			refTable.setTextField("text");	
			comboActionCode = new XCombo(tableComposite, adTableManager, refTable.getKeyField(), refTable.getTextField(), SWT.NONE, true, false);   	
			comboActionCode.setLayoutData(gridDataText);
			
			SquareButton addBtn = UIControlsFactory.createButton(tableComposite, Message.getString(ExceptionBundle.bundle.CommonAdd()), null);
			addBtn.addSelectionListener(new SelectionAdapter() {
				@SuppressWarnings("unchecked")
				public void widgetSelected(SelectionEvent e) {
					List<MLotIQCDetail> allMLotIQCs = new ArrayList<MLotIQCDetail>();
					List<MLotIQCDetail> mLotIQCs = (List<MLotIQCDetail>)tableManager.getInput();
					if (mLotIQCs == null) {
						mLotIQCs = new ArrayList<MLotIQCDetail>();
					}
					allMLotIQCs.addAll(mLotIQCs);
					MLotIQCDetail iqc = new MLotIQCDetail();
					iqc.setmComponentUnitId(mlot.getmLotId());
					iqc.setDefectCode(comboBadCode.getText());
					iqc.setDefectQty(BigDecimal.valueOf(Long.valueOf(iqcBadQtyText.getText())));
					iqc.setActionCode(comboActionCode.getText());
					Long num = 0l;
					for (MLotIQCDetail mIQC : mLotIQCs) {
						num = num + mIQC.getDefectQty().longValue();
					}
					num = num + iqc.getDefectQty().longValue();
					if (num > Double.valueOf(testQtyText.getText())) {
						UI.showError(Message.getString("mm.iqc_check_num_badqty"));//���������ܴ��ڼ�������
						return;
					}
					allMLotIQCs.add(iqc);
					tableManager.setInput(allMLotIQCs);
					
					badQtyText.setText(num.toString());
					if (testQtyText.getText() != null && !"".equals(testQtyText.getText())) {
						if (badQtyText.getText() != null && !"".equals(badQtyText.getText())) {						
							badYieldText.setText(String.format("%.4f",  Double.valueOf(badQtyText.getText())/Double.valueOf(testQtyText.getText())));						
						} else {
							badYieldText.setText("1");
						}
					}
				}
			});
			
			SquareButton deleteBtn = UIControlsFactory.createButton(tableComposite, Message.getString(ExceptionBundle.bundle.CommonDelete()), null);
			deleteBtn.addSelectionListener(new SelectionAdapter() {
				@SuppressWarnings("unchecked")
				public void widgetSelected(SelectionEvent e) {
					List<Object> os = tableManager.getCheckedObject();
					
					List<MLotIQCDetail> details = (List<MLotIQCDetail>)(List)tableManager.getInput();
					if (details == null) {
						details = new ArrayList<MLotIQCDetail>();
					}	
					if (os.size() != 0) {
						for (Object o : os) {
							MLotIQCDetail mIQC = (MLotIQCDetail)o;
							details.remove(mIQC);							
						}
					}
					tableManager.setInput(details);
					
					List<MLotIQCDetail> mIQCs = (List<MLotIQCDetail>)tableManager.getInput();
					Long num = 0l;
					for (MLotIQCDetail mIQC : mIQCs) {
						num = num + mIQC.getDefectQty().longValue();
					}
					badQtyText.setText(num.toString());
					if (testQtyText.getText() != null && !"".equals(testQtyText.getText())) {
						if (badQtyText.getText() != null && !"".equals(badQtyText.getText())) {						
							badYieldText.setText(String.format("%.6f", 1 - Double.valueOf(badQtyText.getText())/Double.valueOf(testQtyText.getText())));						
						} else {
							badYieldText.setText("1");
						}
					}
				}
			});			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	protected static void nameCheckAdapter(VerifyEvent e , Text text) {
		if (e.text.matches("\\d*")) {
			if ("".equals(text.getText()) && e.text.matches("0")) {
				  e.doit = false;
		     } else {
			      e.doit = true;
		    }
		} else {
			e.doit = false;
		}
	}
	
	@Override
	protected void okPressed() {
		saveIQCData();
	}
	
	private void saveIQCData() {
		try {
			if (iqcDatas != null && iqcDatas.size() > 0) {
				if (!StringUtil.isEmpty(iqcDatas.get(0).getJudgeResult())) {
					UI.showError(Message.getString("mm.iqc_mlot_state_not_allow"));	
					return;
				}
			}
			
			//�����Ǳ�����ֲ������롢�����Ͷ�����
			MLotIQC mLotIQC = null;
			if (iqcDatas != null && iqcDatas.size() > 0) {
				mLotIQC = iqcDatas.get(0);
			} else {
				mLotIQC = new MLotIQC();
				mLotIQC.setmLotId(mlot.getmLotId());
				mLotIQC.setmLotRrn(mlot.getObjectRrn());
			}	
			
			if (testQtyText.getText() != null && !"".equals(testQtyText.getText())) {
				mLotIQC.setLotSampQty(BigDecimal.valueOf(Long.valueOf(testQtyText.getText())));
			} 
			if (badQtyText.getText() != null && !"".equals(badQtyText.getText())) {
				mLotIQC.setBadQty(BigDecimal.valueOf(Long.valueOf(badQtyText.getText())));		
			} else {
				mLotIQC.setBadQty(BigDecimal.ZERO);
			}
				
			List<MLotIQCDetail> allDetails = new ArrayList<MLotIQCDetail>();
			List<MLotIQCDetail> details = (List<MLotIQCDetail>)(List)tableManager.getInput();
			if (details != null && details.size() > 0) {
				allDetails.addAll(details);
			}
			
			for (MLotIQCDetail detail : allDetails) {
				detail.setOrgRrn(Env.getOrgRrn());
				detail.setIsActive(true);
				detail.setmComponentUnitId(null);
				detail.setmComponentUnitRrn(null);							
			}
			mLotIQC.setDetails(allDetails);
			mLotIQC.setComments(null);				
			
			MMManager mmManager = Framework.getService(MMManager.class);
			mmManager.saveIqcData(mlot, mLotIQC, Env.getSessionContext());		
		
			UI.showInfo(Message.getString("mm.iqc_save_data_success"));//�����ɹ���
			close();					
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(Message.getString("mm.iqc_detect"));
		createFormContent(parent);
		return parent;
	}

}
