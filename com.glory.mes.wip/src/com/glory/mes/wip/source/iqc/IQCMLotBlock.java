package com.glory.mes.wip.source.iqc;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class IQCMLotBlock extends EntityBlock {
	
	private static final Logger logger = Logger.getLogger(IQCMLotBlock.class);
	EntityProperties page;
	
	public IQCMLotBlock(ListTableManager tableManager) {
		super(tableManager);
	}
	
	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try {
			ADTable table = getTableManager().getADTable();
			Class<?> klass = Class.forName(table.getModelClass());
			page = new IQCMLotProperties();
			page.setTable(table);
			page.setMasterParent(this);
			detailsPart.registerPage(klass, page);
		} catch (Exception e) {
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((IQCMLotProperties)page).setFocus();
	}
}
