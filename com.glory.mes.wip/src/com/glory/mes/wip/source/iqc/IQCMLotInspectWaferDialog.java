package com.glory.mes.wip.source.iqc;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotIQC;
import com.glory.mes.mm.lot.model.MLotIQCDetail;
import com.glory.framework.core.exception.ExceptionBundle;

public class IQCMLotInspectWaferDialog extends BaseTitleDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
    private static int MIN_DIALOG_HEIGHT = 500;
    
	public ListEditorTableManager tableManager;
	public Text textMainQty;
	
	public ADManager entityManager;
	
	protected MLot mlot;
	public List<MComponentUnit> componentUnitList;
	public List<MLotIQC> iqcDatas;
	
	public IQCMLotInspectWaferDialog(MLot mlot) {
		super();
		this.mlot = mlot;
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setText(Message.getString("mm.iqc_samp_inspect"));
	}
	
	@Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }
	
	protected void createFormContent(Composite parent) {		
		FormToolkit toolkit = new FormToolkit(parent.getDisplay());
		try {
			Composite complete = new Composite(parent, SWT.NONE);
			complete.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			GridLayout layout = new GridLayout(1, false);
			GridData gd = new GridData(GridData.FILL_BOTH);
			complete.setLayout(layout);				
			complete.setLayoutData(gd);
			
			entityManager = (ADManager)Framework.getService(ADManager.class);
			iqcDatas = entityManager.getEntityList(Env.getOrgRrn(), MLotIQC.class, Env.getMaxResult(), "mLotRrn = " + mlot.getObjectRrn(), "");
            createCompositeTop(complete, toolkit); //�����������
			createCompositeBottom(complete, toolkit); //�����ײ����																	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createCompositeTop(Composite parent, FormToolkit toolkit) {
		try {
			GridLayout layout = new GridLayout(6, false);
			GridData gd = new GridData(GridData.FILL_HORIZONTAL);
			
			Group group = new Group(parent, SWT.NONE);
			group.setText(Message.getString("mm.iqc_lot_info"));
			group.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			group.setLayout(layout);
			group.setLayoutData(gd);
			
			GridData gridDataLabel = new GridData(GridData.FILL_HORIZONTAL);		
			gridDataLabel.minimumWidth = 60;
			GridData gridDataText = new GridData(GridData.FILL_HORIZONTAL);		
			gridDataText.minimumWidth = 120;
			
			Label labelLotId = toolkit.createLabel(group, Message.getString("mm.mlot_id"), SWT.NONE);
			labelLotId.setLayoutData(gridDataLabel);
			Text lotIdText = toolkit.createText(group, "");
			lotIdText.setLayoutData(gridDataText);
			lotIdText.setEnabled(false);
			lotIdText.setText(mlot.getmLotId() != null ? mlot.getmLotId() : "");
			
			Label labelMaterialType = toolkit.createLabel(group, Message.getString("mm.iqc_lot_mattype"), SWT.NONE);
			labelMaterialType.setLayoutData(gridDataLabel);
			Text materialTypeText = toolkit.createText(group, "");
			materialTypeText.setLayoutData(gridDataText);
			materialTypeText.setEnabled(false);
			materialTypeText.setText(mlot.getMaterialType() != null ? mlot.getMaterialType() : "");
			
			Label labelMainQty = toolkit.createLabel(group, Message.getString("mm.iqc_lot_main_qty"), SWT.NONE);
			labelMainQty.setLayoutData(gridDataLabel);
			textMainQty = toolkit.createText(group, "");
			textMainQty.setLayoutData(gridDataText);
			textMainQty.setText(mlot.getMainQty() != null ? mlot.getMainQty().toString() : "0");
			textMainQty.setEnabled(false);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
		
	protected void createCompositeBottom(Composite parent, FormToolkit toolkit) {
		try {
			GridLayout layout = new GridLayout(1, false);
			GridData gd = new GridData(GridData.FILL_BOTH);
			
			Group group = new Group(parent, SWT.NONE);
			group.setText(Message.getString("mm.iqc_bad_judge"));
			group.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			group.setLayout(layout);
			group.setLayoutData(gd);
			
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "MMMLotIQCDetail");
			tableManager = new ListEditorTableManager(adTable, true);
			tableManager.newViewer(group);
			
			if (iqcDatas != null && iqcDatas.size() > 0) {
				List<MLotIQCDetail> details = entityManager.getEntityList(Env.getOrgRrn(), MLotIQCDetail.class, Env.getMaxResult(),
						" iqcRrn = " + iqcDatas.get(0).getObjectRrn(), " mComponentUnitId ");
				tableManager.setInput(details);
			}
			
			Composite btnComposite = toolkit.createComposite(group);
			GridLayout layoutList = new GridLayout(5, false);
			btnComposite.setLayout(layoutList);
			GridData gdList = new GridData(GridData.FILL_BOTH);	
			gdList.horizontalAlignment = SWT.RIGHT;
			gdList.grabExcessHorizontalSpace = true;		
			btnComposite.setLayoutData(gdList);
			
			SquareButton addBtn = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonAdd()), null);
			addBtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent e) {					
					try {
						IQCWaferSelectListDialog dialog = new IQCWaferSelectListDialog(Display.getCurrent().getActiveShell(), mlot);
						if (dialog.open() == Dialog.OK) {
							if (dialog.getSelectComponentUnits() != null && dialog.getSelectComponentUnits().size() > 0) {
								List<MLotIQCDetail> allIqcDetails = new ArrayList<MLotIQCDetail>();
								List<MLotIQCDetail> iqcDetails = (List<MLotIQCDetail>)(List)tableManager.getInput();
								allIqcDetails.addAll(iqcDetails);
								for (MComponentUnit unit : dialog.getSelectComponentUnits()) {
									MLotIQCDetail iqcDetail = new MLotIQCDetail();
									iqcDetail.setmComponentUnitId(unit.getmComponentId());
									iqcDetail.setmComponentUnitRrn(unit.getObjectRrn());
									allIqcDetails.add(iqcDetail);
								}
								tableManager.setInput(allIqcDetails);
							}
						}							
					} catch (Exception e1) {
						ExceptionHandlerManager.asyncHandleException(e1);
					}					
				}
			});	
			
			SquareButton delBtn = UIControlsFactory.createButton(btnComposite, Message.getString(ExceptionBundle.bundle.CommonDelete()), null);
			delBtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent e) {					
					try {
						List<Object> os = tableManager.getCheckedObject();//��ȡѡ�е�ԲƬ��
						if (CollectionUtils.isNotEmpty(os)) {
							tableManager.getInput().removeAll(os);
						}
					} catch (Exception e1) {
						ExceptionHandlerManager.asyncHandleException(e1);
					}					
				}
			});				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}			
	}

	@Override
	protected void okPressed() {
		saveIqcData();
	}
	
	public void saveIqcData() {
		try {	
			if (iqcDatas != null && iqcDatas.size() > 0) {
				if (!StringUtil.isEmpty(iqcDatas.get(0).getJudgeResult())) {
					UI.showError(Message.getString("mm.iqc_mlot_state_not_allow"));	
					return;
				}
			}
			
			List<MLotIQCDetail> allIqcDetails = new ArrayList<MLotIQCDetail>();
			
			List<MLotIQCDetail> iqcTetails = (List<MLotIQCDetail>)(List)tableManager.getInput();
			if (iqcTetails != null && iqcTetails.size() > 0) {
				allIqcDetails.addAll(iqcTetails);
			}
			
			for (MLotIQCDetail allIqcDetail : allIqcDetails) {
				allIqcDetail.setOrgRrn(Env.getOrgRrn());
				allIqcDetail.setIsActive(true);
				if (StringUtil.isEmpty(allIqcDetail.getDefectCode())) {
					UI.showError(Message.getString("mm.iqc_data_no_input"));
					return;
				}
				if (StringUtil.isEmpty(allIqcDetail.getActionCode())) {
					UI.showError(Message.getString("mm.iqc_data_no_input"));
					return;
				}
			}
			
			MLotIQC mLotIQC = null;
			if (iqcDatas != null && iqcDatas.size() > 0) {
				mLotIQC = iqcDatas.get(0);
			} else {
				mLotIQC = new MLotIQC();
				mLotIQC.setmLotId(mlot.getmLotId());
				mLotIQC.setmLotRrn(mlot.getObjectRrn());
			}	
			mLotIQC.setComments(null);
			mLotIQC.setDetails(allIqcDetails);		
	 
			MMManager mmManager = Framework.getService(MMManager.class);
			mmManager.saveIqcData(mlot, mLotIQC, Env.getSessionContext());
			
			UI.showInfo(Message.getString("mm.iqc_save_data_success"));//�����ɹ���
			close();						
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(Message.getString("mm.iqc_detect"));
		createFormContent(parent);
		return parent;
	}
	
}
