package com.glory.mes.wip.source.iqc;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotIQC;

public class IQCWaferInfoChangeDialog extends BaseTitleDialog {
	
	public static final String TABLE_NAME = "MMMComponentUnit";
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 500;
	
	public CheckBoxFixEditorTableManager manager;
	private List<MComponentUnit> units;
	private MLot mLot;
	
	protected IQCWaferInfoChangeDialog(Shell parentShell) {
		super(parentShell);
	}
	
	public IQCWaferInfoChangeDialog(Shell parentShell, MLot mLot) {
		this(parentShell);
		this.mLot = mLot;
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setText(Message.getString("mm.iqc_lot_wafer_list"));
	}
	
	@Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }
	
	protected void createFormContent(Composite parent) {	
		Composite content = new Composite(parent, SWT.NONE);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setBackground(new Color(Display.getCurrent(), 255, 255, 255));

		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			String whereCause = " parentMLotRrn = " + mLot.getObjectRrn();
			units = entityManager.getEntityList(Env.getOrgRrn(), MComponentUnit.class, Env.getMaxResult(), whereCause, "");
	
			ADManager adManager = Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
            manager = new CheckBoxFixEditorTableManager(adTable);
            manager.newViewer(content);
            manager.setInput(units);
		} catch (Exception e) {			
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void okPressed() {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			List<MLotIQC> iqcDatas = entityManager.getEntityList(Env.getOrgRrn(), MLotIQC.class, Env.getMaxResult(), "mLotRrn = " + mLot.getObjectRrn(), "");
			if (iqcDatas != null && iqcDatas.size() > 0) {
				if (!StringUtil.isEmpty(iqcDatas.get(0).getJudgeResult())) {
					UI.showError(Message.getString("mm.iqc_mlot_state_not_allow"));	
					return;
				}
			}
			
			List<MComponentUnit> mComponentUnits = (List<MComponentUnit>)(List)manager.getInput();
			List<MComponentUnit> allMComponentUnits = new ArrayList<MComponentUnit>();
			allMComponentUnits.addAll(mComponentUnits);
			
			MMManager mmManager = Framework.getService(MMManager.class);
			mmManager.modifyMComponentUnits(mLot, allMComponentUnits, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		close();
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(Message.getString("wip.changewafer_wafer_info"));
		createFormContent(parent);
		return parent;
	}
	
}
