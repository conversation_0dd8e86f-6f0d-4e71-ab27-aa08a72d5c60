package com.glory.mes.wip.source.iqc;

import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotIQC;
import com.glory.mes.mm.lot.model.MLotIQCDetail;

public class IQCMLotProperties extends EntityProperties {
	
	protected static final String MATERIAL_TYPE_IC = "IC";
	protected static final String MATERIAL_TYPE_WF = "WAFER";
	
	protected ToolItem itemChange;
	protected ToolItem itemDcop;
	protected ToolItem itemReceive;
	protected ToolItem itemRejectAll;
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChange(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDcop(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemReceive(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRejectAll(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemChange(ToolBar tBar) {
		itemChange = new ToolItem(tBar,SWT.PUSH);
		itemChange.setText(Message.getString("wip.changewafer_wafer_info"));
		itemChange.setImage(SWTResourceCache.getImage("modify"));
		itemChange.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeAdapter();
			}
		});
	}
	
	protected void createToolItemDcop(ToolBar tBar) {
		itemDcop = new ToolItem(tBar, SWT.PUSH);
		itemDcop.setText(Message.getString("mm.iqc_detect"));
		itemDcop.setImage(SWTResourceCache.getImage("dcop"));
		itemDcop.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				dcopAdapter(event);
			}
		});
	}
	
	protected void createToolItemReceive(ToolBar tBar) {
        itemReceive = new ToolItem(tBar, SWT.PUSH);
        itemReceive.setText(Message.getString("common.receive"));
        itemReceive.setImage(SWTResourceCache.getImage("receive"));
        itemReceive.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent event) {
                receiveMLotAdapter(event);
            }
        });
    }
	
	protected void createToolItemRejectAll(ToolBar tBar) {
		itemRejectAll = new ToolItem(tBar, SWT.PUSH);
		itemRejectAll.setText(Message.getString("common.deny"));
		itemRejectAll.setImage(SWTResourceCache.getImage("return_lot"));
		itemRejectAll.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				rejectAllAdapter(event);
			}
		});
	}
	
	protected void changeAdapter() {
		try {
			MLot mlot = (MLot) getAdObject();
			if (mlot != null && mlot.getObjectRrn() != null) {
				if (MATERIAL_TYPE_WF.equals(mlot.getSubMatType())) {
					IQCWaferInfoChangeDialog dialog = new IQCWaferInfoChangeDialog(Display.getCurrent().getActiveShell(), mlot);
					if (dialog.open() == Dialog.OK) {
						UI.showInfo(Message.getString("common.modify_success"));
					}
				} else {
					UI.showError(Message.getString("mm.iqc_mattype_is_ic_not_allow"));
					return;
				}
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void dcopAdapter(SelectionEvent event) {
		try {
			MLot mlot = (MLot) getAdObject();
			if (mlot != null) {
				if (MATERIAL_TYPE_IC.equals(mlot.getSubMatType())) {
					IQCMLotInspectICDialog inputWaferDialog = new IQCMLotInspectICDialog(mlot);
					if (inputWaferDialog.open() == Dialog.OK) {
					}
				} else if (MATERIAL_TYPE_WF.equals(mlot.getSubMatType())) {
					IQCMLotInspectWaferDialog inputWaferDialog = new IQCMLotInspectWaferDialog(mlot);
					if (inputWaferDialog.open() == Dialog.OK) {						
					}					
				}
			}
			refreshAdapter();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void receiveMLotAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    for (IForm detailForm : getDetailForms()) {
                        PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
                                detailForm.getCopyProperties());
                    }
                                                                       
                    MLot mlot = (MLot) getAdObject();
                    if (mlot != null) {
                    	if (MLot.HOLDSTATE_ON.equals(mlot.getHoldState())) {
                    		UI.showError(Message.getString("wip.lot_is_holdstate"));
                    		return;
                    	}
                    	ADManager adManager = Framework.getService(ADManager.class);
                    	List<Warehouse> warehouses = null;
                    	if (MATERIAL_TYPE_WF.equals(mlot.getSubMatType())) {
                    		warehouses = adManager.getEntityList(Env.getOrgRrn(), Warehouse.class, Env.getMaxResult(), 
                        			"warehouseType = '" + Warehouse.WAREHOUSETYPE_STOCK + "' and warehouseGroup1 = 'receive' and warehouseGroup2 = 'WaferReceive'" , "");
                    	} else {
                    		warehouses = adManager.getEntityList(Env.getOrgRrn(), Warehouse.class, Env.getMaxResult(), 
                        			"warehouseType = '" + Warehouse.WAREHOUSETYPE_STOCK + "' and warehouseGroup1 = 'receive' and warehouseGroup2 = 'ICReceive'" , "");
                    	}
                    	
                    	List<MLotIQC> iqcDatas = adManager.getEntityList(Env.getOrgRrn(), MLotIQC.class, 
                				Env.getMaxResult(), "mLotRrn = " + mlot.getObjectRrn(), "");
                		if (iqcDatas == null || iqcDatas.size() == 0) {
                			UI.showError(Message.getString("mm.iqc_data_is_null"));
                			return;
                		}
                		for (MLotIQC iqcData : iqcDatas) {
                    		List<MLotIQCDetail> details = adManager.getEntityList(Env.getOrgRrn(), MLotIQCDetail.class, Env.getMaxResult(),
            						" iqcRrn = " + iqcDatas.get(0).getObjectRrn(), "");
                    		iqcData.setDetails(details);
                		} 
                		
                		MMManager mmManager = Framework.getService(MMManager.class);
                    	if (warehouses != null && warehouses.size() > 0) {                    		               		                 		
                    		mmManager.iqcMLot(mlot, iqcDatas.get(0), false, warehouses.get(0), Env.getSessionContext());	                   		
                    	} else {
                    		mmManager.iqcMLot(mlot, iqcDatas.get(0), false, null, Env.getSessionContext());	
                    	}
                    	UI.showInfo(Message.getString("common.receive_success"));
                    }
                    refreshAdapter();
                    this.masterParent.refresh();
                }
            } 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void rejectAllAdapter(SelectionEvent event) {
		try {
			form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                    for (IForm detailForm : getDetailForms()) {
                        PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
                                detailForm.getCopyProperties());
                    }
                                                                       
                    MLot mlot = (MLot) getAdObject();
                    if (mlot != null) {
                    	if (MLot.HOLDSTATE_ON.equals(mlot.getHoldState())) {
                    		UI.showError(Message.getString("wip.lot_is_holdstate"));
                    		return;
                    	}
                    	ADManager adManager = Framework.getService(ADManager.class);                    	                	
                    	List<MLotIQC> iqcDatas = adManager.getEntityList(Env.getOrgRrn(), MLotIQC.class, 
                				Env.getMaxResult(), "mLotRrn = " + mlot.getObjectRrn(), "");
                		if(iqcDatas != null && iqcDatas.size() > 0){
                			for (MLotIQC iqcData : iqcDatas) {
                				List<MLotIQCDetail> details = adManager.getEntityList(Env.getOrgRrn(), MLotIQCDetail.class, Env.getMaxResult(),
                						" iqcRrn = " + iqcDatas.get(0).getObjectRrn(), "");
                				iqcData.setDetails(details);
                			}
                		} else {
                			UI.showError(Message.getString("mm.iqc_all_reject_mlotiqc_not_exist"));
                    		return;
                		}
                		             		
                		MMManager mmManager = Framework.getService(MMManager.class);             	              	
                		mmManager.iqcMLot(mlot, iqcDatas.get(0), true, null, Env.getSessionContext());	                    	
                    	UI.showInfo(Message.getString("mm.iqc_reject_all_success"));
                    }
                    refreshAdapter();
                    this.masterParent.refresh();
                }
            } 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
