package com.glory.mes.wip.source.iqc;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class IQCMLotEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.source.iqc.IQCMLotEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new IQCMLotBlock(new ListTableManager(adTable));
	}
}
