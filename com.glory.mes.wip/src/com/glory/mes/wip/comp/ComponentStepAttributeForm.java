package com.glory.mes.wip.comp;

import java.util.List;

import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.ComponentUnitAttributeValue;

public class ComponentStepAttributeForm extends Composite {

	public static final String TABLE_NAME = "WIPComponentUnit";
	
	private String tableName;
	private ComponentAttributeTableManager tableManager;
	private List<StepAttribute> stepAttributes;
	private List<ComponentUnit> components;
	
	public ComponentStepAttributeForm(Composite parent, 
			List<StepAttribute> stepAttributes, List<ComponentUnit> components) {
		this(parent, null, stepAttributes, components);
	}
	
	public ComponentStepAttributeForm(Composite parent, String tableName, 
			List<StepAttribute> stepAttributes, List<ComponentUnit> components) {
		super(parent, SWT.NONE);
		if (tableName != null && tableName.trim().length() > 0) {
			this.tableName = tableName;
		} else {
			this.tableName = TABLE_NAME;
		}
		this.stepAttributes = stepAttributes;
		this.components = components;
	}
	
	public void createForm() {
		try {
			FormToolkit toolkit = new FormToolkit(Display.getDefault());
			GridLayout layout = new GridLayout(1, true);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 5;
			layout.marginWidth = 5;
			layout.marginHeight = 5;
			setLayout(layout);
			
			setLayoutData(new GridData(GridData.FILL_BOTH));
		
			ADManager entityManager = Framework.getService(ADManager.class);
			final ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), tableName); 
			tableManager = new ComponentAttributeTableManager(adTable, stepAttributes);
			TableViewer viewer = (TableViewer)tableManager.createViewer(this, toolkit, 50);			
			GridData gd = new GridData(GridData.FILL_BOTH);
			viewer.getTable().setLayoutData(gd);		
			tableManager.setInput(components);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	public List<ComponentUnitAttributeValue> getComponentAttributeValues(){
		return tableManager.getComponentAttributeValues();
	}
}
