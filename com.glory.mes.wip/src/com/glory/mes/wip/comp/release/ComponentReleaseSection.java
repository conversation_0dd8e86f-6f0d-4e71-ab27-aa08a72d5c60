package com.glory.mes.wip.comp.release;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.ADQueryEntityListSection;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.framework.security.model.ADUserGroup;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class ComponentReleaseSection extends ADQueryEntityListSection {
	
	public static final String KEY_MULTIRELEASE = "multiRelease";

	public CheckBoxFixEditorTableManager manager;
	protected AuthorityToolItem itemMultiRelease;
	protected LotAction lotAction = new LotAction();
	
	protected EntityForm compReleaseForm;
	protected ADManager adManager;
	protected List<String> currentUserGroups;
	
	public ComponentReleaseSection(String queryName, ListTableManager tableManager) {
		super(queryName, tableManager);
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemMultiHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemMultiHold(ToolBar tBar) {
		itemMultiRelease = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_MULTIRELEASE);
		//itemMultiRelease.setText(Message.getString("wip.multirelease"));
		itemMultiRelease.setAuthEventAdaptor(this::multiReleaseAdapter);
		itemMultiRelease.setText(Message.getString("wip.component_release_dorelease"));
		itemMultiRelease.setImage(SWTResourceCache.getImage("release-lot"));
//		itemMultiRelease.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				multiReleaseAdapter(event);
//			}
//		});
	}
	
	public void createContents(final IManagedForm form, Composite parent, int sectionStyle) {
		super.createContents(form, parent, sectionStyle);
		
		Composite client = (Composite)section.getClient();
		
        compReleaseForm = new EntityForm(client,  SWT.NONE, new LotAction(), getADTable1(), form.getMessageManager()); 
        compReleaseForm.setLayout(new GridLayout());
        compReleaseForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        try {
			adManager = Framework.getService(ADManager.class);
			ADUser User = new ADUser();
			User.setObjectRrn(Env.getUserRrn());
			User.setOrgRrn(Env.getOrgRrn());
			ADUser currUser = (ADUser) adManager.getEntity(User);
			List<ADUserGroup>  userGroups = currUser.getUserGroups();
			userGroups = userGroups.stream().filter(ls->ls.getOrgRrn().equals(Env.getOrgRrn())).collect(Collectors.toList());
			currentUserGroups = new ArrayList<String>();
			for(int i = 0;i < userGroups.size();i++) {
				currentUserGroups.add(userGroups.get(i).getName());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void multiReleaseAdapter(SelectionEvent event) {
        try {
        	if(compReleaseForm.validate()) {
                managedForm.getMessageManager().removeAllMessages();
                LinkedHashMap<String, IField> fields = compReleaseForm.getFields();

                for (IField f : fields.values()) {
                    if (f.getId().equals("actionCode")) {
                        String hCode = (String) f.getValue();
                        lotAction.setActionCode(hCode);
                        continue;
                    }
                    if (f.getId().equals("actionReason")) {
                        String hReason = (String) f.getValue();
                        lotAction.setActionReason(hReason);
                        continue;
                    }
                    if (f.getId().equals("actionComment")) {
                        String hComment = (String) f.getValue();
                        lotAction.setActionComment(hComment);
                        continue;
                    }
                }
                List<Object> selectComMapList =  tableManager.getCheckedObject();
    			if (selectComMapList.size() != 0) {
    				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
    				boolean sysParameter = MesCfMod.isHoldCompCascadeLot(Env.getOrgRrn(), sysParamManager);
    	        		if(!sysParameter) {
    	        			ComponentManager componentManager = Framework.getService(ComponentManager.class);
    	        			componentManager.releaseComponents(selectComMapList, lotAction, Env.getSessionContext());
        				} else {
        					LotManager lotManager = Framework.getService(LotManager.class);
        					SessionContext sc = Env.getSessionContext();
        					if (itemMultiRelease.getData(LotAction.ACTION_TYPE_OPERATOR) != null) {
        						sc.setUserName((String) itemMultiRelease.getData(LotAction.ACTION_TYPE_OPERATOR));
        					}
        					
							lotManager.releaseComponentByHoldCode(selectComMapList, lotAction, sc);
        				}
    	        	UI.showError(Message.getString("wip.component_release_secuss"));
    	    		refresh();
                }else {
                	 UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                }
            } else {
            	UI.showWarning(Message.getString("warn.required_entry"));
    			return;
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
	}
	
	protected ADTable getADTable1() {
		ADTable midTable = getADManger().getADTable(Env.getOrgRrn(), "WIPComponentReleaseAction");
		return midTable;
	}
	
	@Override
	public Map<String, Object> getParameterMap() {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("orgRrn", Env.getOrgRrn());
		paramMap.put("holdOwner", currentUserGroups);
		return paramMap;
	}
	
	@Override
	public void refresh() { 
		super.refresh();
		lotAction = new LotAction();
		compReleaseForm.setObject(lotAction);
		compReleaseForm.loadFromObject();
	}
}
