package com.glory.mes.wip.comp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.model.BaseAttribute;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.viewers.FixEditorTableManager;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.ComponentUnitAttributeValue;
import com.glory.mes.wip.model.LotAttribute;

public class ComponentAttributeTableManager extends FixEditorTableManager {

	private static final String FIELD_ATTRIBUTE_PREFIX = "attribute";
	private int attributeCount = 1;
	private List<ADField> stepField = new ArrayList<ADField>();
	
	public ComponentAttributeTableManager(ADTable adTable, 
			List<StepAttribute> stepAttributes) {
		super(adTable);
//		try {
//			List<ADField> adFields = adTable.getFields();
//			List<String> editorColumns = new ArrayList<String>();
//			ADManager adManager = Framework.getService(ADManager.class);
//			for (StepAttribute stepAttribute : stepAttributes) {
//				if (stepAttribute.getLotAttribute() == null) {
//					LotAttribute lotAttribute = new LotAttribute();
//					lotAttribute.setObjectRrn(stepAttribute.getLotAttributeRrn());
//					lotAttribute = (LotAttribute)adManager.getEntity(lotAttribute);
//					stepAttribute.setLotAttribute(lotAttribute);
//				}
//				ADField adField = getADField(stepAttribute.getLotAttribute());
//				adField.setIsMain(true);
//				adField.setName(FIELD_ATTRIBUTE_PREFIX + attributeCount);
//				adFields.add(adField);
//				stepField.add(adField);
//				editorColumns.add(adField.getName());
//				if (attributeCount > 10) {
//					break;
//				}
//				attributeCount++;
//			}
//			adTable.setFields(adFields);
//			this.setEditorColumns(editorColumns);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
	}
	
	public void setInput(List<ComponentUnit> components) {
    	super.setInput(components);
    }
	
	public List<ComponentUnitAttributeValue> getComponentAttributeValues(){
		List<ComponentUnitAttributeValue> compAttributeValues = new ArrayList<ComponentUnitAttributeValue>();
//		List<ComponentUnit> components = (List<ComponentUnit>)getInput();
//		for (ComponentUnit component : components) {
//			for (int i = 1; i < attributeCount; i++) {
//				try {
//					ComponentUnitAttributeValue compAttributeValue = new ComponentUnitAttributeValue();
//					compAttributeValue.setOrgRrn(Env.getOrgRrn());
//					compAttributeValue.setIsActive(true);
//					compAttributeValue.setCreated(new Date());
//					compAttributeValue.setCreatedBy(Env.getUserRrn());
//					compAttributeValue.setUpdatedBy(Env.getUserRrn());
//					compAttributeValue.setAttributeName(stepField.get(i - 1).getName());
//					compAttributeValue.setAttributeValue(PropertyUtil.getValueString(
//							PropertyUtils.getProperty(component, FIELD_ATTRIBUTE_PREFIX + i)));
//					compAttributeValues.add(compAttributeValue);
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
//			}
//		}
		return compAttributeValues;
	}
	
	public ADField getADField(BaseAttribute attribute) {
		ADField field = new ADField();
		field.setName(attribute.getName());
		field.setDisplayLength(attribute.getDisplayLength());
		field.setIsSameline(attribute.getIsSameline());
		field.setIsMandatory(attribute.getIsMandatory());
		field.setIsUpper(attribute.getIsUpper());
		field.setDisplayType(attribute.getDisplayType());
		field.setDataType(attribute.getDataType());
		field.setNamingRule(attribute.getNamingRule());
		field.setMinValue(attribute.getMinValue());
		field.setMaxValue(attribute.getMaxValue());
		field.setReftableRrn(attribute.getReftableRrn());
		field.setRefListName(attribute.getRefListName());
		field.setUreflistName(attribute.getUreflistName());
		field.setReferenceRule(attribute.getReferenceRule());
		field.setDefaultValue(attribute.getDefaultValue());
		field.setLabel(attribute.getLabel());
		field.setLabel_zh(attribute.getLabel_zh());

		return field;
	}
}
