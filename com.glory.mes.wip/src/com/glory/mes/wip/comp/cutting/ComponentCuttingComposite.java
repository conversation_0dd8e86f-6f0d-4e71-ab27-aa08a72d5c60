package com.glory.mes.wip.comp.cutting;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.FormLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.SquareButtonBar;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.model.MaterialConvert;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.ConvertManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentAssignComposite;
import com.glory.mes.wip.comp.ComponentComposite;
import com.glory.mes.wip.comp.ComponentComposite.OccupationPolicy;
import com.glory.mes.wip.mm.MaterialConvertContext;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.InContext;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * ҳ�������������
 * ���Ϊ�ȴ�Cutting��Component��Ϣ(Glass)
 * �м�Ϊ�Ѿ�Cutting��Component��Ϣ(Panel)
 * �����������ҵ���ܽ��,��ʾ�ڵ�ǰ�豸�е�Panel
 * 
 * �ұ�Ϊ�µ��ؾ���Ϣ,��Cutting��Panel,װ���ؾ���
 */
public class ComponentCuttingComposite extends ComponentAssignComposite {

	//private static final Logger logger = Logger.getLogger(ComponentCuttingComposite.class);
	
	private static final int GridWidthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 3 - 30;
	
	protected boolean showCuttingCarrierFlag;
	protected Carrier cuttingCarrier;
	protected ComponentComposite cuttingComponentComposite;
	
	private MaterialConvert materialConvert;
	
	protected Text txtEqpId;
	
	public ComponentCuttingComposite(Composite parent, int style) {
		super(parent, style, false, false, true);
	}

	public void createForm() {
		try {
			GridLayout layout = new GridLayout(4, false);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			setLayout(layout);
			setLayoutData(new GridData(GridData.FILL_BOTH));
			
			this.showCuttingCarrierFlag = true;
			createCuttingComponent(this);
			
			createSourceComponent(this);
			createMiddleComponent(this);
			createTargetComponent(this);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createCuttingComponent(final Composite parent) {
		GridLayout layout = new GridLayout(1, false);
		layout.verticalSpacing = 10;
		layout.horizontalSpacing = 10;
		layout.marginWidth = 10;
		layout.marginHeight = 10;
		
		Composite cuttingComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_BOTH);
		//gd.widthHint = GridWidthHint;
		cuttingComp.setLayoutData(gd);
		cuttingComp.setLayout(layout);
		
		if (showCuttingCarrierFlag) {
			//label
			Composite labelCompsite = new Composite(cuttingComp, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(3, false));
			Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
			lblCarrierId.setText(Message.getString("wip.cutting_carrier_id"));
			
			//text
			Text txtCuttingCarrierId = new Text(labelCompsite, SWT.BORDER);
			txtCuttingCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			GridData gText = new GridData();
			gText.widthHint = 216;
			txtCuttingCarrierId.setLayoutData(gText);
			txtCuttingCarrierId.setTextLimit(32);
			txtCuttingCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					Text tLotId = ((Text) event.widget);
					tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
					case SWT.KEYPAD_CR:
						String carrierId = txtCuttingCarrierId.getText().toUpperCase();
						cuttingCarrier = searchCarrier(carrierId, cuttingComponentComposite, true);
					}
				}
			});
		}
		
		cuttingComponentComposite = new ComponentComposite(cuttingComp, 0, false, false);
		cuttingComponentComposite.init();
		
		//create bar
		SquareButtonBar buttonBar = new SquareButtonBar(cuttingComp, SWT.NONE);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		buttonBar.setLayoutData(gd);
		buttonBar.setLayout(new FormLayout());

		// add button
		SquareButton btnCutting = UIControlsFactory.createButton(buttonBar, UIControlsFactory.BUTTON_DEFAULT);
		btnCutting.setText(Message.getString("common.cutting"));
		btnCutting.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				cuttingAdapter();
			}
		});
		buttonBar.addSquareButton(btnCutting);
	}
    
	@Override
	protected void createSourceComponent(final Composite parent) {
		GridLayout layout = new GridLayout(1, false);
		layout.verticalSpacing = 10;
		layout.horizontalSpacing = 10;
		layout.marginWidth = 10;
		layout.marginHeight = 10;
		
		Composite sourceComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_VERTICAL);
		gd.widthHint = GridWidthHint;
		sourceComp.setLayoutData(gd);
		sourceComp.setLayout(layout);
	
		//¼���
		Composite labelCompsite = new Composite(sourceComp, SWT.NONE);
		labelCompsite.setLayout(new GridLayout(3, false));
		
		Label lblEqpId = new Label(labelCompsite, SWT.NONE);
		lblEqpId.setText(Message.getString("ras.equipment_id"));
		
		GridData gText = new GridData();
		gText.widthHint = 216;
		
		txtEqpId = new Text(labelCompsite, SWT.BORDER);
		txtEqpId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
		txtEqpId.setLayoutData(gText);
		txtEqpId.setTextLimit(32);
		txtEqpId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					queryEqpCellAdapter();
				}
			}
		});
		
		if (sourceIsCarrier) {
			sourceComponentComposite = new ComponentComposite(sourceComp, 0, false);
			sourceComponentComposite.init();
			
			sourceComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter(false);
				}
			});
		} else {
			sourceTableManager = new ListTableManager(ComponentComposite.getDefaultADTable(), true);
			sourceTableManager.setSortFlag(true);
			sourceTableManager.newViewer(sourceComp);
 //			sourceTableManager.setTableBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TABLE_HEADER_BG));
			sourceTableManager.addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter(false);
				}
			});
		}
	}
	
	@Override
	protected void createTargetComponent(final Composite parent) {
		GridLayout layout = new GridLayout(1, false);
		layout.verticalSpacing = 10;
		layout.horizontalSpacing = 10;
		layout.marginWidth = 10;
		layout.marginHeight = 10;
		
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_VERTICAL);
		gd.widthHint = GridWidthHint;
		targetComp.setLayoutData(gd);
		targetComp.setLayout(layout);

		if (showTargetCarrierFlag) {
			Composite labelCompsite = new Composite(targetComp, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(3, false));
			
			Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
			lblCarrierId.setText(Message.getString("wip.target_carrier_id"));
			
			txtTargetCarrierId = new HeaderText(labelCompsite, SWTResourceCache.getImage("header-text-carrier"));
			txtTargetCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtTargetCarrierId.setTextLimit(32);
			txtTargetCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					Text tLotId = ((Text) event.widget);
					tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
					case SWT.KEYPAD_CR:
						String carrierId = txtTargetCarrierId.getText().toUpperCase();
						targetCarrier = searchCarrier(carrierId, targetComponentComposite, true);
					}
				}
			});
		}
		
		targetComponentComposite = new ComponentComposite(targetComp, 0, false);
		targetComponentComposite.init();
		targetComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				backAdapter();
			}
		});
		
		//create bar
		SquareButtonBar buttonBar = new SquareButtonBar(targetComp, SWT.NONE);
		gd = new GridData(GridData.FILL_HORIZONTAL);
		buttonBar.setLayoutData(gd);
		buttonBar.setLayout(new FormLayout());

		// add button
		SquareButton btnAssign = UIControlsFactory.createButton(buttonBar, UIControlsFactory.BUTTON_DEFAULT);
		btnAssign.setText(Message.getString("wip.assign"));
		btnAssign.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
		buttonBar.addSquareButton(btnAssign);
	}
	
	protected void cuttingAdapter() {
		try {
			String eqpId = txtEqpId.getText().toUpperCase();
			if (StringUtil.isEmpty(eqpId)) {
				UI.showWarning(Message.getString("error.eqp_not_exist"));
				return;
			}
			if (!checkEqpValid(eqpId)) {
				return;
			}
			
			if (cuttingComponentComposite.getSelectedObj() == null) {
                UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
			}
			ComponentUnit component = (ComponentUnit)cuttingComponentComposite.getSelectedObj();

			//��ȡ������Ϣ
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLot(component.getParentUnitRrn());
			if (lot == null) {
				return;
			}
			
			//��ȡMaterialConvert
			ConvertManager convertManager = Framework.getService(ConvertManager.class);
			materialConvert = convertManager.getMaterialConvert(lot);
			if (materialConvert == null) {
				return;
			}
			
			lot.setEquipmentId(eqpId); //�豸��
			
			MaterialConvertContext context = new MaterialConvertContext();
			context.setConvert(materialConvert);			
			context.setComponent(component);
			context.setLot(lot);
			
			context = convertManager.componentConvert(context, Env.getSessionContext());
			
			UI.showInfo(Message.getString("wip.cutting_successed"));
			
			List<ComponentUnit> resultComponents = context.getResultComponents();
			
			//�Ƴ��Ѿ�Cutting�Ķ���
			cuttingComponentComposite.removeComponent(component);
			
			//�����ɵ�Component����
			if (sourceIsCarrier) {
				sourceComponentComposite.addComponentAppend(resultComponents);
			} else {
				sourceTableManager.addList(resultComponents);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void saveAdapter() {
		try {
			String carrierId = txtTargetCarrierId.getText().toUpperCase();
			
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			if (carrier == null) {
				UI.showWarning(Message.getString("mm.carrier_is_not_exist"));
				return;
			}
			
			//������Ч�Լ��
			durableManager.checkCarrierAvailable(Env.getSessionContext(), carrier);
			if (!carrier.getIsAvailable()) {
				UI.showWarning(Message.getString(carrier.getMessage()));
				return;
			}
			
			//���target �б�
			List<ComponentUnit> lstTargetComponent = targetComponentComposite.getRealComponents();
			if (lstTargetComponent == null || lstTargetComponent.size() == 0) {
				UI.showWarning(Message.getString("wip.target_component_is_empty"));
				return;
			}
			
			// processUnit
			List<ProcessUnit> lstProcessUnit = new ArrayList<ProcessUnit>();
			lstProcessUnit.addAll(lstTargetComponent);
			
			// new lot
			Lot lot = new Lot();
			lot.setDurable(carrierId);
			lot.setSubProcessUnit(lstProcessUnit);
			
			// lot list
			List<Lot> lstLot = new ArrayList<Lot>();
			lstLot.add(lot);
			
			// InContext
			InContext inContext = new InContext();
			inContext.setLots(lstLot);
			
			/**
			 * ���ú�̨��
			 * ������ʱ��ʵ�֣�����������Ŀ������ȷ�Ϻ�̨��ôʵ�֡�2018-5-4
			 */
			UI.showInfo(Message.getString("Function incomplete. No call any mothed of server"));
			
			//CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
			//carrierLotManager.assignCarrierLot(carrier, "", lstTargetComponent, true, true, Env.getSessionContext());
			
			//UI.showInfo(Message.getString("wip.assign_success"));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void queryEqpCellAdapter() {
		try {
			String eqpId = txtEqpId.getText().toUpperCase();
			if (StringUtil.isEmpty(eqpId)) {
				sourceComponentComposite.initComponents(new ArrayList<ComponentUnit>());
				return;
			}
			
			List<ComponentUnit> lstComponent = getEqpComponent(eqpId);
			if (sourceIsCarrier) {
				sourceComponentComposite.initComponents(lstComponent);
			} else {
				sourceTableManager.setInput(lstComponent);
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ��ȡ�豸��Cell
	 * @param eqpId
	 * @return
	 * @throws Exception
	 */
	private List<ComponentUnit> getEqpComponent(String eqpId) throws Exception {
		List<ComponentUnit> lstComponentUnit = new ArrayList<ComponentUnit>();
		
		if (!checkEqpValid(eqpId)) {
			return lstComponentUnit;
		}
		
		ADManager adManager = Framework.getService(ADManager.class);
		lstComponentUnit = adManager.getEntityList(Env.getOrgRrn(), ComponentUnit.class, Integer.MAX_VALUE, 
				"equipmentId = '" + eqpId + "'", "");
		
		//����������Ϣ
		lstComponentUnit = getComponentWithLotInfo(lstComponentUnit);
		
		return lstComponentUnit;
	}
	
	/**
	 * ����豸�Ƿ���Ч
	 * @param eqpId
	 * @return
	 * @throws Exception
	 */
	private boolean checkEqpValid(String eqpId) throws Exception {
		RASManager rasManager = Framework.getService(RASManager.class);
		Equipment eqp = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), eqpId);
		if (eqp == null) {
			UI.showWarning(Message.getString("error.eqp_not_exist"));
			return false;
		}
		
		return true;
	}
	
	
	@Override
	protected void goAdapter(Boolean isMove) {
		try {
			if (sourceComponentComposite != null) {
				ComponentUnit sourceComponentUnit = sourceComponentComposite.getSelectedObj();
				if (sourceComponentUnit == null) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}
				String sourcePosition = sourceComponentUnit.getPosition();
				if(isMove) {
					if (targetComponentComposite.addComponent(sourceComponentComposite.getSelectedObj(), sourcePosition, OccupationPolicy.REJECT)) {
						sourceComponentComposite.removeComponent(sourcePosition);
					}
				} else {
					String targetPosition = targetComponentComposite.getCurrentPositon();
					if (StringUtil.isEmpty(targetPosition)) {
						UI.showWarning(Message.getString("wip.target_position_is_not_select"));
						return;
					}
					if (targetComponentComposite.addComponent(sourceComponentComposite.getSelectedObj(), targetPosition, OccupationPolicy.REJECT)) {
						sourceComponentComposite.removeComponent(sourcePosition);
					}
					targetComponentComposite.addCurrentPosition();
				}
			} else {
				List<Object> objects = sourceTableManager.getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}

				List<ComponentUnit> sourceComponentUnits = new ArrayList<ComponentUnit>();
				for (Object obj : objects) {
					sourceComponentUnits.add((ComponentUnit)obj);
				}
				
				String targetPosition = targetComponentComposite.getCurrentPositon();
				if (StringUtil.isEmpty(targetPosition)) {
					List<ComponentUnit> addedComponentUnits = targetComponentComposite.addComponentAppend(sourceComponentUnits);
					sourceTableManager.removeList(addedComponentUnits);
				} else {
					List<ComponentUnit> addedComponentUnits = targetComponentComposite.addComponentList(sourceComponentUnits, targetPosition, OccupationPolicy.REJECT);
					sourceTableManager.removeList(addedComponentUnits);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void backAdapter() {
		try {
			ComponentUnit targetComponentUnit = targetComponentComposite.getSelectedObj();
			if (targetComponentUnit == null) {
				UI.showWarning(Message.getString("wip.target_component_is_not_select"));
				return;
			}
			if (sourceComponentComposite != null) {
				//���ѡ�е�Component�Ƿ���sourceComponentUnits
				if (!sourceComponentComposite.getInitCompIdPositionMap().keySet().contains(targetComponentUnit.getComponentId())) {
					UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
					return;
				}
				
				//����Ż�ԭ����λ��
				String sourcePosition = sourceComponentComposite.getInitCompIdPositionMap().get(targetComponentUnit.getComponentId());
				String targetPosition = targetComponentUnit.getPosition();
				if (sourceComponentComposite.addComponent(targetComponentUnit, sourcePosition, OccupationPolicy.REJECT)) {
					targetComponentComposite.removeComponent(targetPosition);
				}
			} else {
				//���ѡ�е�Component�Ƿ���sourceComponentUnits
				if (targetComponentComposite.getInitCompIdPositionMap().keySet().contains(targetComponentUnit.getComponentId())) {
					UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
					return;
				}
				
				String targetPosition = targetComponentUnit.getPosition();
				targetComponentComposite.removeComponent(targetPosition);
				
				//to source
				if (sourceTableManager.getInput().size() == 0) {
					sourceTableManager.add(targetComponentUnit);
				} else {
					int index = sourceTableManager.getInput().size();
					sourceTableManager.insert(index, targetComponentUnit);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
}
