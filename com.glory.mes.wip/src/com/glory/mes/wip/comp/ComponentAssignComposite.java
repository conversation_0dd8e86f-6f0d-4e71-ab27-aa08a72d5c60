package com.glory.mes.wip.comp;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.comp.ComponentComposite.OccupationPolicy;
import com.glory.mes.wip.lot.AbstractAssignComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;


public class ComponentAssignComposite extends AbstractAssignComposite {

	private static final Logger logger = Logger.getLogger(ComponentAssignComposite.class);

	/**
	 * ָ��Source�Ƿ���Carrier��ʽ��ʾ,Ĭ��Ϊtrue
	 * ������Table����ʽ��ʾ
	 */
	public boolean sourceIsCarrier = true;
	
	/**
	 * ��ʾԴ�ؾ�������λ
	 */
	public boolean showSourceCarrierFlag;
	/**
	 * ��ʾĿ���ؾ�������λ
	 */
	public boolean showTargetCarrierFlag;
	
	public Carrier sourceCarrier;

	public ListTableManager sourceTableManager;
	public ComponentComposite sourceComponentComposite;
	public ComponentComposite targetComponentComposite;
	
	public HeaderText txtSourceLotId;
	public HeaderText txtSourceCarrierId;
	public HeaderText txtTargetCarrierId;
	
	public Button btnMove;
	public Button btnGo;
	public Button btnBack;
	
	public ComponentAssignComposite(Composite parent, int style, boolean sourceIsCarrier,
			boolean showSourceCarrierFlag, boolean showTargetCarrierFlag) {
		super(parent, style);
		this.sourceIsCarrier = sourceIsCarrier;
		this.showSourceCarrierFlag = showSourceCarrierFlag;
		this.showTargetCarrierFlag = showTargetCarrierFlag;
		createForm();
	}
	
	@Override
	protected void createAssignComposite() {
		this.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		this.setBackgroundMode(SWT.INHERIT_FORCE);
		createSourceComponent(this);
		createMiddleComponent(this);
		createTargetComponent(this);
	}

	protected void createSourceComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_BOTH);
//		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
//		gd.heightHint = (int) ((Toolkit.getDefaultToolkit().getScreenSize().height) / 1.8);
		targetComp.setLayoutData(gd);

		if (sourceIsCarrier) {
			if (showSourceCarrierFlag) {
				Composite labelCompsite = new Composite(targetComp, SWT.NONE);
				labelCompsite.setLayout(new GridLayout(3, false));
				
				Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
				lblCarrierId.setText(Message.getString("wip.source_carrier_id"));
				
				txtSourceCarrierId = new HeaderText(labelCompsite, SWTResourceCache.getImage("header-text-carrier"));
				txtSourceCarrierId.setTextLimit(32);
				txtSourceCarrierId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						Text tLotId = ((Text) event.widget);
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
						switch (event.keyCode) {
						case SWT.CR:
						case SWT.KEYPAD_CR:
							String carrierId = txtSourceCarrierId.getText().toUpperCase();
							sourceCarrier = searchCarrier(carrierId, sourceComponentComposite, true,false);
						}
					}
				});
			}
			
			sourceComponentComposite = new ComponentComposite(targetComp, 0, true, true);
			sourceComponentComposite.init();
			
			sourceComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter(true);
				}
			});
		} else {
			if (showSourceCarrierFlag) {
				Composite labelCompsite = new Composite(targetComp, SWT.NONE);
				labelCompsite.setLayout(new GridLayout(3, false));
				
				Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
				lblCarrierId.setText(Message.getString("wip.lot_id"));
				
				txtSourceLotId = new HeaderText(labelCompsite, SWTResourceCache.getImage("header-text-lot"));;
				txtSourceLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtSourceLotId.setTextLimit(32);
				txtSourceLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						Text tLotId = ((Text) event.widget);
						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
						switch (event.keyCode) {
						case SWT.CR:
						case SWT.KEYPAD_CR:
							String lotId = txtSourceLotId.getText().toUpperCase();
							sourceLot = searchLot(lotId);
						}
					}
				});
			}
			
			sourceTableManager = new ListTableManager(ComponentComposite.getDefaultADTable(), true);
			sourceTableManager.setSortFlag(true);
			sourceTableManager.newViewer(targetComp);
			sourceTableManager.addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable natTable, MouseEvent event) {
					goAdapter(true);
				}
			});
		}
	}
	
	protected void createMiddleComponent(final Composite parent) {
		Composite centerComposite = new Composite(parent, SWT.NONE);
	    final GridLayout buttonLayout = new GridLayout();
	    buttonLayout.marginWidth = 2;
	    buttonLayout.marginHeight = 2;
	    centerComposite.setLayout(buttonLayout);
		GridData buttonGd = new GridData(SWT.CENTER, SWT.CENTER, false, false);
		centerComposite.setLayoutData(buttonGd);
		
		btnMove = new Button(centerComposite, SWT.PUSH);
		btnMove.setText("=");
		btnMove.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));

		btnMove.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				goAdapter(true);
			}
		});
		
		btnGo = new Button(centerComposite, SWT.PUSH);
		btnGo.setText("->");
		btnGo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));

		btnGo.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				goAdapter(false);
			}
		});
		
		btnBack = new Button(centerComposite, SWT.PUSH);
		btnBack.setText("<-");
		btnBack.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		btnBack.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				backAdapter();
			}
		});
		
	}
	
	protected void createTargetComponent(final Composite parent) {
		Composite targetComp = new Composite(parent, SWT.NONE);
		GridData gd = new GridData(GridData.FILL_BOTH);
//		gd.widthHint = (Toolkit.getDefaultToolkit().getScreenSize().width) / 4;
//		gd.heightHint = (int) ((Toolkit.getDefaultToolkit().getScreenSize().height) / 1.8);
		targetComp.setLayoutData(gd);

		if (showTargetCarrierFlag) {
			Composite labelCompsite = new Composite(targetComp, SWT.NONE);
			labelCompsite.setLayout(new GridLayout(3, false));
			
			Label lblCarrierId = new Label(labelCompsite, SWT.NONE);
			lblCarrierId.setText(Message.getString("wip.target_carrier_id"));
			
			txtTargetCarrierId = new HeaderText(labelCompsite, SWTResourceCache.getImage("header-text-carrier"));
//			txtTargetCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
			txtTargetCarrierId.setTextLimit(32);
			txtTargetCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					Text tLotId = ((Text) event.widget);
					tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
					case SWT.KEYPAD_CR:
						String carrierId = txtTargetCarrierId.getText().toUpperCase();
						targetCarrier = searchCarrier(carrierId, targetComponentComposite, true);
					}
				}
			});
		}
		
		targetComponentComposite = new ComponentComposite(targetComp, 0, false, true);
		targetComponentComposite.init();
		targetComponentComposite.getTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				backAdapter();
			}
		});
	}
	
	public Carrier searchCarrier(String carrierId, ComponentComposite componentComposite, boolean isValid) {
		return searchCarrier(carrierId, null, componentComposite, isValid,true);
	}
	
	public Carrier searchCarrier(String carrierId, ComponentComposite componentComposite, boolean isValid,
			boolean isSetTargetCarrier) {
		return searchCarrier(carrierId, null, componentComposite, isValid, isSetTargetCarrier);
	}
	
	public Carrier searchCarrier(String carrierId,String sourceCarrierId, ComponentComposite componentComposite, boolean isValid,boolean isSetTargetCarrier) {
		try {
			if (!StringUtil.isEmpty(carrierId)) {
				Carrier targetCarrier = searchCarrier(carrierId, isValid, false);
				
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<ComponentUnit> componentUnits = carrierLotManager.getComponentByCarrierId(Env.getOrgRrn(), carrierId);
				if(StringUtils.equals(carrierId, sourceCarrierId)) {
					componentUnits = Lists.newArrayList();
				}else {
					//��ѯ����
					componentUnits = getComponentWithLotInfo(componentUnits);
				}
				componentComposite.setCount(targetCarrier.getCapacity().intValue());
				componentComposite.setAscFlag(targetCarrier.getSlotDirectionAsc());
				componentComposite.initComponents(componentUnits);
				if(isSetTargetCarrier) {
					setTargetCarrier(targetCarrier);
				}
				return targetCarrier;
			} else {
				componentComposite.initComponents(new ArrayList<ComponentUnit>());
			}
		} catch(Exception e) {
			logger.error("ComponentAssignComposite searchCarrier:", e);
		}
		return null;
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot sourceLot = super.searchLot(lotId);
			List<ComponentUnit> components = new ArrayList<ComponentUnit>();
			if (sourceLot == null) {
				sourceTableManager.setInput(components);
				return sourceLot;
			}
			
			if (sourceLot.getSubProcessUnit() != null && sourceLot.getSubProcessUnit().size() > 0) {
				for (ProcessUnit processUnit : sourceLot.getSubProcessUnit()) {
					ComponentUnit compUnit = (ComponentUnit)processUnit;
					compUnit.setLotId(sourceLot.getLotId());
					components.add(compUnit);
				}
			} 
			
			sourceTableManager.setInput(components);
			setSourceLot(sourceLot);
			return sourceLot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	protected void goAdapter(Boolean isMove) {
		try {
			if (sourceComponentComposite != null) {
				List<Object> objects  = sourceComponentComposite.getTableManager().getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}
				
				List<ComponentUnit> sourceComponentUnits = new ArrayList<ComponentUnit>();
				for (Object obj : objects) {
					ComponentUnit unit = (ComponentUnit)obj;
					unit.setFromPosition(unit.getPosition());
					sourceComponentUnits.add(unit);
				}
				
				for (ComponentUnit componentUnit : sourceComponentUnits) {
					String sourcePosition = componentUnit.getPosition();
					if(isMove) {
						if (targetComponentComposite.addComponent(componentUnit, sourcePosition, OccupationPolicy.REJECT)) {
							sourceComponentComposite.removeComponent(sourcePosition);
						} else {
							break;
						}
					} else {
						String targetPosition = targetComponentComposite.getCurrentPositon();
						if (StringUtil.isEmpty(targetPosition)) {
							UI.showWarning(Message.getString("wip.target_position_is_not_select"));
							return;
						}
						if (targetComponentComposite.addComponent(componentUnit, targetPosition, OccupationPolicy.REJECT)) {
							sourceComponentComposite.removeComponent(sourcePosition);
						} else {
							break;
						}
						targetComponentComposite.addCurrentPosition();
					}
				}
			} else {
				List<Object> objects = sourceTableManager.getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}

				List<ComponentUnit> sourceComponentUnits = new ArrayList<ComponentUnit>();
				for (Object obj : objects) {
					ComponentUnit unit = (ComponentUnit)obj;
					unit.setFromPosition(unit.getPosition());
					sourceComponentUnits.add(unit);
				}
				
				String targetPosition = targetComponentComposite.getCurrentPositon();
				if (StringUtil.isEmpty(targetPosition)) {
					List<ComponentUnit> addedComponentUnits = targetComponentComposite.addComponentAppend(sourceComponentUnits);
					sourceTableManager.removeList(addedComponentUnits);
				} else {
					List<ComponentUnit> addedComponentUnits = addComponentList(sourceComponentUnits, targetPosition, OccupationPolicy.REJECT);
					sourceTableManager.removeList(addedComponentUnits);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ����˳�����Component
	 * ��ָ����λ�ÿ�ʼ
	 * @throws CloneNotSupportedException 
	 */
	public List<ComponentUnit> addComponentList(List<ComponentUnit> componentUnits, String position, OccupationPolicy occupationPolicy) throws CloneNotSupportedException {
		boolean startFlag = false;
		int i = 0;
		
		Map<String, ComponentUnit> carrierMap = targetComponentComposite.getCarrierMap();
		
		List<ComponentUnit> addedComponentUnits = new ArrayList<ComponentUnit>();
		for (String key : carrierMap.keySet()) {
			if (key.equals(position)) {
				startFlag = true;
			}
			if (startFlag) {
				if (i == componentUnits.size()) {
					break;
				}
				ComponentUnit component = componentUnits.get(i);
				if (!targetComponentComposite.addComponent(component, key, occupationPolicy)) {
					return addedComponentUnits;
				}
				addedComponentUnits.add(component);
				i++;
			}
		}
		
		return addedComponentUnits;
	}
	
	protected void backAdapter() {
		try {
			List<Object> objects  = targetComponentComposite.getTableManager().getCheckedObject();
			if (objects == null || objects.isEmpty()) {
				UI.showWarning(Message.getString("wip.target_position_is_not_select"));
				return;
			}
			
			List<ComponentUnit> targetComponentUnits = new ArrayList<ComponentUnit>();
			for (Object obj : objects) {
				ComponentUnit unit = (ComponentUnit)obj;
				if(unit.getComponentId() != null) {
					targetComponentUnits.add(unit);
				}
			}
			
			if (sourceComponentComposite != null) {
				for (ComponentUnit componentUnit : targetComponentUnits) {
					//���ѡ�е�Component�Ƿ���sourceComponentUnits
					if (!sourceComponentComposite.getInitCompIdPositionMap().keySet().contains(componentUnit.getComponentId())) {
						UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
						return;
					}
					
					//����Ż�ԭ����λ��
					String sourcePosition = sourceComponentComposite.getInitCompIdPositionMap().get(componentUnit.getComponentId());
					String targetPosition = componentUnit.getPosition();
					if (sourceComponentComposite.addComponent(componentUnit, sourcePosition, OccupationPolicy.REJECT)) {
						targetComponentComposite.removeComponent(targetPosition);
					}
				}
			} else {
				for (ComponentUnit componentUnit : targetComponentUnits) {
					//���ѡ�е�Component�Ƿ���sourceComponentUnits
					if (targetComponentComposite.getInitCompIdPositionMap().keySet().contains(componentUnit.getComponentId())) {
						UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
						return;
					}
					
					String targetPosition = componentUnit.getPosition();
					targetComponentComposite.removeComponent(targetPosition);
					
					//to source
					if (sourceTableManager.getInput().size() == 0) {
						sourceTableManager.add(componentUnit);
					} else {
						int index = sourceTableManager.getInput().size();
						sourceTableManager.insert(index, componentUnit);
					}
				}
				
				
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refresh() {
		super.refresh();
		if (txtSourceCarrierId != null) {
			txtSourceCarrierId.setText("");
		}		
		if (sourceComponentComposite != null) {
			searchCarrier("", sourceComponentComposite, true);			
		}
		
		if (sourceTableManager != null) {
			//
		}
		
		if (targetComponentComposite != null) {
			searchCarrier("", targetComponentComposite, true);			
		}
	}
	
	/**
	 * ����������Ϣ
	 * @param lstComponentUnit
	 * @return
	 * @throws Exception
	 */
	public List<ComponentUnit> getComponentWithLotInfo(List<ComponentUnit> lstComponentUnit) throws Exception {
		//��ѯ����
		LotManager lotManager = Framework.getService(LotManager.class);
		Map<Long, Lot> lotMap = new LinkedHashMap<Long, Lot>();				
		for (ComponentUnit comp : lstComponentUnit) {
			if (comp.getParentUnitRrn() != null && comp.getParentUnitRrn() != 0) {
				Lot lot = lotMap.get(comp.getParentUnitRrn());
				if (lot == null) {
					lot = lotManager.getLot(comp.getParentUnitRrn());
					lotMap.put(comp.getParentUnitRrn(), lot);
				}
				comp.setLotId(lot.getLotId());
			}
		}
		
		return lstComponentUnit;
	}

	@Override
	public void setSourceLot(Lot sourceLot) {
		super.setSourceLot(sourceLot);
		if (txtSourceLotId != null) {
			if (sourceLot != null) {
				txtSourceLotId.setText(sourceLot.getLotId());
			} else {
				txtSourceLotId.setText("");
			}
		}
	}

	@Override
	public void setTargetCarrier(Carrier targetCarrier) {
		super.setTargetCarrier(targetCarrier);
		if (txtTargetCarrierId != null) {
			if (targetCarrier != null) {
				txtTargetCarrierId.setText(targetCarrier.getDurableId());
			} else {
				txtTargetCarrierId.setText("");
			}
		}
	}

	
}
