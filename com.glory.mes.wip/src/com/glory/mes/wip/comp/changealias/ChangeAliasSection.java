package com.glory.mes.wip.comp.changealias;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.identify.IdentifySection;
import com.glory.mes.wip.lot.run.bylot.RunByLotCarrierLotComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeAliasSection extends LotSection {
	
	private static final Logger logger = Logger.getLogger(IdentifySection.class);

	protected AuthorityToolItem itemModify;
	
	public static final String KEY_IDENTIFY = "modify";
	public static final String AD_TABLE = "WIPComponentUnitInfo";

	public ChangeAliasSection() {
		super();
	}

	public ChangeAliasSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.sectiontitle_lotdetail"));
		initAdObject();
	}
	
	public void initAdObject() {
		setAdObject(new Lot());
		refresh();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemChange(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemChange(ToolBar tBar) {
		itemModify = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_IDENTIFY);
		itemModify.setText(Message.getString("wip.change"));
		itemModify.setImage(SWTResourceCache.getImage("modify"));
		itemModify.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeAdapter(event);
			}
		});
	}


	protected void changeAdapter(SelectionEvent event) {
		try {
			Lot lot = (Lot) getAdObject();
			if (lot != null && lot.getObjectRrn() != null) {
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lotWithComps = lotManager.getLotWithComponent(lot.getObjectRrn());
				
				if (CollectionUtils.isNotEmpty(lotWithComps.getSubProcessUnit()) && lotWithComps.getSubProcessUnit().get(0) instanceof ComponentUnit) {
					ChangeAliasDialog dialog = new ChangeAliasDialog((List)lotWithComps.getSubProcessUnit());
					if (dialog.open() == Dialog.OK) {
						componentManager.changeComponentAlias(dialog.getSelectComponentUnits(), Env.getSessionContext());
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
						refresh();
					}
				}

			}
		} catch (Exception e) {
			logger.error("Method changeAdapter() in ChangeAliasSection",e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void statusChanged(String newStatus){}
	
	@Override
	public void refresh() {
		try {
			ADBase adBase = getAdObject();
			if(adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));	
				if(carrierLotComposite != null) {
					carrierLotComposite.loadComponentUnits((Lot)adBase);
				}
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		super.refresh();
	}
	
	@Override
	public String getCompTableName() {
		return AD_TABLE;
	}
}
