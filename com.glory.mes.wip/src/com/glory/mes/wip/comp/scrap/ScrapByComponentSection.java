package com.glory.mes.wip.comp.scrap;

import java.math.BigDecimal;
import java.util.IntSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.HorizontalSection;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class ScrapByComponentSection extends HorizontalSection {

	private static final String TABLE_NAME = "WIPScrapByComponentLine";

	protected ScrapByComponentEntityForm entityForm;

	protected ListTableManager scrapTableManager;
	private Text txtLotId;
	private SquareButton scrapBtn;
	
	private ComponentUnit currentUnit;
	private List<ComponentUnit> scrapUnits = Lists.newArrayList();
	private Map<LotScrap, LotAction> scrapActionsMap = Maps.newHashMap();
	
	private String scrapType;

	public ScrapByComponentSection(ADTable adTable, String rightTitle, String leftTitle, String scrapType) {
		super(adTable, rightTitle, leftTitle);
		this.scrapType = scrapType;
	}

	@Override
	protected void createRightContent(Composite client, FormToolkit toolkit) {
		
		Composite outTableComposite = toolkit.createComposite(client);
		outTableComposite.setLayout(new GridLayout(2, false));
		outTableComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

		scrapTableManager = new ListTableManager(searchADTable(TABLE_NAME));
		scrapTableManager.newViewer(outTableComposite);
		scrapTableManager.addDoubleClickListener(new IMouseAction() {

			@Override
			public void run(NatTable natTable, MouseEvent event) {
				scrapActionsMap.remove(scrapTableManager.getSelectedObject());
				scrapTableManager.remove(scrapTableManager.getSelectedObject());
			}
		});

		Composite btnComposite = toolkit.createComposite(client);
		GridData btnGd = new GridData(GridData.FILL_HORIZONTAL);
		btnGd.verticalAlignment = SWT.BOTTOM;
		btnGd.horizontalAlignment = SWT.RIGHT;
		btnComposite.setLayout(new GridLayout(2, false));
		btnComposite.setLayoutData(btnGd);

		scrapBtn = createButton(btnComposite, Message.getString("wip.scrap"));
		scrapBtn.setEnabled(false);
		scrapBtn.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				scrapAdapter();
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}
		});
	}

	@Override
	protected void createLeftContent(Composite client, FormToolkit toolkit) {
		// ɨ��
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(4, false));
		top.setLayoutData(gd);

		GridData gText = new GridData();
		gText.widthHint = 216;

		Label label = toolkit.createLabel(top, Message.getString("wip.component_unit_id"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		label.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
		txtLotId = toolkit.createText(top, "", SWT.BORDER);
		txtLotId.setLayoutData(gText);
		txtLotId.setTextLimit(64);
		txtLotId.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					Lot lot = null;
					String lotId = tLotId.getText();

					if (!StringUtil.isEmpty(lotId)) {
						tLotId.setText(lotId);
						lot = searchLot(lotId);
						tLotId.selectAll();
						if (lot == null) {
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							lot = new Lot();
							scrapBtn.setEnabled(false);
						} else {
							statusChanged(lot.getState());
						}
						
						getEntityForm().setObject(lot);
						getEntityForm().loadFromObject();
						getEntityForm().refreshScrapCodeList();
					}
					break;
				}
			}
		});

		// Form
		ScrolledForm inTableForm = toolkit.createScrolledForm(client);
		inTableForm.setLayout(new GridLayout(1, false));
		inTableForm.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite inTableFormBody = inTableForm.getBody();
		inTableFormBody.setLayout(new GridLayout(1, false));
		inTableFormBody.setLayoutData(new GridData(GridData.FILL_BOTH));

		entityForm = new ScrapByComponentEntityForm(getADManger(), inTableFormBody, SWT.NONE, null, getAdTable().getFields(),
				2, scrapType, getManagedForm().getMessageManager());
		entityForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		entityForm.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));

		// ��ť
		Composite btnComp = toolkit.createComposite(client);
		GridData inbtnGd = new GridData(GridData.FILL_HORIZONTAL);
		inbtnGd.verticalAlignment = SWT.BOTTOM;
		inbtnGd.horizontalAlignment = SWT.RIGHT;
		btnComp.setLayout(new GridLayout(1, false));
		btnComp.setLayoutData(inbtnGd);

		SquareButton addBtn = createButton(btnComp, Message.getString(ExceptionBundle.bundle.CommonAdd()));
		addBtn.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				addAdapter();
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}
		});

	}

	@SuppressWarnings("unchecked")
	protected void addAdapter() {
		try {
			getManagedForm().getMessageManager().removeAllMessages();
			if (getEntityForm().getObject() != null) {
				boolean saveFlag = true;
				if (!getEntityForm().saveToObject()) {
					saveFlag = false;
				}

				if (saveFlag) {
					Lot lot = (Lot) getEntityForm().getObject();
					LotScrap scrap = getEntityForm().getScrapAction();
					List<? extends Object> lotActions = scrapTableManager.getInput();
					if (scrap.getSubQty() == null || BigDecimal.ZERO.compareTo(scrap.getSubQty()) >= 0) {
						UI.showInfo(Message.getString("wip.scrap_qty_must_greater_than_zero"));
						return;
					}
					
					IntSummaryStatistics sum = lotActions.stream().filter(o -> lot.getAttribute2().equals(((LotScrap) o).getComponentRrn())).collect(
							Collectors.summarizingInt(o -> ((LotScrap) o).getSubQty().intValue()));
					
					// ��鱨�������Ƿ������������
					BigDecimal compareQty = lot.getSubQty() == null ? BigDecimal.ZERO : lot.getSubQty();
					compareQty = compareQty.subtract(scrap.getSubQty());
					
					if (compareQty.compareTo(new BigDecimal(sum.getSum())) < 0) {
						UI.showError(Message.getString("wip.scrap_add_exceed"));
						return;
					}
					
					scrapTableManager.add(scrap);
					
					// �����ϲ���
					if (!scrapUnits.contains(getCurrentUnit())) {
						scrapUnits.add(getCurrentUnit());
					}
					
					LotAction action = new LotAction();
					action.setLotRrn(scrap.getLotRrn());
					action.setActionCode(scrap.getActionCode());
					action.setActionReason(scrap.getActionReason());
					action.setActionComment(scrap.getActionComment());
					action.setActionType(LotAction.ACTIONTYPE_SCRAP);
					List<ProcessUnit> qtyUnits = (List<ProcessUnit>) scrap.getAttribute1();
					action.setActionUnits(qtyUnits);
					
					scrapActionsMap.put(scrap, action);
					scrapBtn.setEnabled(true);
				}
			}
			txtLotId.selectAll();
			txtLotId.setFocus();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void scrapAdapter() {
		try {
			if (CollectionUtils.isEmpty(scrapUnits) || MapUtils.isEmpty(scrapActionsMap)) {
				return;
			}
			
			List<ComponentUnit> sureScrapUnits = Lists.newArrayList();
			Map<String, List<LotAction>> scrapUnitsMap = Maps.newHashMap();
			// ���¼�����Ҫ���ϵ�Ƭ
			for (ComponentUnit unit : scrapUnits) {
				List<LotAction> lotActions = Lists.newArrayList();
				for (LotScrap lotScrap : scrapActionsMap.keySet()) {
					if (unit.getComponentId().equals(lotScrap.getComponentId())) {
						lotActions.add(scrapActionsMap.get(lotScrap));
					}
				}
				
				if (CollectionUtils.isNotEmpty(lotActions)) {
					scrapUnitsMap.put(unit.getComponentId(), lotActions);
					sureScrapUnits.add(unit);
				}
			}
			
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			componentManager.scrapComponentQty(sureScrapUnits, scrapUnitsMap, true, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			clear();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private void clear() {
		scrapTableManager.setInput(Lists.newArrayList());
		if (getCurrentUnit() != null) {
			Lot lot = searchLot(getCurrentUnit().getComponentId());
			lot = lot == null ? new Lot() : lot;
			getEntityForm().setObject(lot);
			getEntityForm().loadFromObject();
		}
		
		scrapUnits.clear();
		scrapActionsMap.clear();
		scrapBtn.setEnabled(false);
	}

	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_WAIT.equals(newStatus) || LotStateMachine.STATE_RUN.equalsIgnoreCase(newStatus)
				|| LotStateMachine.STATE_FIN.equals(newStatus)) {
			scrapBtn.setEnabled(true);
		} else {
			scrapBtn.setEnabled(false);
		}
	}
	
	@Override
	protected Lot searchLot(String componentId) {
		try {
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			ComponentUnit componentUnit = componentManager.getComponentByComponentId(Env.getOrgRrn(), componentId);
			setCurrentUnit(componentUnit);
			
			if (getCurrentUnit().getParentUnitRrn() == null || 
					LotStateMachine.COMCLASS_COM.equals(getCurrentUnit().getComClass())) {
				return null;
			}
			
			// չʾLot��Ϣ��Panel����
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLot(componentUnit.getParentUnitRrn());
			lot.setAttribute1(componentUnit.getComponentId());
			lot.setAttribute2(componentUnit.getObjectRrn());
			lot.setMainQty(componentUnit.getMainQty());
			lot.setSubQty(componentUnit.getSubQty());
			lot.setState(componentUnit.getState());
			lot.setComClass(componentUnit.getComClass());
			return lot;
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	public ScrapByComponentEntityForm getEntityForm() {
		return entityForm;
	}

	public void setEntityForm(ScrapByComponentEntityForm entityForm) {
		this.entityForm = entityForm;
	}

	public ComponentUnit getCurrentUnit() {
		return currentUnit;
	}

	public void setCurrentUnit(ComponentUnit currentUnit) {
		this.currentUnit = currentUnit;
	}
	
}
