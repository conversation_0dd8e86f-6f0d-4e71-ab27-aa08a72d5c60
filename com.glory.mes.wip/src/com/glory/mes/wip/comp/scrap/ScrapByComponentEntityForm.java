package com.glory.mes.wip.comp.scrap;

import java.math.BigDecimal;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotScrap;
import com.google.common.collect.Lists;

public class ScrapByComponentEntityForm extends EntityForm {

	private static final Logger logger = Logger.getLogger(ScrapByComponentEntityForm.class);

	private IField fieldDefectCode;
	private static final String DEFECTCODE = "DefectCode";
	public static final String DEFECTCODE_ID = "defectCode";
	private IField fieldReasonCode;
	private static final String REASONCODE = "ReasonCode";
	public static final String REASONCODE_ID = "reasonCode";
	private IField fieldComments;
	private static final String COMMENTS = "Comments";
	public static final String COMMENTS_ID = "comments";

	private IField fieldQty;
	private static final String QTY = "Qty";
	public static final String QTY_ID = "qty";
	
	private IField fieldMainQty;
	private static final String MAINQTY = "MainQty";
	public static final String MAINQTY_ID = "mainQty";
	
	private static final String SCRAPTYPE_MAIN = "MAIN";
	private boolean isMainQty;
	
	public static final String DEFAULT_SCRAP_TABLE = "ScrapCode";

	public ScrapByComponentEntityForm(ADManager adManager, Composite parent, int style, Object object, List<ADField> adfields,
			int gridY, String scrapType, IMessageManager mmng) {
		super(parent, style, object, mmng);
		this.allADfields = adfields;
		this.setGridY(gridY);
		this.setADManager(adManager);
		this.setMainQty(SCRAPTYPE_MAIN.equals(scrapType));
		createForm();
	}
	@Override
	protected void createContent() {
		super.createContent();
		getForm().getBody().setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			ADField separatorADField = new ADField();
			separatorADField.setIsSameline(true);
			SeparatorField separatorField = createSeparatorField("Separator", Message.getString("wip.scrap_info"));
			separatorField.setADField(separatorADField);
			this.addField("Separator", separatorField);
			
			ADField mandatoryADField = new ADField();
			mandatoryADField.setIsMandatory(true);
			
			fieldDefectCode = createUserRefList(DEFECTCODE_ID, Message.getString("wip.scrapcode_lot") + "*", getScrapCode(), true);
			fieldDefectCode.setADField(mandatoryADField);

			fieldReasonCode = createText(REASONCODE, Message.getString("wip.scrap_reason") + "*", 64);
			fieldReasonCode.setADField(mandatoryADField);

			fieldComments = createText(COMMENTS_ID, Message.getString("wip.remark"), "", 128);
			fieldQty = createText(QTY_ID, Message.getString("wip.subqty") + "*", "", 32);
			fieldQty.setMandatory(true);
			
			if (isMainQty) {
				fieldMainQty = createText(MAINQTY_ID, Message.getString("wip.mainqty") + "*", "", 32);
				fieldMainQty.setMandatory(true);
			}
			

			addField(DEFECTCODE, fieldDefectCode);
			addField(REASONCODE, fieldReasonCode);
			addField(COMMENTS, fieldComments);
			if (isMainQty) {
				addField(MAINQTY, fieldMainQty);
			}
			
			addField(QTY, fieldQty);
		} catch (Exception e) {
			logger.error("ScrapByComponentEntityForm : addFields()", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refreshScrapCodeList() {
		try {
			RefTableField tableField = (RefTableField) fieldDefectCode;
			List<ADURefList> input = getADManger().getADURefList(Env.getOrgRrn(), getScrapCode());
			tableField.setInput(input);
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
	}
	
	protected String getScrapCode() throws Exception {
        String scrapCode = null;
        PrdManager prdManager = Framework.getService(PrdManager.class);
        if (getLot() != null && getLot().getObjectRrn() != null
                && getLot().getStepRrn() != null) {
            Step step = new Step();
            step.setObjectRrn(getLot().getStepRrn());
            step = (Step) prdManager.getSimpleProcessDefinition(step);
            scrapCode = step.getScrapCodeSrc();
        } else {
            scrapCode = DEFAULT_SCRAP_TABLE;
        }
        if (scrapCode == null || scrapCode.trim().length() == 0) {
            scrapCode = DEFAULT_SCRAP_TABLE;
        }
        return scrapCode;
    }

	private Lot getLot() {
		return (Lot) getObject();
	}
	@Override
	public void refresh() {
		super.refresh();
		((RefTableField) fieldDefectCode).setValue(null, true);
		((TextField)  fieldReasonCode).setText("");
		((TextField) fieldQty).setText("");
		((TextField) fieldComments).setText("");
		if (isMainQty) {
			((TextField) fieldMainQty).setText("");
		}
	}

	public LotScrap getScrapAction() {
		Lot comp = (Lot) getObject();

		LotScrap lotAction = new LotScrap();
		lotAction.setActionCode(DBUtil.toString(fieldDefectCode.getValue()));
		lotAction.setActionReason(DBUtil.toString(fieldReasonCode.getValue()));
		lotAction.setActionComment(DBUtil.toString(fieldComments.getValue()));
		lotAction.setLotId(comp.getLotId());
		lotAction.setLotRrn(comp.getObjectRrn());
		lotAction.setComponentId(DBUtil.toString(comp.getAttribute1()));
		lotAction.setComponentRrn(DBUtil.toLong(comp.getAttribute2()));
		
		BigDecimal scrapQty = new BigDecimal(DBUtil.toString(fieldQty.getValue()));
		BigDecimal mainQty = BigDecimal.ZERO;
		if (fieldMainQty != null) {
			String mainStr = DBUtil.toString(fieldMainQty.getValue());
			if (!StringUtil.isEmpty(mainStr)) {
				mainQty = new BigDecimal(DBUtil.toString(fieldMainQty.getValue()));
			}
		}
		lotAction.setSubQty(scrapQty);
		lotAction.setMainQty(mainQty);
		
		ComponentUnit componentUnit = new ComponentUnit();
		componentUnit.setMainQty(mainQty);
		componentUnit.setSubQty(scrapQty);
		componentUnit.setObjectRrn(DBUtil.toLong(comp.getAttribute2()));
		componentUnit.setComponentId(DBUtil.toString(comp.getAttribute1()));
		lotAction.setAttribute1(Lists.newArrayList(componentUnit));
		return lotAction;
	}

	@Override
	public boolean validate() {
		mmng.removeAllMessages();

		boolean defectCodeIsNull = GenericValidator.isBlankOrNull((String) fieldDefectCode.getValue());
		boolean reasonCodeIsNull = GenericValidator.isBlankOrNull((String) fieldReasonCode.getValue());
		boolean qtyIsNull = GenericValidator.isBlankOrNull((String) fieldQty.getValue())
				|| !GenericValidator.isInt((String) fieldQty.getValue());

		boolean validFlag = true;
		if (!defectCodeIsNull) {
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(DEFECTCODE, String.format(Message.getString("common.ismandatry"), DEFECTCODE), null,
					IMessageProvider.ERROR, fieldDefectCode.getControls()[1]);
		}

		if (!reasonCodeIsNull) {
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(REASONCODE, String.format(Message.getString("common.ismandatry"), REASONCODE), null,
					IMessageProvider.ERROR, fieldReasonCode.getControls()[1]);
		}

		if (!qtyIsNull) {
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			mmng.addMessage(QTY, String.format(Message.getString("common.ismandatry"), QTY), null,
					IMessageProvider.ERROR, fieldQty.getControls()[1]);
		}
		
		if (fieldMainQty != null) {
			boolean mainQtyIsNull = GenericValidator.isBlankOrNull((String) fieldMainQty.getValue())
					|| !GenericValidator.isInt((String) fieldMainQty.getValue());
			if (!mainQtyIsNull) {
				validFlag = validFlag && true;
			} else {
				validFlag = validFlag && false;
				mmng.addMessage(MAINQTY, String.format(Message.getString("common.ismandatry"), MAINQTY), null,
						IMessageProvider.ERROR, fieldMainQty.getControls()[1]);
			}
		}
		return validFlag;
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.equals(fieldQty) && !f.equals(fieldMainQty) && !f.equals(fieldComments)
						&& !f.equals(fieldDefectCode) && !f.equals(fieldReasonCode)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			refresh();
		}
	}

	@Override
	public boolean saveToObject() {
		if (object != null) {
			if (!validate()) {
				return false;
			}
			return true;
		}
		return false;
	}

	@Override
	public Color getBackground() {
		return SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE);
	}
	public boolean isMainQty() {
		return isMainQty;
	}
	public void setMainQty(boolean isMainQty) {
		this.isMainQty = isMainQty;
	}
	
}
