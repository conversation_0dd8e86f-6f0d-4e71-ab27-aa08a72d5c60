package com.glory.mes.wip.custom;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.model.WipFlagDef;
import com.google.common.collect.Maps;

@SuppressWarnings("restriction")
public class LotFlagDefineComposite extends CustomCompsite {

	protected ScrolledForm form;
	protected int flagSize = 25;
	protected Map<String, TextField> flagTextMap = Maps.newHashMap();
	
	private static final String FIELD_NAME = "flagName";
	private static final String FIELD_DESC = "flagDesc";
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		FFormToolKit formToolKit = new FFormToolKit(parent.getDisplay());

		GridLayout layout = new GridLayout();
        layout.verticalSpacing = 0;
        layout.horizontalSpacing = 0;
        layout.marginWidth = 0;
        layout.marginHeight = 0;
		
		form = formToolKit.createScrolledForm(parent);        
        form.setLayoutData(new GridData(GridData.FILL_BOTH));
        form.setLayout(layout);
		
		Composite composite = form.getBody();
		layout = new GridLayout(8, false);
		layout.verticalSpacing = DPIUtil.autoScaleDown(12);
        layout.horizontalSpacing = DPIUtil.autoScaleDown(3);
        layout.marginWidth = 0;
        layout.marginHeight = 0;
        layout.marginLeft = DPIUtil.autoScaleDown(20);
        layout.marginRight = DPIUtil.autoScaleDown(5);
        layout.marginTop = DPIUtil.autoScaleDown(15);
        layout.marginBottom = 0;
		composite.setLayout(layout);
		GridData gd = new GridData();
		composite.setLayoutData(gd);
		
		int index = 1;
		for (int i = 0; i < flagSize * 2; i++) {
			String id;
			String label;
			if (i % 2 == 0) {
				id = FIELD_NAME + index;
				label = String.format(Message.getString("wip.flagdef_name"), index);
			} else {
				id = FIELD_DESC + index;
				label = String.format(Message.getString("wip.flagdef_desc"), index);
				index++;
			}
			TextField textField = new TextField(id);
			textField.setLabel(label);
			textField.createContent(composite, toolkit);
			
			Control[] ctrls = textField.getControls();
			if (ctrls == null || ctrls.length == 0) {
				continue;
			}
			
			for (Control control : ctrls) {
				gd = (GridData)control.getLayoutData();
				if (gd == null) {
					gd = new GridData();
				}
				gd.verticalIndent = 0;
				gd.horizontalIndent = DPIUtil.autoScaleDown(15);
				control.setLayoutData(gd);
			}
			flagTextMap.put(id, textField);
		}
		return composite;
	}
	
	@Override
	public void refresh() {
		
	}

	@SuppressWarnings("unchecked")
	@Override
	public void setValue(Object value) {
		flagTextMap.values().stream().forEach(text -> {
			text.setText(null);
		});
		
		List<WipFlagDef> wipFlagDefs = (List<WipFlagDef>) value;
		for (WipFlagDef wipFlagDef : wipFlagDefs) {
			String name = FIELD_NAME + wipFlagDef.getFlagBit();
			String desc = FIELD_DESC + wipFlagDef.getFlagBit();
			for (String key : flagTextMap.keySet()) {
				if (key.equals(name)) {
					flagTextMap.get(key).setText(wipFlagDef.getFlagName());
				} else if (key.equals(desc)) {
					flagTextMap.get(key).setText(wipFlagDef.getFlagDesc());
				}
			}
		}
	}

	@Override
	public Object getValue() {
		Map<String, WipFlagDef> map = Maps.newHashMap();
		for (String key : flagTextMap.keySet()) {
			if (key.contains(FIELD_NAME)) {
				if (!StringUtil.isEmpty(flagTextMap.get(key).getText())) {
					String flagBit = key.substring(FIELD_NAME.length(), key.length());
					if (map.get(flagBit) == null) {
						WipFlagDef flagDef = new WipFlagDef();
						flagDef.setOrgRrn(Env.getOrgRrn());
						flagDef.setCreatedBy(Env.getUserName());
						flagDef.setFlagBit(Integer.valueOf(flagBit));
						flagDef.setFlagName(flagTextMap.get(key).getText());
						map.put(flagBit, flagDef);
					} else {
						map.get(flagBit).setFlagName(flagTextMap.get(key).getText());
					}
				}
			} else if (key.contains(FIELD_DESC)) {
				if (!StringUtil.isEmpty(flagTextMap.get(key).getText())) {
					String flagBit = key.substring(FIELD_DESC.length(), key.length());
					if (map.get(flagBit) == null) {
						WipFlagDef flagDef = new WipFlagDef();
						flagDef.setOrgRrn(Env.getOrgRrn());
						flagDef.setCreatedBy(Env.getUserName());
						flagDef.setFlagBit(Integer.valueOf(flagBit));
						flagDef.setFlagDesc(flagTextMap.get(key).getText());
						map.put(flagBit, flagDef);
					} else {
						map.get(flagBit).setFlagDesc(flagTextMap.get(key).getText());
					}
				}
			}
		}
		List<WipFlagDef> flagDefs = map.values().stream()
				.filter(flag -> !StringUtil.isEmpty(flag.getFlagName()))
				.sorted(Comparator.comparing(WipFlagDef::getFlagBit))
				.collect(Collectors.toList());
		return flagDefs;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		
	}

}
