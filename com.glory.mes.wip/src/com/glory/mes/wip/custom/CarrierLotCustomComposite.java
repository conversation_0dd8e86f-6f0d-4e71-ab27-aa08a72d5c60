package com.glory.mes.wip.custom;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.bylot.RunByLotQueryTableManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;

/**
 * �����ؾߺ���ʾ�ؾ������е�������Ϣ
 * ѡ������,��ʾ���ε���ϸ��Ϣ
 */
public class CarrierLotCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(CarrierLotCustomComposite.class);
	
	public static final String ATTRIBUTE_SHOW_CHEKBOX = "ShowCheckBox";
	public static final String ATTRIBUTE_NOT_SHOW_CARRIER_INPUT = "NotShowCarrierInput";
	public static final String ATTRIBUTE_SHOW_LOT_INPUT = "ShowLotInput";
	public static final String ATTRIBUTE_SHOW_OPERATOR_INPUT = "ShowOperatorInput";
	public static final String ATTRIBUTE_SHOW_LOT_DETAIL = "ShowLotDetail";
	public static final String ATTRIBUTE_SHOW_COMPONENT = "ShowComponent";
	public static final String ATTRIBUTE_LOTTABLENAME = "LotTableName";
	public static final String ATTRIBUTE_COMPTABLENAME = "CompTableName";
	public static final String ATTRIBUTE_TABLE_HEIGT_HHINT = "TableHeigthHint";
	public static final String ATTRIBUTE_LOTLISTAUTOSIZE = "LotListAutoSize";
	
	private static final String TABLE_NAME = "WIPLotByCarrier";
	private static final String COMP_TABLE_NAME = "WIPRunByLotComponentUnit";
	
	protected String lotTableName;
	protected String compTableName;
	
	protected boolean checkFlag;
	protected boolean showLotFlag;
	protected boolean showDetailFlag = false;
	protected boolean notShowCarrierFlag = false;
	protected boolean showOperatorFlag = false;
	protected boolean showComponentFlag = false;
	
	protected boolean lotListAutoSize = true;
	
	protected ListTableManager lotTableManager;
	protected ListTableManager compTableManager;

	protected EntityForm lotDetailsForm;
	protected String lblCarrier;
	protected HeaderText txtCarrierId;
	protected HeaderText txtLotId;
	protected HeaderText txtOperator;
	protected Label lblOperatorId;
	
	protected Integer tableHeigthHint;
	
	public CarrierLotCustomComposite() {
		super();
	}
	
	public CarrierLotCustomComposite(boolean checkFlag) {
		setCheckFlag(checkFlag);
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		try {
			parent.setLayout(new GridLayout(1, false));
			parent.setLayoutData(new GridData(GridData.FILL_BOTH));
			parent.setBackgroundMode(SWT.INHERIT_FORCE);

			int gridY = 0;
			if (!notShowCarrierFlag) {
				gridY += 2;
			}
			if (showLotFlag) {
				gridY += 2;
			}
			if (showOperatorFlag) {
				gridY += 2;
			}
			Composite carrierComposite = null; 
			if (gridY != 0) {
				carrierComposite = new Composite(parent, SWT.NONE);
				carrierComposite.setLayout(new GridLayout(gridY, false));
			}

			if (!notShowCarrierFlag) {
				Label lblCarrierId = new Label(carrierComposite, SWT.NONE);
		        if (StringUtil.isEmpty(lblCarrier)) {
					lblCarrierId.setText(Message.getString("wip.carrier_id"));
		        } else {
		        	lblCarrierId.setText(lblCarrier);
		        }
				lblCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtCarrierId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-carrier"));
				txtCarrierId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtCarrierId.setTextLimit(32);
				
				final String carrierIdEvent = "carrierId";
				EventHandler defaultCarrierEvent = new EventHandler() {
		             public void handleEvent(Event event) {
		            	 getLotsByCarrierId((String)event.getProperty(GlcEvent.PROPERTY_DATA));
		             }
		        }; 
		        subscribeDefaultEvent(carrierIdEvent, GlcEvent.EVENT_ENTERPRESSED, defaultCarrierEvent);
				txtCarrierId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// �س��¼�
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String carrierId = ((Text) event.widget).getText();
							if (!StringUtil.isEmpty(carrierId)) {
								postEvent(carrierIdEvent, GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(carrierId));	
							}
						}
					}
				});
			}
			
			if (showLotFlag) {
				Label lblLotId = new Label(carrierComposite, SWT.NONE);
				lblLotId.setText(Message.getString("wip.lot_id"));
				lblLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));

				txtLotId = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-lot"));
				txtLotId.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
				txtLotId.setTextLimit(64);
				
				final String lotIdEvent = "lotId";
				EventHandler defaultLotEvent = new EventHandler() {
		             public void handleEvent(Event event) {
		            	 getLotByLotId((String)event.getProperty(GlcEvent.PROPERTY_DATA));
		             }
		        }; 
		        subscribeDefaultEvent(lotIdEvent, GlcEvent.EVENT_ENTERPRESSED, defaultLotEvent);
				txtLotId.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {
						// �س��¼�
						if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
							String lotId = ((Text) event.widget).getText().trim();
							if (!StringUtil.isEmpty(lotId)) {
								postEvent(lotIdEvent, GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(lotId));	
		    				}
						}
					}
				});
			}
			if (showOperatorFlag) {
	    		lblOperatorId = new Label(carrierComposite, SWT.NONE);
	    		lblOperatorId.setText(Message.getString("wip.operator"));
	    		lblOperatorId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
				
	    		txtOperator = new HeaderText(carrierComposite, SWTResourceCache.getImage("header-text-op"));
	    		txtOperator.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
	    		txtOperator.setTextLimit(32);
	    		
	    		final String operatorId = "operator";
				EventHandler defaultOperatorEvent = new EventHandler() {
		             public void handleEvent(Event event) {
		            	 //TODO
		             }
		        }; 
		        subscribeDefaultEvent(operatorId, GlcEvent.EVENT_ENTERPRESSED, defaultOperatorEvent);
	    		txtOperator.addKeyListener(new KeyAdapter() {
	    			@Override
	    			public void keyPressed(KeyEvent event) {
	    				if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
	    					String operator = ((Text) event.widget).getText().trim();
	    					postEvent(operatorId, GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(operator));
	    				}
	    			}

	    		});
	    		txtOperator.addFocusListener(new FocusListener() {
	    			public void focusGained(FocusEvent e) {
	    			}

	    			public void focusLost(FocusEvent e) {
	    			}
	    		});
			}
			
			Composite composite = new Composite(parent, SWT.NONE);
			GridLayout layout = new GridLayout(showComponentFlag ? 2 : 1, true);
			layout.horizontalSpacing = 0;
			layout.verticalSpacing = 0;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			layout.marginLeft = 0;
			layout.marginRight = 0;
			layout.marginTop = 0;
			layout.marginBottom = 0;
			composite.setLayout(layout);
			
			GridData gridData = new GridData(GridData.FILL_BOTH);
			if (tableHeigthHint != null) {
				gridData = new GridData(GridData.FILL_HORIZONTAL);
				int minHeightHint = Toolkit.getDefaultToolkit().getScreenSize().height / 6;
				if (minHeightHint > tableHeigthHint) {
					tableHeigthHint = minHeightHint;
				}
				gridData.heightHint = tableHeigthHint;
			}
			composite.setLayoutData(gridData);
			composite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_FORM_TOOLKIT_BG));
			
			Composite lotComposite = new Composite(composite, SWT.NULL);
			configureBody(lotComposite);

			final String lotTableId = "lotTable";
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), getLotTableName());
			lotTable.setIsFilter(false);
			lotTableManager = new RunByLotQueryTableManager(lotTable, checkFlag);
			lotTableManager.setAutoSizeFlag(lotListAutoSize);
			lotTableManager.newViewer(lotComposite);
			lotTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					StructuredSelection selection = (StructuredSelection) event.getSelection();
					Lot lot = (Lot) selection.getFirstElement();
					if (lotDetailsForm != null) { 
						lotDetailsForm.setObject(lot);
						lotDetailsForm.loadFromObject();
					}
					postEvent(lotTableId, GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(lot));
				}
			});
			
			if (showComponentFlag) {
				Composite compComposite = new Composite(composite, SWT.NONE);
				configureBody(compComposite);
				
				ADTable compTable = adManager.getADTable(Env.getOrgRrn(), getCompTableName());
				compTable.setIsFilter(false);
				compTableManager = new ListTableManager(compTable);
				compTableManager.setAutoSizeFlag(true);
				compTableManager.newViewer(compComposite);
			}
			
			if (showDetailFlag) {
				Composite lotDetailsComposite = new Composite(parent, SWT.NONE);
				lotDetailsComposite.setLayout(new GridLayout(1, false));
//				lotDetailsComposite.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_MAIN_SUBAPP_DEFAULT_BG));
	
				lotDetailsForm = new EntityForm(lotDetailsComposite, SWT.NONE, lotTable, null);
			}
		} catch (Exception e) {
			logger.error("CarrierLotCustomComposite createForm error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return parent;
	}
	
	public void getLotByLotId(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			if (lot != null) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.add(lot);
				
				lotTableManager.setInput(lots);
				
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				lotTableManager.refresh();
				// Ĭ��ȫѡ
				if (checkFlag) {
					lotTableManager.setCheckedObject(lot);
				}
				lotTableManager.setSelection(new StructuredSelection(new Object[] {lot}));
				loadComponentUnits(lot);
			} else {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();

				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				
				lotTableManager.setSelection(new StructuredSelection(new Object[] {new Lot()}));
				
				if (showComponentFlag) {
					compTableManager.setInput(Lists.newArrayList());
					compTableManager.refresh();
				}
			}
		} catch (Exception e) {
			logger.error("CarrierLotCustomComposite getLotByLotId error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			boolean existLot = false;
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isNotEmpty(lots)) {
					lotTableManager.setInput(lots);
					// Ĭ��ȫѡ
					if (checkFlag) {
						for (Lot lot : lots) {
							lotTableManager.setCheckedObject(lot);
						}
					}
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					lotTableManager.refresh();
					
					loadComponentUnits(lots);
					existLot = true;
				}
			} 
			if (carrier == null || !existLot) {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				
				if (showComponentFlag) {
					compTableManager.setInput(Lists.newArrayList());
					compTableManager.refresh();
				}
			}
			
			if (lotDetailsForm != null) {
				lotDetailsForm.setObject(new Lot());
				lotDetailsForm.loadFromObject();
			}
		} catch (Exception e) {
			logger.error("CarrierLotCustomComposite getLotByLotId error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void loadComponentUnits(Lot lot) throws Exception {
		if(!showComponentFlag) {
			return;
		}
		
		if (!showComponentFlag || lot == null || lot.getObjectRrn() == null) {
			compTableManager.setInput(Lists.newArrayList());
			compTableManager.refresh();
			return;
		}
		
		if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lotWithComps = lotManager.getLotWithComponent(lot.getObjectRrn());
			
			if (CollectionUtils.isNotEmpty(lotWithComps.getSubProcessUnit())) {
				List<ComponentUnit> componentUnits = Lists.newArrayList();
				for (ProcessUnit processUnit : lotWithComps.getSubProcessUnit()) {
					ComponentUnit componentUnit = (ComponentUnit) processUnit;
					componentUnit.setAttribute1(lot.getLotId());
					componentUnits.add(componentUnit);
				}
				
				componentUnits = componentUnits.stream().sorted(Comparator.comparingInt(
						c -> StringUtil.isEmpty(c.getPosition()) ? 0 : Integer.valueOf(c.getPosition()))).collect(Collectors.toList());
				
				compTableManager.setInput(componentUnits);
				compTableManager.refresh();
			} else {
				compTableManager.setInput(Lists.newArrayList());
				compTableManager.refresh();
			}
		} else {
			compTableManager.setInput(Lists.newArrayList());
			compTableManager.refresh();
		}
	}
	
	public void loadComponentUnits(List<Lot> lots) throws Exception {
		if(!showComponentFlag) {
			return;
		}
		
		if (!showComponentFlag || CollectionUtils.isEmpty(lots)) {
			compTableManager.setInput(Lists.newArrayList());
			compTableManager.refresh();
			return;
		}
		
		List<ComponentUnit> allUnits = Lists.newArrayList();
		for (Lot lot : lots) {
			if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lotWithComps = lotManager.getLotWithComponent(lot.getObjectRrn());
				
				if (CollectionUtils.isNotEmpty(lotWithComps.getSubProcessUnit())) {
					List<ComponentUnit> componentUnits = Lists.newArrayList();
					for (ProcessUnit processUnit : lotWithComps.getSubProcessUnit()) {
						ComponentUnit componentUnit = (ComponentUnit) processUnit;
						componentUnit.setAttribute1(lot.getLotId());
						componentUnits.add(componentUnit);
					}
					componentUnits = componentUnits.stream().sorted(Comparator.comparingInt(
							c -> StringUtil.isEmpty(c.getPosition()) ? 0 : Integer.valueOf(c.getPosition()))).collect(Collectors.toList());
					
					allUnits.addAll(componentUnits);
				}
			}
		}
		
		if (CollectionUtils.isNotEmpty(allUnits)) {
			compTableManager.setInput(allUnits);
			compTableManager.refresh();
		} else {
			compTableManager.setInput(Lists.newArrayList());
			compTableManager.refresh();
		}
	}
	
	@Override
	public void refresh() {
	}

	@Override
	public void setValue(Object value) {
	}

	@Override
	public Object getValue() {
		return null;
	}
	
	public HeaderText getTxtCarrierId() {
		return txtCarrierId;
	}

	public void setTxtCarrierId(HeaderText txtCarrierId) {
		this.txtCarrierId = txtCarrierId;
	}

	public HeaderText getTxtLotId() {
		return txtLotId;
	}

	public void setTxtLotId(HeaderText txtLotId) {
		this.txtLotId = txtLotId;
	}

	public ListTableManager getLotTableManager() {
		return lotTableManager;
	}

	public void setLotTableManager(ListTableManager lotTableManager) {
		this.lotTableManager = lotTableManager;
	}
	
	public ListTableManager getCompTableManager() {
		return compTableManager;
	}

	public void setCompTableManager(ListTableManager compTableManager) {
		this.compTableManager = compTableManager;
	}
	
	public EntityForm getLotDetailsForm() {
		return lotDetailsForm;
	}

	public void setLotDetailsForm(EntityForm lotDetailsForm) {
		this.lotDetailsForm = lotDetailsForm;
	}

	public String getLblCarrier() {
		return lblCarrier;
	}

	public void setLblCarrier(String lblCarrier) {
		this.lblCarrier = lblCarrier;
	}
	
	public int getTableHeigthHint() {
		return tableHeigthHint;
	}

	public void setTableHeigthHint(int tableHeigthHint) {
		this.tableHeigthHint = tableHeigthHint;
	}
	
	public HeaderText getTxtOperator() {
		return txtOperator;
	}

	public void setTxtOperator(HeaderText txtOperator) {
		this.txtOperator = txtOperator;
	}
	
	public String getLotTableName() {
		if (StringUtil.isEmpty(lotTableName)) {
			return TABLE_NAME;
		}
		return lotTableName;
	}

	public void setLotTableName(String lotTableName) {
		this.lotTableName = lotTableName;
	}

	public void setCompTableName(String compTableName) {
		this.compTableName = compTableName;
	}

	public String getCompTableName() {
		if (StringUtil.isEmpty(compTableName)) {
			return COMP_TABLE_NAME;
		}
		return compTableName;
	}

	public boolean isCheckFlag() {
		return checkFlag;
	}

	public void setCheckFlag(boolean checkFlag) {
		this.checkFlag = checkFlag;
	}

	public boolean isShowLotFlag() {
		return showLotFlag;
	}

	public void setShowLotFlag(boolean showLotFlag) {
		this.showLotFlag = showLotFlag;
	}

	public boolean isShowDetailFlag() {
		return showDetailFlag;
	}

	public void setShowDetailFlag(boolean showDetailFlag) {
		this.showDetailFlag = showDetailFlag;
	}

	public boolean isShowOperatorFlag() {
		return showOperatorFlag;
	}

	public void setShowOperatorFlag(boolean showOperatorFlag) {
		this.showOperatorFlag = showOperatorFlag;
	}

	public boolean isShowComponentFlag() {
		return showComponentFlag;
	}

	public void setShowComponentFlag(boolean showComponentFlag) {
		this.showComponentFlag = showComponentFlag;
	}
	
	public Label getLblOperatorId() {
		return lblOperatorId;
	}

	public void setLblOperatorId(Label lblOperatorId) {
		this.lblOperatorId = lblOperatorId;
	}
	
	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_SHOW_CHEKBOX:
				checkFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_LOT_INPUT:
				showLotFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_NOT_SHOW_CARRIER_INPUT:
				notShowCarrierFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_OPERATOR_INPUT:
				showOperatorFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_LOT_DETAIL:
				showDetailFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_COMPONENT:
				showComponentFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_LOTLISTAUTOSIZE:
				lotListAutoSize = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_LOTTABLENAME:
				lotTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TABLE_HEIGT_HHINT:
				tableHeigthHint = formAttribute.getIntValue();
				break;
			case ATTRIBUTE_COMPTABLENAME:
				compTableName = formAttribute.getStringValue();
				break;
			}
		}
	}
	
    public boolean isLotListAutoSize() {
		return lotListAutoSize;
	}

	public void setLotListAutoSize(boolean lotListAutoSize) {
		this.lotListAutoSize = lotListAutoSize;
	}

	public void preDestory() {
		super.preDestory();
    }

}
