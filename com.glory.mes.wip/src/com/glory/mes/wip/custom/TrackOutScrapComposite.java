package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.IMessageProvider;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.GenericValidator;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class TrackOutScrapComposite extends CustomCompsite {

	private static String TABLE_NAME = "WIPTrackOutScrapQty";
	public static final String TABLE_SCRAP = "WIPLotScrapunitl";
	public static final String DEFAULT_SCRAP_TABLE = "ScrapCode";
	public static final String DEFAULT_DEPT_TABLE = "DEPT";
	
	protected static String HEADER_SCRAP_CODE = Message.getString("wip.trackout_scrapcode");
	
	protected FormToolkit toolkit;
	protected Composite parent;
	protected ScrolledForm form;
	protected ManagedForm mform;
	
	protected Step step;
	protected List<Lot> lots;
	protected Map<ListEditorTableManager, Lot> tableManagerMap = new HashMap<ListEditorTableManager, Lot>();
	protected Map<CheckBoxTableViewerManager, Lot> checkBoxTableManagerMap = new HashMap<CheckBoxTableViewerManager, Lot>();
	protected Map<CheckBoxTableViewerManager, XCombo> xcomboMap = new HashMap<CheckBoxTableViewerManager, XCombo>();
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		this.toolkit = toolkit;
		this.parent = parent;
		return createConent();
	}
	
	public Composite createConent() {
		form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		if (CollectionUtils.isNotEmpty(lots)) {
			Composite body = form.getBody();
			GridLayout layout = new GridLayout(this.lots.size(), true);
			layout.horizontalSpacing = 8;
			
			body.setLayout(layout);
			body.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			for (int i = 0; i < this.getLots().size(); i++) {
				createUnit(body, this.getLots().get(i));
			}
		}
		mform = new ManagedForm(toolkit, form);
		return parent;
	}
	
	public Composite createUnit(Composite com, Lot lot) {
		Group group = new Group(com, SWT.NONE);
		GridLayout layout = new GridLayout(1, true);
		layout.numColumns = 1;
		layout.marginRight = 1;
		layout.marginLeft = 1;
		group.setLayout(layout);
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.widthHint = 250;
		group.setLayoutData(gd);
		group.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
		
		if (lot.getEquipmentId() != null && lot.getEquipmentId().trim().length() > 0) {
			group.setText(lot.getLotId() + "(" + lot.getEquipmentId() + ")");
		} else {
			group.setText(lot.getLotId());
		}
		
		if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			createTableComponent(group, toolkit, lot);
		} else if (QtyUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			createTableQty(group, lot);
		}
		return group;
	}
	
	protected void createTableQty(Composite composite, Lot lot) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			ListEditorTableManager tableManager = new ListEditorTableManager(adTable, false);
			tableManager.setIndexFlag(true);
			tableManager.newViewer(composite);
			
			String scrapCode = getScrapCode();
			List<ADURefList> list = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, 
					Env.getMaxResult(), "referenceName = '" + scrapCode + "' ", "");
			List<QtyUnit> scrapCodeList = new ArrayList<QtyUnit>();
			for (ADURefList ref : list) {
				QtyUnit scrap = new QtyUnit();
				scrap.setActionCode(ref.getText());
				scrap.setDescription(ref.getDescription());
				scrapCodeList.add(scrap);
			}
			tableManager.setInput(scrapCodeList);		
			tableManagerMap.put(tableManager, lot);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createTableComponent(Composite composite, FormToolkit toolkit, Lot lot) {
		Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
		tableContainer.setLayout(new GridLayout());
		tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_SCRAP);
			CheckBoxTableViewerManager tableManager = new CheckBoxTableViewerManager(adTable);
			tableManager.newViewer(tableContainer);
			
			LotManager manager = Framework.getService(LotManager.class);
			lot = manager.getLotWithComponent(lot.getObjectRrn());
			
			List<ProcessUnit> subProcessUnit = lot.getSubProcessUnit();
			for (int i = subProcessUnit.size() - 1; i >= 0; i--) {
				ComponentUnit componentUnit = (ComponentUnit) subProcessUnit.get(i);
				if (LotStateMachine.STATE_SCRAP.equals(componentUnit.getState())) {
					subProcessUnit.remove(i);
				}
			}
			tableManager.setInput(subProcessUnit);
			tableManager.addICheckChangedListener(new ICheckChangedListener() {
				
				@Override
				public void checkChanged(List<Object> eventObjects, boolean checked) {
					if (checked) {
						boolean flag = true;
						if(xcomboMap.get(tableManager).getText() == null
								|| "".equals(xcomboMap.get(tableManager).getText().trim())) {
							flag = false;
							UI.getActiveShell().getDisplay().asyncExec(new Runnable() {
								@Override
								public void run() {
									UI.showWarning(String.format(Message.getString("wip.scrap_code_required"), HEADER_SCRAP_CODE));
									for (Object object : eventObjects) {
										ComponentUnit compositeUnit = (ComponentUnit) object;
										tableManager.unCheckObject(object);
										compositeUnit.setActionCode("");
									}
								}
							});
						}
						for (Object object : eventObjects) {
							ComponentUnit compositeUnit = (ComponentUnit) object;
							if (flag) {
								compositeUnit.setActionCode(xcomboMap.get(tableManager).getText());
							} else {
								tableManager.unCheckObject(object);
								compositeUnit.setActionCode("");
							}
						}
					} else {
						for (Object object : eventObjects) {
							((ComponentUnit)object).setActionCode("");
						}
					}
				}
			});
			
			Composite scrapComp = toolkit.createComposite(composite, SWT.NULL);
	        scrapComp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
	        GridLayout gl = new GridLayout(5, false);
	        scrapComp.setLayout(gl);

			toolkit.createLabel(scrapComp, Message.getString("wip.scrap_code") + "*", SWT.NULL);
			XCombo comboScrapCode = RCPUtil.getUserRefListCombo(scrapComp, getScrapCode(), Env.getOrgRrn(), true);
			GridData cGd = new GridData(GridData.FILL_HORIZONTAL);
			cGd.horizontalSpan = 4;
			comboScrapCode.setLayoutData(cGd);
			
			toolkit.createLabel(scrapComp, Message.getString("wip.comment") + "*", SWT.NULL);
			Text commentText = toolkit.createText(scrapComp, "", SWT.BORDER);
			commentText.setLayoutData(cGd);
			
			toolkit.createLabel(scrapComp, Message.getString("wip.reason_sector") + "*", SWT.NULL);
			XCombo comboScrapDept = RCPUtil.getUserRefListCombo(scrapComp, DEFAULT_DEPT_TABLE, Env.getOrgRrn(), true);
			comboScrapDept.setLayoutData(cGd);
			
			checkBoxTableManagerMap.put(tableManager, lot);
			xcomboMap.put(tableManager, comboScrapCode);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected String getScrapCode() {
		try {
			if (step != null && step.getScrapCodeSrc() != null 
					&& step.getScrapCodeSrc().trim().length() > 0) {
				return step.getScrapCodeSrc();
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return "ScrapCode";
	}
	
	@Override
	public void refresh() {
		if (form != null && !form.isDisposed()) {
			form.dispose();
			form = null;
		}
		createConent();
	}

	@Override
	public void setValue(Object value) {
	}

	@Override
	public Object getValue() {
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
	}
	
	public List<Lot> getLots() {
		return lots;
	}
	
	public void setLots(List<Lot> lots) {
		this.lots = lots;
	}
	
	public void setStep(Step step) {
		this.step = step;
	}
	
	public List<LotAction> getScrapLotActions() {
		List<LotAction> scrapLotAction = new ArrayList<LotAction>();
		if (ComponentUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			for (CheckBoxTableViewerManager manager : checkBoxTableManagerMap.keySet()) {
				List<ProcessUnit> scrapUnits = new ArrayList<ProcessUnit>();
				List<Object> objs = manager.getCheckedObject();
				for (Object obj : objs) {
					ProcessUnit unit = (ProcessUnit) obj;
					unit.setEquipmentId(checkBoxTableManagerMap.get(manager).getEquipmentId());
					scrapUnits.add(unit);
				}
				Map<String, List<ProcessUnit>> scrapActionMap = new HashMap<String, List<ProcessUnit>>();
				for (ProcessUnit processUnit : scrapUnits) {								
					ComponentUnit componentUnit = (ComponentUnit)processUnit;
					if (scrapActionMap.containsKey(componentUnit.getActionCode())) {
						List<ProcessUnit> list = scrapActionMap.get(componentUnit.getActionCode());
						list.add(componentUnit);
						scrapActionMap.put(componentUnit.getActionCode(), list);
					} else {
						List<ProcessUnit> list = new ArrayList<ProcessUnit>();
						list.add(componentUnit);
						scrapActionMap.put(componentUnit.getActionCode(), list);
					}
				}	
				
				for (String key : scrapActionMap.keySet()) {
					LotAction lotAction = new LotAction();				
					lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
					lotAction.setActionCode(key);	
					
					List<ProcessUnit> processUnits = scrapActionMap.get(key);
					String equipmentId = processUnits.get(0).getEquipmentId();
					if (StringUtil.isEmpty(equipmentId)) {
						equipmentId = checkBoxTableManagerMap.get(manager).getEquipmentId();
					}
					lotAction.setActionUnits(processUnits);
					lotAction.setEquipmentId(equipmentId);
					
					lotAction.setLotRrn(checkBoxTableManagerMap.get(manager).getObjectRrn());
					scrapLotAction.add(lotAction);
				}
			}
		} else if (QtyUnit.getUnitType().equals(lots.get(0).getSubUnitType())) {
			for (ListEditorTableManager tableManager : tableManagerMap.keySet()) {
				List<ProcessUnit> scrapUnits = new ArrayList<ProcessUnit>();
				for (Object unit : tableManager.getInput()) {
					QtyUnit qtyUnit = (QtyUnit)unit;
					if (qtyUnit.getMainQty() != null) {
						qtyUnit.setEquipmentId(tableManagerMap.get(tableManager).getEquipmentId());
						scrapUnits.add(qtyUnit);
					}
				}
				for (ProcessUnit processUnit : scrapUnits) {
					QtyUnit qtyUnit = (QtyUnit)processUnit;
					LotAction lotAction = new LotAction();
					
					lotAction.setActionType(LotAction.ACTIONTYPE_SCRAP);
					lotAction.setActionCode(qtyUnit.getActionCode());	
					List<ProcessUnit> qtyUnits = new ArrayList<ProcessUnit>();
					qtyUnits.add(qtyUnit);
					lotAction.setActionUnits(qtyUnits);
					
					String equipmentId = qtyUnit.getEquipmentId();
					if (StringUtil.isEmpty(equipmentId)) {
						equipmentId = tableManagerMap.get(tableManager).getEquipmentId();
					}
					lotAction.setEquipmentId(equipmentId);
					
					lotAction.setLotRrn(tableManagerMap.get(tableManager).getObjectRrn());
					scrapLotAction.add(lotAction);	
				}				
			}
		}
		return scrapLotAction;
	}
	
	public boolean validate() {
		for (XCombo unit : xcomboMap.values()) {
			IMessageManager mmng = mform.getMessageManager();
	        mmng.removeAllMessages();
	        boolean validateFlag = true;
	        boolean sourceIsNull = GenericValidator.isBlankOrNull(unit.getText());
	        if (sourceIsNull) {
	            mmng.addMessage(HEADER_SCRAP_CODE,
	                    String.format(Message.getString("common.ismandatry"), HEADER_SCRAP_CODE), null,
	                    IMessageProvider.ERROR, unit);
	            validateFlag = false;
	        }
	        return validateFlag;
		}
		return true;
	}

}
