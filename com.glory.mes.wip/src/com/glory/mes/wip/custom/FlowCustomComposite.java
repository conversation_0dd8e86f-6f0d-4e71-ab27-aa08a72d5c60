package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.IElementComparer;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseAdapter;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeItem;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADButton;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapter;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.adapter.ElseStateItemAdapter;
import com.glory.mes.prd.adapter.EndIfStateItemAdapter;
import com.glory.mes.prd.adapter.IfStateItemAdapter;
import com.glory.mes.prd.adapter.MoveToStateItemAdapter;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.custom.PRDFlowCustomComposite;
import com.glory.mes.prd.designer.model.MoveToState;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ElseState;
import com.glory.mes.prd.workflow.graph.node.EndIfState;
import com.glory.mes.prd.workflow.graph.node.IfState;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.lot.flow.LotItemAdapter;
import com.glory.mes.wip.lot.flow.LotProcedureAdapter;
import com.glory.mes.wip.lot.flow.LotProcedureStateItemAdapter;
import com.glory.mes.wip.lot.flow.LotReworkStateItemAdapter;
import com.glory.mes.wip.lot.flow.LotStepStateItemAdapter;
import com.glory.mes.wip.model.Lot;

public class FlowCustomComposite extends PRDFlowCustomComposite {

	private static final Logger logger = Logger.getLogger(FlowCustomComposite.class);
	
	public static final String CONTROL_LOT_ID = "lotId";
	public static final String FLOW_TYPE_LOT = "Lot";
	public static final String ATTRIBUTE_IS_MENU = "IsMenu";
	public static final String ACTIONTYPE_START = "start";
	public static final String ACTIONTYPE_END = "end";
	public static final String ACTIONTYPE_RETURN = "return";
	

	protected String flowType = FLOW_TYPE_LOT;
	//�Ҽ��˵�
	protected Menu menu;
	protected TreeItem currentSelectedItem;
	//��ʼ����
	protected TreeItem startStepStateItem;
	//��������
	protected TreeItem endStepStateItem;
	//���ع���
	protected TreeItem returnStepStateItem;
	//�Զ���ڵ�
	protected TreeItem customizeStepStateItem;
	
	protected Label lblId;
   
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite container = toolkit.createComposite(parent, SWT.NONE);
		container = CustomCompsite.configureBody(container);
		try {
			if (isShowInput) {
				Composite titleComposite = toolkit.createComposite(container, SWT.NONE);
				titleComposite.setLayout(new GridLayout(6, false));
				titleComposite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
				
				lblId = new Label(titleComposite, SWT.NONE);
				lblId.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
				lblId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
				switch (flowType) {
					case FLOW_TYPE_LOT:
						lblId.setText(Message.getString("wip.lot_id"));
						break;
					case FLOW_TYPE_PART:
						lblId.setText(Message.getString("wip.part_name"));
						break;
					case FLOW_TYPE_PROCESS:
						lblId.setText(Message.getString("wip.process_name"));
						break;
					case FLOW_TYPE_PROCEDURE:
						lblId.setText(Message.getString("wip.procedure_name"));
						break;					
				}			
		
				switch (flowType) {
					case FLOW_TYPE_LOT:
						createTextField(titleComposite, toolkit, CONTROL_LOT_ID);
						break;
					case FLOW_TYPE_PART:
						if (!isSearch) {
							createTextField(titleComposite, toolkit, CONTROL_PART);
						} else {
							createSearchField(titleComposite, toolkit, CONTROL_PART, refTableName);
						}
						break;
					case FLOW_TYPE_PROCESS:
						if (!isSearch) {
							createTextField(titleComposite, toolkit, CONTROL_PROCESS);
						} else {
							createSearchField(titleComposite, toolkit, CONTROL_PROCESS, refTableName);
						}
						break;
					case FLOW_TYPE_PROCEDURE:
						if (!isSearch) {
							createTextField(titleComposite, toolkit, CONTROL_PROCEDURE);
						} else {
							createSearchField(titleComposite, toolkit, CONTROL_PROCEDURE, refTableName);
						}
						break;
				}
			}
			
			treeManager = getTreeManager();
			if (treeManager == null) {
				treeManager = new LotFlowTreeManager();
				switch (flowType) {
				case FLOW_TYPE_LOT:
					treeManager = new LotFlowTreeManager();
					break;
				case FLOW_TYPE_PART:
					treeManager = new ProcessFlowTreeManager();
					break;
				case FLOW_TYPE_PROCESS:
					treeManager = new ProcessFlowTreeManager();
					break;
				case FLOW_TYPE_PROCEDURE:
					treeManager = new ProcessFlowTreeManager();
					break;
				}		
			}
			viewer = (TreeViewer) treeManager.createViewer(new Tree(container, SWT.SINGLE| SWT.BORDER 
					| SWT.V_SCROLL | SWT.H_SCROLL | SWT.FULL_SELECTION), toolkit);

			tree = viewer.getTree();
			GridData gd = new GridData();
			gd.grabExcessHorizontalSpace = true;
			gd.grabExcessVerticalSpace = true;
			gd.horizontalAlignment = SWT.FILL;
			gd.verticalAlignment = SWT.FILL;
			gd.heightHint = tree.getItemHeight() * DPIUtil.autoScaleUpUsingNativeDPI(itemHeight);
			gd.widthHint = 55;
			tree.setLayoutData(gd);

			viewer.setComparer(flowComparer);
			
			if (treeButtons != null) {
				initMenu(treeButtons);
			}
			
			if(!isMenu) {
				tree.addMouseListener(new MouseAdapter() {
					public void mouseDown(MouseEvent e) {
						doSelect(e);
					}
				});
			}
		} catch (Exception e) {
			logger.error("FlowCustomComposite createForm error:", e);
		}
		return container;
	}

	@Override
	public void refresh() {
		startStepStateItem = null;
		endStepStateItem = null;
		returnStepStateItem = null;
		currentSelectedItem = null;
		customizeStepStateItem = null;
	}

	@Override
	public void setValue(Object value) {}

	@Override
	public Object getValue() {
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_IS_SHOW_INPUT:
				isShowInput = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_FLOW_TYPE:
				flowType = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_IS_SEARCH:
				isSearch = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_REFTABLENAME:
				refTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_ITEM_HEIGHT:
				itemHeight = formAttribute.getIntValue() == null ? itemHeight : formAttribute.getIntValue();				
				break;
			case ATTRIBUTE_IS_MENU:
				isMenu = formAttribute.getBooleanValue();
				break;
			}
		}	
	}

	public void loadFlowTreeByLot(Lot lot) {
		try {
			if (lot != null) {
				if (txtId != null) {
					txtId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				}
				
				PrdManager prdManager = Framework.getService(PrdManager.class);
				ProcessDefinition pf = prdManager.getProcessInstance(lot.getProcessInstanceRrn()).getProcessDefinition();
				List<ADBase> processes = new ArrayList<ADBase>();
				processes.add(pf);
				List<Node> nodeList = prdManager.getProcessFlowList(lot.getProcessInstanceRrn());				
				for (Node node : nodeList) {
					//������ڷ���,����Ҫ���ӷ���ǰ��StepState��Ϊ�����ĸ��ڵ�
					if (node instanceof ReworkState) {
						ReworkState reworkState = (ReworkState)node;
						Long nodeRrn = reworkState.getCurrentToken().getLastNodeRrn();
						if (nodeRrn != null) {
							ADManager adManager = Framework.getService(ADManager.class);
							Node stepNode = new Node();
							stepNode.setObjectRrn(nodeRrn);
							stepNode = (Node)adManager.getEntity(stepNode);
							processes.add(stepNode);
						}
					}
					processes.add(node);
				}	
				
				String currentPath = "";
				for (Node node : nodeList) {
					if (node instanceof ProcedureState || node instanceof StepState) {
						currentPath += node.getName() + "/";
					}
				}
				
				ItemAdapterFactory adapterFactory = treeManager.adapterFactory;
				LotItemAdapter lotAdapter = (LotItemAdapter)adapterFactory.getAdapter(Lot.class);
				LotProcedureAdapter lotProcedureAdapter = (LotProcedureAdapter)adapterFactory.getAdapter(Procedure.class);
				LotReworkStateItemAdapter lotReworkStateAdapter = (LotReworkStateItemAdapter)adapterFactory.getAdapter(ReworkState.class);
				lotReworkStateAdapter.setLot(lot);
				LotProcedureStateItemAdapter lotProcedureStateAdapter = (LotProcedureStateItemAdapter)adapterFactory.getAdapter(ProcedureState.class);
				LotStepStateItemAdapter lotStepStateAdapter = (LotStepStateItemAdapter)adapterFactory.getAdapter(StepState.class);
				lotAdapter.refresh(lot, currentPath, nodeList);
				lotProcedureAdapter.refresh(lot, currentPath, nodeList);
				lotReworkStateAdapter.refresh(lot, currentPath, nodeList);
				lotProcedureStateAdapter.refresh(lot, currentPath, nodeList);
				lotStepStateAdapter.refresh(lot, currentPath, nodeList);
				
				((LotFlowTreeManager)treeManager).setInput(lot);
				viewer.collapseAll();
				
				currentFlowList = (List)processes;
				setCurrentFlow(currentFlowList);	
				
			} else {
				if (txtId != null) {
					txtId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				}
				treeManager.setInput(null);
			}
		} catch (Exception e) {
			logger.error("FlowCustomComposite getLotByLotId error:", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ��ʼ���Ҽ��˵�
	 */
	public void initMenu(List<ADButton> treeButtons) {
     
		MenuManager mgr = new MenuManager();
		
		for (ADButton button : treeButtons) {
			Action action;
			switch (button.getName()) {
			//�Ա��������һ�¸�ֵ�Ҽ���ť
			case ADButtonDefault.TREE_BUTTON_NAME_START:
				//���ø�ֵ��ť����
				action = new StartStepStateAction();
				break;
			case ADButtonDefault.TREE_BUTTON_NAME_END:
				action = new EndStepStateAction();
				break;
			case ADButtonDefault.TREE_BUTTON_NAME_STARTEND:
				action = new StartEndStateAction();
				break;
			case ADButtonDefault.TREE_BUTTON_NAME_RETURN:
				action = new ReturnToStepStateAction();
				break;
			case ADButtonDefault.TREE_BUTTON_NAME_STARTRETURN:
				action = new StartReturnAction();
				break;
			default:
				action = new CommonAction(button.getName());
				break;
			}
			
			if (action != null) {
				action.setText(I18nUtil.getI18nMessage(button, "label"));
				action.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage(button.getImage())));
				mgr.add(action);
			}
			menu = mgr.createContextMenu(tree);
		}				
	}
	
	public void doSelect(MouseEvent e) {
		if (e.button == 3) {
			// ����Ҽ�
			TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
			if (treeItem != null) {
				IStructuredSelection selection = (IStructuredSelection) viewer.getSelection();
				Object obj = selection.getFirstElement();
				if (obj.equals(treeItem.getData())) {// ����һ�����ѡ�еĽڵ�
					this.currentSelectedItem = treeItem;
					if (obj instanceof StepState) {
						// ���ѡ�е���StepState�ڵ�
						// ��ʾ�Ҽ��˵�
						tree.setMenu(menu);
					} else {
						// ����ȡ���Ҽ��˵�
						tree.setMenu(null);
					}
				}
			} else {
				this.currentSelectedItem = null;
				tree.setMenu(null);
			}
			postEvent(null, GlcEvent.EVENT_RIGHTCLICK, GlcEvent.buildEventData(currentSelectedItem));	
		} else if (e.button == 1) {
			// ������
			TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
			if (treeItem != null) {
				IStructuredSelection selection = (IStructuredSelection) viewer.getSelection();
				Object obj = selection.getFirstElement();
				postEvent(null, GlcEvent.EVENT_CLICK, GlcEvent.buildEventData(treeItem));	
			}
		}	
	}
	
	public void reset() {
		if (startStepStateItem != null) {
			startStepStateItem.setBackground(
					Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
			startStepStateItem = null;
		}
		if (endStepStateItem != null) {
			endStepStateItem.setBackground(
					Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
			endStepStateItem = null;
		}
		if (returnStepStateItem != null) {
			returnStepStateItem.setBackground(
					Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
			returnStepStateItem = null;
		}
		currentSelectedItem = null;
	}
	
	/**
	 * �ж�ѡ�нڵ��Ƿ�Ϊ��ǰ���̽ڵ�
	 */
	public boolean isCurrentProcedure(TreeItem selectedItem) {
		if (currentFlowList != null && selectedItem != null) {
			Object[] nodes = currentFlowList.toArray();
			//�����ڶ���ĵ�ǰ���̽ڵ�
			Node node1 = (Node) nodes[nodes.length-2];
			if (selectedItem.getParentItem() != null) {
				Node selectedParentNode = (Node) selectedItem.getParentItem().getData();
				if (node1.equals(selectedParentNode)) {
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * �ж�ѡ�нڵ��Ƿ�Ϊ�������̽ڵ�(�Ƿ���������)
	 */
	public boolean isNormalNode(TreeItem selectedItem) {
		if (selectedItem != null && selectedItem.getParentItem() != null) {
			Node selectedParentNode = (Node) selectedItem.getParentItem().getData();
			if (!(selectedParentNode instanceof ReworkState)) {
				return true;
			}
		}
		return false;
	}

	IElementComparer flowComparer = new IElementComparer() {
		public boolean equals(Object a, Object b) {
			if (a instanceof List && b instanceof List) {
				return a.equals(b);
			} else if (a instanceof Lot && b instanceof Lot) {
				return a.equals(b);
			} else if (a instanceof Node && b instanceof Node) {
				Node af = (Node) a;
				Node bf = (Node) b;
				return af.getObjectRrn().equals(bf.getObjectRrn());
			} else if(a instanceof ProcessDefinition && b instanceof ProcessDefinition) {
				ProcessDefinition ap = (ProcessDefinition)a;
				ProcessDefinition bp = (ProcessDefinition)b;
				return ap.getObjectRrn().equals(bp.getObjectRrn());
			} else if(a instanceof FutureHold && b instanceof FutureHold) {
				FutureHold fa = (FutureHold)a;
				FutureHold fb = (FutureHold)b;
				return fa.getObjectRrn().equals(fb.getObjectRrn());
			}
			return false;
		}

		public int hashCode(Object element) {
			if (element instanceof Node) {
				Node af = (Node) element;
				if (af.getObjectRrn() == null) {  //af.getNodeId()
					return af.getObjectRrn().intValue();
				} else {
					return af.getObjectRrn().intValue();
				}
			}
			return element.hashCode();
		}
	};
	
	protected class LotFlowTreeManager extends TreeViewerManager {
		
		@Override
		protected ItemAdapterFactory createAdapterFactory() {
	        ItemAdapterFactory factory = new ItemAdapterFactory();
	        ItemAdapter itemAdapter = new LotItemAdapter();
	        try {
	        	factory.registerAdapter(List.class, itemAdapter);
	        	factory.registerAdapter(Lot.class, itemAdapter);
	        	factory.registerAdapter(Process.class, itemAdapter);
	        	factory.registerAdapter(Procedure.class, new LotProcedureAdapter());
	        	factory.registerAdapter(ReworkState.class, new LotReworkStateItemAdapter());
	        	factory.registerAdapter(ProcedureState.class, new LotProcedureStateItemAdapter());
		        factory.registerAdapter(StepState.class, new LotStepStateItemAdapter());
		        factory.registerAdapter(MoveToState.class, new MoveToStateItemAdapter());
		        factory.registerAdapter(IfState.class, new IfStateItemAdapter());
		        factory.registerAdapter(ElseState.class, new ElseStateItemAdapter());
		        factory.registerAdapter(EndIfState.class, new EndIfStateItemAdapter());
	        } catch (Exception e){
	        	logger.error(e.getMessage(), e);
	        }
	        return factory;
	    }
	}
	
	/**
	 * ����ѡ��ʼ�ڵ�Ķ���
	 * 1,��ʼ�ڵ����λ�ڽ����ڵ�֮ǰ
	 * 2,����ԭ����ʼ�ڵ�
	 * 3,���������startEnd,������Ϊnull,����������ɫ
	 * 4,���ÿ�ʼ�ڵ��ɫΪ��ɫ
	 */
	public class StartStepStateAction extends Action {
		@Override
		public void run() {			
			//��ʼ�ڵ����λ�ڽ����ڵ�֮ǰ
			//��Ϊ��Procedure����,tree.getItems()[0]ΪProcedure
			//����һ�㼴ΪStepState��
			if (endStepStateItem != null) {
				//TODO ������
				TreeItem parentItem = endStepStateItem.getParentItem();
				if (parentItem.indexOf(endStepStateItem) < parentItem.indexOf(currentSelectedItem)) {
					UI.showError(Message.getString("wip.prd_start_before_end"));
					return;
				}
			}
			
			//���ǰһ�趨��ʼ�ڵ���ɫ
			if(startStepStateItem != null) {
				startStepStateItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				startStepStateItem = null;
			}
			
			
			//���õ�ǰ��ʼ�ڵ���ɫ
			currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_GREEN));
			startStepStateItem = currentSelectedItem;
			if (ACTIONTYPE_START.equals(actionType)) {
				if(customizeStepStateItem != null) {
					customizeStepStateItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
					customizeStepStateItem = null;
				}
				customizeStepStateItem = currentSelectedItem;
			}
			
			Map<String, Object> map = GlcEvent.buildEventData(startStepStateItem);
			map.put(GlcEvent.PROPERTY_MENU_ACTION_NAME, ADButtonDefault.TREE_BUTTON_NAME_START);
			map.put(GlcEvent.PROPERTY_CUSTOM_COMPOSITE, this);
			postEvent(null, GlcEvent.EVENT_MENU_ACTION, map);	
		}
	}
	
	/**
	 * ����ѡ������ڵ�Ķ���
	 * 1,�����ڵ����λ�ڿ�ʼ�ڵ�֮��
	 * 2,����ԭ�������ڵ�
	 * 3,���������startEnd,������Ϊnull,����������ɫ
	 * 4,���ý����ڵ��ɫΪ��ɫ
	 */
	public class EndStepStateAction extends Action {
		@Override
		public void run() {			
			//�����ڵ����λ�ڿ�ʼ�ڵ�֮ǰ
			//��Ϊ��Procedure����,tree.getItems()[0]ΪProcedure
			//����һ�㼴ΪStepState��
			if (startStepStateItem != null) {
				//TODO ������
				TreeItem parentItem = startStepStateItem.getParentItem();
				if (parentItem.indexOf(startStepStateItem) > parentItem.indexOf(currentSelectedItem)) {
					UI.showError(Message.getString("wip.prd_start_before_end"));
					return;
				}
			}
			//���ǰһ�趨�����ڵ���ɫ
			if(endStepStateItem != null) {
				endStepStateItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				endStepStateItem = null;
			}
			
			//���õ�ǰ�����ڵ���ɫ
			currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_RED));
			endStepStateItem = currentSelectedItem;
			if (ACTIONTYPE_END.equals(actionType)) {
				if(customizeStepStateItem != null) {
					customizeStepStateItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
					customizeStepStateItem = null;
				}
				customizeStepStateItem = currentSelectedItem;
			}
			
			Map<String, Object> map = GlcEvent.buildEventData(endStepStateItem);
			map.put(GlcEvent.PROPERTY_MENU_ACTION_NAME, ADButtonDefault.TREE_BUTTON_NAME_END);
			map.put(GlcEvent.PROPERTY_CUSTOM_COMPOSITE, this);
			postEvent(null, GlcEvent.EVENT_MENU_ACTION, map);	
		}
	}
	
	/**
	 * ����ѡ��ʼ�����ڵ�Ķ���
	 * 1,��ʼ�ڵ�ͽ����ڵ�Ϊͬһ��StepState
	 * 2,����ԭ����ʼ�ڵ�ͽ����ڵ�
	 * 3,���������startEnd,������Ϊnull,����������ɫ
	 * 4,���ÿ�ʼ�ڵ��ɫΪ��ɫ
	 */
	public class StartEndStateAction extends Action {
		@Override
		public void run() {			
			//����ԭ����ʼ�ڵ�
			if (startStepStateItem != null) {
				startStepStateItem.setBackground(
						Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				startStepStateItem = null;
			}
			//����ԭ�������ڵ�
			if (endStepStateItem != null) {
				endStepStateItem.setBackground(
						Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				endStepStateItem = null;
			}
			
			//���õ�ǰ��ʼ�����ڵ���ɫ
			currentSelectedItem.setBackground(
					Display.getCurrent().getSystemColor(SWT.COLOR_DARK_YELLOW));
			startStepStateItem = currentSelectedItem;
			endStepStateItem = currentSelectedItem;
			
			Map<String, Object> map = GlcEvent.buildEventData(currentSelectedItem);
			map.put(GlcEvent.PROPERTY_MENU_ACTION_NAME, ADButtonDefault.TREE_BUTTON_NAME_STARTEND);
			map.put(GlcEvent.PROPERTY_CUSTOM_COMPOSITE, this);
			postEvent(null, GlcEvent.EVENT_MENU_ACTION, map);
		}
	}

	/**
	 * ���巵�ؽڵ�Ķ���
	 * 1,���������Return,������Ϊnull,����������ɫ
	 * 2,����Return�ڵ��ɫΪ��ɫ
	 */
	public class ReturnToStepStateAction extends Action {
		@Override
		public void run() {
			//���ǰһ�趨�����ڵ���ɫ
			if (returnStepStateItem != null) {
				returnStepStateItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				returnStepStateItem = null;
			}
			
			//���õ�ǰ�����ڵ���ɫ
			currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_BLUE));
			returnStepStateItem = currentSelectedItem;
			
			Map<String, Object> map = GlcEvent.buildEventData(returnStepStateItem);
			map.put(GlcEvent.PROPERTY_MENU_ACTION_NAME, ADButtonDefault.TREE_BUTTON_NAME_RETURN);
			map.put(GlcEvent.PROPERTY_CUSTOM_COMPOSITE, this);
			postEvent(null, GlcEvent.EVENT_MENU_ACTION, map);
		}
	}
	
	/**
	 * ����ѡ��ʼ�������صĶ���
	 * 1,��ʼ�ڵ�ͷ��ؽڵ�Ϊͬһ��StepState
	 * 2,����ԭ����ʼ�ڵ�ͷ��ؽڵ�
	 * 3,���������startReturn,������Ϊnull,����������ɫ
	 * 4,���ÿ�ʼ�ڵ��ɫΪ��ɫ
	 */
	public class StartReturnAction extends Action {
		@Override
		public void run() {
			//����ԭ����ʼ�ڵ�
			if (startStepStateItem != null) {
				startStepStateItem.setBackground(
						Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				startStepStateItem = null;
			}
			//����ԭ�����ؽڵ�
			if (returnStepStateItem != null) {
				returnStepStateItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
				returnStepStateItem = null;
			}

			//���õ�ǰ��ʼ���ؽڵ���ɫ
			currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_GREEN));
			startStepStateItem = currentSelectedItem;
			returnStepStateItem = currentSelectedItem;
			
			Map<String, Object> map = GlcEvent.buildEventData(currentSelectedItem);
			map.put(GlcEvent.PROPERTY_MENU_ACTION_NAME, ADButtonDefault.TREE_BUTTON_NAME_STARTRETURN);
			map.put(GlcEvent.PROPERTY_CUSTOM_COMPOSITE, this);
			postEvent(null, GlcEvent.EVENT_MENU_ACTION, map);
		}
	}
	
	public class CommonAction extends Action {
		String action;
		public CommonAction(String action) {
			this.action = action;
		}
		@Override
		public void run() {
			Map<String, Object> map = GlcEvent.buildEventData(currentSelectedItem);
			map.put(GlcEvent.PROPERTY_MENU_ACTION_NAME, action);
			map.put(GlcEvent.PROPERTY_CUSTOM_COMPOSITE, this);
			postEvent(null, GlcEvent.EVENT_MENU_ACTION, map);	
		}
	}

	public TreeItem getCurrentSelectedItem() {
		return currentSelectedItem;
	}

	public void setCurrentSelectedItem(TreeItem currentSelectedItem) {
		this.currentSelectedItem = currentSelectedItem;
	}

	public TreeItem getStartStepStateItem() {
		return startStepStateItem;
	}

	public void setStartStepStateItem(TreeItem startStepStateItem) {
		this.startStepStateItem = startStepStateItem;
	}

	public TreeItem getEndStepStateItem() {
		return endStepStateItem;
	}

	public void setEndStepStateItem(TreeItem endStepStateItem) {
		this.endStepStateItem = endStepStateItem;
	}

	public TreeItem getReturnStepStateItem() {
		return returnStepStateItem;
	}

	public void setReturnStepStateItem(TreeItem returnStepStateItem) {
		this.returnStepStateItem = returnStepStateItem;
	}

	public Label getLblId() {
		return lblId;
	}

	public void setLblId(Label lblId) {
		this.lblId = lblId;
	}
}
