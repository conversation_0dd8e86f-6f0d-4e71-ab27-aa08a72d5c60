package com.glory.mes.wip.custom;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Layout;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.mes.wip.custom.depend.MsgConsoleView;

public class ByEqpConsoleCustomComposite extends CustomCompsite {
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		
		Layout layout = parent.getLayout();
		if (layout instanceof GridLayout) {
			//((GridLayout) layout).marginRight = 0;
			//((GridLayout) layout).marginTop = 0;
			//((GridLayout) layout).marginLeft = 0;
		}

		parent.setLayout(layout);
		
		MsgConsoleView consoleView = new MsgConsoleView(parent, SWT.NONE);
		return consoleView;
	}

	@Override
	public void refresh() {
	}

	@Override
	public void setValue(Object value) {
	}

	@Override
	public Object getValue() {
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		
	}

	@Override
	public void preDestory() {
		super.preDestory();
	}

}
