package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.exception.WipExceptionBundle;

public class UnavailableEqpComposite extends CustomCompsite {

	protected List<Equipment> availableEqps;
	protected FormToolkit toolkit;
	protected Composite topComposite;
	protected Composite parent;
	protected ScrolledComposite sc;
	
	public static final String ATTRIBUTE_COMPOSITE_HEIGHT = "CompositeHeight";
	protected int heightHint = 250;
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		this.toolkit = toolkit;
		this.parent = parent;
		sc = new ScrolledComposite(parent, SWT.BORDER | SWT.V_SCROLL);
		sc.setLayout(new GridLayout(10, false));
		GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
		gridData.heightHint = heightHint == 0 ? 250 : heightHint;
		sc.setLayoutData(gridData);
		sc.setExpandHorizontal(true);
		sc.setExpandVertical(true);
		sc.setMinWidth(300);
		if (availableEqps != null && availableEqps.size() > 0) {
			sc.setMinHeight(availableEqps.size() * 100);
		} else {
			sc.setMinHeight(500);
		}
		return parent;
	}
	
	public Composite createConent() {
		topComposite = new Composite(sc, SWT.NONE);
		topComposite.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));// White color
		GridLayout gd = new GridLayout(1, true);
		topComposite.setLayout(gd);
		GridData data = new GridData(GridData.FILL_VERTICAL);
		topComposite.setLayoutData(data);
		
		List<Equipment> inValiableEqp = new ArrayList<Equipment>();
		if (availableEqps!=null && availableEqps.size() > 0) {
			for (int i = 0; i < availableEqps.size(); i++) {
				Equipment equ = availableEqps.get(i);
				if (!equ.getIsAvailable()) {
					inValiableEqp.add(equ);
				}
			}
		}
		
		if (CollectionUtils.isNotEmpty(inValiableEqp)) {
			for (Equipment equipment : inValiableEqp) {
				Label lab1 = toolkit.createLabel(topComposite, equipment.getEquipmentId() + ":");
				lab1.setForeground(Display.getCurrent().getSystemColor(SWT.COLOR_RED));
				lab1.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_MENU_ITEM));
				Label lab2 = toolkit.createLabel(topComposite, Message.formatString(equipment.getMessage()), SWT.READ_ONLY);
				lab2.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
			}
		} else {
			Label lab1 = toolkit.createLabel(topComposite, Message.getString(WipExceptionBundle.bundle.NoAbnormalEqpInfo()));
			lab1.setForeground(Display.getCurrent().getSystemColor(SWT.COLOR_DARK_GREEN));
			lab1.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_MENU_ITEM));
		}
		sc.setContent(topComposite);
		return parent;
	}
	
	@Override
	public void refresh() {
		if (topComposite != null && !topComposite.isDisposed()) {
			topComposite.dispose();
			topComposite = null;
		}
		createConent();
	}

	@Override
	public void setValue(Object value) {
		this.availableEqps = (List<Equipment>) value;
	}

	@Override
	public Object getValue() {
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		for (ADFormAttribute formAttribute : attributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_COMPOSITE_HEIGHT:
				heightHint = formAttribute.getIntValue();
				break;
			}
		}
	}

}
