package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.model.Lot;

public class LotStepAttributeComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(LotStepAttributeComposite.class);
	
	protected FormToolkit toolkit;
	protected Composite composite;
	protected Composite parent;
	protected Lot lot;
	private LotStepAttributeForm attributeForm;
	
	protected List<StepAttribute> stepAttributes = new ArrayList<StepAttribute>();
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		this.toolkit = toolkit;
		this.parent = parent;
		return createConent();
	}
	
	public Composite createConent() {
		try {
			composite = new Composite(parent, SWT.NULL);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			composite.setLayoutData(gd);
			
			if (stepAttributes != null && stepAttributes.size() > 0) {
				composite.setLayoutData(gd);
				
				Equipment eqp = null;
			    attributeForm = new LotStepAttributeForm(composite, SWT.NONE, null, 
						lot, stepAttributes, eqp);
				attributeForm.createForm();
				attributeForm.setLayoutData(gd);
			}
		} catch (Exception e) {
			logger.error("ShowStepOperationForm : addFields()", e);
		}
		return parent;
	}
	
	@Override
	public void refresh() {
		if (composite != null && !composite.isDisposed()) {
			composite.dispose();
			composite = null;
		}
		createConent();
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}
	
	public boolean validate() {
		return !attributeForm.saveToObject();
	}
	
	@Override
	public void setValue(Object value) {
		this.stepAttributes = (List<StepAttribute>) value;
	}

	@Override
	public Object getValue() {
		return attributeForm.getAttributeValues();
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		
	}

}
