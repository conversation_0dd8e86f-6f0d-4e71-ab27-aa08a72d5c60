package com.glory.mes.wip.custom;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.CollectionUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.custom.depend.WaitingLotTableManager;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;

@Deprecated
public class ByEqpWaittingLotsCustomComposite extends CustomCompsite {
	
	private static final String TABLE_NAME_ATTR = "TableName";
	private static final String AUTOSIZE_ATTR = "AutoSize";
	private static final String TABLE_NAME = "WIPByEqpWaitingLot";
	
	private String tableName;
	
	protected boolean indexFlag = false;
	protected boolean autoSizeFlag = false;
	protected WaitingLotTableManager tableManager;
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite waitComp = toolkit.createComposite(parent, SWT.NONE);
		configureBody(waitComp);

		ADTable adTable = getADManger().getADTable(Env.getOrgRrn(), getTableName());
		tableManager = new WaitingLotTableManager(adTable, true);
		tableManager.setIndexFlag(indexFlag);
		tableManager.setAutoSizeFlag(autoSizeFlag);
		tableManager.newViewer(waitComp);
				
		tableManager.addDoubleClickListener(getDoubleClickListener());
		tableManager.addSelectionChangedListener(getSelectChangeListener());
		attachToolTip();
		return waitComp;
	}
	
	protected IMouseAction getDoubleClickListener() {
		return new AbstractMouseSelectionAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				doubleClick();
		    }
		};
	}

	protected ISelectionChangedListener getSelectChangeListener() {
		return new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				selectedChanged(event);
			}
		};
	};
	
	public void doubleClick() {
		Object obj = tableManager.getSelectedObject();
		field.postEvent(GlcEvent.EVENT_DOUBLE_CLICK, GlcEvent.buildEventData(obj));
	}

	public void selectedChanged(SelectionChangedEvent event) {
		StructuredSelection selection = (StructuredSelection) event.getSelection();
		Object obj = selection.getFirstElement();
		field.postEvent(GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(obj));
	}
	
	@Override
	public void refresh() {
		tableManager.refresh();
	}

	@Override
	public void setValue(Object value) {
		if (value instanceof List) {
			tableManager.setInput((List<? extends Object>) value);
		}
	}

	public WaitingLotTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(WaitingLotTableManager tableManager) {
		this.tableManager = tableManager;
	}

	@Override
	public Object getValue() {
		return null;
	}
	
	public List<Lot> getTrackInLots() {
		List<Object> objects = Lists.newArrayList(tableManager.getCheckedObject());
		if (CollectionUtils.isEmpty(objects)) {
			return null;
		}
		return objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
	}
	
	public void setCheckedLots(List<Lot> lots) {
		if (CollectionUtils.isNotEmpty(lots)) {
			if (CollectionUtils.isNotEmpty(tableManager.getCheckedObject())) {
				for(Object object : tableManager.getCheckedObject()) {
					((CheckBoxTableViewerManager)tableManager.getTableManager()).unCheckObject(object);
				}
			}		
			lots.forEach(l -> tableManager.setCheckedObject(l));
		}
	}
	

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		if (CollectionUtils.isNotEmpty(attributes)) {
			for (ADFormAttribute formAttribute : attributes) {
				switch (formAttribute.getAttributeName()) {
				case TABLE_NAME_ATTR:
					if (!StringUtil.isEmpty(formAttribute.getStringValue())) {
						setTableName(formAttribute.getStringValue());
					}
					break;
				case AUTOSIZE_ATTR:
					autoSizeFlag = formAttribute.getBooleanValue();
					break;
				}
			}	
		}
	}

	@Override
	public void preDestory() {
		super.preDestory();
	}

	public String getTableName() {
		return StringUtil.isEmpty(tableName) ? TABLE_NAME : tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	private void attachToolTip() {
		ByEqpLotListTableToolTip toolTip = new ByEqpLotListTableToolTip(tableManager);
		toolTip.setPopupDelay(500);
		toolTip.activate();
		toolTip.setShift(new Point(10, 10));
	}

}
