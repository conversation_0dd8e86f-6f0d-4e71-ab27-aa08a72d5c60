package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.lot.sorting.SortModel;

public class SortingCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(SortingCustomComposite.class);

	public static final String SORTING_TABLE_NAME = "WIPSortingModel";
	public static final String ATTRIBUTE_SHOW_SORT_FLAG = "ShowSortFlag";
	public static final String ATTRIBUTE_IS_SORTING_FLAG = "IsSortingFlag";
	public static final String ATTRIBUTE_NO_SHOW_EQUIPMENT_FLAG = "NoShowEquipmentFlag";
	public static final String ATTRIBUTE_ACTION_TYPE = "ActionType";
	
	public static final String FIELD_EQUIPMENTID = "equipmentId";
	public static final String FIELD_FROMPORTID = "fromPortId";
	public static final String FIELD_TOPORTID = "toPortId";
	public static final String FIELD_ACTIONTYPE = "actionType";
	public static final String FIELD_SORT = "sort";
	
	protected EntityForm sorterForm;
	
	/**
	 * ��ʾSortComposite����
	 */
	protected boolean showSortFlag = false;
	
	/**
	 * ����SortĬ��ֵ
	 */
	protected boolean isSortingFlag = false;
	
	protected boolean showPortFlag = true;

	protected boolean noShowEquipmentFlag = false;
	protected String adTable;
	protected String actionType;
	protected RadioField radioField;
	protected BooleanField booleanField;
	
	public static final String ACTIONTYPE_SPLIT = "Split";
	public static final String ACTIONTYPE_MERGE = "Merge";
	public static final String ACTIONTYPE_INTERNAL = "Internal";
	public static final String ACTIONTYPE_EXTERNAL = "External";

	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		if (showSortFlag) {
			try {
				ADManager adManager = Framework.getService(ADManager.class);
				ADTable sorterTable = null;
				try {
					sorterTable = adManager.getADTable(Env.getOrgRrn(), getAdTable());
				} catch (Exception e) {
					logger.error("SortingCustomComposite createForm error:", e);
				}
				if (sorterTable == null) {
					sorterTable = createDefaultTable();
				}
				if(actionType != null && actionType.trim().length() > 0) {
					ADField adField = createField();
					sorterTable.getFields().add(0, adField);
				}
				for (ADField adField : sorterTable.getFields()) {
					if (FIELD_EQUIPMENTID.equals(adField.getName()) 
							|| FIELD_ACTIONTYPE.equals(adField.getName()) 
							|| FIELD_FROMPORTID.equals(adField.getName()) 
							|| FIELD_TOPORTID.equals(adField.getName())) {
						if (noShowEquipmentFlag) {
							adField.setIsDisplay(false);
						} else {
							adField.setIsDisplay(true);
						}
					}
					if (FIELD_FROMPORTID.equals(adField.getName()) 
							|| FIELD_TOPORTID.equals(adField.getName())) {
						if (showPortFlag) {
							adField.setIsDisplay(true);
						} else {
							adField.setIsDisplay(false);
						}
					}
				}
				
				SortModel sortModel = new SortModel();
				sortModel.setSort(isSortingFlag);
				parent.setBackgroundMode(SWT.INHERIT_FORCE);
				sorterForm = new EntityForm(parent, SWT.NULL, sortModel, sorterTable, null);
				if(actionType != null && actionType.trim().length() > 0) {
					radioField =  (RadioField) sorterForm.getFields().get(FIELD_ACTIONTYPE);
					if(radioField != null) {
						radioField.addValueChangeListener( new IValueChangeListener() {
							@Override
							public void valueChanged(Object obj, Object obj1) {
								if(ACTIONTYPE_INTERNAL.equals(obj1)) {
									sorterForm.saveToObject();
									SortModel model = (SortModel) sorterForm.getObject();
									model.setSort(false);
									model.setEquipmentId("");
									model.setFromPortId("");
									model.setToPortId("");
									sorterForm.setObject(model); 
									sorterForm.loadFromObject();
									
									sorterForm.getFields().values().stream().filter(f ->  !FIELD_ACTIONTYPE.equals(f.getId())).forEach(f -> f.setEnabled(false));
								}else {
									sorterForm.saveToObject();
									SortModel model = (SortModel) sorterForm.getObject();
									model.setSort(true);
									sorterForm.setObject(model);
									sorterForm.loadFromObject();
									
									sorterForm.getFields().values().stream().filter(f -> !FIELD_ACTIONTYPE.equals(f.getId())).forEach(f -> f.setEnabled(true));
								}
							}
						});
					}
				}
				
				booleanField =  (BooleanField) sorterForm.getFields().get(FIELD_SORT);
				if(radioField == null && booleanField != null) {
					booleanField.addValueChangeListener( new IValueChangeListener() {
						@Override
						public void valueChanged(Object obj, Object obj1) {
							if(Boolean.FALSE.equals(obj1)) {
								sorterForm.saveToObject();
								SortModel model = (SortModel) sorterForm.getObject();
								sorterForm.setObject(model);
								sorterForm.loadFromObject();
								sorterForm.getFields().values().stream().filter(f -> !FIELD_SORT.equals(f.getId())).forEach(f -> f.setEnabled(false));
							}else {
								sorterForm.saveToObject();
								SortModel model = (SortModel) sorterForm.getObject();
								sorterForm.setObject(model);
								sorterForm.loadFromObject();
								
								sorterForm.getFields().values().stream().filter(f -> !FIELD_SORT.equals(f.getId())).forEach(f -> f.setEnabled(true));
							}
						}
					});
				}
				return sorterForm;
			} catch (Exception e) {
				logger.error("SortingCustomComposite createForm error:", e);
				e.printStackTrace();
			}
		}
		return parent;
	}
	
	@Override
	public void refresh() {
		sorterForm.loadFromObject();
		sorterForm.refresh();
	}

	@Override
	public void setValue(Object value) {
		sorterForm.setObject(value);
	}
	
	public void clear() {
		sorterForm.setObject(new SortModel());
		for (IField f : sorterForm.getFields().values()) {
			f.setValue(null);
			f.refresh();
		}
	}

	@Override
	public Object getValue() {
		sorterForm.saveToObject();
		return sorterForm.getObject();
	}
	
	public EntityForm getForm() {
		return sorterForm;
	}

	public RadioField getRadio() {
		return radioField;
	}
	
	public String getAdTable() {
		if (!StringUtil.isEmpty(adTable)) {
			return adTable;
		}
		return SORTING_TABLE_NAME;
	}

	public void setAdTable(String adTable) {
		this.adTable = adTable;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		if (formAttributes != null) {
			for (ADFormAttribute formAttribute : formAttributes) {
				switch (formAttribute.getAttributeName()) {
				case ATTRIBUTE_SHOW_SORT_FLAG:
					showSortFlag = formAttribute.getBooleanValue();
					break;
				case ATTRIBUTE_IS_SORTING_FLAG:
					isSortingFlag = formAttribute.getBooleanValue();
					break;
				
				case ATTRIBUTE_NO_SHOW_EQUIPMENT_FLAG:
					noShowEquipmentFlag = formAttribute.getBooleanValue();
					break;
				case ATTRIBUTE_ACTION_TYPE:
					actionType = formAttribute.getStringValue();
					break;
				}
			}	
		}
	}
	
	

	@Override
	public void preDestory() {
		super.preDestory();
	}

	public ADField createField() {
		ADField adField = new ADField();
		adField.setName(FIELD_ACTIONTYPE);
		adField.setIsDisplay(true);
		adField.setIsSameline(true);
		adField.setDisplayType("radio");
		if(ACTIONTYPE_SPLIT.equals(actionType)) {
			adField.setRefListName("SplitType");
			adField.setLabel("Split Type");
			adField.setLabel_zh("��������");
		}else if(ACTIONTYPE_MERGE.equals(actionType)) {
			adField.setRefListName("MergeType");
			adField.setLabel("Merge Type");
			adField.setLabel_zh("��������");
		}
		return adField;
	}

	public ADTable createDefaultTable() {
		ADTable defaultTable = new ADTable();
		List<ADField> adFields = new ArrayList<ADField>();
		
		ADField adField = new ADField();
		adField.setName("row");
		adField.setIsMain(true);
		adField.setIsDisplay(true);
		adField.setLabel("row");
		adField.setLabel_zh("��");
		adField.setDisplayLength(2L);
		adFields.add(adField);

		adField = new ADField();
		adField.setName("columnName");
		adField.setIsMain(true);
		adField.setIsDisplay(true);
		adField.setLabel("ColumnName");
		adField.setLabel_zh("����");
		adField.setDisplayLength(4L);
		adFields.add(adField);
		
		adField = new ADField();
		adField.setName("message");
		adField.setIsMain(true);
		adField.setIsDisplay(true);
		adField.setLabel("Eerror Message");
		adField.setLabel_zh("������Ϣ");
		adFields.add(adField);

		defaultTable.setFields(adFields);
		return defaultTable;
	}
	
	public boolean isShowSortFlag() {
		return showSortFlag;
	}

	public void setShowSortFlag(boolean showSortFlag) {
		this.showSortFlag = showSortFlag;
	}

	public boolean isSortingFlag() {
		return isSortingFlag;
	}

	public void setSortingFlag(boolean isSortingFlag) {
		this.isSortingFlag = isSortingFlag;
	}

	public boolean isNoShowEquipmentFlag() {
		return noShowEquipmentFlag;
	}

	public void setNoShowEquipmentFlag(boolean noShowEquipmentFlag) {
		this.noShowEquipmentFlag = noShowEquipmentFlag;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}
	
	public boolean isShowPortFlag() {
		return showPortFlag;
	}

	public void setShowPortFlag(boolean showPortFlag) {
		this.showPortFlag = showPortFlag;
	}

}
