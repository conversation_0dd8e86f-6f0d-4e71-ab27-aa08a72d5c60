package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.custom.CTabItem;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.comp.ComponentComposite.DiffType;
import com.glory.mes.wip.comp.ComponentComposite.OccupationPolicy;
import com.glory.mes.wip.custom.ComponentCustomComposite.CarrierFieldType;
import com.glory.mes.wip.model.ComponentUnit;
import com.google.common.collect.Lists;

public class ComponentAssignCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(ComponentAssignCustomComposite.class);

	public static final String ID_SOURCE_COMPONENT = "sourceComponent";
	public static final String ID_TARGET_COMPONENT = "targetComponent";
	public static final String ID_SORT_INFO = "sortInfo";

	public static final String ATTRIBUTE_SOURCE_SHOW_CHEKBOX = "SourceShowCheckBox";
	public static final String ATTRIBUTE_SOURCE_SHOW_CARRIER = "SourceShowCarrier";
	public static final String ATTRIBUTE_SOURCE_EDIT_CARRIER = "SourceEditCarrier";
	public static final String ATTRIBUTE_SOURCE_SHOW_LOT = "SourceShowLot";
	public static final String ATTRIBUTE_SOURCE_IS_ASC = "SourceIsAsc";
	public static final String ATTRIBUTE_SOURCE_CARRIER_VALIDATE = "SourceCarrierValidate";
	public static final String ATTRIBUTE_SOURCE_CARRIER_SIZE = "SourceCarrierSize";
	public static final String ATTRIBUTE_SOURCE_TABLE_NAME = "SourceTableName";
	public static final String ATTRIBUTE_SOURCE_EXTEND_TABLE_NAME = "SourceExtendTableName";
	public static final String ATTRIBUTE_SOURCE_ITEM_ADAPTER = "SourceItemAdapter";
	public static final String ATTRIBUTE_SOURCE_IS_MULTI = "SourceIsMulti";

	public static final String ATTRIBUTE_TARGET_SHOW_CHEKBOX = "TargetShowCheckBox";
	public static final String ATTRIBUTE_TARGET_SHOW_CARRIER = "TargetShowCarrier";
	public static final String ATTRIBUTE_TARGET_SHOW_LOT = "TargetShowLot";
	public static final String ATTRIBUTE_TARGET_IS_ASC = "TargetIsAsc";
	public static final String ATTRIBUTE_TARGET_CARRIER_VALIDATE = "TargetCarrierValidate";
	public static final String ATTRIBUTE_TARGET_CARRIER_SIZE = "TargetCarrierSize";
	public static final String ATTRIBUTE_TARGET_TABLE_NAME = "TargetTableName";
	public static final String ATTRIBUTE_TARGET_EXTEND_TABLE_NAME = "TargetExtendTableName";
	public static final String ATTRIBUTE_TARGET_ITEM_ADAPTER = "TargetItemAdapter";
	public static final String ATTRIBUTE_TARGET_CARRIER_FIELD_TYPE = "TargetCarrierFieldType";
	public static final String ATTRIBUTE_TARGET_IS_MULTI = "TargetIsMulti";
	
	public static final String ATTRIBUTE_SHOW_SORT_FLAG = "ShowSortFlag";
	public static final String ATTRIBUTE_SORTING_FLAG = "SortingFlag";
	
	public static final String ATTRIBUTE_SHOW_BTNGO = "NotShowBtnGo";
	public static final String ATTRIBUTE_FORM_HEIGHT = "FormHeight";
	public static final String ATTRIBUTE_NO_SHOW_EQUIPMENT_FLAG = "NoShowEquipmentFlag";
	public static final String ATTRIBUTE_ACTION_TYPE = "ActionType";
	public static final String ATTRIBUTE_SORTING_TABLE_NAME = "SortingTableName";
	
	protected boolean sourceShowCarrierFlag;
	protected boolean sourceEditCarrierFlag = false;
	protected boolean sourceShowLotFlag;
	protected boolean sourceAscFlag;
	protected boolean sourceCheckFlag;
	protected boolean sourceCheckCarrierVaildFlag = false;
	protected Integer sourceCarrierSize;
	protected String sourceTableName;
	protected String sourceExtendTableName;
	protected String sourceItemAdapter;
	protected boolean sourceIsMulti = false;

	protected boolean targetShowCarrierFlag;
	protected boolean targetShowLotFlag;
	protected boolean targetAscFlag;
	protected boolean targetCheckFlag;
	protected boolean targetCheckCarrierVaildFlag = false;
	protected Integer targetCarrierSize;
	protected String targetTableName;
	protected String targetExtendTableName;
	protected String targetItemAdapter;
	protected String targetCarrierFieldType;
	protected boolean targetIsMulti = false;
	
	protected Integer formHeight;
	protected String actionType;
	protected String sortingTableName;

	protected boolean showSortFlag = false;
	protected boolean sortingFlag = false;
	protected boolean notShowBtnGo = false;
	protected boolean noShowEquipmentFlag = false;

	public CTabFolder sourceTabFolder;
	public CTabFolder targetTabFolder;
	private FormToolkit toolkit;
	
	public ComponentCustomComposite sourceComponentComposite;
	public ComponentCustomComposite targetComponentComposite;
	
	/**
	 * isMultiΪtrueʱ��Ч,��Ϊ�����÷�,�����⴦��,��׼����²�Ӧ��ʹ�ô˷���
	 */
	public List<ComponentCustomComposite> multiSourceComponentComposite;
	public List<ComponentCustomComposite> multiTargetComponentComposite;
	public List<Combo> multiSourcePort = Lists.newArrayList();
	public List<Combo> multiTargetPort = Lists.newArrayList();

	public SortingCustomComposite sortingCustomComposite;
	protected List<ADFormAttribute> fieldFormAttributes;
	
	public Button btnGo;
	public Button btnBack;
	public Button btnMove;
	public Button btnSourceAdd;
	public Button btnSourceDelete;
	public Button btnTargetAdd;
	public Button btnTargetDelete;
	
	public EntityForm sourceExtendForm;
	public EntityForm targetExtendForm;

	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		try {
			GridLayout layout = new GridLayout(1, false);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			parent.setLayout(layout);
			parent.setLayoutData(new GridData(GridData.FILL_BOTH));
			parent.setBackgroundMode(SWT.INHERIT_FORCE);
			
			createComponentForm(toolkit, parent);
			
			Composite sotingComposite = createSortingComponent(toolkit, parent, sortingTableName, actionType);
			sortingCustomComposite = (SortingCustomComposite)sotingComposite.getData();
		} catch (Exception e) {
			logger.error("ComponentAssignCustomComposite createForm error:", e);
		}
		return parent;
	}
	
	public void createComponentForm(FormToolkit toolkit, Composite parent) {
		Composite componentForm = toolkit.createComposite(parent);
		GridLayout layout = new GridLayout(3, false);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		componentForm.setLayout(layout);
		componentForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		parent.setBackgroundMode(SWT.INHERIT_FORCE);
		
		if (sourceIsMulti || targetIsMulti) {
			createSourceTabFolder(toolkit, componentForm);
			createMiddleComponent(toolkit, componentForm);
			createTargetTabFolder(toolkit, componentForm);
			if (!sourceIsMulti) {
				sourceComponentComposite = multiSourceComponentComposite.get(0);
			}
			if (!targetIsMulti) {
				targetComponentComposite = multiTargetComponentComposite.get(0);
			}
		} else {
			Composite sourceComp = createSourceComponent(toolkit, componentForm, null);
			sourceComponentComposite = (ComponentCustomComposite)sourceComp.getData();
			createMiddleComponent(toolkit, componentForm);
			Composite targetComp = createTargetComponent(toolkit, componentForm, null);
			targetComponentComposite = (ComponentCustomComposite)targetComp.getData();
			addTargetCarrierIdEnterPressed(targetComponentComposite, null);
		}
		
		if (!StringUtil.isEmpty(sourceExtendTableName) || !StringUtil.isEmpty(targetExtendTableName)) {
			createExtendForm(toolkit, componentForm);
		}
	}

	protected void createSourceTabFolder(FormToolkit toolkit, Composite parent) {
		sourceTabFolder = new CTabFolder(parent, SWT.FLAT | SWT.TOP);
		toolkit.adapt(sourceTabFolder, true, true);
		GridData gd = new GridData(GridData.FILL_BOTH);
		if (formHeight != null) {
			gd.heightHint = formHeight;
		}
		gd.widthHint = 80;
		sourceTabFolder.setLayoutData(gd);
		this.toolkit = toolkit;
		
		addSourceTabItem(toolkit, sourceTabFolder, 0);
	}
	
	protected void addSourceTabItem(FormToolkit toolkit, CTabFolder tabFolder, int seqNo) {
		CTabItem item = new CTabItem(tabFolder, SWT.BORDER);
		item.setText("Tab" + seqNo);
		
		Composite sourceComp = createSourceComponent(toolkit, tabFolder, seqNo);
		item.setControl(sourceComp);
		
		ComponentCustomComposite sourceComponentComposite = (ComponentCustomComposite)sourceComp.getData();
		if (multiSourceComponentComposite != null) {
			multiSourceComponentComposite.add(sourceComponentComposite);
		} else {
			multiSourceComponentComposite = Lists.newArrayList(sourceComponentComposite);
		}
		
		sourceTabFolder.redraw();
		sourceTabFolder.setSelection(seqNo);
	}

	protected void removeSourceTabItem(int i) {
		if (i == 0) {
			return;
		}
		
		ComponentCustomComposite componentCustomComposite = multiSourceComponentComposite.get(i);
		
		// �����ǰ�ؾ����Ƴ���component
		List<ComponentUnit> deleteComponents = componentCustomComposite.getComponentComposite().getComponentsDiff().get(DiffType.REMOVE);
		if (!CollectionUtils.isEmpty(deleteComponents)) {
			String message = Message.getString("wip.target_component_exist_move_source_component") + Message.getString(ExceptionBundle.bundle.CommonConfirmDelete());
			if (!UI.showConfirm(message)) {
				return;
			}
			for (ComponentUnit deleteComponent : deleteComponents) {
				getTargetComponentComposite().getTableManager().setCheckedObject(deleteComponent);
			}
			targetTabFolder.setSelection(i);

			backAdapter();
		}
		
		CTabItem item = sourceTabFolder.getItem(i);
		item.dispose();
		
		multiSourceComponentComposite.remove(componentCustomComposite);
		multiSourcePort.remove(multiSourcePort.get(i));
		sourceTabFolder.redraw();
	}
	
	protected void createTargetTabFolder(FormToolkit toolkit, Composite parent) {
		targetTabFolder = new CTabFolder(parent, SWT.FLAT | SWT.TOP);
		toolkit.adapt(targetTabFolder, true, true);
		GridData gd = new GridData(GridData.FILL_BOTH);
		if (formHeight != null) {
			gd.heightHint = formHeight;
		}
		gd.widthHint = 80;
		targetTabFolder.setLayoutData(gd);
		
		addTargetTabItem(toolkit, targetTabFolder, 0);
		targetTabFolder.setSelection(0);
	}
	
	protected void addTargetCarrierIdEnterPressed(ComponentCustomComposite targetComponentComposite, CTabItem item) {
		if (targetComponentComposite.txtCarrierId != null) {
			targetComponentComposite.txtCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String carrierId = ((Text) event.widget).getText().trim();
						if (!StringUtil.isEmpty(carrierId) && item != null) {
							item.setText(carrierId);	
						}
						// ����Ƭ����ת�ƻ�ȥ
						List<ComponentUnit> addComponents = getTargetComponentComposite().getComponentComposite().getComponentsDiff().get(DiffType.ADD);
						if (CollectionUtils.isNotEmpty(addComponents)) {
							for (ComponentUnit addComponent : addComponents) {
								getTargetComponentComposite().getComponentComposite().getTableManager().setCheckedObject(addComponent);
							}
							backAdapter();
						}
						getTargetComponentComposite().getComponentsByCarrier(carrierId, true);
					}
				}
			});
		}
	}
	
	protected void addTargetTabItem(FormToolkit toolkit, CTabFolder tabFolder, int seqNo) {
		CTabItem item = new CTabItem(tabFolder, SWT.BORDER);
		item.setText("Tab" + seqNo);
		
		Composite targetComp = createTargetComponent(toolkit, tabFolder, seqNo);
		item.setControl(targetComp);
		ComponentCustomComposite targetComponentComposite = (ComponentCustomComposite)targetComp.getData();
		
		addTargetCarrierIdEnterPressed(targetComponentComposite, item);
		if(getSortingCustomComposite() != null && getSortingCustomComposite().getRadio() != null) {
			RadioField radioField = getSortingCustomComposite().getRadio();
			if (SortingCustomComposite.ACTIONTYPE_INTERNAL.equals(radioField.getValue())) {
				targetComponentComposite.txtCarrierId.setEnabled(false);
			} else {
				targetComponentComposite.txtCarrierId.setEnabled(true);
			}
		}
		
		if (multiTargetComponentComposite != null) {
			multiTargetComponentComposite.add(targetComponentComposite);
		} else {
			multiTargetComponentComposite = Lists.newArrayList(targetComponentComposite);
		}
		
		targetTabFolder.redraw();
	}

	protected void removeTargetTabItem(int i) {
		if (i == 0) {
			return;
		}
		
		// ���Ŀ���ؾ��е�component
		ComponentCustomComposite componentCustomComposite = multiTargetComponentComposite.get(i);
		List<ComponentUnit> addComponents = componentCustomComposite.getComponentComposite().getComponentsDiff().get(DiffType.ADD);
		if (!CollectionUtils.isEmpty(addComponents)) {
			String message = Message.getString("wip.target_component_exist_move_source_component") + Message.getString(ExceptionBundle.bundle.CommonConfirmDelete());
			if (!UI.showConfirm(message)) {
				return;
			}
			for (ComponentUnit addComponent : addComponents) {
				getTargetComponentComposite().getTableManager().setCheckedObject(addComponent);
			}
			targetTabFolder.setSelection(i);
			backAdapter();
		}
		
		CTabItem item = targetTabFolder.getItem(i);
		item.dispose();
		
		multiTargetComponentComposite.remove(componentCustomComposite);
		multiTargetPort.remove(multiTargetPort.get(i));
		targetTabFolder.redraw();
	}
	
	protected Composite createSourceComponent(FormToolkit toolkit, Composite parent, Integer seqNo) {
		Composite sourceComp = new Composite(parent, SWT.NULL);
		GridData gd = new GridData(GridData.FILL_BOTH);
		if (formHeight != null) {
			gd.heightHint = formHeight;
		}
		gd.widthHint = 100;
		sourceComp.setLayoutData(gd);

		ComponentCustomComposite sourceComponentComposite = new ComponentCustomComposite();
		if (StringUtil.isEmpty(id)) {
			sourceComponentComposite.setId(ID_SOURCE_COMPONENT);
		} else {
			sourceComponentComposite.setId(id + "." + ID_SOURCE_COMPONENT);
		}
		if (seqNo != null) {
			sourceComponentComposite.setId(sourceComponentComposite.getId() + seqNo);
		}
		sourceComponentComposite.setShowCarrierFlag(sourceShowCarrierFlag);
		sourceComponentComposite.setEditCarrierFlag(sourceEditCarrierFlag);
		sourceComponentComposite.setShowLotFlag(sourceShowLotFlag);
		sourceComponentComposite.setAscFlag(sourceAscFlag);
		sourceComponentComposite.setCheckFlag(sourceCheckFlag);
		sourceComponentComposite.setCheckCarrierVaildFlag(sourceCheckCarrierVaildFlag);
		sourceComponentComposite.setCarrierSize(sourceCarrierSize);
		sourceComponentComposite.setField(this.getField());
		sourceComponentComposite.setTableName(sourceTableName);
		sourceComponentComposite.setItemAdapter(sourceItemAdapter);
		sourceComponentComposite.setLblCarrier(Message.getString("wip.source_carrier_id"));
		sourceComponentComposite.createForm(toolkit, sourceComp);
		sourceComp.setData(sourceComponentComposite);
		
		if (showSortFlag && (sourceIsMulti || targetIsMulti)) {
			// ��ʾSourcePort
			Composite portComposite = new Composite(sourceComp, SWT.NONE);
			gd = new GridData(GridData.FILL_HORIZONTAL);
			portComposite.setLayout(new GridLayout(2, false));
			portComposite.setLayoutData(gd);
			Label lblSourcePort = new Label(portComposite, SWT.NONE);
			lblSourcePort.setText(Message.getString("ras.source_port"));
			
			Combo combo = new Combo(portComposite, SWT.NONE | SWT.READ_ONLY);
			gd = new GridData(GridData.FILL_VERTICAL);
			gd.widthHint = 132;
			combo.setLayoutData(gd);
			
			//�鿴ǰ����б��Ƿ����ֵ��ֱ������
			if (CollectionUtils.isNotEmpty(multiSourcePort) && multiSourcePort.get(0).getItemCount() > 0) {
				for (String item : multiSourcePort.get(0).getItems()) {
					combo.add(item);
				}
			}
			multiSourcePort.add(combo);
		}
		
		return sourceComp;
	}
	
	protected void createMiddleComponent(FormToolkit toolkit, Composite parent) {
		Composite centerComposite = new Composite(parent, SWT.NONE);
	    final GridLayout buttonLayout = new GridLayout();
	    buttonLayout.marginWidth = 10;
	    buttonLayout.marginHeight = 10;
	    centerComposite.setLayout(buttonLayout);
		GridData buttonGd = new GridData(SWT.CENTER, SWT.CENTER, false, false);
		centerComposite.setLayoutData(buttonGd);
		
		if (sourceIsMulti) {
			btnSourceAdd = new Button(centerComposite, SWT.PUSH);
			btnSourceAdd.setText("<+");
			btnSourceAdd.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
			btnSourceAdd.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					addTabAdapter(true);
				}
			});

			btnSourceDelete = new Button(centerComposite, SWT.PUSH);
			btnSourceDelete.setText("<x");
			btnSourceDelete.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
			btnSourceDelete.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					deleteTabAdapter(true);
				}
			});
		}
		if (targetIsMulti) {
			btnTargetAdd = new Button(centerComposite, SWT.PUSH);
			btnTargetAdd.setText("+>");
			btnTargetAdd.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
			btnTargetAdd.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					addTabAdapter(false);
				}
			});
			
			btnTargetDelete = new Button(centerComposite, SWT.PUSH);
			btnTargetDelete.setText("x>");
			btnTargetDelete.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
			btnTargetDelete.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					deleteTabAdapter(false);
				}
			});
		}
		
		btnMove = new Button(centerComposite, SWT.PUSH);
		btnMove.setText("=");
		btnMove.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		btnMove.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				goAdapter(true);
			}
		});
		
		if(!notShowBtnGo) {
			btnGo = new Button(centerComposite, SWT.PUSH);
			btnGo.setText("->");
			btnGo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
			btnGo.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					goAdapter(false);
				}
			});
		}
		
		btnBack = new Button(centerComposite, SWT.PUSH);
		btnBack.setText("<-");
		btnBack.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		btnBack.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {	
				backAdapter();
			}
		});
		
	}
	
	protected Composite createTargetComponent(FormToolkit toolkit, Composite parent, Integer seqNo) {
		Composite targetComp = new Composite(parent, SWT.NULL);
		GridData gd = new GridData(GridData.FILL_BOTH);
		if (formHeight != null) {
			gd.heightHint = formHeight;
		}
		gd.widthHint = 100;
		targetComp.setLayoutData(gd);

		ComponentCustomComposite targetComponentComposite = new ComponentCustomComposite();
		if (StringUtil.isEmpty(id)) {
			targetComponentComposite.setId(ID_TARGET_COMPONENT);
		} else {
			targetComponentComposite.setId(id + "." + ID_TARGET_COMPONENT);
		}
		if (seqNo != null) {
			targetComponentComposite.setId(targetComponentComposite.getId() + seqNo);
		}
		targetComponentComposite.setShowCarrierFlag(targetShowCarrierFlag);
		targetComponentComposite.setShowLotFlag(targetShowLotFlag);
		targetComponentComposite.setAscFlag(targetAscFlag);
		targetComponentComposite.setCheckFlag(targetCheckFlag);
		targetComponentComposite.setCheckCarrierVaildFlag(targetCheckCarrierVaildFlag);
		targetComponentComposite.setCarrierSize(targetCarrierSize);
		targetComponentComposite.setTableName(targetTableName);
		targetComponentComposite.setField(this.getField());
		targetComponentComposite.setItemAdapter(targetItemAdapter);
		targetComponentComposite.setTargetFlag(true);
		targetComponentComposite.setLblCarrier(Message.getString("wip.target_carrier_id"));
		if (StringUtils.isNotEmpty(targetCarrierFieldType)) {
			targetComponentComposite.setCarrierFieldType(Enum.valueOf(CarrierFieldType.class, targetCarrierFieldType));
		}
		targetComponentComposite.createForm(toolkit, targetComp);
		targetComponentComposite.getField().unsubscribeDefaultEvent(targetComponentComposite.getId() + "-carrierId-" + GlcEvent.EVENT_ENTERPRESSED);
		targetComp.setData(targetComponentComposite);
		
		if (showSortFlag && (sourceIsMulti || targetIsMulti)) {
			// ��ʾTargetPort
			Composite portComposite = new Composite(targetComp, SWT.NONE);
			gd = new GridData(GridData.FILL_HORIZONTAL);
			portComposite.setLayout(new GridLayout(2, false));
			portComposite.setLayoutData(gd);
			Label lblTargetPort = new Label(portComposite, SWT.NONE);
			lblTargetPort.setText(Message.getString("ras.target_port"));
			
			Combo combo = new Combo(portComposite, SWT.NONE | SWT.READ_ONLY);
			gd = new GridData(GridData.FILL_VERTICAL);
			gd.widthHint = 132;
			combo.setLayoutData(gd);
			
			//�鿴ǰ����б��Ƿ����ֵ��ֱ������
			if (CollectionUtils.isNotEmpty(multiTargetPort) && multiTargetPort.get(0).getItemCount() > 0) {
				for (String item : multiTargetPort.get(0).getItems()) {
					combo.add(item);
				}
			}
			multiTargetPort.add(combo);
		}
		return targetComp;
	}
	
	protected Composite createSortingComponent(FormToolkit toolkit, Composite parent, String sortingTable, String actionType) {
		Composite sotingComposite = new Composite(parent, SWT.NULL);
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.widthHint = 100;
		sotingComposite.setLayout(new GridLayout(1, false));
		sotingComposite.setLayoutData(gd);
		sotingComposite.setBackgroundMode(SWT.INHERIT_FORCE);

		SortingCustomComposite sortingCustomComposite = new SortingCustomComposite();
		if (StringUtil.isEmpty(id)) {
			sortingCustomComposite.setId(ID_SORT_INFO);
		} else {
			sortingCustomComposite.setId(id + "." + ID_SORT_INFO);
		}
		sortingCustomComposite.setShowSortFlag(showSortFlag);
		sortingCustomComposite.setSortingFlag(sortingFlag);
		sortingCustomComposite.setNoShowEquipmentFlag(noShowEquipmentFlag);
		if (sourceIsMulti || targetIsMulti) {
			// ���ؾ�ʱ����������ʾPort
			sortingCustomComposite.setShowPortFlag(false);
		}
		sortingCustomComposite.setActionType(actionType);
		sortingCustomComposite.setField(this.getField());
		sortingCustomComposite.setAdTable(sortingTable);
		sortingCustomComposite.createForm(toolkit, sotingComposite);
		configureBody(sortingCustomComposite.getForm());
		sotingComposite.setData(sortingCustomComposite);
		if (sourceIsMulti || targetIsMulti) {
			// ���ؾ���Ҫ�����豸ѡ��չʾ��Ӧ��port��
			RefTableField reftableField = (RefTableField)sortingCustomComposite.getForm().getFields().get(SortingCustomComposite.FIELD_EQUIPMENTID);
			EventHandler defaultEquipmentEvent = new EventHandler() {
	             public void handleEvent(Event event) {
	            	 getPortsByEquipmentId((String)event.getProperty(GlcEvent.PROPERTY_DATA));
	             }
	        };
	        subscribeDefaultEvent(SortingCustomComposite.FIELD_EQUIPMENTID, GlcEvent.EVENT_VALUECHANGE, defaultEquipmentEvent);
			reftableField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					postEvent(SortingCustomComposite.FIELD_EQUIPMENTID, GlcEvent.EVENT_VALUECHANGE, GlcEvent.buildEventData(((RefTableField)sender).getComboControl().getText()));	
    			}
			});
		}
		return sotingComposite;
	}

	/**
	 * �������(ComponentList)�·����ɶ�̬ҳ��,�Է��������չ
	 */
	protected void createExtendForm(FormToolkit toolkit, Composite parent) {
		try {
			if (!StringUtil.isEmpty(sourceExtendTableName)) {
				Composite sourceComposite = new Composite(parent, SWT.NONE);
				GridData gd = new GridData(GridData.FILL_HORIZONTAL);
				gd.widthHint = 100;
				sourceComposite.setLayout(new GridLayout(1, false));
				sourceComposite.setLayoutData(gd);
				ADTable adTable = getTable(sourceExtendTableName);
				sourceExtendForm = new EntityForm(sourceComposite, SWT.NONE, adTable, null, false);
				sourceExtendForm.createForm();
				configureBody(sourceExtendForm);
			} else {
				toolkit.createComposite(parent, SWT.NONE);
			}
			
			toolkit.createComposite(parent, SWT.NONE);
			
			if (!StringUtil.isEmpty(targetExtendTableName)) {
				Composite targetComposite = new Composite(parent, SWT.NONE);
				GridData gd = new GridData(GridData.FILL_HORIZONTAL);
				gd.widthHint = 100;
				targetComposite.setLayout(new GridLayout(1, false));
				targetComposite.setLayoutData(gd);
				ADTable adTable = getTable(targetExtendTableName);
				targetExtendForm = new EntityForm(targetComposite, SWT.NONE, adTable, null, false);
				List<ADFormAttribute> fieldAttributes = Lists.newArrayList();
				for (ADField adField : adTable.getFields()) {
					for (ADFormAttribute fieldFormAttribute : fieldFormAttributes) {
						ADFormAttribute adFormAttribute = (ADFormAttribute) fieldFormAttribute.clone();
						adFormAttribute.setFieldName(adField.getName());
						fieldAttributes.add(adFormAttribute);
					}
				}
				targetExtendForm.setFormAttributes(fieldAttributes);
				targetExtendForm.createForm();
				configureBody(targetExtendForm);
			} else {
				toolkit.createComposite(parent, SWT.NONE);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void goAdapter(Boolean isMove) {
		try {
			if (getSourceComponentComposite() != null) {
				List<Object> objects  = getSourceComponentComposite().getTableManager().getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}
				
				List<ComponentUnit> sourceComponentUnits = new ArrayList<ComponentUnit>();
				for (Object obj : objects) {
					ComponentUnit unit = (ComponentUnit)obj;
					unit.setFromPosition(unit.getPosition());
					sourceComponentUnits.add(unit);
				}
				
				for (ComponentUnit componentUnit : sourceComponentUnits) {
					String sourcePosition = componentUnit.getPosition();
					if(isMove) {
						if (getTargetComponentComposite().getComponentComposite().addComponent(componentUnit, sourcePosition, OccupationPolicy.REJECT)) {
							getSourceComponentComposite().getComponentComposite().removeComponent(sourcePosition);
						} else {
							break;
						}
					}else {
						String targetPosition = getTargetComponentComposite().getComponentComposite().getCurrentPositon();
						if (StringUtil.isEmpty(targetPosition)) {
							UI.showWarning(Message.getString("wip.target_position_is_not_select"));
							return;
						}
						if (getTargetComponentComposite().getComponentComposite().addComponent(componentUnit, targetPosition, OccupationPolicy.REJECT)) {
							getSourceComponentComposite().getComponentComposite().removeComponent(sourcePosition);
						} else {
							break;
						}
						getTargetComponentComposite().getComponentComposite().addCurrentPosition();
					}
				}
			} else {
				List<Object> objects = getSourceComponentComposite().getTableManager().getCheckedObject();
				if (objects == null || objects.isEmpty()) {
					UI.showWarning(Message.getString("wip.source_component_is_not_select"));
					return;
				}

				List<ComponentUnit> sourceComponentUnits = new ArrayList<ComponentUnit>();
				for (Object obj : objects) {
					sourceComponentUnits.add((ComponentUnit)obj);
				}
				
				String targetPosition = getTargetComponentComposite().getComponentComposite().getCurrentPositon();
				if (StringUtil.isEmpty(targetPosition)) {
					List<ComponentUnit> addedComponentUnits = getTargetComponentComposite().getComponentComposite().addComponentAppend(sourceComponentUnits);
					getSourceComponentComposite().getTableManager().removeList(addedComponentUnits);
				} else {
					List<ComponentUnit> addedComponentUnits = addComponentList(sourceComponentUnits, targetPosition, OccupationPolicy.REJECT);
					getSourceComponentComposite().getTableManager().removeList(addedComponentUnits);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void backAdapter() {
		try {
			List<Object> objects  = getTargetComponentComposite().getTableManager().getCheckedObject();
			if (objects == null || objects.isEmpty()) {
				UI.showWarning(Message.getString("wip.target_position_is_not_select"));
				return;
			}
			
			List<ComponentUnit> targetComponentUnits = new ArrayList<ComponentUnit>();
			for (Object obj : objects) {
				targetComponentUnits.add((ComponentUnit)obj);
			}
			
			if (getSourceComponentComposite() != null) {
				for (ComponentUnit componentUnit : targetComponentUnits) {
					//���ѡ�е�Component�Ƿ���sourceComponentUnits
					if (!getSourceComponentComposite().getComponentComposite().getInitCompIdPositionMap().keySet().contains(componentUnit.getComponentId())) {
						UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
						return;
					}
					
					//����Ż�ԭ����λ��
					String sourcePosition = getSourceComponentComposite().getComponentComposite().getInitCompIdPositionMap().get(componentUnit.getComponentId());
					String targetPosition = componentUnit.getPosition();
					if (getSourceComponentComposite().getComponentComposite().addComponent(componentUnit, sourcePosition, OccupationPolicy.REJECT)) {
						getTargetComponentComposite().getComponentComposite().removeComponent(targetPosition);
					}
				}
			} else {
				for (ComponentUnit componentUnit : targetComponentUnits) {
					//���ѡ�е�Component�Ƿ���sourceComponentUnits
					if (getTargetComponentComposite().getComponentComposite().getInitCompIdPositionMap().keySet().contains(componentUnit.getComponentId())) {
						UI.showWarning(Message.getString("wip.target_component_is_not_in_source"));
						return;
					}
					
					String targetPosition = componentUnit.getPosition();
					getTargetComponentComposite().getComponentComposite().removeComponent(targetPosition);
					
					//to source
					if (getSourceComponentComposite().getTableManager().getInput().size() == 0) {
						getSourceComponentComposite().getTableManager().add(componentUnit);
					} else {
						int index = getSourceComponentComposite().getTableManager().getInput().size();
						getSourceComponentComposite().getTableManager().insert(index, componentUnit);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/** 
	 * ����Tabҳ
	 */
	public void addTabAdapter(boolean isSource) {
		try {
			if (isSource) {
				addSourceTabItem(this.toolkit, sourceTabFolder, multiSourceComponentComposite.size());		
			} else {
				addTargetTabItem(this.toolkit, targetTabFolder, multiTargetComponentComposite.size());		
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/** 
	 * ɾ��Tabҳ
	 */
	public void deleteTabAdapter(boolean isSource) {
		try {
			int i = 0;
			if (isSource) {
				i = multiSourceComponentComposite.size() - 1;
			} else {
				i = multiTargetComponentComposite.size() - 1;
			}
			
			//����ɾ����ֻʣһҳ
			if (i > 0) {
				if (isSource) {
					removeSourceTabItem(i);
				} else {
					removeTargetTabItem(i);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ����˳�����Component
	 * ��ָ����λ�ÿ�ʼ
	 * @throws CloneNotSupportedException 
	 */
	public List<ComponentUnit> addComponentList(List<ComponentUnit> componentUnits, String position, OccupationPolicy occupationPolicy) throws CloneNotSupportedException {
		boolean startFlag = false;
		int i = 0;
		
		Map<String, ComponentUnit> carrierMap = getTargetComponentComposite().getComponentComposite().getCarrierMap();
		
		List<ComponentUnit> addedComponentUnits = new ArrayList<ComponentUnit>();
		for (String key : carrierMap.keySet()) {
			if (key.equals(position)) {
				startFlag = true;
			}
			if (startFlag) {
				if (i == componentUnits.size()) {
					break;
				}
				ComponentUnit component = componentUnits.get(i);
				if (!getTargetComponentComposite().getComponentComposite().addComponent(component, key, occupationPolicy)) {
					return addedComponentUnits;
				}
				addedComponentUnits.add(component);
				i++;
			}
		}
		
		return addedComponentUnits;
	}
	
	public void getPortsByEquipmentId(String equipmentId) {
		try {
			List<String> targetPortIds = Lists.newArrayList();
			List<String> sourcePortIds = Lists.newArrayList();
			if (!StringUtil.isEmpty(equipmentId)) {
				RASManager rasManager = Framework.getService(RASManager.class);
				List<Port> ports = rasManager.getPortsByEquipment(Env.getOrgRrn(), equipmentId, false);
				for (Port port : ports) {
					if (Port.PORTTYPE_PL.equals(port.getPortType()) || Port.PORTTYPE_PB.equals(port.getPortType())) {
						sourcePortIds.add(port.getPortId());
					}
					if (Port.PORTTYPE_PU.equals(port.getPortType()) || Port.PORTTYPE_PB.equals(port.getPortType())) {
						targetPortIds.add(port.getPortId());
					}
				}
			}
			multiSourcePort.forEach(combo -> {
				combo.removeAll();
				for (String sourcePortId : sourcePortIds) {
					combo.add(sourcePortId);
				}
			});
			multiTargetPort.forEach(combo -> {
				combo.removeAll();
				for (String targetPortId : targetPortIds) {
					combo.add(targetPortId);
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	@Override
	public void refresh() {
		if (sourceIsMulti || targetIsMulti) {
			for (ComponentCustomComposite targetComponentComposite : multiTargetComponentComposite) {
				if (targetComponentComposite.txtCarrierId != null) {
					String targetCarrierId = targetComponentComposite.txtCarrierId.getText();
					targetComponentComposite.getComponentsByCarrier(targetCarrierId, true);
				} else if (targetComponentComposite.txtLotId != null) {
					String targetLotId = targetComponentComposite.txtLotId.getText();
					targetComponentComposite.getComponentsByLot(targetLotId, true);
				}
			}
			
			for (ComponentCustomComposite sourceComponentComposite : multiSourceComponentComposite) {
				if (sourceComponentComposite.txtCarrierId != null) {
					String sourceCarrierId = sourceComponentComposite.txtCarrierId.getText();
					sourceComponentComposite.getComponentsByCarrier(sourceCarrierId, true);
				} else if (sourceComponentComposite.txtLotId != null) {
					String sourceLotId = sourceComponentComposite.txtLotId.getText();
					sourceComponentComposite.getComponentsByLot(sourceLotId, true);
				}
			}
		} else {
			if (targetComponentComposite.txtCarrierId != null) {
				String targetCarrierId = targetComponentComposite.txtCarrierId.getText();
				targetComponentComposite.getComponentsByCarrier(targetCarrierId, true);
			} else if (targetComponentComposite.txtLotId != null) {
				String targetLotId = targetComponentComposite.txtLotId.getText();
				targetComponentComposite.getComponentsByLot(targetLotId, true);
			}
			if (sourceComponentComposite.txtCarrierId != null) {
				String sourceCarrierId = sourceComponentComposite.txtCarrierId.getText();
				sourceComponentComposite.getComponentsByCarrier(sourceCarrierId, true);
			} else if (sourceComponentComposite.txtLotId != null) {
				String sourceLotId = sourceComponentComposite.txtLotId.getText();
				sourceComponentComposite.getComponentsByLot(sourceLotId, true);
			}
		}
		sortingCustomComposite.getForm().refresh();
	}
	
	public void clear() {
		if (sourceIsMulti || targetIsMulti) {
			//ɾ�������tabҳ������յ�һ��tabҳ������
			for (int i = 0; i < multiSourceComponentComposite.size(); i++) {
				sourceTabFolder.setSelection(i);
				CTabItem item = sourceTabFolder.getItem(i);
				if (i > 0) {
					item.dispose();
					multiSourceComponentComposite.remove(multiSourceComponentComposite.get(i));
					multiSourcePort.remove(multiSourcePort.get(i));
				} else {
					item.setText("Tab0");
					multiSourcePort.get(0).removeAll();
				}
			}
			multiSourceComponentComposite.get(0).setLot(null);
			multiSourceComponentComposite.get(0).getComponentComposite().initComponents(Lists.newArrayList());
			sourceTabFolder.redraw();
			
			for (int i = 0; i < multiTargetComponentComposite.size(); i++) {
				targetTabFolder.setSelection(i);
				CTabItem item = targetTabFolder.getItem(i);
				if (i > 0) {
					item.dispose();
					multiTargetComponentComposite.remove(multiTargetComponentComposite.get(i));
					multiTargetPort.remove(multiTargetPort.get(i));
				} else {
					item.setText("Tab0");
					multiTargetPort.get(0).removeAll();
				}
			}
			multiTargetComponentComposite.get(0).setLot(null);
			multiTargetComponentComposite.get(0).getComponentComposite().initComponents(Lists.newArrayList());
			targetTabFolder.redraw();
		} else {
			sourceComponentComposite.setLot(null);
			sourceComponentComposite.getComponentComposite().initComponents(Lists.newArrayList());
			targetComponentComposite.setLot(null);
			targetComponentComposite.getComponentComposite().initComponents(Lists.newArrayList());
		}
		sortingCustomComposite.clear();
	}

	@Override
	public void setValue(Object value) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Object getValue() {
		// TODO Auto-generated method stub
		return null;
	}
	
	public ADTable getTable(String tableName){
		ADTable adTable = null;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			adTable = adManager.getADTable(Env.getOrgRrn(), tableName);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return adTable;
	}
	
	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		fieldFormAttributes = formAttributes;
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_SOURCE_SHOW_CHEKBOX:
				sourceCheckFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SOURCE_SHOW_CARRIER:
				sourceShowCarrierFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SOURCE_EDIT_CARRIER:
				sourceEditCarrierFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SOURCE_SHOW_LOT:
				sourceShowLotFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SOURCE_IS_ASC:
				sourceAscFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SOURCE_CARRIER_VALIDATE:
				sourceCheckCarrierVaildFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SOURCE_CARRIER_SIZE:
				sourceCarrierSize = formAttribute.getIntValue() == null ? 25 : formAttribute.getIntValue();
				break;
			case ATTRIBUTE_SOURCE_TABLE_NAME:
				sourceTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_SOURCE_EXTEND_TABLE_NAME:
				sourceExtendTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_SOURCE_ITEM_ADAPTER:
				sourceItemAdapter = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_SOURCE_IS_MULTI:
				sourceIsMulti = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TARGET_SHOW_CHEKBOX:
				targetCheckFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TARGET_SHOW_CARRIER:
				targetShowCarrierFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TARGET_SHOW_LOT:
				targetShowLotFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TARGET_IS_ASC:
				targetAscFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TARGET_CARRIER_VALIDATE:
				targetCheckCarrierVaildFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TARGET_CARRIER_SIZE:
				targetCarrierSize = formAttribute.getIntValue() == null ? 25 : formAttribute.getIntValue();
				break;
			case ATTRIBUTE_TARGET_TABLE_NAME:
				targetTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TARGET_EXTEND_TABLE_NAME:
				targetExtendTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TARGET_ITEM_ADAPTER:
				targetItemAdapter = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TARGET_CARRIER_FIELD_TYPE:
				targetCarrierFieldType = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_TARGET_IS_MULTI:
				targetIsMulti = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_SORT_FLAG:
				showSortFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SORTING_FLAG:
				sortingFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_BTNGO:
				notShowBtnGo = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_FORM_HEIGHT:
				formHeight = formAttribute.getIntValue();
				break;
			case ATTRIBUTE_NO_SHOW_EQUIPMENT_FLAG:
				noShowEquipmentFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_ACTION_TYPE:
				actionType = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_SORTING_TABLE_NAME:
				sortingTableName = formAttribute.getStringValue();
				break;
			}
		}
	}
		
    public void preDestory() {
		super.preDestory();
    }

	public ComponentCustomComposite getSourceComponentComposite() {
		if (sourceIsMulti) {
			return multiSourceComponentComposite.get(sourceTabFolder.getSelectionIndex());
		}
		return sourceComponentComposite;
	}

	public ComponentCustomComposite getTargetComponentComposite() {
		if (targetIsMulti) {
			return multiTargetComponentComposite.get(targetTabFolder.getSelectionIndex());
		}
		return targetComponentComposite;
	}

	public List<ComponentCustomComposite> getMultiSourceComponentComposite() {
		return multiSourceComponentComposite;
	}

	public List<ComponentCustomComposite> getMultiTargetComponentComposite() {
		return multiTargetComponentComposite;
	}

	public SortingCustomComposite getSortingCustomComposite() {
		return sortingCustomComposite;
	}

	public EntityForm getSourceExtendForm() {
		return sourceExtendForm;
	}

	public EntityForm getTargetExtendForm() {
		return targetExtendForm;
	}

	public CTabFolder getSourceTabFolder() {
		return sourceTabFolder;
	}

	public CTabFolder getTargetTabFolder() {
		return targetTabFolder;
	}

	public List<Combo> getMultiSourcePort() {
		return multiSourcePort;
	}

	public List<Combo> getMultiTargetPort() {
		return multiTargetPort;
	}

	public String getTargetCarrierId() {
		CarrierFieldType carrierFieldType = this.getTargetComponentComposite().getCarrierFieldType();
		   switch (carrierFieldType) {
			case TEXT:
				return this.getTargetComponentComposite().txtCarrierId.getText();
			case REFTABLE:
				return (String) this.getTargetComponentComposite().refCarrierId.getValue();
		   }
		return "";
	}
}
