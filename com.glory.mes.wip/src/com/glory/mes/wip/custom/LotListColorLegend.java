package com.glory.mes.wip.custom;

import org.eclipse.swt.SWT;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.TableLegend;
import com.glory.framework.core.util.StringUtil;

public class LotListColorLegend extends Composite {
	
	private LotListComposite control;
	private Composite body;

	public LotListColorLegend(Composite parent, int style, LotListComposite control) {
		super(parent, style);
		this.control = control;
	}

	protected void createContent() {
		FFormToolKit toolkit = new FFormToolKit(getDisplay());
		
        GridLayout layout = new GridLayout(1, false);
        layout.verticalSpacing = 0;
        layout.horizontalSpacing = DPIUtil.autoScaleUpUsingNativeDPI(10);
        layout.marginWidth = 0;
        layout.marginHeight = DPIUtil.autoScaleUpUsingNativeDPI(3);
        setLayout(layout);
        setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
         
        toolkit.setBackground(getParent().getBackground());
        
        body = toolkit.createComposite(this, SWT.NONE);
        GridLayout bodyLayout = new GridLayout(control.getTableLegends().size() * 2, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(bodyLayout);
		GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
		gridData.horizontalAlignment = SWT.END;
		body.setLayoutData(gridData);
        
        for (TableLegend legend : control.getTableLegends()) {
        	Label label = toolkit.createLabel(body, "xxxxx");
        	label.setBackground(legend.getColor());
        	label.setForeground(legend.getColor());
        	label.setToolTipText(legend.getLegendText());
        	
        	Label label1 = toolkit.createLabel(body, legend.getLegendText());
        	
        	if (!StringUtil.isEmpty(legend.getLegendDetail())) {
        		label1.setToolTipText(legend.getLegendDetail());
        	}
		}
	}
	
	public void reflow() {
		body.dispose();
		
		createContent();
		layout();
	}
}
