package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.action.ActionContributionItem;
import org.eclipse.jface.action.IContributionItem;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import org.eclipse.nebula.widgets.nattable.ui.NatEventData;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.FormLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.EntityQueryProgress;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.query.QueryForm;
import com.glory.framework.base.ui.custom.InfiniteProgressPanel;
import com.glory.framework.base.ui.dialog.QueryProgressMonitorDialog;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.FreezeSummaryListTableManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.SummaryListTableManager;
import com.glory.framework.base.ui.nattable.TableLegend;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.nattable.color.ICellBackgroundFunc;
import com.glory.framework.base.ui.nattable.color.ICellForegroundFunc;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.action.LotActionFactory;
import com.glory.mes.wip.lot.action.LotMenuAction;
import com.glory.mes.wip.lot.contextmenu.LotContextMenuConfiguration;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotListComposite extends CustomCompsite {
	
	private static final Logger logger = Logger.getLogger(LotListComposite.class);
	
	public static final String EVENT_SETDISABLE = "setDisable";
	public static final String EVENT_SETINPUT = "setInput";
	public static final String EVENT_SETSELECTION = "setSelection";
	public static final String EVENT_SETCHECK = "setCheck";

	public static final String ATTRIBUTE_IS_AUTOSIZE = "IsAutoSize";

	public static final String ATTRIBUTE_SHOW_CHEKBOX = "ShowCheckBox";
	public static final String ATTRIBUTE_SHOW_INDEX = "ShowIndex";
	public static final String ATTRIBUTE_INIT_MENU = "InitMemu";

	public static final String ATTRIBUTE_SUMMARY_COLUMNS = "SummaryColumnNames";
	public static final String ATTRIBUTE_FREEZE_COLUMN = "FreezeColumnPosition";
	public static final String ATTRIBUTE_FREEZE_ROW = "FreezeRowPosition";

	public static final String ATTRIBUTE_SHOW_ICON_LEGEND = "ShowIconLegend";
	public static final String ATTRIBUTE_SHOW_COLOR_LEGEND = "ShowColorLegend";
	
	public static final String ATTRIBUTE_TABLE_NAME = "TableName";
	
	
	/**
	 * Ĭ�ϳ�ʱʱ��������
	 */
	public static final int DEFAULT_TIMEOUT = 60*3;
	
	public static final String ATTRIBUTE_SHOW_CONDITION = "ShowCondition";
	public static final String ATTRIBUTE_CUSTOM_QUERY = "IsCustomQuery";
	public static final String ATTRIBUTE_SHOW_QUERY_BUTTON = "ShowQueryButton";
	public static final String ATTRIBUTE_SHOW_PROGRESS_PANEL = "ShowProgressPanel";
	public static final String ATTRIBUTE_QUERY_TIME_OUT = "QueryTimeOut";
	public static final String ATTRIBUTE_SHOW_COUNT = "ShowCount";
	
	public List<LotMenuAction> customMenuActions = Lists.newArrayList();
	public IRefresh iRefresh;
	
	//��ҳ������ʾ��ѯ����
	protected boolean isShowCondition = false;
	protected boolean isCustomQuery = false;
	protected boolean isShowQueryButton = true;
	protected boolean isShowQueryProgress = false;
	protected boolean isShowCount = true;
	protected int queryTimeOut = -1;
	
	protected boolean isCheckBox = false;
	protected boolean isIndex = false;
	protected boolean isAutoSize = false;
	protected boolean initMenu = true;
	
	protected boolean isShowIconLegend = false;
	protected boolean isShowColorLegend = false;
	
	protected String[] summaryColumns;
	protected String summaryColumn;
	
	protected int freezeColumnPosition = -1;
	protected int freezeRowPosition = -1;
	
	protected static int MONITOR_THRESHOLD = 999;
	protected static int PROGRESS_THRESHOLD = 100;
	
	protected StringBuffer sb = new StringBuffer("");
	private long totalNumber;
	private int showNumber;
	private QueryForm queryForm;
	private SquareButton queryButton;
	private Label descControl;
		
	protected ADTable adTable;
	protected ListTableManager listTableManager;
	protected LotListColorLegend legend;
	protected ListItemAdapter itemAdapter;
	
	protected List<String> disableLotMenuActions = Lists.newArrayList();
	protected List<TableLegend> tableLegends = Lists.newArrayList();

	protected List<Object> disableObjects = new ArrayList<Object>();
	
	protected EventHandler setDisableHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	Object obj = event.getProperty(GlcEvent.PROPERTY_DATA);
        	disableObjects.clear();
        	if (obj != null) {
        		if (obj instanceof List) {
        			disableObjects.addAll((List)obj);
        		} else {
        			disableObjects.add(obj);
        		}
        	}
        }
	};
	
	protected EventHandler setInputHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	setValue(event.getProperty(GlcEvent.PROPERTY_DATA));
        	refresh();
        }
	};
	
	protected EventHandler setSelectionHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	Object obj = event.getProperty(GlcEvent.PROPERTY_DATA);
        	if (obj != null) {
        		listTableManager.setSelection(new StructuredSelection(new Object[] {obj}));
        	}
        }
	};
	
	protected EventHandler setCheckHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	Object obj = event.getProperty(GlcEvent.PROPERTY_DATA);
        	if (obj != null) {
        		boolean reset = false;
            	Object isReset = event.getProperty(GlcEvent.PROPERTY_ISRESET);
            	if (isReset != null && isReset instanceof Boolean) {
            		reset = true;
            	}
        		if (obj instanceof List) {
        			List list = (List)obj;
        			if (reset) {
            			((CheckBoxTableViewerManager)listTableManager.getTableManager()).setCheckedObject(list);
        			} else {
	        			for (Object l : list) {
	        				listTableManager.setCheckedObject(l);
	        			}
        			}
        		} else {
        			if (reset) {
            			((CheckBoxTableViewerManager)listTableManager.getTableManager()).setCheckedObject(Lists.newArrayList(obj));
        			} else {
        				listTableManager.setCheckedObject(obj);
        			}
        		}
        	}
        }
	};

	public void initSubscribeEvent() {
		subscribeEvent(null, EVENT_SETDISABLE, setDisableHandler);
		subscribeEvent(null, EVENT_SETINPUT, setInputHandler);
		subscribeEvent(null, EVENT_SETSELECTION, setSelectionHandler);
		subscribeEvent(null, EVENT_SETCHECK, setCheckHandler);
	}
	
	public void preDestory() {
		unsubscribeEvent(setDisableHandler);
		unsubscribeEvent(setInputHandler);
		unsubscribeEvent(setSelectionHandler);
		unsubscribeEvent(setCheckHandler);
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		
		if (isShowCondition) {
			Composite queryComp = toolkit.createComposite(parent);
	        GridData gd = new GridData(GridData.FILL_HORIZONTAL);
	        queryComp.setLayoutData(gd);
	        
	        FormLayout formLayout = new FormLayout();
	        formLayout.marginHeight = 0;
	        formLayout.marginWidth = 10;
	        queryComp.setLayout(formLayout);
	        
	        queryForm = createQueryFrom(queryComp);
	        
	        //�ж��Ƿ���ʾQueryButton
	        if (isShowQueryButton) {
		        queryButton = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
		        queryButton.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));
		
		        int n = DPIUtil.autoScaleUpUsingNativeDPI(100);
		        
		        FormData fd = new FormData();
		        fd.bottom = new FormAttachment(queryForm, (int) (-15 * n / 100d), SWT.BOTTOM);
		        fd.left = new FormAttachment(queryForm, 20, SWT.RIGHT);
				queryButton.setLayoutData(fd);
				if (!isCustomQuery) {
					queryButton.addSelectionListener(new SelectionAdapter() {
						public void widgetSelected(SelectionEvent event) {
							if (isShowQueryProgress) {
								final InfiniteProgressPanel waitPanel = InfiniteProgressPanel.getInfiniteProgressPanelFor(UI.getActiveShell());
								waitPanel.setText("Please wait...");
								waitPanel.setTextColor(UI.getActiveShell().getDisplay().getSystemColor(SWT.COLOR_DARK_RED));
								waitPanel.start();
								Display.getDefault().syncExec(new Runnable() {
									@Override
									public void run() {
										try {
											queryAdapter();
										} catch(Exception e) {
											logger.error("Error at query adapter.", e);
										} finally {
											waitPanel.stop();
										}
									}
								});
							} else {
								queryAdapter();
							}
						}
					});
				} else {
					if (isShowQueryProgress) {
    					final InfiniteProgressPanel waitPanel = InfiniteProgressPanel.getInfiniteProgressPanelFor(UI.getActiveShell());
    					waitPanel.setText("Please wait...");
    					waitPanel.setTextColor(UI.getActiveShell().getDisplay().getSystemColor(SWT.COLOR_DARK_RED));
    					waitPanel.start();
    					final Thread thread = new Thread(new Runnable() {
    						@Override
    						public void run() {
    							try {
    		    					Map<String, Object> eventData = GlcEvent.buildEventData(null, queryForm, null);
    		    					if (queryTimeOut < 0) {
    		    						queryTimeOut = DEFAULT_TIMEOUT;
    		    					}
    								GlcEvent.sendRequest(getEventBroker(), getField().getFullTopic(GlcEvent.EVENT_QUERY), eventData, queryTimeOut);
    							} catch(Exception e) {
    								logger.error("Error at query adapter.", e);
    							} finally {
    								waitPanel.stop();
    							}
    						}
    					});
    					thread.start();
    				} else {
    					Map<String, Object> eventData = GlcEvent.buildEventData(null, queryForm, null);
    					getField().postEvent(GlcEvent.EVENT_QUERY, eventData);
    				}
				}
	        }
		}
        
		Composite tableContainer = toolkit.createComposite(parent, SWT.NONE);
		
		if (isShowColorLegend) {
			if (CollectionUtils.isEmpty(tableLegends)) {
				initDefaultLegend();
			}
			legend = new LotListColorLegend(tableContainer, SWT.NONE, this);
			legend.createContent();
		}

		listTableManager = createListTableManager(adTable);
		listTableManager.setAutoSizeFlag(!isAutoSize);
		listTableManager.setIndexFlag(isIndex);
		listTableManager.newViewer(tableContainer);
		listTableManager.setFreezeColumn();
		listTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Object obj = selection.getFirstElement();
				field.postEvent(GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(obj));
			}
		});
		
		listTableManager.addDoubleClickListener(new AbstractMouseSelectionAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				NatEventData natEventData = (NatEventData) event.data;
				String actionColumnName = listTableManager.getTableManager()
						.getColumnProperty(natTable.getColumnIndexByPosition(natEventData.getColumnPosition()));

				Map<String, Object> properties = Maps.newHashMap();
				properties.put(GlcEvent.PROPERTY_TABLE_COLUMN_NAME, actionColumnName);

				Object obj = listTableManager.getSelectedObject();
				properties.put(GlcEvent.PROPERTY_DATA, obj);

				Object actionValue = null;
				try {
					actionValue = PropertyUtil.getProperty(obj, actionColumnName, false);
				} catch (Exception e) {
				}
				properties.put(GlcEvent.PROPERTY_TABLE_COLUMN_VALUE, actionValue);

				field.postEvent(GlcEvent.EVENT_DOUBLE_CLICK, GlcEvent.buildEventData(properties));
			}
		});		
		
		 if (isShowCondition && isShowCount) {
	        createDescriptionControl(toolkit, parent);     
		}
		// ���Disable
		/*if (listTableManager.getTableManager() instanceof CheckBoxTableViewerManager) {
			((CheckBoxTableViewerManager) listTableManager.getTableManager())
					.setCheckBoxDisableListener(new ICheckBoxDisableListener() {
						public boolean isDisable(Object object) {
							if (disableObjects != null && disableObjects.contains(object)) {
								return true;
							}
							return false;
						}
					});
		}*/

		configureBody(tableContainer);
		attachToolTip();
		initSubscribeEvent();
		initColor();
		if (initMenu) {
			initMenu();
		}
		
		return tableContainer;
	}
	
	protected QueryForm createQueryFrom(Composite queryComp) {
		return new QueryForm(getADManger(), queryComp, SWT.NONE, this.adTable, new FMessageManager());
	}
	
	protected long getEntityNumber() {
		try{ 
			ADManager entityManager = getADManger();
			totalNumber = entityManager.getEntityCount(Env.getOrgRrn(), getTableManager().getADTable(), getWhereClause(), queryForm.getParmaterMap());
			return totalNumber;
		} catch (Exception e){
			logger.error("LotListComposite : createSectionDesc ", e);
		}
		return 0;
	}
	
	public String getWhereClause() {
		String whereClause = sb.toString();
		whereClause = StringUtil.relpaceWildcardCondition(whereClause);
		return whereClause;
	}
	
	protected void createWhereClause() {
		sb = new StringBuffer("");
		sb.append(" 1=1 ");
		if (queryForm != null){
			sb.append(queryForm.createWhereClause());
		}		
	}
	
	public void queryAdapter() {
		getQueryForm().removeAllMessages();
		if (!getQueryForm().validate()){
			return;
		}
		
		createWhereClause();
		refresh();
	}
	
	public void createDescriptionControl(FormToolkit toolkit, Composite client) {
		if (descControl == null) {
			descControl = toolkit.createLabel(client, "");
			GridData gridData = new GridData(GridData.FILL_HORIZONTAL);
			descControl.setLayoutData(gridData);
		}
	}
	
	protected void createDesc(){
		if (descControl != null) {
			String text = Message.formatString(ExceptionBundle.bundle.CommonTotalShow(String.valueOf(totalNumber), String.valueOf(showNumber)));
			descControl.setText("  " + text);
		}
	}
	
	public ListTableManager createListTableManager(ADTable adTable) {
		ListTableManager manager;
		if (isFreeze()) {
			TableViewerManager tableManager = createTableViewerManager(adTable, isCheckBox);
			manager = new FreezeSummaryListTableManager(tableManager, summaryColumns, freezeColumnPosition, freezeRowPosition);
			manager.setSortFlag(false);
		} else if (isSummary()) {
			TableViewerManager tableManager = createTableViewerManager(adTable, isCheckBox);
			manager = new SummaryListTableManager(tableManager, summaryColumns);
		} else {
			manager = new ListTableManager(adTable, isCheckBox);
		}
		manager.getTableManager().setAdapterFactory(createAdapterFactory(manager, adTable.getName()));
		return manager;
	}
	
	private void initDefaultLegend() {
		TableLegend legend = new TableLegend();
		legend.setSeqNo(1);
		legend.setColor(SWTResourceCache.getColor(SWTResourceCache.COLOR_LOT_HOLD));
		legend.setLegendText(Message.getString("wip.lot_hold"));
		legend.setLegendDetail(Message.getString("wip.lot_hold_legend_detail"));
		getTableLegends().add(legend);
	}
	
	public void initColor() {
		HoldLotColor holdLotColor = new HoldLotColor();
		
		addBackgroundFunc(holdLotColor);
		addForegroundFunc(holdLotColor);
	}
	
	/**
	 * ��ʼ��Menu
	 * customMenuActions��Ϊ����ʹ��customMenuActions,����ʹ��defaultMenuActions
	 */
	public void initMenu() {
		MenuManager menuManager = listTableManager.getTableManager().getMenuManager();
		if (menuManager == null) {
			menuManager = new MenuManager();
			if (!CollectionUtils.isEmpty(customMenuActions)) {
				for (LotMenuAction action : customMenuActions) {
					action.setParent(this);
					menuManager.add(action);
				}
			} else {
				for (LotMenuAction action : LotActionFactory.getLotMenuActionByAuthName(getField().getAuthority())) {
					action.setParent(this);
					menuManager.add(action);
				}
			}
			
			Menu contextMenu = menuManager.createContextMenu(listTableManager.getNatTable().getParent());
			LotContextMenuConfiguration config = new LotContextMenuConfiguration(listTableManager.getNatTable(), contextMenu);
			 
			listTableManager.getNatTable().addConfiguration(config); 
			listTableManager.getNatTable().configure();
		} else {
			//ÿ��init�����Ƴ��ϴ���ӵ�ActionContributionItem�ͷָ���
			IContributionItem[] items = menuManager.getItems();
			for (IContributionItem item : items) {
				if (item instanceof ActionContributionItem || item instanceof Separator) {
					menuManager.remove(item);
				}
			}
			
			if (!CollectionUtils.isEmpty(customMenuActions)) {
				int i = 0;
				for (LotMenuAction action : customMenuActions) {
					action.setParent(this);
					menuManager.insert(i, new ActionContributionItem(action));
					i++;
				}
				
				Separator sep2 = new Separator();
				menuManager.insert(i, sep2);
			} else {
				int i = 0;
				for (LotMenuAction action : LotActionFactory.getLotMenuActionByAuthName(getField().getAuthority())) {
					action.setParent(this);
					menuManager.insert(i, new ActionContributionItem(action));
					i++;
				}
				
				Separator sep2 = new Separator();
				menuManager.insert(i, sep2);
			}
		}
	}
	
	public TableViewerManager createTableViewerManager(ADTable adTable, boolean checkFlag) {
		TableViewerManager manager;
		if (checkFlag) {
			manager = new CheckBoxTableViewerManager(adTable);
		} else {
			manager = new TableViewerManager(adTable);
		}
		return manager;
	}
	
	public ItemAdapterFactory createAdapterFactory(ListTableManager manager, String colorModelName) {
		ItemAdapterFactory factory = manager.getTableManager().getAdapterFactory();
		if (factory == null) {
			factory = new ItemAdapterFactory();
		}
		itemAdapter = new LotListItemAdapter();
		factory.registerAdapter(Object.class, itemAdapter);
		return factory;
	}
	
	public boolean isFreeze() {
		if (freezeColumnPosition != -1 || freezeRowPosition != -1) {
			return true;
		} else {
			return false;
		}
	}
	
	public boolean isSummary() {
		if (summaryColumns == null || summaryColumns.length == 0) {
			return false;
		}
		return true;
	}

	public IRefresh getRefreshObject() {
		return this.iRefresh;
	}
	
	public void setRefreshObject(IRefresh object) {
		this.iRefresh = object;
	}

	@Override
	public void refresh() {
		if (isShowCondition) {
			try {
				long count = getEntityNumber();
	        	ADManager manager = getADManger();
	        	
	        	List<Object> adList = new ArrayList<Object>();
	        	if (count > MONITOR_THRESHOLD) {
					EntityQueryProgress progress = new EntityQueryProgress(getADManger(), count, PROGRESS_THRESHOLD, listTableManager.getADTable(), getWhereClause(), "", queryForm.getParmaterMap());
					QueryProgressMonitorDialog progressDiglog = new QueryProgressMonitorDialog(UI.getActiveShell(), "");
					progressDiglog.run(true, true, progress);
					adList = progress.getAdList();
				} else {
					if (listTableManager.getADTable().isContainMainAttribute()) {
						List<ADBase> currentList = manager.getEntityList(Env.getOrgRrn(), listTableManager.getADTable(), 
			            		0, Env.getMaxResult(), getWhereClause(), "", true, listTableManager.getADTable().getMainAttributes(), queryForm.getParmaterMap());
						adList.addAll(currentList);
					} else {
						List<ADBase> currentList = manager.getEntityList(Env.getOrgRrn(), listTableManager.getADTable(), 0, 
			            		Env.getMaxResult(), getWhereClause(), "", false, null, queryForm.getParmaterMap());
						adList.addAll(currentList);
					}
			    }
	        	
	        	showNumber = adList.size();
				createDesc();
				listTableManager.setInput(adList);
			} catch (Exception e) {
	        	logger.error("Error LotListComposite : refresh() " + e.getMessage(), e);
				ExceptionHandlerManager.asyncHandleException(e);
	        }
		} else {
			listTableManager.refresh();
		}
	}

	@Override
	public void setValue(Object value) {
		List<Object> val = (List<Object>)value;
        if (val != null) {
        	listTableManager.setInput(val);
        } else {
        	listTableManager.setInput(new ArrayList<Object>());
        }
	}

	@Override
	public Object getValue() {
		return Lists.newArrayList(listTableManager.getInput());
	}

	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute fieldAttribute : formAttributes) {
			switch (fieldAttribute.getAttributeName()) {
			case ATTRIBUTE_SHOW_CHEKBOX:
				isCheckBox = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_INDEX:
				isIndex = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SUMMARY_COLUMNS:
				summaryColumn = fieldAttribute.getStringValue();
				if (!StringUtil.isEmpty(summaryColumn)) {
					summaryColumns = summaryColumn.split(";");
				}
				break;
			case ATTRIBUTE_FREEZE_COLUMN:
				freezeColumnPosition = fieldAttribute.getIntValue() == null ? freezeColumnPosition : fieldAttribute.getIntValue();
				break;
			case ATTRIBUTE_FREEZE_ROW:
				freezeRowPosition = fieldAttribute.getIntValue() == null ?  freezeRowPosition : fieldAttribute.getIntValue();
				break;
			case ATTRIBUTE_IS_AUTOSIZE:
				isAutoSize = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_ICON_LEGEND:
				isShowIconLegend = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_COLOR_LEGEND:
				isShowColorLegend = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_TABLE_NAME:
				if (StringUtil.isEmpty(fieldAttribute.getStringValue())) {
					break;
				}
				adTable = getADManger().getADTableDeep(Env.getOrgRrn(), fieldAttribute.getStringValue());
				break;
				
			case ATTRIBUTE_SHOW_CONDITION:
				isShowCondition = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_QUERY_BUTTON:
				isShowQueryButton = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_CUSTOM_QUERY:
				isCustomQuery = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_SHOW_PROGRESS_PANEL:
				isShowQueryProgress = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_QUERY_TIME_OUT:
				if (fieldAttribute.getIntValue() != null) {
					queryTimeOut = fieldAttribute.getIntValue();
				}
				break;
			case ATTRIBUTE_SHOW_COUNT:
				isShowCount = fieldAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_INIT_MENU:
				String value = fieldAttribute.getValue();
				if (!StringUtil.isEmpty(value)) {
					initMenu = fieldAttribute.getBooleanValue();
				}
				
				break;
			}
		}
	}
	
	private void attachToolTip() {
		ByEqpLotListTableToolTip toolTip = new ByEqpLotListTableToolTip(listTableManager);
		toolTip.setPopupDelay(500);
		toolTip.activate();
		toolTip.setShift(new Point(10, 10));
	}
	
	/**
	 * ��ȡ��ѡ�ļ�¼�б�
	 * @return
	 */
	public List<Object> getCheckedObjects() {
		return listTableManager.getCheckedObject();
	}
	
	/**
	 * ��ȡѡ�еļ�¼����
	 * @return
	 */
	public Object getSelectedObject() {
		return listTableManager.getSelectedObject();
	}

	/**
	 * ��ȡListTableManager����
	 * @return
	 */
	public ListTableManager getTableManager() {
		return listTableManager;
	}

	/**
	 * �����ɫ������Ϣ
	 */
	public void clearColor() {
		itemAdapter.getCellBackgroupFuncs().clear();
		itemAdapter.getCellForegroupFuncs().clear();
		tableLegends = Lists.newArrayList();
	}
	
	/**
	 * ���ӱ���ɫ��ɫ����
	 * @param func
	 */
	public void addBackgroundFunc(ICellBackgroundFunc func) {
		itemAdapter.getCellBackgroupFuncs().add(func);
	}
	
	/**
	 * ����ǰ��ɫ��ɫ����
	 * @param func
	 */
	public void addForegroundFunc(ICellForegroundFunc func) {
		itemAdapter.getCellForegroupFuncs().add(func);
	}
	
	/**
	 * ��ȡͼ����Ϣ�б�
	 * @return
	 */
	public List<TableLegend> getTableLegends() {
		return tableLegends;
	}

	/**
	 * ����ͼ����Ϣ�б�<br/>
	 * ÿ�����ö���ˢ��ͼ����ʾ
	 * 
	 * @param tableLegends
	 */
	public void setTableLegends(List<TableLegend> tableLegends) {
		this.tableLegends = tableLegends;
		if (isShowColorLegend && legend != null) {
			legend.reflow();
		}
	}
	
	/**
	 * �б�����һ�м�¼
	 * @param object
	 */
	public void refreshAdd(Object object) {
		getTableManager().insert(0, object);
		totalNumber++;
		createDesc();
	}
	
	/**
	 * �����б��е�һ�м�¼
	 * @param object
	 */
	public void refreshUpdate(Object object) {
		getTableManager().update(object);
	}
	
	/**
	 * �Ƴ��б��е�һ�м�¼
	 * @param object
	 */
	public void refreshDelete(Object object) {
		getTableManager().remove(object);
		totalNumber--;
		createDesc();
	}

	/**
	 * �Ƴ��б��еĶ��м�¼
	 * @param baseList
	 */
	public void refreshBatchDelete(List<ADBase> baseList) {
		for (Object object : baseList) {
			getTableManager().remove(object);
		}
		totalNumber -= baseList.size();
		createDesc();
	}
	
	/**
	 * ��ȡQueryForm����
	 * @return
	 */
	public QueryForm getQueryForm() {
		return queryForm;
	}

	/**
	 * ��ȡIEventBroker����
	 * @return
	 */
	public IEventBroker getEventBroker() {
		return getField().getEventBroker();
	}
	
	/**
	 * ��ȡ�ѹ�ѡ�������б�
	 * @return
	 */
	public List<Lot> getSelectedLots() {
		if (listTableManager.getTableManager() instanceof CheckBoxTableViewerManager) {
			return (List)getCheckedObjects();
		} else {
			List<Lot> lots = Lists.newArrayList();
			if (listTableManager.getSelectedObject() != null) {
				lots.add((Lot)getSelectedObject());
			}	
			return lots;
		}
	}
	
	/**
	 * �����Զ���Ĳ˵���
	 * @param menuAction
	 */
	public void addCustomMenuAction(LotMenuAction menuAction) {
		customMenuActions.add(menuAction);
	}
	
	/**
	 * holdState = 'on'ʱ����ɫ����
	 * <AUTHOR>
	 *
	 */
	private class HoldLotColor implements ICellBackgroundFunc, ICellForegroundFunc {

		@Override
		public Color getForegroundColor(Object element, String id) {
			return null;
		}

		@Override
		public Color getBackgroundColor(Object element, String id) {
			if ("holdState".equals(id)) {
				Lot lot = (Lot) element;
				if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
					return SWTResourceCache.getColor(SWTResourceCache.COLOR_LOT_HOLD);
				}
			}
			return null;
		}
	}

}
