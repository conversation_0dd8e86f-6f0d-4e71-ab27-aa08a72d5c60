package com.glory.mes.wip.custom;

import java.util.List;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.custom.depend.StepTreeView;

public class ByStepTreeCustomComposite extends CustomCompsite {

public static final String ATTRIBUTE_TREE_ID = "TreeId";
	
	protected StepTreeView treeView;
	protected String treeId = "ByStep";

	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		treeView = new StepTreeView(treeId);
		treeView.createPartControl(parent);
		treeView.getViewer().addSelectionChangedListener(new ISelectionChangedListener() {
			
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				TreeSelection selection = (TreeSelection)event.getSelection();
				if (selection.getFirstElement() != null) {
					EntityNodeObject entityNodeObject = (EntityNodeObject) selection.getFirstElement();
					if (entityNodeObject.getEntityObject() instanceof Step) {
						EntityNodeObject nodeObject = (EntityNodeObject)selection.getFirstElement();
						Step step = (Step) nodeObject.getEntityObject();
						postEvent(null, GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(step));	
					}
				}
			}
		});
		
		return parent;
	}

	@Override
	public void refresh() {
		treeView.refresh();
	}

	@Override
	public void setValue(Object value) {
		/*if (value != null) {
			treeView.getViewer().setInput(value);
		}*/
	}

	@Override
	public Object getValue() {
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		for (ADFormAttribute formAttribute : attributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_TREE_ID:
				treeId = StringUtil.isEmpty(formAttribute.getStringValue()) ? treeId : formAttribute.getStringValue();
				break;
			}
		}
	}

	@Override
	public void preDestory() {
		super.preDestory();
	}
	
	public void doSelection(Step step) {
		treeView.notifySelectionChange(step);
	}


}
