package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.eclipse.swt.custom.StyleRange;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.widgets.Composite;

public class ByEqpConsole extends StyledText {
	private ConsoleContext context;

	public ByEqpConsole(Composite parent, int style) {
		super(parent, style);
		this.context = new ConsoleContext();
	}
	
	public enum msgType {
		MSG_INFORMATION,
		MSG_WARNING,
		MSG_ERROR
	}
	
	public void print() {
		this.setText(context.getValue());
		List<StyleRange> ranges = context.getRanges();
		for (StyleRange styleRange : ranges) {
			setStyleRange(styleRange);
		}
	}
	public void info(String message) {
		context.put(msgType.MSG_INFORMATION, message);
		print();
	}
	
	public void warning(String message) {
		context.put(msgType.MSG_WARNING, message);
		print();
	}
	
	public void error(String message) {
		context.put(msgType.MSG_ERROR, message);
		print();
	}
	
}
