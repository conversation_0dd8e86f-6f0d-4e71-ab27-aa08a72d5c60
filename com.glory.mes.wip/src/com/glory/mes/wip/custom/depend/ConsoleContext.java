package com.glory.mes.wip.custom.depend;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.custom.StyleRange;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.DateUtil;
import com.glory.mes.wip.custom.depend.ByEqpConsole.msgType;

public class ConsoleContext {
	private Map<Integer, Map<msgType, String>> msgSet = new HashMap<>();
	private List<StyleRange> ranges = new ArrayList<>();
	
	
	public void put(msgType msgType, String msgStr) {
		Map<msgType, String> mapStr = new HashMap<>();
		String prefix = DateUtil.formatTime(new Date());
		switch (msgType) {
		case MSG_INFORMATION:
			prefix += " INFO ";
			msgStr = prefix + msgStr + "\n";
			break;
		case MSG_WARNING:
			prefix += " WARNING ";
			msgStr = prefix + msgStr + "\n";
			break;
		case MSG_ERROR:
			prefix += " ERROR ";
			msgStr = prefix + msgStr + "\n";
			break;
		default:				
	}
		mapStr.put(msgType, msgStr);
		Integer seqNo = msgSet.size();
		msgSet.put(seqNo, mapStr);
	}
	
	public Map<Integer, Map<msgType, String>> getMsgSet() {
		return msgSet;
	}
	
	public String getValue() { 
		StringBuffer value = new StringBuffer();
		ranges.clear();
		if (msgSet.size() <= 8) {
			for (int i = msgSet.size(); i > 0; i--){
				Map<msgType, String> msg = msgSet.get(i-1);
				StyleRange range = new StyleRange();
				buildMsg(range, msg, value);
				ranges.add(range);
			} 
		} else {
			for(int i = msgSet.size(); i > msgSet.size() - 8; i--){
				Map<msgType, String> msg = msgSet.get(i-1);
				StyleRange range = new StyleRange();
				buildMsg(range, msg, value);
				ranges.add(range);
			}
		}
		return value.toString();
	}
	
	public List<StyleRange> getRanges() {
		return ranges;
	}
	
	protected void buildMsg(StyleRange range, Map<msgType, String> msg, StringBuffer value){
		msgType type = msg.keySet().iterator().next();
		String message = msg.get(type);
			switch (type) {
			case MSG_INFORMATION:
				value.append(message);
				break;
			case MSG_WARNING:
				range.start = value.length();
				value.append(message);
				range.length = message.length();
				range.foreground = SWTResourceCache.getColor(SWTResourceCache.COLOR_BLUE);
				break;
			case MSG_ERROR:
				range.start = value.length();
				value.append(message);
				range.length = message.length();
				range.foreground = SWTResourceCache.getColor(SWTResourceCache.COLOR_RED);
				break;
			default:				
		}	
	}
}
