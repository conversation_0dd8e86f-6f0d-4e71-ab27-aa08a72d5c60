package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeColumn;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTree;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Stage;
import com.glory.mes.prd.model.Step;

public class StepTreeManager extends TreeViewerManager {
	
	private static final Logger logger = Logger.getLogger(StepTreeManager.class);
	
	private List<ADTree> treeNodes;
	
	
	public StepTreeManager(List<ADTree> treeNodes) {
		this.treeNodes = treeNodes;
	}
	
	@Override
	protected ItemAdapterFactory createAdapterFactory() {
		ItemAdapterFactory factory = new ItemAdapterFactory();
		try {
			factory.registerAdapter(ADTree.class, new StepTreeNodeAdapter(treeNodes));
			factory.registerAdapter(EntityNodeObject.class, new StepTreeNodeAdapter(treeNodes));	
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return factory;
	}
	
	protected void fillColumns(Tree tree, String[] columns,
			String[] columnsHeader, int[] columnsWidths) {
		if (columns != null) {
			tree.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
			tree.setLinesVisible(true);
			tree.setHeaderVisible(true);

			int totleSize = 0;
			for (int i = 0; i < columns.length; i++) {
				TreeColumn column;
				column = new TreeColumn(tree, SWT.NONE);
				if (columnsHeader != null) {
					column.setText(columnsHeader[i]);
				} else {
					column.setText(columns[i]);
				}
				totleSize += columnsWidths[i];
				column.setData("id", columns[i]);
				column.setWidth(columnsWidths[i]);
				column.setResizable(true);
			}
		}
		tree.pack();
	}
	
	public void refresh() {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			List<Step> allSteps = prdManager.getActiveSteps(Env.getOrgRrn());
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<Stage> allStages = adManager.getEntityList(Env.getOrgRrn(), Stage.class);
			
			StepTreeNodeAdapter treeAdapter = (StepTreeNodeAdapter)adapterFactory.getAdapter(ADTree.class);
			StepTreeNodeAdapter nodeAdapter = (StepTreeNodeAdapter)adapterFactory.getAdapter(EntityNodeObject.class);
			treeAdapter.refresh(allSteps, allStages);
			nodeAdapter.refresh(allSteps, allStages);
		} catch (Exception e) {
			logger.error("EqpTreeManager : refresh()", e);
		}
	}

	public List<ADTree> getTreeNodes() {
		return treeNodes;
	}

	public void setTreeNodes(List<ADTree> treeNodes) {
		this.treeNodes = treeNodes;
	}
}
