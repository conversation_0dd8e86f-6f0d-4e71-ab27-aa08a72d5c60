package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.IElementComparer;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTree;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.eqp.Equipment;

public class CarrierEqpTreeView  extends EqpTreeView {
	
	protected static final Logger logger = Logger.getLogger(CarrierEqpTreeView.class);
	
	protected CarrierEqpTreeManager treeManager;
	
	public CarrierEqpTreeView() {
		
	}
	public CarrierEqpTreeView(String treeId) {
		this.TREE_ID = treeId;
	}
	
	public void createPartControl(final Composite parent) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			treeNodes = entityManager.getEntityList(Env.getOrgRrn(), ADTree.class, Integer.MAX_VALUE, " category = '" + TREE_ID +"'", "");
			
			GridData gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 300;	
			
			treeManager = new CarrierEqpTreeManager(treeNodes);
			viewer = (TreeViewer)treeManager.createViewer(parent, new FormToolkit(parent.getDisplay()));
			viewer.addSelectionChangedListener(new ISelectionChangedListener(){
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					if (event.getSelection().isEmpty()) {
						return;
					}
					if (event.getSelection() instanceof TreeSelection) {
						TreeSelection selection = (TreeSelection)event.getSelection();
						if (selection.getFirstElement() != null) {
							EntityNodeObject entityNodeObject = (EntityNodeObject) selection.getFirstElement();
							if (entityNodeObject.getEntityObject() instanceof Equipment) {
								EntityNodeObject nodeObject = (EntityNodeObject)selection.getFirstElement();
								Equipment eqp = (Equipment) nodeObject.getEntityObject();
								eqp = refreshEqp(eqp);
							}
						}
					}
				}				
			});
			// ����һ��UI�߳����������ݡ���Ȼ�����tree�˵�����
			parent.getDisplay().asyncExec(new Runnable() {
				public void run() {
					init();
				}
			});
			
			IElementComparer flowComparer = new IElementComparer() {
				public boolean equals(Object a, Object b) {
					if (a instanceof List && b instanceof List) {
						return a.equals(b);
					} else if (a instanceof EntityNodeObject && b instanceof EntityNodeObject) {
						EntityNodeObject nodeA = (EntityNodeObject) a;
						EntityNodeObject nodeB = (EntityNodeObject) b;
						ADBase baseA = (ADBase) nodeA.getEntityObject();
						ADBase baseB = (ADBase) nodeB.getEntityObject();
						return baseA.getObjectRrn().compareTo(baseB.getObjectRrn()) == 0;
					} else if (a instanceof ADTree && b instanceof ADTree) {
						ADTree adTreeA = (ADTree) a;
						ADTree adTreeB = (ADTree) b;
						return adTreeA.getObjectRrn().compareTo(adTreeB.getObjectRrn()) == 0;
					} 
					return false;
				}

				public int hashCode(Object element) {
					if (element instanceof EntityNodeObject) {
						EntityNodeObject af = (EntityNodeObject) element;
						ADBase baseA = (ADBase) af.getEntityObject();
						if (baseA.getObjectRrn() != null) {  //af.getNodeId()
							return baseA.getObjectRrn().intValue();
						}
					}
					return element.hashCode();
				}
			};
			viewer.setComparer(flowComparer);
		} catch (Exception e) {
			logger.error("EqpTreeView createPartControl error:", e);
		}
	}
}
