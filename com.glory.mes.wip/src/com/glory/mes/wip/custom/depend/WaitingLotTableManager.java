package com.glory.mes.wip.custom.depend;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;

@Deprecated
public class WaitingLotTableManager extends ListTableManager {

	public WaitingLotTableManager(ADTable adTable) {
		super(adTable);
	}

	public WaitingLotTableManager(ADTable adTable, boolean checkFlag) {
		super(adTable, checkFlag);
	}
	
	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new WaitingLotItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
	
//	@Override
//	public StructuredViewer createViewer(Composite parent, FormToolkit toolkit, String[] columns, String[] columnsHeaders, int[] columnsSize, int heightHint) {
//		StructuredViewer viewer = super.createViewer(parent, toolkit, columns, columnsHeaders, columnsSize, heightHint);
//		
//		WaitingLotViewerComparator lotComparator = new WaitingLotViewerComparator(null);
//		viewer.setComparator(lotComparator);
//		return viewer;
//	}
//
//	class WaitingLotViewerComparator extends AdapterBasedViewerComparator {
//		
//		public WaitingLotViewerComparator(Hashtable<String, Comparator<Object>> map) {
//			super(map);
//		}
//
//		@Override
//		public int compare(Viewer viewer, Object e1, Object e2) {
//			if (sortingColumn != null && sortingColumn.length() > 0) {
//				return super.compare(viewer, e1, e2);
//			}
//			
//			Lot lot1 = (Lot) e1;
//			Lot lot2 = (Lot) e2;
//
//			if (lot1.getPriority() != null) {
//				if (lot2.getPriority() != null) {
//					int pResult = lot1.getPriority().compareTo(
//							lot2.getPriority());
//					if (pResult != 0) {
//						return pResult;
//					}
//				} else {
//					return -1;
//				}
//			} else {
//				if (lot2.getPriority() != null) {
//					return 1;
//				}
//			}
//			if (lot1.getRemainTime() != null) {
//				if (lot2.getRemainTime() != null) {
//					int pResult = lot1.getRemainTime().compareTo(
//							lot2.getRemainTime());
//					if (pResult != 0) {
//						return -pResult;
//					}
//				} else {
//					return -1;
//				}
//			} else {
//				if (lot2.getRemainTime() != null) {
//					return 1;
//				}
//			}
//			return 0;
//		}
//	}

}
