package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Label;
import org.eclipse.jface.window.ToolTip;
  
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class EquipmentToolTip extends ToolTip {
	
	private Composite toolTipArea;
	private Equipment equipment;

	public EquipmentToolTip(Control control, int style, boolean manualActivation) {
		super(control, style, manualActivation);
	}

	public EquipmentToolTip(Control control, Equipment equipment) {
		super(control);
		this.equipment = equipment;
	}

	@Override
	protected Composite createToolTipContentArea(Event event, Composite parent) {
		if((toolTipArea == null || toolTipArea.isDisposed()) && equipment != null){
			toolTipArea = new Composite(parent, SWT.BORDER);
			FillLayout fl_toolTipArea = new FillLayout(SWT.VERTICAL);
			fl_toolTipArea.marginWidth = 10;
			toolTipArea.setLayout(fl_toolTipArea);
			
			Label lblEqp = new Label(toolTipArea, SWT.NONE);
			lblEqp.setText(equipment.getEquipmentId());
			
			new Label(toolTipArea, SWT.SEPARATOR | SWT.HORIZONTAL);
			
			Label lblEqpDesc = new Label(toolTipArea, SWT.NONE);
			lblEqpDesc.setText(Message.getString("ras.equipment_description") + ":" + equipment.getDescription());
			Label lblEqpState = new Label(toolTipArea, SWT.NONE);
			lblEqpState.setText(Message.getString("ras.equipment_state") + ":" + equipment.getState());
			
			try {
				LotManager manager = Framework.getService(LotManager.class);
				List<Lot> lots = manager.getRunningLotsByEqp(Env.getOrgRrn(), equipment.getEquipmentId());
				if (lots != null && lots.size() > 0) {
					new Label(toolTipArea, SWT.SEPARATOR | SWT.HORIZONTAL);
					Label lblLot = new Label(toolTipArea, SWT.NONE);
					lblLot.setText(Message.getString("wip.lot_id") + ":" + lots.get(0).getLotId() + "(" + lots.get(0).getMainQty() + ")");
					for (int i = 1; i < lots.size(); i++) {
						Lot lot = lots.get(i);
						lblLot = new Label(toolTipArea, SWT.NONE);
						lblLot.setText("     " + lot.getLotId()+ "(" + lot.getMainQty() + ")");
					}
				}
			} catch (Exception e) {
				
			}
			

		}
		return toolTipArea;
	}
}
