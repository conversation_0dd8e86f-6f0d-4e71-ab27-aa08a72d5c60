package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTree;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.model.Location;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;

public class CarrierEqpTreeManager extends EqpTreeManager {
	
	private static final Logger logger = Logger.getLogger(CarrierEqpTreeManager.class);
	
	private List<ADTree> treeNodes;
	
	public CarrierEqpTreeManager(List<ADTree> treeNodes) {
		super(treeNodes);
		this.treeNodes = treeNodes;
	}

	@Override
	protected ItemAdapterFactory createAdapterFactory() {
		ItemAdapterFactory factory = new ItemAdapterFactory();
		try {
			factory.registerAdapter(ADTree.class, new CarrierEqpTreeNodeAdapter(treeNodes));
			factory.registerAdapter(EntityNodeObject.class, new CarrierEqpTreeNodeAdapter(treeNodes));	
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return factory;
	}
	
	@Override
	public void refresh() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<Equipment> allEquipments = adManager.getEntityList(Env.getOrgRrn(), Equipment.class);
			//ȥɸѡ����Ȩ�޵��豸
			RASManager rasManager = Framework.getService(RASManager.class);
			allEquipments = rasManager.getAvailableEquipmentByUser(allEquipments, true, Env.getSessionContext());
			
			List<Location> allLocaltions = adManager.getEntityList(Env.getOrgRrn(), Location.class);
			
			EqpTreeNodeAdapter treeAdapter = (CarrierEqpTreeNodeAdapter)adapterFactory.getAdapter(ADTree.class);
			EqpTreeNodeAdapter nodeAdapter = (CarrierEqpTreeNodeAdapter)adapterFactory.getAdapter(EntityNodeObject.class);
			treeAdapter.refresh(allEquipments, allLocaltions);
			nodeAdapter.refresh(allEquipments, allLocaltions);
		} catch (Exception e) {
			logger.error("EqpTreeManager : refresh()", e);
		}
	}
}
