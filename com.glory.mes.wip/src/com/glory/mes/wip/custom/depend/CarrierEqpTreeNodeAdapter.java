package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.model.ADTree;
import com.glory.mes.base.model.Location;
import com.glory.mes.ras.eqp.Equipment;

public class CarrierEqpTreeNodeAdapter extends EqpTreeNodeAdapter {

	public CarrierEqpTreeNodeAdapter(List<ADTree> treeNodes) {
		super(treeNodes);
	}
	private static final Logger logger = Logger.getLogger(CarrierEqpTreeNodeAdapter.class);
	
	/**
	 * ���ݵ�ǰλ�ã�����ѯ�豸
	 * @param location
	 * @param allEquipments
	 * @param adManager
	 * @return
	 */
	public List<Equipment> getEquipmentByLocation(Location currentLocation, List<Equipment> locationEquipments) {
		try {	
			for (Equipment equipment : allEquipments) {
				if (currentLocation.getName().equals(equipment.getLocation())) {
					if (!locationEquipments.contains(equipment)) {
						if ("MainEqp".equals(equipment.getCategory()) && "WETFCL".equals(equipment.getEqpType())) {//ֻ��ʾClean��̨���ֻ�̨���ַ�ʽ��Ϊ�豸����ΪWETFCL
							locationEquipments.add(equipment);
						}
					}
				}
			}
			
			for (Location location : allLocaltions) {
				if (currentLocation.getObjectRrn().equals(location.getParentLocationRrn())) {						
					getEquipmentByLocation(location, locationEquipments);
				}
			}		
		} catch(Exception e) {
			logger.error(e.getMessage(), e);
		}
		return locationEquipments;
	}

	public void refresh(List<Equipment> allEquipments, List<Location> allLocaltions) {
		this.allEquipments = allEquipments;
		this.allLocaltions = allLocaltions;
	}
	
}
