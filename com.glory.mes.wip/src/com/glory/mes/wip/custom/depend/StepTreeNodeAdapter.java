package com.glory.mes.wip.custom.depend;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTree;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.entitymanager.tree.EntityTreeNodeAdapter;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Stage;
import com.glory.mes.prd.model.Step;

public class StepTreeNodeAdapter extends EntityTreeNodeAdapter {

	private static final Logger logger = Logger.getLogger(StepTreeNodeAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	
	protected List<Stage> allStages;
	protected List<Step> allSteps;
	
	public StepTreeNodeAdapter(List<ADTree> treeNodes) {
		super(treeNodes);
		
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			allSteps = prdManager.getActiveSteps(Env.getOrgRrn());
			
			ADManager adManager = Framework.getService(ADManager.class);		
			allStages = adManager.getEntityList(Env.getOrgRrn(), Stage.class);
		} catch (Exception e) {
			logger.error("StepTreeView refrsh error:", e);
		}
	}
	
	@Override
	public Object[] getChildren(Object object) {
		try {
			if (object instanceof EntityNodeObject) {
				EntityNodeObject parentObject = (EntityNodeObject)object;
				
				List<ADTree> childTreeNodes = parentObject.getChildTreeObjects();
				if (childTreeNodes.size() > 0) {
					List<EntityNodeObject> entityNodeObjects = new ArrayList<EntityNodeObject>();
					ADManager adManager = Framework.getService(ADManager.class);
					
					boolean isStageStepNode = false;
					if (Step.class.getSimpleName().equals(childTreeNodes.get(0).getModelName())) {
						if (((EntityNodeObject)object).getEntityObject() instanceof Stage) {
							isStageStepNode = true;
							Stage stage = (Stage) ((EntityNodeObject)object).getEntityObject();
							
							List<Step> stageSteps = new ArrayList<Step>();
							stageSteps = getStepByStage(stage, stageSteps);
							if (stageSteps != null && stageSteps.size() > 0) {
								for (Step stageStep : stageSteps) {	
									boolean flag = true;
									for (EntityNodeObject entityNodeObject : entityNodeObjects) {
										if (((Step)entityNodeObject.getEntityObject()).getName().equals(stageStep.getName())) {
											flag = false;
											break;
										}
									}	
									if (flag) {
										EntityNodeObject newEntityNodeObject = new EntityNodeObject();
										newEntityNodeObject.setEntityObject(stageStep);
										newEntityNodeObject.setTreeObject(childTreeNodes.get(0));
										entityNodeObjects.add(newEntityNodeObject);
									}
								}
								//���°��豸ID����
								Collections.sort(entityNodeObjects, new Comparator<EntityNodeObject>() {  								  
						            @Override  
						            public int compare(EntityNodeObject o1, EntityNodeObject o2) { 
						            	return ((Step)o1.getEntityObject()).getName()
						            			.compareTo(((Step)o2.getEntityObject()).getName());
						            }  
						        }); 
							}
							
						}
					}
					
					if (!isStageStepNode) {
						for (ADTree childTreeNode : childTreeNodes) {
							Class calzz = Class.forName(childTreeNode.getModelClass());
							String whereCaluse = childTreeNode.getWhereClause();
							if (whereCaluse != null && whereCaluse.trim().length() > 0) {
								Map<String, String> paramMap = new HashMap<String, String>();
								List<String> params = StringUtil.parseClauseParam(whereCaluse);
								for (String param : params) {
									paramMap.put(param, String.valueOf(PropertyUtil.getPropertyForString(parentObject.getEntityObject(), param)));
								}	
								whereCaluse = StringUtil.parseClause(whereCaluse, paramMap);
							}
							List<Object> entityObjects = adManager.getEntityList(Env.getOrgRrn(), calzz, Integer.MAX_VALUE, whereCaluse, childTreeNode.getOrderByClause());
							if (entityObjects.size() > 0) {
								//����ӽڵ���²�ADTree��Ϣ
								List<ADTree> thisChildTreeNodes = new ArrayList<ADTree>();
								for (ADTree treeNode : treeNodes) {
									if (childTreeNode.getObjectRrn().equals(treeNode.getParentRrn())) {
										thisChildTreeNodes.add(treeNode);
									}
								}
								
								for (Object entityObject : entityObjects) {
									EntityNodeObject entityNodeObject = new EntityNodeObject();
									entityNodeObject.setEntityObject(entityObject);
									entityNodeObject.setTreeObject(childTreeNode);
									entityNodeObject.setChildTreeObjects(thisChildTreeNodes);
									entityNodeObjects.add(entityNodeObject);
								}
							}	
						}	
					}
										 					 
					return entityNodeObjects.toArray();
				}
			}
		} catch (Exception e) {
	        logger.error("Error at LocationItemAdapter : getChildren() ", e);
		}
		
		return EMPTY;
	}
	
	/**
	 * ���ݵ�ǰλ�ã�����ѯ�豸
	 * @param location
	 * @param allEquipments
	 * @param adManager
	 * @return
	 */
	public List<Step> getStepByStage(Stage currentStage, List<Step> stageSteps) {
		try {	
			for (Step step : allSteps) {
				if (currentStage.getName().equals(step.getStageId())) {
					if (!stageSteps.contains(step)) {
						stageSteps.add(step);
					}
				}
			}	
		} catch(Exception e) {
			logger.error(e.getMessage(), e);
		}
		return stageSteps;
	}

	public void refresh(List<Step> allSteps, List<Stage> allStages) {
		this.allSteps = allSteps;
		this.allStages = allStages;
	}
	
}
