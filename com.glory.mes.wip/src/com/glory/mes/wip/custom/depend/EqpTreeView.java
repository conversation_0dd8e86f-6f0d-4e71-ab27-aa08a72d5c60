package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.IElementComparer;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTree;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.Location;
import com.glory.mes.ras.eqp.Equipment;
import com.google.common.collect.Lists;

public class EqpTreeView {
	
	protected static final Logger logger = Logger.getLogger(EqpTreeView.class);
	
	public String TREE_ID = "ByEQP";
	
	protected TreeViewer viewer;
	protected List<ADTree> treeNodes = null;
	protected EqpTreeManager treeManager;
	
	public EqpTreeView() {
		
	}
	public EqpTreeView(String treeId) {
		this.TREE_ID = treeId;
	}
	
	public void createPartControl(final Composite parent) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			treeNodes = entityManager.getEntityList(Env.getOrgRrn(), ADTree.class, Integer.MAX_VALUE, " category = '" + TREE_ID +"'", "");
			
			GridData gd = new GridData(GridData.FILL_BOTH);
			gd.heightHint = 300;	
			
			treeManager = new EqpTreeManager(treeNodes);
			viewer = (TreeViewer)treeManager.createViewer(parent, new FormToolkit(parent.getDisplay()));
			viewer.addSelectionChangedListener(new ISelectionChangedListener(){
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					if (event.getSelection().isEmpty()) {
						return;
					}
					if (event.getSelection() instanceof TreeSelection) {
						TreeSelection selection = (TreeSelection)event.getSelection();
						if (selection.getFirstElement() != null) {
							EntityNodeObject entityNodeObject = (EntityNodeObject) selection.getFirstElement();
							if (entityNodeObject.getEntityObject() instanceof Equipment) {
								EntityNodeObject nodeObject = (EntityNodeObject)selection.getFirstElement();
								Equipment eqp = (Equipment) nodeObject.getEntityObject();
								eqp = refreshEqp(eqp);
							}
						}
					}
				}				
			});
			// ����һ��UI�߳����������ݡ���Ȼ�����tree�˵�����
			parent.getDisplay().asyncExec(new Runnable() {
				public void run() {
					init();
				}
			});
			
			IElementComparer flowComparer = new IElementComparer() {
				public boolean equals(Object a, Object b) {
					if (a instanceof List && b instanceof List) {
						return a.equals(b);
					} else if (a instanceof EntityNodeObject && b instanceof EntityNodeObject) {
						EntityNodeObject nodeA = (EntityNodeObject) a;
						EntityNodeObject nodeB = (EntityNodeObject) b;
						ADBase baseA = (ADBase) nodeA.getEntityObject();
						ADBase baseB = (ADBase) nodeB.getEntityObject();
						return baseA.getObjectRrn().compareTo(baseB.getObjectRrn()) == 0;
					} else if (a instanceof ADTree && b instanceof ADTree) {
						ADTree adTreeA = (ADTree) a;
						ADTree adTreeB = (ADTree) b;
						return adTreeA.getObjectRrn().compareTo(adTreeB.getObjectRrn()) == 0;
					} 
					return false;
				}

				public int hashCode(Object element) {
					if (element instanceof EntityNodeObject) {
						EntityNodeObject af = (EntityNodeObject) element;
						ADBase baseA = (ADBase) af.getEntityObject();
						if (baseA.getObjectRrn() != null) {  //af.getNodeId()
							return baseA.getObjectRrn().intValue();
						}
					}
					return element.hashCode();
				}
			};
			viewer.setComparer(flowComparer);
		} catch (Exception e) {
			logger.error("EqpTreeView createPartControl error:", e);
		}
	}
	
	public void refresh() {
		try {	
			TreePath[] paths = viewer.getExpandedTreePaths();
			
			ADTree root = null;
			for (ADTree treeNode : treeNodes) {
				if (treeNode.getParentRrn() == null) {
					root = treeNode;
					break;
				}
			}
			
			if (root != null) {
				treeManager.refresh();
				
				viewer.setInput(root);	
				viewer.expandToLevel(paths.length + 1);		
			}
		} catch(Exception e) {
			logger.error("Error at Refresh ", e);
		}		
	}

	public Equipment refreshEqp(Equipment eqp) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			eqp = (Equipment)adManager.getEntity(eqp);
			viewer.update(eqp, null);
			return eqp;
		} catch (Exception e) {
			logger.error("EqpTreeView refreshEqp error:", e);
		}
		return eqp;
	}
	
	public void init() {
		try {
			ADTree root = null;
			for (ADTree treeNode : treeNodes) {
				if (treeNode.getParentRrn() == null) {
					root = treeNode;
					break;
				}
			}
			if (root != null) {
				viewer.setInput(root);
				viewer.expandToLevel(1);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("EqpTreeView refrsh error:", e);
		}
	}
	
	public void notifySelectionChange(Equipment equipment) {
		try {
			viewer.expandAll();
			viewer.update(equipment, null);
			
			ADManager adManager = Framework.getService(ADManager.class);
			String whereClause = " name = '" + equipment.getLocation() + "'";
			List<Location> locations = adManager.getEntityList(Env.getOrgRrn(), Location.class, 1, whereClause, null);
			
			if (CollectionUtils.isEmpty(locations)) {
				return;
			}
			
			EntityNodeObject parentObject = new EntityNodeObject();
			parentObject.setTreeObject(treeNodes.get(0));
			parentObject.setEntityObject(locations.get(0));
			parentObject.setChildTreeObjects(Lists.newArrayList(treeNodes.get(1)));
			
			EntityNodeObject nodeObject = new EntityNodeObject();
			nodeObject.setTreeObject(treeNodes.get(1));
			nodeObject.setEntityObject(equipment);
			TreeSelection section = new TreeSelection(new TreePath(new Object[] {parentObject, nodeObject}));
			viewer.setSelection(section);
		} catch (Exception e) {
			logger.error("EqpTreeManager : notifySelectionChange()", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public TreeViewer getViewer() {
		return viewer;
	}

	public void setViewer(TreeViewer viewer) {
		this.viewer = viewer;
	}
	public List<ADTree> getTreeNodes() {
		return treeNodes;
	}
	public void setTreeNodes(List<ADTree> treeNodes) {
		this.treeNodes = treeNodes;
	}
}
