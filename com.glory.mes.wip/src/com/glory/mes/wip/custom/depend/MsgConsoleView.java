package com.glory.mes.wip.custom.depend;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;

public class MsgConsoleView extends Composite {
	
	private static StyledText styledText;
	

	public MsgConsoleView(Composite parent, int style) {
		super(parent, style);
		createPartControl();
		
		this.setLayout(new GridLayout(1, true));
		this.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	protected void createPartControl() {
		styledText = new ByEqpConsole(this, SWT.MULTI | SWT.READ_ONLY | SWT.BORDER | SWT.V_SCROLL);
		styledText.setLayout(new GridLayout(1, true));
		styledText.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	public static StyledText getInstance() {
		if (styledText != null) {
			return styledText;
		}
		return null;
	}
}
