package com.glory.mes.wip.custom.depend;

import java.util.List;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

@Deprecated
public class WaitingLotItemAdapter extends ListItemAdapter<Lot> {

	@Override
	public Color getBackground(Object element, String id) {
		Lot lot = (Lot)element;
    	if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
			return new Color(Display.getCurrent(), 255, 111, 111);
    	} 
    	return null;
	}
	
	/*public String getText(Object object, String id) {
		if ("remainTime".equals(id)) {
			Lot lot = (Lot)object;
			if (lot.getQueueTime() != null) {
				return String.valueOf((System.currentTimeMillis() - lot.getQueueTime().getTime())/(60 * 1000));
			}
			
		}
		return super.getText(object, id);
	}*/
	
	@Override
	public List<Image> getImages(Object object) {
		Lot lot = (Lot) object;
		List<Image> images = Lists.newArrayList();
		
		// ���Priority��ֻ֧��1~10��������ʾ����
		if (lot.getPriority() != null && (lot.getPriority() > 0 && lot.getPriority() < 11)) {
			images.add(SWTResourceCache.getImage("lotstatus_priority_" + lot.getPriority()));
		} else {
			images.add(SWTResourceCache.getImage("lotstatus_priority_unknown"));
		}
		
		// ���hold״̬
		if (Lot.HOLDSTATE_OFF.equals(lot.getHoldState())) {
			images.add(SWTResourceCache.getImage("lotstatus_no_hold"));
		} else {
			images.add(SWTResourceCache.getImage("lotstatus_hold"));
		}
		
		String state = lot.getState();
		if (LotStateMachine.STATE_WAIT.equals(state)) {
			images.add(SWTResourceCache.getImage("lotstatus_state_wait"));
		} else if (LotStateMachine.STATE_DISP.equals(state)) {
			images.add(SWTResourceCache.getImage("lotstatus_state_disp"));
		} else {
			images.add(SWTResourceCache.getImage("lotstatus_state_unknown"));
		}
		return images;
	}
	
}
