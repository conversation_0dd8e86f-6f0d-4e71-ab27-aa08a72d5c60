package com.glory.mes.wip.custom;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.depend.RunningLotTableManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

@Deprecated
public class ByEqpRunningLotsCustomComposite extends CustomCompsite {
	
	private static final String TABLE_NAME_ATTR = "TableName";
	private static final String AUTOSIZE_ATTR = "AutoSize";
	
	private static final String TABLE_NAME = "WIPByEqpRunningLot";
	
	protected boolean autoSizeFlag = false;
	private String tableName;
	
	protected RunningLotTableManager tableManager;

	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite runComp = toolkit.createComposite(parent, SWT.NONE);
		configureBody(runComp);
		
		ADTable adTable = getADManger().getADTable(Env.getOrgRrn(), getTableName());
		tableManager = new RunningLotTableManager(adTable);
		tableManager.setAutoSizeFlag(autoSizeFlag);
		tableManager.newViewer(runComp);
		tableManager.addSelectionChangedListener(new ISelectionChangedListener() {
			
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Object obj = selection.getFirstElement();
				field.postEvent(GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(obj));
			}
		});
		
		tableManager.addDoubleClickListener(new IMouseAction() {

			@Override
			public void run(NatTable natTable, MouseEvent event) {
				Object obj = tableManager.getSelectedObject();
				field.postEvent(GlcEvent.EVENT_DOUBLE_CLICK, GlcEvent.buildEventData(obj));
			}		
		});
		
		attachToolTip();
		
		return runComp;
	}

	@Override
	public void refresh() {
		tableManager.refresh();
	}

	@Override
	public void setValue(Object value) {
		if (value instanceof List) {
			tableManager.setInput((List<? extends Object>) value);
		}
	}

	@Override
	public Object getValue() {
		return null;
	}
	
	public RunningLotTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(RunningLotTableManager tableManager) {
		this.tableManager = tableManager;
	}

	public Lot getTrackOutLot() {
		return (Lot) tableManager.getSelectedObject();
	}
	
	public List<Lot> getRunningLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		if (CollectionUtils.isNotEmpty(attributes)) {
			for (ADFormAttribute formAttribute : attributes) {
				switch (formAttribute.getAttributeName()) {
				case TABLE_NAME_ATTR:
					if (!StringUtil.isEmpty(formAttribute.getStringValue())) {
						setTableName(formAttribute.getStringValue());
					}
					break;
				case AUTOSIZE_ATTR:
					autoSizeFlag = formAttribute.getBooleanValue();
					break;
				}
			}	
		}
	}

	@Override
	public void preDestory() {
		super.preDestory();
	}

	public String getTableName() {
		return StringUtil.isEmpty(tableName) ? TABLE_NAME : tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	
	private void attachToolTip() {
		ByEqpLotListTableToolTip toolTip = new ByEqpLotListTableToolTip(tableManager);
		toolTip.setPopupDelay(500);
		toolTip.activate();
		toolTip.setShift(new Point(10, 10));
	}
}
