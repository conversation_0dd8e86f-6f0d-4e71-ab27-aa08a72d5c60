package com.glory.mes.wip.custom;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.tree.EntityNodeObject;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.custom.depend.CarrierEqpTreeView;

public class ByCarrierEqpTreeCustomComposite extends ByEqpEqpTreeCustomComposite {
	
	public static final String ATTRIBUTE_TREE_ID = "TreeId";
	
	protected CarrierEqpTreeView treeView;
	protected String treeId = "ByEQP";

	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		treeView = new CarrierEqpTreeView(treeId);
		treeView.createPartControl(parent);
		treeView.getViewer().addSelectionChangedListener(new ISelectionChangedListener() {
			
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				TreeSelection selection = (TreeSelection)event.getSelection();
				if (selection.getFirstElement() != null) {
					EntityNodeObject entityNodeObject = (EntityNodeObject) selection.getFirstElement();
					if (entityNodeObject.getEntityObject() instanceof Equipment) {
						EntityNodeObject nodeObject = (EntityNodeObject)selection.getFirstElement();
						Equipment eqp = (Equipment) nodeObject.getEntityObject();
						postEvent(null, GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(eqp));	
					}
				}
			}
		});
		
		return parent;
	}
	
	@Override
	public void refresh() {
		treeView.refresh();
	}
}
