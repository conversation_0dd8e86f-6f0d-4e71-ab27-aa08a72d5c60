package com.glory.mes.wip.custom;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseAdapter;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;

public class NewPartFlowCustomComposite extends FlowCustomComposite{
	
	private static final Logger logger = Logger.getLogger(NewPartFlowCustomComposite.class);

	public static final String CONTROL_PROCESS = "atlProcess";
	
	public static final String ATTRIBUTE_ATLPROCESSREFTABLE = "AtlProcessRefTable";
	
	protected Label processLabel;
	protected String atlProcessRefTable;
	protected ListTableManager tableManager;
	
	protected IField refTableField;
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite container = toolkit.createComposite(parent, SWT.NONE);
		container = CustomCompsite.configureBody(container);
		try {
			if (isShowInput) {
				Composite titleComposite = toolkit.createComposite(container, SWT.NONE);
				titleComposite.setLayout(new GridLayout(6, false));
				titleComposite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
				
				lblId = new Label(titleComposite, SWT.NONE);
				lblId.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
				lblId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
				switch (flowType) {
					case FLOW_TYPE_LOT:
						lblId.setText(Message.getString("wip.lot_id"));
						break;
					case FLOW_TYPE_PART:
						lblId.setText(Message.getString("wip.part_name"));
						break;
					case FLOW_TYPE_PROCESS:
						lblId.setText(Message.getString("wip.process_name"));
						break;
					case FLOW_TYPE_PROCEDURE:
						lblId.setText(Message.getString("wip.procedure_name"));
						break;					
				}			
		
				switch (flowType) {
					case FLOW_TYPE_LOT:
						createTextField(titleComposite, toolkit, CONTROL_LOT_ID);
						break;
					case FLOW_TYPE_PART:
						if (!isSearch) {
							createTextField(titleComposite, toolkit, CONTROL_PART);
						} else {
							createSearchField(titleComposite, toolkit, CONTROL_PART, refTableName);
							processLabel = new Label(titleComposite, SWT.NONE);
							processLabel.setText(Message.getString("wip.alternate.process"));
							processLabel.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
							processLabel.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
							createRefTableField(titleComposite, toolkit, CONTROL_PROCESS, atlProcessRefTable);
						}
						break;
					case FLOW_TYPE_PROCESS:
						if (!isSearch) {
							createTextField(titleComposite, toolkit, CONTROL_PROCESS);
						} else {
							createSearchField(titleComposite, toolkit, CONTROL_PROCESS, refTableName);
						}
						break;
					case FLOW_TYPE_PROCEDURE:
						if (!isSearch) {
							createTextField(titleComposite, toolkit, CONTROL_PROCEDURE);
						} else {
							createSearchField(titleComposite, toolkit, CONTROL_PROCEDURE, refTableName);
						}
						break;
				}
			}
			
			treeManager = getTreeManager();
			if (treeManager == null) {
				treeManager = new LotFlowTreeManager();
				switch (flowType) {
				case FLOW_TYPE_LOT:
					treeManager = new LotFlowTreeManager();
					break;
				case FLOW_TYPE_PART:
					treeManager = new ProcessFlowTreeManager();
					break;
				case FLOW_TYPE_PROCESS:
					treeManager = new ProcessFlowTreeManager();
					break;
				case FLOW_TYPE_PROCEDURE:
					treeManager = new ProcessFlowTreeManager();
					break;
				}		
			}
			viewer = (TreeViewer) treeManager.createViewer(new Tree(container, SWT.SINGLE| SWT.BORDER 
					| SWT.V_SCROLL | SWT.H_SCROLL | SWT.FULL_SELECTION), toolkit);

			tree = viewer.getTree();
			GridData gd = new GridData();
			gd.grabExcessHorizontalSpace = true;
			gd.grabExcessVerticalSpace = true;
			gd.horizontalAlignment = SWT.FILL;
			gd.verticalAlignment = SWT.FILL;
			gd.heightHint = tree.getItemHeight() * DPIUtil.autoScaleUpUsingNativeDPI(itemHeight);
			gd.widthHint = 55;
			tree.setLayoutData(gd);

			viewer.setComparer(flowComparer);
			
			if (treeButtons != null) {
				initMenu(treeButtons);
			}
			
			if(!isMenu) {
				tree.addMouseListener(new MouseAdapter() {
					public void mouseDown(MouseEvent e) {
						doSelect(e);
					}
				});
			}
		} catch (Exception e) {
			logger.error("FlowCustomComposite createForm error:", e);
		}
		return container;
	}
	
	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_IS_SHOW_INPUT:
				isShowInput = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_FLOW_TYPE:
				flowType = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_IS_SEARCH:
				isSearch = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_REFTABLENAME:
				refTableName = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_ATLPROCESSREFTABLE:
				atlProcessRefTable = formAttribute.getStringValue();
				break;
			case ATTRIBUTE_ITEM_HEIGHT:
				itemHeight = formAttribute.getIntValue() == null ? itemHeight : formAttribute.getIntValue();				
				break;
			case ATTRIBUTE_IS_MENU:
				isMenu = formAttribute.getBooleanValue();
				break;
			}
		}	
	}
	
	public void createRefTableField(Composite fieldComposite, FormToolkit toolkit, String controlId, String refTableName) {
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			List<ADRefTable> refTables = entityManager.getEntityList(Env.getOrgRrn(), 
					ADRefTable.class, Env.getMaxResult(), " name = '" + refTableName + "'", "");
			if (refTables == null || refTables.size() == 0) {
				return;
			}
			ADRefTable refTable = refTables.get(0);
			ADTable adTable = entityManager.getADTable(refTable.getTableRrn());		
			
			int mStyle = SWT.READ_ONLY | SWT.BORDER;
			
			tableManager = new ListTableManager(adTable);
			refTableField = new RefTableField("", tableManager, refTable, mStyle);
			refTableField.setADManager(getADManger());
			refTableField.setLabel(null);
			refTableField.createContent(fieldComposite, toolkit);
			refTableField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					if (newValue != null) {
						postEvent(controlId, GlcEvent.EVENT_SELECTION_CHANGED, GlcEvent.buildEventData(newValue));	
    				}		
				}
			});	
		} catch (Exception e) {
			logger.error("PRDFlowCustomComposite : createSearchField", e);
		}
	}

	public IField getRefTableField() {
		return refTableField;
	}

	public void setRefTableField(IField refTableField) {
		this.refTableField = refTableField;
	}

	public ListTableManager getTableManager() {
		return tableManager;
	}

	public void setTableManager(ListTableManager tableManager) {
		this.tableManager = tableManager;
	}
	
}
