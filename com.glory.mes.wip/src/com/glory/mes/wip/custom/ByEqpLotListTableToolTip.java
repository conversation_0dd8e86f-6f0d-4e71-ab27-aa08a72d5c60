package com.glory.mes.wip.custom;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.window.DefaultToolTip;
import org.eclipse.jface.window.ToolTip;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Event;

import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.model.Lot;

public class ByEqpLotListTableToolTip extends DefaultToolTip {
	
	private ListTableManager listTableManager;

	public ByEqpLotListTableToolTip(ListTableManager listTableManager) {
		super(listTableManager.getNatTable(), ToolTip.NO_RECREATE, false);
		this.listTableManager = listTableManager;
	}

	@Override
	protected Object getToolTipArea(Event event) {
		int col = listTableManager.getNatTable().getColumnPositionByX(event.x);
		int row = listTableManager.getNatTable().getRowPositionByY(event.y);

		return new Point(col, row);
	}

	@Override
	protected String getText(Event event) {
		int rowNum = listTableManager.getNatTable().getRowPositionByY(event.y);
		if (rowNum < 1) {
			return null;
		}
		
		if (listTableManager.getADTable().getIsFilter()) {
			if (rowNum < 2) {
				return null;
			}
		}
		int num = listTableManager.getNatTable().getRowIndexByPosition(rowNum);
		
		List<? extends Object> input = listTableManager.getInput();
		if (CollectionUtils.isNotEmpty(input) && listTableManager.getTableManager().getSortedInput().size() -1 >= num ) {
			Object object = listTableManager.getTableManager().getSortedInput().get(num);
			if (object instanceof Lot) {
				Lot lot = (Lot) object;
				return  "Priority : " + lot.getPriority() + "\r\n" +
						"Hold State : " + lot.getHoldState() + "\r\n" +
						"Lot State : " + lot.getState() + "\r\n" +
						"Control ID : " + (StringUtil.isEmpty(lot.getControlId()) ? "" : lot.getControlId());
			}
		}
		
		
		return super.getText(event);
	}

}
