package com.glory.mes.wip;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.eclipse.swt.graphics.Color;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.EquipmentStateColor;

public class RASColor {
	
	private final static Logger logger = Logger.getLogger(RASColor.class);
	private static final RASColor instance = new RASColor();
	private static Set<String> unVisibleStates = new HashSet<String>();
	private static Map<String, Color> colors = new HashMap<String, Color>();
	
	public static RASColor getInstance() {
    	return instance;
    }
	
	public static Color getColor(String state){
		return colors.get(state);
	}
	
	public static Collection<Color> getColors(){
		return colors.values();
	}
	
	public static Set<String> getStates(){
		return colors.keySet();
	}
	
	public static Boolean getIsVisible(String state) {
		if (unVisibleStates.contains(state)) {
			return false;
		}
		return true;
	}
	
	public static void load(){
		try {
			RASManager manager = Framework.getService(RASManager.class);
			List<EquipmentStateColor> stateColors = manager.getEqpStateColors(Env.getOrgRrn());
			for (EquipmentStateColor es : stateColors) {
				if (!es.getIsVisible()) {
					unVisibleStates.add(es.getState());
				}
				String color = es.getColor();
				if(color != null && color.trim().length() > 0){
					String[] rgbStr = color.split(",");
					int[] rgb = new int[rgbStr.length];
					for (int i  =0; i < rgbStr.length; i++){
						rgb[i] = Integer.parseInt(rgbStr[i]);
					}
					
					Color col = new Color(null, rgb[0], rgb[1], rgb[2]);
					colors.put(es.getState(), col);
				}
			}
		} catch (Exception e) {
			logger.error("RASColor : Load error ", e);
        }
	}
}
