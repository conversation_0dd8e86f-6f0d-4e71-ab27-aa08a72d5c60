package com.glory.mes.wip;

import org.apache.log4j.Logger;
import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;

/**
 * The activator class controls the plug-in life cycle
 */
public class Activator extends AbstractUIPlugin {

	// The plug-in ID
	public static final String LBL_PLUGIN_ID = "com.glory.common.label"; //$NON-NLS-1$
	// The plug-in ID
	public static final String PLUGIN_ID = "com.glory.mes.wip";
	public static final String RTD_PLUGIN_ID = "com.glory.rtd";
	public static final Logger logger = Logger.getLogger(Activator.class);

	// The shared instance
	private static Activator plugin;
	
	private static BundleContext bundleContext;
	
	/**
	 * The constructor
	 */
	public Activator() {
	}

	/**
	 * @return the bundleContext
	 */
	public BundleContext getBundleContext() {
		return bundleContext;
	}
	
	/*
	 * (non-Javadoc)
	 * @see org.eclipse.ui.plugin.AbstractUIPlugin#start(org.osgi.framework.BundleContext)
	 */
	public void start(BundleContext context) throws Exception {
		super.start(context);
		Activator.bundleContext = context;
		initializeRasColors();
		plugin = this;
		startLblBundle();
	}
	
	public void startLblBundle() throws Exception {
		Bundle bundle = org.eclipse.core.runtime.Platform.getBundle(LBL_PLUGIN_ID);
		if (bundle != null && 
				(bundle.getState() == Bundle.RESOLVED || bundle.getState() == Bundle.STARTING)) {
			bundle.start();
		}
	}

	/*
	 * (non-Javadoc)
	 * @see org.eclipse.ui.plugin.AbstractUIPlugin#stop(org.osgi.framework.BundleContext)
	 */
	public void stop(BundleContext context) throws Exception {
		plugin = null;
		super.stop(context);
	}

	/**
	 * Returns the shared instance
	 *
	 * @return the shared instance
	 */
	public static Activator getDefault() {
		return plugin;
	}

	protected void initializeRasColors(){
    	try{
    		RASColor.load();
    	} catch (Exception e){
    		logger.error("initializeRasColors error:", e);
    	}
    }
	
	public static boolean isRTDAvailable() {
		Bundle[] bundles = bundleContext.getBundles();
		boolean flag = false;
	    for (Bundle bundle : bundles) {
	    	if (RTD_PLUGIN_ID.equals(bundle.getSymbolicName())) {
	    		flag = true;
	    		break;
	    	}
	    }
	    
	    return flag;
	}
}
