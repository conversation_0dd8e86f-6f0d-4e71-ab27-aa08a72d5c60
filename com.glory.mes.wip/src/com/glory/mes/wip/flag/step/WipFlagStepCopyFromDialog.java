package com.glory.mes.wip.flag.step;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.WipFlagStep;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class WipFlagStepCopyFromDialog extends BaseTitleDialog {
	
	private RefTableField copyFromField;
	private Object value;
	
	public WipFlagStepCopyFromDialog(Shell parentShell) {
		super(parentShell);
	}
	
	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("copy-dialog"));
	    setTitle(Message.getString(ExceptionBundle.bundle.CommonCopyFrom()));
		setMessage(Message.getString(ExceptionBundle.bundle.CommonCopyFromDesc()));
		
		FormToolkit toolkit = new FormToolkit(Display.getCurrent());
		Composite content = toolkit.createComposite(parent);
		GridLayout layout = new GridLayout();
		layout.numColumns = 2;
		layout.marginHeight = 5;
		layout.marginWidth = 10;
		content.setLayout(layout);
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		copyFromField = createRefTableField(content, "copyFrom", "CopyFrom", true);
        
        return content;
	}
	
	public RefTableField createRefTableField(Composite parent, String id, String label, boolean isMandatory) {
		try {
			FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());
    		ADRefTable refTable = new ADRefTable();
    		refTable.setTextField("partName");
    		refTable.setKeyField("objectRrn");
    	
    		com.glory.framework.base.ui.nattable.ListTableManager tableManager = new com.glory.framework.base.ui.nattable.ListTableManager(getADTable());
    		
    		ADManager adManager = (ADManager)Framework.getService(ADManager.class);
    		List<WipFlagStep> wipFlagSteps = adManager.getEntityList(Env.getOrgRrn(), WipFlagStep.class, Env.getMaxResult(), "", "");		
    		if (CollectionUtils.isNotEmpty(wipFlagSteps)) {
				Map<String, List<WipFlagStep>> flagStepMap = new HashMap<String, List<WipFlagStep>>();
				for (WipFlagStep wipFlagStep : wipFlagSteps) {
					if (flagStepMap.containsKey(wipFlagStep.getCategory() + wipFlagStep.getPartName())) {
						List<WipFlagStep> flagSteps = flagStepMap.get(wipFlagStep.getCategory() + wipFlagStep.getPartName());
						flagSteps.add(wipFlagStep);
						flagStepMap.put(wipFlagStep.getCategory() + wipFlagStep.getPartName(), flagSteps);
					} else {
						List<WipFlagStep> flagSteps = new ArrayList<WipFlagStep>();
						flagSteps.add(wipFlagStep);
						flagStepMap.put(wipFlagStep.getCategory() + wipFlagStep.getPartName(), flagSteps);
					}
				}
				
				List<WipFlagStep> queryWipFlagSteps = Lists.newArrayList();
				for (String key : flagStepMap.keySet()) {
					WipFlagStep wipFlagStep = new WipFlagStep();
					wipFlagStep.setObjectRrn(flagStepMap.get(key).get(0).getObjectRrn());
					wipFlagStep.setCategory(flagStepMap.get(key).get(0).getCategory());
					wipFlagStep.setPartName(flagStepMap.get(key).get(0).getPartName());
					queryWipFlagSteps.add(wipFlagStep);
				}				
				tableManager.setInput(queryWipFlagSteps);
			}			
    		
    		int style = SWT.BORDER;
    		RefTableField fe = new RefTableField(id, tableManager, refTable, style, false);
            fe.setLabel(label);
            fe.setWidth(32);
            
            fe.createContent(parent, toolkit);
            
            return fe;
    	} catch (Exception e) {
    		ExceptionHandlerManager.asyncHandleException(e);
    	} 
		return null;
	}

	public ADTable getADTable() {
		ADTable adTable = new ADTable();
		adTable.setModelName(WipFlagStep.class.getSimpleName());
		adTable.setModelClass(WipFlagStep.class.getName());
		
		List<ADField> adFields = new ArrayList<ADField>();
		ADField adField1 = new ADField();
		adField1.setName("category");
		adField1.setIsMain(true);
		adField1.setIsDisplay(true);
		adField1.setLabel("Category");
		adField1.setLabel_zh("Category");
		adFields.add(adField1);

		ADField adField2 = new ADField();
		adField2.setName("partName");
		adField2.setIsMain(true);
		adField2.setIsDisplay(true);
		adField2.setLabel(Message.getString("wip.part_name"));
		adField2.setLabel_zh(Message.getString("wip.part_name"));
		adFields.add(adField2);

		adTable.setFields(adFields);
		return adTable;
	}
	
	@Override
	protected void okPressed() {
		if (copyFromField.getValue() != null) {
			setValue(copyFromField.getValue());
		    super.okPressed();
	    }
	}   

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

}
