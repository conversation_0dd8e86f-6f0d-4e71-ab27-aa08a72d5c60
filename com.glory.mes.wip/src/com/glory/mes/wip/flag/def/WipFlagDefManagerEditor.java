package com.glory.mes.wip.flag.def;

import java.util.List;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.LotFlagDefineComposite;
import com.glory.mes.wip.model.WipFlagDef;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class WipFlagDefManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.flag.def.WipFlagDefManagerEditor";

	private static final String FIELD_WIPFLAGDEF = "wipFlagDef";
	private static final String FIELD_FLAGLIST = "flagList";
	private static final String FIELD_FLAGCOMPOSITE = "flagComposite";
	private static final String FIELD_FLAGCATEGORY = "flagCategory";
	private static final String FIELD_FLAGDEFINE = "flagDefine";

	private static final String BUTTON_SAVE = "save";

	protected GlcFormField wipFlagDefField;
	protected GlcFormField flagListField;
	protected CustomField flagCompositeField;
	protected ListTableManagerField flagCategoryField;
	protected ListTableManagerField flagDefineField;
	protected LotFlagDefineComposite lotFlagDefineComposite;
	
	private static final String REFERENCE_NAME = "WipFlagCategory";
	private static String CURRENT_CATEGORY;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		wipFlagDefField = form.getFieldByControlId(FIELD_WIPFLAGDEF, GlcFormField.class);
		flagListField = wipFlagDefField.getFieldByControlId(FIELD_FLAGLIST, GlcFormField.class);
		flagCompositeField = wipFlagDefField.getFieldByControlId(FIELD_FLAGCOMPOSITE, CustomField.class);
		flagCategoryField = flagListField.getFieldByControlId(FIELD_FLAGCATEGORY, ListTableManagerField.class);
		flagDefineField = flagListField.getFieldByControlId(FIELD_FLAGDEFINE, ListTableManagerField.class);
		lotFlagDefineComposite = (LotFlagDefineComposite) flagCompositeField.getCustomComposite();

		subscribeAndExecute(eventBroker, flagCategoryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::flagCategorySelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		init();
	}

	private void init() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADRefList> refLists = adManager.getADRefList(Env.getOrgRrn(), REFERENCE_NAME);
			flagCategoryField.getListTableManager().setInput(refLists);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@SuppressWarnings("unchecked")
	private void saveAdapter(Object object) {
		try {
			if (StringUtil.isEmpty(CURRENT_CATEGORY)) {
				UI.showError(Message.getString("wip.please_select_category"));
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			List<WipFlagDef> wipFlagDefs = (List<WipFlagDef>) lotFlagDefineComposite.getValue();
			wipFlagDefs.stream().forEach(flag -> flag.setCategory(CURRENT_CATEGORY));
			lotManager.saveWipFlagDefs(wipFlagDefs, CURRENT_CATEGORY, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void flagCategorySelectionChanged(Object object) {
		try {
			ADRefList adRefList = (ADRefList) flagCategoryField.getListTableManager().getSelectedObject();
			CURRENT_CATEGORY = adRefList.getKey();
			ADManager adManager = Framework.getService(ADManager.class);
			List<WipFlagDef> list = adManager.getEntityList(Env.getOrgRrn(), WipFlagDef.class, Env.getMaxResult(), "category = '" + CURRENT_CATEGORY + "'", "");
			flagDefineField.getListTableManager().setInput(list);
			lotFlagDefineComposite.setValue(list);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void refresh() {
		try {
			if (StringUtil.isEmpty(CURRENT_CATEGORY)) {
				flagDefineField.getListTableManager().setInput(Lists.newArrayList());
				lotFlagDefineComposite.setValue(Lists.newArrayList());
			} else {
				ADManager adManager = Framework.getService(ADManager.class);
				List<WipFlagDef> list = adManager.getEntityList(Env.getOrgRrn(), WipFlagDef.class, Env.getMaxResult(), "category = '" + CURRENT_CATEGORY + "'", "");
				flagDefineField.getListTableManager().setInput(list);
				lotFlagDefineComposite.setValue(list);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

}