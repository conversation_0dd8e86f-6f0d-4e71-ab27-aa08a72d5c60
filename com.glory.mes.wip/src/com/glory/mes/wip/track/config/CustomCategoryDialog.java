package com.glory.mes.wip.track.config;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class CustomCategoryDialog extends EntityDialog {

	public CustomCategoryDialog(ADTable table, ADBase adObject) {
		super(table, adObject);
	}

	protected boolean saveAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (Form detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return false;
	}
	
}
