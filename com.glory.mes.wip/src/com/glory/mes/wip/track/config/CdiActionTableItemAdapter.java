package com.glory.mes.wip.track.config;

import org.eclipse.swt.graphics.Color;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.wip.cdi.LotFlowCdiPoint;

public class CdiActionTableItemAdapter extends ListItemAdapter<ADAuthority> {
	
	@Override
	public Color getForeground(Object element, String id) {
		LotFlowCdiPoint lotFlowCdiPoint = (LotFlowCdiPoint)element;
    	if (!lotFlowCdiPoint.getIsEnable()) {
			return SWTResourceCache.getColor(SWTResourceCache.COLOR_BUTTON_INACTIVE_FONT);
    	} 
    	return SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK);
	}
	
}
