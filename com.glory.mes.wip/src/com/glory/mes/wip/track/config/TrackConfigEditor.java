package com.glory.mes.wip.track.config;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefName;
import com.glory.framework.activeentity.model.ADSysParameterValue;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.editor.row.ListRowEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.cdi.IFlowCdiAction;
import com.glory.mes.wip.cdi.LotFlowCdiPoint;
import com.glory.mes.wip.cdi.client.FlowCdiActionManager;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;

public class TrackConfigEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.track.config.TrackConfigEditor";
	
	public static final String FIELD_PARAMETERGLC = "parameterGlc";
	public static final String FIELD_CDIACTIONGLC = "cdiActionGlc";
	public static final String FIELD_GLOBALPARAMETERS = "globalParameters";
	public static final String FIELD_TRACKINPARAMETERS = "trackInParameters";
	public static final String FIELD_TRACKOUTPARAMETER = "trackOutParameters";
	public static final String FIELD_POSITIONGLC = "positionGlc";
	public static final String FIELD_GLOBALCDICONFIG = "globalCdiConfig";
	public static final String FIELD_STEPCATEGORYGLC = "stepCategoryGlc";
	public static final String FIELD_CATEGORYCDICONFIG = "categoryCdiConfig";
	
	public static final String FIELD_ACTIONPOSITION = "actionPosition";
	public static final String FIELD_FLOWPOSITION = "flowPosition";
	public static final String FIELD_STEPCATEGORY = "stepCategory";
	public static final String FIELD_CUSTOMSTEPCATEGORY = "customStepCategory";

	public static final String BUTTON_EDIT = "edit";
	public static final String BUTTON_SAVE = "save";
	
	public static final String BUTTON_CLEAR = "clear";
	public static final String BUTTON_EDIT_CATEGORY_CDI = "editCategoryCdi";
	public static final String BUTTON_SAVE_CATEGORY_CDI = "saveCategoryCdi";
	
	public static final String BUTTON_ADD = "add";
//	public static final String BUTTON_REMOVE = "remove";

	protected GlcFormField parameterGlcField;
	protected GlcFormField cdiActionGlcField;
	protected EntityFormField globalParametersField;
	protected EntityFormField trackInParametersField;
	protected EntityFormField trackOutParametersField;
	
	protected GlcFormField positionGlcField;
	protected ListTableManagerField actionPositionField;
	protected ListTableManagerField flowPositionField;
	protected ListTableManagerField globalCdiConfigField;
	protected GlcFormField stepCategoryGlcField;
	protected ListTableManagerField stepCategoryField;
	protected ListTableManagerField customStepCategoryField;
	protected ListTableManagerField categoryCdiConfigField;
	
	private String currentCdiPointName;
	private String currentCategoryName;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		parameterGlcField = form.getFieldByControlId(FIELD_PARAMETERGLC, GlcFormField.class);
		cdiActionGlcField = form.getFieldByControlId(FIELD_CDIACTIONGLC, GlcFormField.class);
		
		globalParametersField = parameterGlcField.getFieldByControlId(FIELD_GLOBALPARAMETERS, EntityFormField.class);
		trackInParametersField = parameterGlcField.getFieldByControlId(FIELD_TRACKINPARAMETERS, EntityFormField.class);
		trackOutParametersField = parameterGlcField.getFieldByControlId(FIELD_TRACKOUTPARAMETER, EntityFormField.class);
		
		positionGlcField = cdiActionGlcField.getFieldByControlId(FIELD_POSITIONGLC, GlcFormField.class);
		actionPositionField = positionGlcField.getFieldByControlId(FIELD_ACTIONPOSITION, ListTableManagerField.class);
		flowPositionField = positionGlcField.getFieldByControlId(FIELD_FLOWPOSITION, ListTableManagerField.class);	
		globalCdiConfigField = cdiActionGlcField.getFieldByControlId(FIELD_GLOBALCDICONFIG, ListTableManagerField.class);
		
		stepCategoryGlcField = cdiActionGlcField.getFieldByControlId(FIELD_STEPCATEGORYGLC, GlcFormField.class);
		stepCategoryField = stepCategoryGlcField.getFieldByControlId(FIELD_STEPCATEGORY, ListTableManagerField.class);
		customStepCategoryField = stepCategoryGlcField.getFieldByControlId(FIELD_CUSTOMSTEPCATEGORY, ListTableManagerField.class);
		categoryCdiConfigField = cdiActionGlcField.getFieldByControlId(FIELD_CATEGORYCDICONFIG, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, actionPositionField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::actionPositionSelectionChanged);
		subscribeAndExecute(eventBroker, flowPositionField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::flowPositionSelectionChanged);		
		subscribeAndExecute(eventBroker, stepCategoryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::stepCategorySelectionChanged);
		subscribeAndExecute(eventBroker, customStepCategoryField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::customStepCategorySelectionChanged);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveSysParameterAdapter);
		
		subscribeAndExecute(eventBroker, cdiActionGlcField.getFullTopic(BUTTON_EDIT), this::editCommonFlowCdiPointAdapter);
		subscribeAndExecute(eventBroker, cdiActionGlcField.getFullTopic(BUTTON_SAVE), this::saveCommonFlowCdiPointAdapter);
		
		subscribeAndExecute(eventBroker, cdiActionGlcField.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
		subscribeAndExecute(eventBroker, cdiActionGlcField.getFullTopic(BUTTON_EDIT_CATEGORY_CDI), this::editStepFlowCdiPointAdapter);
		subscribeAndExecute(eventBroker, cdiActionGlcField.getFullTopic(BUTTON_SAVE_CATEGORY_CDI), this::saveStepFlowCdiPointAdapter);
		
		subscribeAndExecute(eventBroker, stepCategoryGlcField.getFullTopic(BUTTON_ADD), this::addAdapter);
//		subscribeAndExecute(eventBroker, stepCategoryGlcField.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
		
		init();
	}

	/**
	 * ��ʼ����Ϊ������λ��ֵ
	 */
	private void init() {
		try {
			//Ϊ������ֵ
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			List<ADSysParameterValue> values = sysParamManager.getSysParameterValues(0);
			if (CollectionUtils.isNotEmpty(values)) {				
				for (ADSysParameterValue value : values) {
					AbstractField field = globalParametersField.getFieldByControlId(value.getName(), AbstractField.class);
					if (field != null) {
						field.setValue(value.getCurrentValue());
						field.refresh();
						continue;
					}				
					field = trackInParametersField.getFieldByControlId(value.getName(), AbstractField.class);
					if (field != null) {
						field.setValue(value.getCurrentValue());
						field.refresh();
						continue;
					}
					field = trackOutParametersField.getFieldByControlId(value.getName(), AbstractField.class);
					if (field != null) {
						field.setValue(value.getCurrentValue());
						field.refresh();
						continue;
					}
				}
			}
			
			List<ADRefName> actionPositions = new ArrayList<ADRefName>();
			ADRefName trackIn = new ADRefName();
			trackIn.setName(IFlowCdiAction.CDI_POINT_TRACK_IN);
			trackIn.setDescription(Message.getString("wip.trackin"));
			actionPositions.add(trackIn);
			ADRefName trackOut = new ADRefName();
			trackOut.setName(IFlowCdiAction.CDI_POINT_TRACK_OUT);
			trackOut.setDescription(Message.getString("wip.trackout"));
			actionPositions.add(trackOut);
			ADRefName abort = new ADRefName();
			abort.setName(IFlowCdiAction.CDI_POINT_ABORT);
			abort.setDescription(Message.getString("wip.abort"));
			actionPositions.add(abort);
			ADRefName edc = new ADRefName();
			edc.setName(IFlowCdiAction.CDI_POINT_EDC);
			edc.setDescription(Message.getString("wip.dcop"));
			actionPositions.add(edc);
			actionPositionField.setValue(actionPositions);
			actionPositionField.refresh();
		
			List<ADRefName> flowPositions = new ArrayList<ADRefName>();
			ADRefName stepQueue = new ADRefName();
			stepQueue.setName(IFlowCdiAction.CDI_POINT_STEP_QUEUE);
			flowPositions.add(stepQueue);		
			ADRefName stepRun = new ADRefName();
			stepRun.setName(IFlowCdiAction.CDI_POINT_STEP_RUN);
			flowPositions.add(stepRun);			
			ADRefName stepEnd = new ADRefName();
			stepEnd.setName(IFlowCdiAction.CDI_POINT_STEP_END);	
			flowPositions.add(stepEnd);		
			ADRefName procedureEnd = new ADRefName();
			procedureEnd.setName(IFlowCdiAction.CDI_POINT_PROCEDURE_END);
			flowPositions.add(procedureEnd);	
			flowPositionField.setValue(flowPositions);
			flowPositionField.refresh();
			
			List<ADURefList> uRefList = this.getADManger().getADURefList(Env.getOrgRrn(), "StepCategory");
			stepCategoryField.setValue(uRefList);
			stepCategoryField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * �������
	 * @param object
	 */
	private void saveSysParameterAdapter(Object object) {
		try {
			List<ADSysParameterValue> sysParameterValues = new ArrayList<ADSysParameterValue>();
			
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			List<ADSysParameterValue> values = sysParamManager.getSysParameterValues(0);
			if (CollectionUtils.isNotEmpty(values)) {				
				for (ADSysParameterValue value : values) {
					AbstractField field = globalParametersField.getFieldByControlId(value.getName(), AbstractField.class);
					if (field != null) {
						String resultValue = (boolean)field.getValue() == true ? "Y" : "N";
						if (!Objects.equal(value.getCurrentValue(), resultValue)) {
							value.setValue(resultValue);
							sysParameterValues.add(value);
						}
						continue;
					}				
					field = trackInParametersField.getFieldByControlId(value.getName(), AbstractField.class);
					if (field != null) {
						String resultValue = (boolean)field.getValue() == true ? "Y" : "N";
						if (!Objects.equal(value.getCurrentValue(), resultValue)) {
							value.setValue(resultValue);
							sysParameterValues.add(value);
						}
						continue;
					}
					field = trackOutParametersField.getFieldByControlId(value.getName(), AbstractField.class);
					if (field != null) {
						String resultValue = (boolean)field.getValue() == true ? "Y" : "N";
						if (!Objects.equal(value.getCurrentValue(), resultValue)) {
							value.setValue(resultValue);
							sysParameterValues.add(value);
						}
						continue;
					}
				}
			}
			
			sysParamManager.saveSysParameterValue(sysParameterValues, Env.getSessionContext());
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ����λ��ѡ���¼�
	 * @param object
	 */
	protected void actionPositionSelectionChanged(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			ADRefName lotFlowCdiPoint = (ADRefName) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotFlowCdiPoint == null) {
				return;
			}
			
			currentCdiPointName = lotFlowCdiPoint.getName();
			currentCategoryName = null;
			
			flowPositionField.getListTableManager().setSelection(new StructuredSelection());
			stepCategoryField.getListTableManager().setSelection(new StructuredSelection());
			customStepCategoryField.getListTableManager().setSelection(new StructuredSelection());
			
			categoryCdiConfigField.setValue(new ArrayList<LotFlowCdiPoint>());
			categoryCdiConfigField.refresh();		
			
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getName())) {
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), lotFlowCdiPoint.getName(), null, null, null);
				globalCdiConfigField.setValue(lotFlowCdiPoints);
				globalCdiConfigField.refresh();
			
				List<ADURefList> customURefs = getCustomCategory(lotFlowCdiPoint.getName());
				customStepCategoryField.setValue(customURefs);
				customStepCategoryField.refresh();
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ��ȡ�Զ������
	 * @param cdiPointName
	 * @return
	 */
	private List<ADURefList> getCustomCategory(String cdiPointName) {
		try {
			List<ADURefList> customURefs = Lists.newArrayList();
			List<LotFlowCdiPoint> categoryLotFlowCdiPoints = this.getADManger().getEntityList(Env.getOrgRrn(), LotFlowCdiPoint.class, Env.getMaxResult(), 
					"stepCategory IS NOT NULL AND cdiPointName = '" + cdiPointName + "' AND (stepName IS NULL or stepName = '')", "");
			if (CollectionUtils.isNotEmpty(categoryLotFlowCdiPoints)) {
				for (LotFlowCdiPoint categoryLotFlowCdiPoint : categoryLotFlowCdiPoints) {
					List<ADURefList> urefList = (List<ADURefList>) stepCategoryField.getValue();
					Optional<ADURefList> optURef = urefList.stream().filter(p->p.getKey().equals(categoryLotFlowCdiPoint.getStepCategory())).findFirst();
					if (!optURef.isPresent()) {
						Optional<ADURefList> optCustomURef = customURefs.stream().filter(p->p.getKey().equals(categoryLotFlowCdiPoint.getStepCategory())).findFirst();
						if (!optCustomURef.isPresent()) {
							ADURefList customURef = new ADURefList();
							customURef.setKey(categoryLotFlowCdiPoint.getStepCategory());
							customURefs.add(customURef);
						}			
					}
				}
			}
			return customURefs;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
			return null;
	}
	
	/**
	 * ����λ��ѡ���¼�
	 * @param object
	 */
	private void flowPositionSelectionChanged(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			ADRefName lotFlowCdiPoint = (ADRefName) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lotFlowCdiPoint == null) {
				return;
			}
			
			currentCdiPointName = lotFlowCdiPoint.getName();
			currentCategoryName = null;
			
			actionPositionField.getListTableManager().setSelection(new StructuredSelection());
			stepCategoryField.getListTableManager().setSelection(new StructuredSelection());
			
			customStepCategoryField.setValue(null);
			customStepCategoryField.refresh();
			
			categoryCdiConfigField.setValue(new ArrayList<LotFlowCdiPoint>());
			categoryCdiConfigField.refresh();		
			
			if (!StringUtil.isEmpty(lotFlowCdiPoint.getName())) {
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), lotFlowCdiPoint.getName(), null, null, null);
				globalCdiConfigField.setValue(lotFlowCdiPoints);
				globalCdiConfigField.refresh();
			}		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * �������ѡ���¼�
	 * @param object
	 */
	private void stepCategorySelectionChanged(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			ADURefList stepCategory = (ADURefList) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (stepCategory == null) {
				return;
			}
			
			ADRefName position = (ADRefName) actionPositionField.getListTableManager().getSelectedObject();
			if (position == null) {
				position= (ADRefName) flowPositionField.getListTableManager().getSelectedObject();
			}
			if (position == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.PleaseSelectActionOrFlowPosition()));
				return;
			}
				
			currentCategoryName = stepCategory.getKey();
			
			customStepCategoryField.getListTableManager().setSelection(new StructuredSelection());
			
			FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
			List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
					position.getName(), null, stepCategory.getKey(), null);
			categoryCdiConfigField.setValue(lotFlowCdiPoints);
			categoryCdiConfigField.refresh();					
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	/**
	 * �Զ������ѡ���¼�
	 * @param object
	 */
	private void customStepCategorySelectionChanged(Object object) {
		try {
			if (object == null) {
				return;
			}
			Event event = (Event) object;
			ADURefList stepCategory = (ADURefList) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (stepCategory == null) {
				return;
			}
			
			ADRefName position = (ADRefName) actionPositionField.getListTableManager().getSelectedObject();
			if (position == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.PleaseSelectActionPosition()));
				return;
			}
				
			currentCategoryName = stepCategory.getKey();
			
			stepCategoryField.getListTableManager().setSelection(new StructuredSelection());
			
			FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
			List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
					position.getName(), null, stepCategory.getKey(), null);
			categoryCdiConfigField.setValue(lotFlowCdiPoints);
			categoryCdiConfigField.refresh();		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 *  �����Զ������̬��
	 * @return
	 */
	public ADTable getAddCatogoryTable() {
		ADTable adTable = new ADTable();
		adTable.setModelName(ADURefList.class.getSimpleName());
		adTable.setModelClass(ADURefList.class.getName());
		adTable.setLabel("Add Catogory");
		adTable.setLabel_zh(Message.getString("common.add") + "Category");
		List<ADField> adFields = new ArrayList<ADField>();
		ADField adField1 = new ADField();
		adField1.setName("key");
		adField1.setIsMain(true);
		adField1.setIsDisplay(true);
		adField1.setLabel("Category");
		adField1.setLabel_zh("Category");
		adField1.setDataType("string");
		adField1.setDisplayType("text");
		adFields.add(adField1);

		ADField adField2 = new ADField();
		adField2.setName("description");
		adField2.setIsMain(true);
		adField2.setIsDisplay(true);
		adField2.setDisplayLength(15l);
		adField2.setLabel(Message.getString("common.description"));
		adField2.setLabel_zh(Message.getString("common.description"));
		adField2.setDataType("string");
		adField2.setDisplayType("text");
//		adFields.add(adField2);

		adTable.setFields(adFields);
		
		return adTable;
	}
	
	/**
	 * �����Զ������
	 * @param object
	 */
	private void addAdapter(Object object) {
		try {
			ADRefName position = (ADRefName) actionPositionField.getListTableManager().getSelectedObject();
			if (position == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.PleaseSelectActionPosition()));
				return;
			}
			
			List<ADURefList> all = Lists.newArrayList();
			all.addAll((Collection<? extends ADURefList>) customStepCategoryField.getListTableManager().getInput());
			EntityDialog dialog = new CustomCategoryDialog(getAddCatogoryTable(), new ADURefList());
			if (dialog.open() == Dialog.OK) {
				ADURefList uRefList = (ADURefList) dialog.getAdObject();
				all.add(uRefList);
				customStepCategoryField.getListTableManager().setInput(all);
				customStepCategoryField.getListTableManager().refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	/**
	 * �Ƴ��Զ������
	 * @param object
	 */
	private void removeAdapter(Object object) {
		List<ADURefList> all = Lists.newArrayList();
		all.addAll((Collection<? extends ADURefList>) customStepCategoryField.getListTableManager().getInput());	
		Object selected = customStepCategoryField.getListTableManager().getSelectedObject();
		all.remove(selected);
		customStepCategoryField.getListTableManager().setInput(all);
		customStepCategoryField.getListTableManager().refresh();
	}
	
	
	/**
	 * �༭ȫ��ע�������
	 * @param object
	 */
	public void editCommonFlowCdiPointAdapter(Object object) {
		List<LotFlowCdiPoint> lotFlowCdiPoints = (List<LotFlowCdiPoint> ) globalCdiConfigField.getValue();
		List<Object> os = globalCdiConfigField.getListTableManager().getCheckedObject();
		if (CollectionUtils.isEmpty(os)) {
			return;
		}
		for (Object o : os) {
			LotFlowCdiPoint LotFlowCdiPoint = (LotFlowCdiPoint) o;
			if (!LotFlowCdiPoint.isAllowDisable() && !LotFlowCdiPoint.getIsEnable()) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.NotAllowDisable()));
				return;
			}
		}
		for (Object o : os) {
			 ((ListRowEditorTableManager)globalCdiConfigField.getListTableManager()).addEditorObjects(o);
		}
		
		List<Object> checkedObjects = new ArrayList<Object>();
		checkedObjects.addAll(os);

		globalCdiConfigField.getListTableManager().setInput(lotFlowCdiPoints);
		
		for (Object o : checkedObjects) {
			globalCdiConfigField.getListTableManager().setCheckedObject(o);
		}
	}
	
	/**
	 * ����ȫ��ע�������
	 * @param object
	 */
	private void saveCommonFlowCdiPointAdapter(Object object) {
		try {	
			if (currentCdiPointName == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.PleaseSelectActionOrFlowPosition()));
				return;
			}
			List<Object> editorObjects = ((ListRowEditorTableManager)globalCdiConfigField.getListTableManager()).getEditorObject();
			if (CollectionUtils.isNotEmpty(editorObjects)) {
				boolean confirmSave = UI.showConfirm(Message.getString("wip.confirm_flowcdi_save"));
				if (confirmSave) {
					List<LotFlowCdiPoint> editorLotFlowCdiPoints = Lists.newArrayList();
					for (Object editorObject : editorObjects) {
						editorLotFlowCdiPoints.add((LotFlowCdiPoint)editorObject);
					}
					FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
					flowCdiActionManager.saveCommonFlowCdiPoint(currentCdiPointName, null, editorLotFlowCdiPoints, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));		
		
					((ListRowEditorTableManager)globalCdiConfigField.getListTableManager()).getEditorObject().clear();
					((ListRowEditorTableManager)globalCdiConfigField.getListTableManager()).getCheckedObject().clear();
					List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), currentCdiPointName, null, null, null);						
					globalCdiConfigField.setValue(lotFlowCdiPoints);
					globalCdiConfigField.refresh();
				}
			} else {
				UI.showError(Message.getString("wip.please_select_a_record_to_edit"));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	
	/**
	 * �༭���ע�������
	 * @param object
	 */
	public void editStepFlowCdiPointAdapter(Object object) {
		List<LotFlowCdiPoint> lotFlowCdiPoints = (List<LotFlowCdiPoint> ) categoryCdiConfigField.getValue();
		List<Object> os = categoryCdiConfigField.getListTableManager().getCheckedObject();
		if (CollectionUtils.isEmpty(os)) {
			return;
		}
		
		for (Object o : os) {
			LotFlowCdiPoint LotFlowCdiPoint = (com.glory.mes.wip.cdi.LotFlowCdiPoint) o;
			if (!LotFlowCdiPoint.isAllowDisable() && !LotFlowCdiPoint.getIsEnable()) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.NotAllowDisable()));
				return;
			}
		}
		
		for (Object o : os) {
			 ((ListRowEditorTableManager)categoryCdiConfigField.getListTableManager()).addEditorObjects(o);
		}
		
		List<Object> checkedObjects = new ArrayList<Object>();
		checkedObjects.addAll(os);

		categoryCdiConfigField.getListTableManager().setInput(lotFlowCdiPoints);
		
		for (Object o : checkedObjects) {
			categoryCdiConfigField.getListTableManager().setCheckedObject(o);
		}
	}
	
	/**
	 * �������ע�������
	 * @param object
	 */
	private void saveStepFlowCdiPointAdapter(Object object) {
		try {	
			if (currentCdiPointName == null) {
				return;
			}
			
			if (currentCategoryName == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.PleaseSelectCategory()));
				return;
			}
			List<Object> editorObjects = ((ListRowEditorTableManager)categoryCdiConfigField.getListTableManager()).getEditorObject();
			if (CollectionUtils.isNotEmpty(editorObjects)) {
				boolean confirmSave = UI.showConfirm(Message.getString("wip.confirm_flowcdi_save"));
				if (confirmSave) {
					List<LotFlowCdiPoint> editorLotFlowCdiPoints = Lists.newArrayList();
					for (Object editorObject : editorObjects) {
						editorLotFlowCdiPoints.add((LotFlowCdiPoint)editorObject);
					}
					FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
					flowCdiActionManager.saveStepFlowCdiPoint(currentCdiPointName, currentCategoryName, null, editorLotFlowCdiPoints, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));		
					
					((ListRowEditorTableManager)categoryCdiConfigField.getListTableManager()).getEditorObject().clear();
					((ListRowEditorTableManager)categoryCdiConfigField.getListTableManager()).getCheckedObject().clear();
					List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
							currentCdiPointName, null, currentCategoryName, null);
					categoryCdiConfigField.setValue(lotFlowCdiPoints);
					categoryCdiConfigField.refresh();		
				}
			} else {
				UI.showError(Message.getString("wip.please_select_a_record_to_edit"));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	/**
	 * ����
	 * @param object
	 */
	private void clearAdapter(Object object) {
		try {
			if (currentCdiPointName == null) {
				return;
			}
			
			if (currentCategoryName == null) {
				UI.showError(Message.getString(WipExceptionBundle.bundle.PleaseSelectCategory()));
				return;
			}
			
			boolean confirmSave = UI.showConfirm(Message.getString("wip.confirm_reset"));
			if (confirmSave) {		
				FlowCdiActionManager flowCdiActionManager = Framework.getService(FlowCdiActionManager.class);
				flowCdiActionManager.resetStepFlowCdiPoint(currentCdiPointName, currentCategoryName, null, Env.getSessionContext());
				
				if (customStepCategoryField.getListTableManager().getSelectedObject() != null) {
					List<ADURefList> customURefs = getCustomCategory(currentCdiPointName);
					customStepCategoryField.setValue(customURefs);
					customStepCategoryField.refresh();
				
					categoryCdiConfigField.setValue(null);
					categoryCdiConfigField.refresh();	
				} else {
					List<LotFlowCdiPoint> lotFlowCdiPoints = flowCdiActionManager.getFlowCdiAcutalActions(Env.getOrgRrn(), 
							currentCdiPointName, null, currentCategoryName, null);
					categoryCdiConfigField.setValue(lotFlowCdiPoints);
					categoryCdiConfigField.refresh();	
				}
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));		
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
}