package com.glory.mes.wip.query.mlot.bylot;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class QueryMLotByLotSection extends QueryEntityListSection implements IRefresh {
	
	String whereClause;
	
	public QueryMLotByLotSection(ListTableManager tableManager) {
		super(tableManager);
	}
		
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
//		createToolItemExport(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	
	@Override
	public void createContents(IManagedForm form, Composite parent,
			int sectionStyle) {
		// TODO Auto-generated method stub
		super.createContents(form, parent, sectionStyle);
	}
}
