<?xml version='1.0' encoding='UTF-8'?>
<!-- Schema file written by PDE -->
<schema targetNamespace="com.glory.mes.wip" xmlns="http://www.w3.org/2001/XMLSchema">
<annotation>
      <appInfo>
         <meta.schema plugin="com.glory.mes.wip" id="byeqp.glc" name="ByEqp GLC Registry"/>
      </appInfo>
      <documentation>
         [Enter description of this extension point.]
      </documentation>
   </annotation>

   <element name="manager">
      <complexType>
         <attribute name="name" type="string" use="required">
            <annotation>
               <documentation>
                  扩展点名称
               </documentation>
            </annotation>
         </attribute>
         <attribute name="class" type="string" use="required">
            <annotation>
               <documentation>
                  扩展Action类名
               </documentation>
            </annotation>
         </attribute>
         <attribute name="managerClass" type="string" use="required">
            <annotation>
               <documentation>
                  扩展对应的管理类名称
               </documentation>
            </annotation>
         </attribute>
         <attribute name="isDefault" type="string">
            <annotation>
               <documentation>
                  是否默认功能，不存在客制化时，以默认功能为主
               </documentation>
            </annotation>
         </attribute>
         <attribute name="isCustomer" type="string">
            <annotation>
               <documentation>
                  是否客制化功能则以以客制化为主
               </documentation>
            </annotation>
         </attribute>
      </complexType>
   </element>

   <annotation>
      <appInfo>
         <meta.section type="since"/>
      </appInfo>
      <documentation>
         [Enter the first release in which this extension point appears.]
      </documentation>
   </annotation>

   <annotation>
      <appInfo>
         <meta.section type="examples"/>
      </appInfo>
      <documentation>
         [Enter extension point usage example here.]
      </documentation>
   </annotation>

   <annotation>
      <appInfo>
         <meta.section type="apiInfo"/>
      </appInfo>
      <documentation>
         [Enter API information here.]
      </documentation>
   </annotation>

   <annotation>
      <appInfo>
         <meta.section type="implementation"/>
      </appInfo>
      <documentation>
         [Enter information about supplied implementation of this extension point.]
      </documentation>
   </annotation>


</schema>
