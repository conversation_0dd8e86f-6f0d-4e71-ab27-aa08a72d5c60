Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Wip Plug-in
Bundle-SymbolicName: com.glory.mes.wip;singleton:=true
Bundle-Version: 8.4.0
Bundle-Activator: com.glory.mes.wip.Activator
Bundle-Vendor: GlorySoft
Require-Bundle: org.eclipse.ui;bundle-version="3.107.0",
 org.eclipse.core.runtime;bundle-version="3.11.0",
 org.eclipse.ui.forms;bundle-version="3.6.200",
 com.glory.framework.lib;bundle-version="8.4.0",
 com.glory.framework.runtime;bundle-version="8.4.0",
 com.glory.mes.prd;bundle-version="8.4.0",
 org.eclipse.ui.console;bundle-version="3.6.0",
 org.eclipse.jface.text;bundle-version="3.10.0",
 com.glory.mes.prd.designer;bundle-version="8.4.0",
 com.glory.edc;bundle-version="8.4.0",
 com.glory.mes.base;bundle-version="8.4.0",
 org.eclipse.nebula.widgets.nattable.core;bundle-version="1.6.0",
 org.eclipse.nebula.widgets.nattable.extension.glazedlists;bundle-version="1.6.0",
 org.eclipse.nebula.widgets.nattable.extension.poi;bundle-version="1.5.1",
 com.glory.mes.mm;bundle-version="8.4.0",
 org.eclipse.e4.ui.services,
 com.glory.common.excel;bundle-version="3.3.0",
 com.glory.framework.base;bundle-version="8.4.0",
 com.glory.mes.ras;bundle-version="8.4.0",
 org.apache.log4j;bundle-version="1.2.15",
 com.glory.common.context;bundle-version="8.4.0",
 javax.annotation;bundle-version="1.3.5",
 org.eclipse.nebula.cwt;bundle-version="1.1.0",
 javax.inject;bundle-version="1.0.0",
 org.apache.commons.lang;bundle-version="2.6.0"
Export-Package: com.glory.mes.wip,
 com.glory.mes.wip.batch,
 com.glory.mes.wip.byeqp,
 com.glory.mes.wip.byeqp.extensionpoint,
 com.glory.mes.wip.carrier,
 com.glory.mes.wip.changeshift.query,
 com.glory.mes.wip.changeshift.refer,
 com.glory.mes.wip.changeshift.to,
 com.glory.mes.wip.comp,
 com.glory.mes.wip.comp.cutting,
 com.glory.mes.wip.comp.hold,
 com.glory.mes.wip.comp.release,
 com.glory.mes.wip.custom,
 com.glory.mes.wip.custom.depend,
 com.glory.mes.wip.edc.offlinelot,
 com.glory.mes.wip.lot,
 com.glory.mes.wip.lot.action,
 com.glory.mes.wip.lot.backup,
 com.glory.mes.wip.lot.bank,
 com.glory.mes.wip.lot.bank.query,
 com.glory.mes.wip.lot.batch,
 com.glory.mes.wip.lot.batchrelease,
 com.glory.mes.wip.lot.bin.repeat.collection,
 com.glory.mes.wip.lot.bonding,
 com.glory.mes.wip.lot.changecmt,
 com.glory.mes.wip.lot.changehold,
 com.glory.mes.wip.lot.changeline,
 com.glory.mes.wip.lot.changelotid,
 com.glory.mes.wip.lot.changemo,
 com.glory.mes.wip.lot.changeparam,
 com.glory.mes.wip.lot.changeprocess,
 com.glory.mes.wip.lot.changeproperty,
 com.glory.mes.wip.lot.changeqty,
 com.glory.mes.wip.lot.condition,
 com.glory.mes.wip.lot.contextmenu,
 com.glory.mes.wip.lot.cut,
 com.glory.mes.wip.lot.cutting,
 com.glory.mes.wip.lot.defect,
 com.glory.mes.wip.lot.detail,
 com.glory.mes.wip.lot.disassembly,
 com.glory.mes.wip.lot.flow,
 com.glory.mes.wip.lot.glc.bylot,
 com.glory.mes.wip.lot.history,
 com.glory.mes.wip.lot.history.trackout,
 com.glory.mes.wip.lot.hold,
 com.glory.mes.wip.lot.identify,
 com.glory.mes.wip.lot.identify.manual,
 com.glory.mes.wip.lot.label,
 com.glory.mes.wip.lot.merge,
 com.glory.mes.wip.lot.movelocation,
 com.glory.mes.wip.lot.multicarrier,
 com.glory.mes.wip.lot.multicarrier.changecarrier,
 com.glory.mes.wip.lot.multicarrier.merge,
 com.glory.mes.wip.lot.multicarrier.scrap,
 com.glory.mes.wip.lot.multicarrier.split,
 com.glory.mes.wip.lot.multicarrier.unscrap,
 com.glory.mes.wip.lot.multihold,
 com.glory.mes.wip.lot.mylotlist,
 com.glory.mes.wip.lot.mylotlist.select.group,
 com.glory.mes.wip.lot.newpart,
 com.glory.mes.wip.lot.pack,
 com.glory.mes.wip.lot.pack.action,
 com.glory.mes.wip.lot.pack.relationquery,
 com.glory.mes.wip.lot.pack.scrap,
 com.glory.mes.wip.lot.pack.splitpack,
 com.glory.mes.wip.lot.pack.splitunpack,
 com.glory.mes.wip.lot.priority,
 com.glory.mes.wip.lot.processor,
 com.glory.mes.wip.lot.processor.batch,
 com.glory.mes.wip.lot.production.offline,
 com.glory.mes.wip.lot.provider,
 com.glory.mes.wip.lot.reassign,
 com.glory.mes.wip.lot.receive,
 com.glory.mes.wip.lot.release,
 com.glory.mes.wip.lot.replenish,
 com.glory.mes.wip.lot.reserved,
 com.glory.mes.wip.lot.revert,
 com.glory.mes.wip.lot.run.abort,
 com.glory.mes.wip.lot.run.bybatch,
 com.glory.mes.wip.lot.run.byeqp.buffer,
 com.glory.mes.wip.lot.run.byeqp.extensionpoint,
 com.glory.mes.wip.lot.run.byeqp.glc,
 com.glory.mes.wip.lot.run.byeqp.sorting,
 com.glory.mes.wip.lot.run.bylocation,
 com.glory.mes.wip.lot.run.bylot,
 com.glory.mes.wip.lot.run.bylot.glc,
 com.glory.mes.wip.lot.run.bylot.op,
 com.glory.mes.wip.lot.run.extensionpoints,
 com.glory.mes.wip.lot.run.manul.movenext,
 com.glory.mes.wip.lot.run.operation,
 com.glory.mes.wip.lot.run.track,
 com.glory.mes.wip.lot.run.track.extensionpoints,
 com.glory.mes.wip.lot.run.track.forms,
 com.glory.mes.wip.lot.run.track.listener,
 com.glory.mes.wip.lot.run.trackin,
 com.glory.mes.wip.lot.run.trackin.byschedule,
 com.glory.mes.wip.lot.run.trackin.carriercheck,
 com.glory.mes.wip.lot.run.trackin.combineeqp,
 com.glory.mes.wip.lot.run.trackin.consumablematerial,
 com.glory.mes.wip.lot.run.trackin.extensionpoints,
 com.glory.mes.wip.lot.run.trackin.multieqp,
 com.glory.mes.wip.lot.run.trackin.multisubeqp,
 com.glory.mes.wip.lot.run.trackin.subcapa,
 com.glory.mes.wip.lot.run.trackmove,
 com.glory.mes.wip.lot.run.trackmove.modal,
 com.glory.mes.wip.lot.run.trackout,
 com.glory.mes.wip.lot.run.trackout.bin.action,
 com.glory.mes.wip.lot.run.trackout.carrierbind,
 com.glory.mes.wip.lot.run.trackout.edc,
 com.glory.mes.wip.lot.run.trackout.identify.auto,
 com.glory.mes.wip.lot.run.trackout.identify.manual,
 com.glory.mes.wip.lot.run.trackout.multieqp,
 com.glory.mes.wip.lot.run.trackout.noscrap,
 com.glory.mes.wip.lot.run.trackout.simple,
 com.glory.mes.wip.lot.run.trackthold,
 com.glory.mes.wip.lot.schedule,
 com.glory.mes.wip.lot.scrap,
 com.glory.mes.wip.lot.scrap.supplement,
 com.glory.mes.wip.lot.ship,
 com.glory.mes.wip.lot.skip,
 com.glory.mes.wip.lot.sorting,
 com.glory.mes.wip.lot.sorting.abnormal,
 com.glory.mes.wip.lot.sorting.changecarrier,
 com.glory.mes.wip.lot.split,
 com.glory.mes.wip.lot.start,
 com.glory.mes.wip.lot.terminate,
 com.glory.mes.wip.lot.transferstate,
 com.glory.mes.wip.lot.unmerge,
 com.glory.mes.wip.lot.unscrap,
 com.glory.mes.wip.lot.unscrapnew,
 com.glory.mes.wip.lot.unship,
 com.glory.mes.wip.lot.unterminate,
 com.glory.mes.wip.mm.warehouseline,
 com.glory.mes.wip.mm.warehousepart,
 com.glory.mes.wip.pp.changewo,
 com.glory.mes.wip.pp.changewo.partflowdialog,
 com.glory.mes.wip.pp.outsource,
 com.glory.mes.wip.pp.po,
 com.glory.mes.wip.pp.reworkwo.start,
 com.glory.mes.wip.pp.schedule,
 com.glory.mes.wip.pp.wo,
 com.glory.mes.wip.pp.wo.bom,
 com.glory.mes.wip.pp.wo.excel,
 com.glory.mes.wip.pp.wo.form,
 com.glory.mes.wip.pp.wo.glc,
 com.glory.mes.wip.pp.wo.start,
 com.glory.mes.wip.pp.wo.start.bylot,
 com.glory.mes.wip.pp.wo.sub,
 com.glory.mes.wip.pp.wo.subbybom,
 com.glory.mes.wip.pp.wo.unstart,
 com.glory.mes.wip.query,
 com.glory.mes.wip.query.lot.bymlot,
 com.glory.mes.wip.query.mlot.bylot,
 com.glory.mes.wip.source.iqc,
 com.glory.mes.wip.util,
 com.glory.mes.wip.wafersource.lot.query,
 com.glory.mes.wip.wafersource.query,
 com.glory.mes.wip.wafersource.receive
Bundle-ActivationPolicy: lazy
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
Import-Package: com.google.common.base,
 org.apache.commons.lang;version="1.9.0",
 org.eclipse.e4.core.services.events,
 org.eclipse.e4.ui.model.application,
 org.eclipse.e4.ui.model.application.ui,
 org.eclipse.e4.ui.model.application.ui.basic,
 org.eclipse.e4.ui.workbench.modeling,
 org.eclipse.nebula.widgets.cdatetime,
 org.osgi.service.event;version="1.3.1"
Bundle-ClassPath: .
Automatic-Module-Name: com.glory.mes.wip
