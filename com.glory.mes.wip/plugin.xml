<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>
<plugin>
   <extension-point id="com.glory.mes.wip.byeqp" name="ByEqp" schema="schema/com.glory.mes.wip.byeqp.exsd"/>
   <extension-point id="com.glory.mes.wip.byeqp.glc" name="ByEqpGlc" schema="schema/com.glory.mes.wip.byeqp.glc.exsd"/>
   <extension-point id="com.glory.mes.wip.bystep" name="ByStep" schema="schema/com.glory.mes.wip.bystep.exsd"/> 
   <extension-point id="com.glory.mes.wip.contextmenu" name="ContextMenu" schema="schema/com.glory.mes.wip.contextmenu.exsd"/>
   <extension-point id="com.glory.mes.wip.lotproviders" name="SpcProvider" schema="schema/com.glory.mes.wip.lotproviders.exsd"/>
   <extension-point id="com.glory.mes.wip.track.forms" name="Action Registry" schema="schema/com.glory.mes.wip.track.forms.exsd"/>
   <extension-point id="com.glory.mes.wip.trackin.check" name="Action Registry" schema="schema/com.glory.mes.wip.trackin.check.exsd"/>
   <extension-point id="com.glory.mes.wip.track.dialogs" name="dialogs" schema="schema/com.glory.mes.wip.track.dialogs.exsd"/>
   <extension-point id="com.glory.mes.wip.operation.action" name="Operation Action" schema="schema/com.glory.mes.wip.operation.action.exsd"/>
   <extension
         point="com.glory.framework.runtime.server">
         <server
         	hostname="alm"
         	isDefault="false"
         	priority="2"
         	class="com.glory.framework.runtime.service.JBossServiceLocator">
			 <property name="prefix" value="ejb:MESwell/"></property>
			 <property name="suffix" value=""></property>
         </server>
    </extension>
    <extension
         point="com.glory.framework.runtime.server">
         <server
         	hostname="spc"
         	isDefault="false"
         	priority="2"
         	class="com.glory.framework.runtime.service.JBossServiceLocator">
			 <property name="prefix" value="ejb:MESwell/"></property>
			 <property name="suffix" value=""></property>
         </server>
    </extension>

	<extension
        point="com.glory.framework.runtime.service">
         <service 
         	class="com.glory.mes.wip.client.LotManager"
  		 	locator="wip/LotManagerBean!">
  	 	 </service> 
  	 	 <service 
         	class="com.glory.mes.wip.client.LotHistoryManager"
  		 	locator="wip/LotHistoryManagerBean!">
  	 	 </service> 
  	 	 <service 
         	class="com.glory.mes.wip.client.CarrierLotManager"
  		 	locator="wip/CarrierLotManagerBean!">
  	 	 </service> 	 	 
  	 	  <service 
         	class="com.glory.mes.wip.client.ComponentManager"
  		 	locator="wip/ComponentManagerBean!">
  	 	 </service> 	 	 
  	 	 <service 
         	class="com.glory.edc.client.EDCManager"
  		 	locator="edc/EDCManagerBean!">
  	 	 </service> 	 	 
  	 	 <service 
         	class="com.glory.mes.base.client.BASManager"
  		 	locator="base/BASManagerBean!">
  	 	 </service>  	 	 
  	 	 <service 
         	class="com.glory.mes.tcard.client.TCardManager"
  		 	locator="tcard/TCardManagerBean!">
  	 	 </service>	 	 
  	 	 <service 
         	class="com.glory.mes.pp.client.PpManager"
  		 	locator="wip/PpManagerBean!">
  	 	 </service> 	 	 
  	 	 <service 
         	class="com.glory.mes.base.client.MBASManager"
  		 	locator="mesbase/MBASManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.MLotManager"
  		 	locator="wip/MLotManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.mm.client.MMManager"
  		 	locator="mm/MMManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.SortingManager"
  		 	locator="wip/SortingManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.ConvertManager"
  		 	locator="wip/ConvertManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.OutsourceManager"
  		 	locator="wip/OutsourceManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.rms.client.RMSManager"
  		 	locator="rms/RMSManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.LotPrepareManager"
  		 	locator="wip/LotPrepareManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.LotProcedureManager"
  		 	locator="wip/LotProcedureManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.cdi.client.FlowCdiActionManager"
  		 	locator="wip/FlowCdiActionManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.FutureQueryManager"
  		 	locator="wip/FutureQueryManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.prd.client.FutureTimerManager"
  		 	locator="wip/FutureTimerManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.ConstraintManager"
  		 	locator="wip/ConstraintManagerBean!">
  	 	 </service>
  	 	 <service 
         	class="com.glory.mes.wip.client.MergeManager"
  		 	locator="wip/MergeManagerBean!">
  	 	 </service>
 	</extension> 
   
    <extension
         point="com.glory.mes.wip.byeqp">
         <manager 
         	name = "ByEqpDefaultPage"
         	eqpProcessMode = "Default"
         	class = "com.glory.mes.wip.byeqp.ByEqpDefaultPage"      	
         	formName = "WIPLotByEqpDefaultPage">
         </manager> 
         
         <manager 
         	name = "ByEqpBufferPage"
         	eqpProcessMode = "Buffer"
         	class = "com.glory.mes.wip.byeqp.buffer.ByEqpBufferPage"      	
         	formName = "WIPLotRunByEqpBufferPage">
         </manager> 
         
         <manager 
         	name = "ByEqpCarrierCleanPage"
         	eqpProcessMode = "Clean"
         	class = "com.glory.mes.wip.byeqp.carrierclean.ByEqpCarrierCleanPage"      	
         	formName = "WIPByEqpCarrierCleanPage">
         </manager>   
   </extension>
   
   <extension
         point="com.glory.mes.wip.byeqp.glc">
         <manager 
         	name = "ByEqpDefaultAction"
         	class = "com.glory.mes.wip.lot.run.byeqp.extensionpoint.DefaultAction"
         	managerClass="com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManagerDefault"
         	isDefault = "Y">
         </manager>   
         <manager
         	name = "ByEqpInternalBufferAction"
         	class = "com.glory.mes.wip.lot.run.byeqp.buffer.InternalBufferAction"
         	managerClass="com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManagerDefault">
         </manager>
         <manager
         	name = "ByEqpSortingJobAction"
         	class = "com.glory.mes.wip.lot.run.byeqp.sorting.SortingJobAction"
         	managerClass="com.glory.mes.wip.lot.run.byeqp.sorting.SortingJobByEqpManagerDefault">
         </manager>
   </extension>
   
   <extension
         point="com.glory.mes.wip.bystep">
         <manager 
         	name="ByStepDefaultPage"
         	class="com.glory.mes.wip.bystep.ByStepDefaultPage"
         	stepUseCategory="Default"
         	formName="WIPLotByStepDefaultPage">
         </manager>     
   </extension>
   
	<extension
         point="com.glory.framework.base.managers">
         <manager
         	object="com.glory.mes.wip.model.Lot"
         	class="com.glory.mes.wip.lot.schedule.ScheduleLotProperties">
         </manager>
        <!--<manager
         	object="com.glory.mes.wip.model.FirstInspect"
         	class="com.glory.mes.wip.lot.eqpfirstinspect.FirstInspectProperties">
         </manager>
         <manager
         	object="com.glory.mes.wip.model.FirstInspectLot"
         	class="com.glory.mes.wip.lot.firstinspect.FirstInspectLotProperties">
         </manager>-->
    </extension>
    
    <extension 
         point="com.glory.framework.base.wizard">
          <!-- WorkOrderBom(工单HasBomNoSource向导页面) -->
         <page
         	name="workOrderBomSelectPage"
	      	category="WorkOrderBom"
	       	class="com.glory.mes.wip.pp.wo.bom.WorkOrderBomSelectPage"
	       	defaultDirect="workOrderBomShowPage"
	       	isStart="true">
         </page>
         <page
         	name="workOrderBomShowPage"
	      	category="WorkOrderBom"
	       	class="com.glory.mes.wip.pp.wo.bom.WorkOrderBomShowPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- ByLot时UnscrapNew向导页  -->
         <wizard
         	name="UnScrapNew"
         	pagecategory="UnScrapNew"
         	class="com.glory.mes.wip.lot.unscrapnew.UnScrapNewWizard">
         </wizard>
	     <page
	     	 name="unScrapNewStartPage"
	     	 category="UnScrapNew"
	     	 class="com.glory.mes.wip.lot.unscrapnew.UnScrapNewStartPage"
	     	 defaultDirect="finish"
	     	 isStart="true">
	     </page>
	     <page
         	name="unScrapNewCompUnitPage"
         	category="UnScrapNew"
         	class="com.glory.mes.wip.lot.unscrapnew.UnScrapNewComponentPage"
         	defaultDirect="finish">
         </page>
         <page
         	name="unScrapNewSchedulePage"
         	category="UnScrapNew"
         	class="com.glory.mes.wip.lot.unscrapnew.UnScrapNewSchedulePage"
         	defaultDirect="finish">
         </page>
         <page
         	name="unScrapNewPartPage"
         	category="UnScrapNew"
         	class="com.glory.mes.wip.lot.unscrapnew.UnScrapNewPartPage"
         	defaultDirect="finish">
         </page>
         <page
         	name="unScrapNewLotListPage"
         	category="UnScrapNew"
         	class="com.glory.mes.wip.lot.unscrapnew.UnScrapNewLotListPage"
         	defaultDirect="finish">
         </page>
         
         <!-- DefaultTrackIn 向导页 -->
         <wizard
         	name="DefaultTrackIn"
         	pagecategory="DefaultTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.TrackInWizard">
         </wizard>
         <page
         	name="batchLotList"
         	category="DefaultTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.BatchTrackInLotListPage"
         	adFormName="TrackInBatchLotPage"
         	defaultDirect="selectEquipment"
         	isStart="true">
         </page>
         <page
         	name="selectEquipment"
         	category="DefaultTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.SelectEquipmentGlcPage"
         	adFormName="TrackInSelectEquipmentPage"
         	defaultDirect="showStepOperation">
         </page>
         <page
         	name="showStepOperation"
         	category="DefaultTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationGlcPage"
         	adFormName="TrackInShowStepOperationPage"
         	defaultDirect="finish">
         </page>
         
         <!-- SubCapaTrackIn 向导页 -->
         <wizard
         	name="SubCapaTrackIn"
         	pagecategory="SubCapaTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.TrackInWizard">
         </wizard>
         <page
         	name="selectEquipment"
         	category="SubCapaTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.subcapa.SelectEquipmentSubCapaPage"
         	defaultDirect="showStepOperation"
         	isStart="true">
         </page>
         <page
         	name="showStepOperation"
         	category="SubCapaTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	defaultDirect="finish">
         </page>
         
          <!-- EquipmentCombineTrackIn 向导页 -->
         <wizard
         	name="EquipmentCombineTrackIn"
         	pagecategory="EquipmentCombineTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.TrackInWizard">
         </wizard>
         <page
         	name="selectEquipment"
         	category="EquipmentCombineTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.combineeqp.SelectCombineEquipmentPage"
         	defaultDirect="showStepOperation"
         	isStart="true">
         </page>
         <page
         	name="showStepOperation"
         	category="EquipmentCombineTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	defaultDirect="finish">
         </page>
         
         <!-- SubEqpTrackIn 向导页 -->
         <wizard
         	name="SubEqpTrackIn"
         	pagecategory="SubEqpTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.TrackInWizard">
         </wizard>
         <page
         	name="selectEquipment"
         	category="SubEqpTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.multisubeqp.MultiSubEquipmentPage"
         	adFormName="TrackInMultiSubEquipmentPage"
         	defaultDirect="showStepOperation"
         	isStart="true">
         </page>
         <page
         	name="showStepOperation"
         	category="SubEqpTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	adFormName="TrackInShowStepOperationPage"
         	defaultDirect="finish">
         </page>
         
         <!-- BatchTrackIn 向导页 -->
         <wizard
         	name="BatchTrackIn"
         	pagecategory="BatchTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.TrackInWizard">
         </wizard>
         <page
         	name="batchLotList"
         	category="BatchTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.BatchTrackInLotListPage"
         	defaultDirect="selectEquipment"
         	isStart="true">
         </page>
         <page
         	name="selectEquipment"
         	category="BatchTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.SelectEquipmentPage"
         	defaultDirect="finish">
         </page>
         
         <!-- MultipleByScheduleTrackIn 向导页 -->
         <wizard
            name="MultipleTrackIn"
            pagecategory="MultipleTrackIn"
            class="com.glory.mes.wip.lot.run.trackin.TrackInWizard">
         </wizard>
         <page
            name="selectTrackInLot"
            category="MultipleTrackIn"
            class="com.glory.mes.wip.lot.run.trackin.byschedule.LotsBySchedulePage"
            defaultDirect="selectEquipment"
            isStart="true">
         </page>
         <page
            name="selectEquipment"
            category="MultipleTrackIn"
            class="com.glory.mes.wip.lot.run.trackin.SelectEquipmentPage"
            defaultDirect="showStepOperation">
         </page>
         <page
         	name="showStepOperation"
         	category="MultipleTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	defaultDirect="finish">
         </page>
         
         <!-- MultiEqpTrackIn 向导页（Track In时输入进站数量，TrackOut时带出） -->
         <wizard
         	name="MultiEqpTrackIn"
         	pagecategory="MultiEqpTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.multieqp.MultiEqpTrackInWizard">
         </wizard>
         <page
         	name="selectEquipment"
         	category="MultiEqpTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.multieqp.MultiEqpTrackInPage"
         	defaultDirect="showStepOperation"
         	isStart="true">
         </page>
         <page
         	name="showStepOperation"
         	category="MultiEqpTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	defaultDirect="finish">
         </page>
         
         <!-- MultiEqpNoNumTrackIn 向导页 -->
         <wizard
         	name="MultiEqpNoNumTrackIn"
         	pagecategory="MultiEqpNoNumTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.multieqp.MultiEqpTrackInWizard">
         </wizard>
         <page
         	name="selectEquipment"
         	category="MultiEqpNoNumTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.multieqp.MultiEqpNoNumTrackInPage"
         	defaultDirect="showStepOperation"
         	isStart="true">
         </page>
         <page
         	name="showStepOperation"
         	category="MultiEqpNoNumTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	defaultDirect="finish">
         </page>
       
         <!-- DefaultTrackOut 向导页 -->
         <wizard
         	name="DefaultTrackOut"
         	pagecategory="DefaultTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.TrackOutWizard">
         </wizard>
         <page
         	name="startPage"
	      	category="DefaultTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutStartGlcPage"
	       	adFormName="TrackOutStartPage"
	       	defaultDirect="trackOutListPage"
	        isStart="true">
         </page>
         <page 
         	name="scrapPage"
	      	category="DefaultTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutScrapGlcPage"
	       	adFormName="TrackOutScrapPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="reworkPage"
	      	category="DefaultTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutReworkGlcPage"
	       	adFormName="TrackOutReworkPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="DefaultTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListGlcPage"
	       	adFormName="TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- EdcTrackOut 向导页 -->
         <wizard
         	name="EdcTrackOut"
         	pagecategory="EdcTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.edc.EdcTrackOutWizard">
         </wizard>
         <page
         	name="edcPage"
	      	category="EdcTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.edc.EdcTrackOutPage"
	       	defaultDirect="startPage"
	       	isStart="true">
	     </page>
         <page
         	name="startPage"
	      	category="EdcTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutStartPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page 
         	name="scrapPage"
	      	category="EdcTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutScrapPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="reworkPage"
	      	category="EdcTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutReworkPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="EdcTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- SimpleTrackOut 向导页(没有返工和报废) -->
         <wizard
         	name="SimpleTrackOut"
         	pagecategory="SimpleTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.TrackOutWizard">
         </wizard>
         <page
         	name="startPage"
	      	category="SimpleTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.simple.SimpleTrackOutStartPage"
	       	defaultDirect="trackOutListPage"
	        isStart="true">
         </page>
         <page
         	name="trackOutListPage"
	      	category="SimpleTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- NoScrapTrackOut 向导页(没有报废) -->
         <wizard
         	name="NoScrapTrackOut"
         	pagecategory="NoScrapTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.TrackOutWizard">
         </wizard>
         <page
         	name="startPage"
	      	category="NoScrapTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.noscrap.NoScrapTrackOutStartPage"
	       	defaultDirect="trackOutListPage"
	        isStart="true">
         </page>
         <page
         	name="reworkPage"
	      	category="NoScrapTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutReworkPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="NoScrapTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- Track TrackOut Hold Lot 向导页 -->
         <wizard
         	name="TrackTHold"
         	pagecategory="TrackTHold"
         	class="com.glory.mes.wip.lot.run.trackthold.TrackTHoldWizard">
         </wizard>
         <page
         	name="startPage"
	      	category="TrackTHold"
	       	class="com.glory.mes.wip.lot.run.trackthold.TrackTHoldStartPage"
	       	defaultDirect="trackOutListPage"
	        isStart="true">
         </page>
         <page 
         	name="scrapPage"
	      	category="TrackTHold"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutScrapPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="reworkPage"
	      	category="TrackTHold"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutReworkPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="TrackTHold"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- MultiEqpTrackOut 向导页 -->
         <wizard
         	name="MultiEqpTrackOut"
         	pagecategory="MultiEqpTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.multieqp.MultiEqpTrackOutWizard">
         </wizard>
         <page
         	name="startPage"
	      	category="MultiEqpTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.multieqp.MultiEqpTrackOutStartPage"
	       	defaultDirect="trackOutListPage"
	        isStart="true">
         </page>
         <page 
         	name="scrapPage"
	      	category="MultiEqpTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.multieqp.MultiEqpScrapPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="reworkPage"
	      	category="MultiEqpTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.multieqp.MultiEqpReworkPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="MultiEqpTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- DefaultTrackMove 向导页 -->
         <wizard
         	name="DefaultTrackMove"
         	pagecategory="DefaultTrackMove"
         	class=" com.glory.mes.wip.lot.run.trackmove.TrackMoveWizard">
         </wizard>
         <page
         	name="selectEquipment"
         	category="DefaultTrackMove"
         	class="com.glory.mes.wip.lot.run.trackin.SelectEquipmentPage"
         	defaultDirect="lotListPage"
         	isStart="true">
         </page>
         <page
         	name="lotListPage"
	      	category="DefaultTrackMove"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutStartPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page 
         	name="scrapPage"
	      	category="DefaultTrackMove"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutScrapPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="reworkPage"
	      	category="DefaultTrackMove"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutReworkPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="DefaultTrackMove"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         <!-- ModuleTrackMove 向导页 -->
         <wizard
         	name="ModuleTrackMove"
         	pagecategory="ModuleTrackMove"
         	class=" com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalWizard">
         </wizard>
         <page
         	name="moduleTrackMovePage"
	      	category="ModuleTrackMove"
	       	class="com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalPage"
	       	defaultDirect="finish"
	       	isStart="true">
         </page>
     		
          <!-- MarkTrackOutWizard 向导页(手动输入WaferId) -->
          <wizard
         	name="MarkTrackOut"
         	pagecategory="MarkTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.TrackOutWizard">
         </wizard>
         <page
         	name="ManualIdentifyTrackOutPage"
         	category="MarkTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.identify.manual.ManualIdentifyTrackOutPage"
         	defaultDirect="trackOutListPage"
         	isStart="true">
         </page>
          <page
         	name="trackOutListPage"
	      	category="MarkTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
         
         <!-- MarkTrackOutWizard 向导页(自动输入WaferId) -->
         <wizard
         	name="AutoIdentifyTrackOut"
         	pagecategory="AutoIdentifyTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.TrackOutWizard">
         </wizard>
         <page
         	name="AutoIdentifyTrackOutPage"
         	category="AutoIdentifyTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.identify.auto.AutoIdentifyTrackOutPage"
         	defaultDirect="trackOutListPage"
         	isStart="true">
         </page>
          <page
         	name="trackOutListPage"
	      	category="AutoIdentifyTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
                  
         <!-- ByEqp时Abort向导页 -->
         <wizard
         	name="DefaultAbort"
         	pagecategory="DefaultAbort"
         	class="com.glory.mes.wip.lot.run.abort.AbortWizard">
         </wizard>
         <page
         	name="batchLotSelect"
	      	category="DefaultAbort"
	       	class="com.glory.mes.wip.lot.run.abort.AbortBatchLotSelectGlcPage"
	       	adFormName="AbortBatchLotSelectPage"
	       	defaultDirect="finish"
	       	isStart="true">
         </page>
         
         <!-- MultiEqpAbort向导页 -->
         <wizard
         	name="MultiEqpAbort"
         	pagecategory="MultiEqpAbort"
         	class="com.glory.mes.wip.lot.run.abort.multieqp.MultiEqpAbortWizard">
         </wizard>
         <page
         	name="batchLotSelect"
	      	category="MultiEqpAbort"
	       	class="com.glory.mes.wip.lot.run.abort.multieqp.MultiEqpAbortSelectPage"
	       	defaultDirect="finish"
	       	isStart="true">
         </page>
          
         <!-- ByLot时MoveNext向导页 -->
         <page
         	name="showStepOperation"
         	category="ByLotMoveNext"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	defaultDirect="selectEquipment"
         	isStart="true">
         </page>      
                           
          <!-- BinActionTrackOut(Bin分档动作处理出站页面) -->
         <wizard
         	name="BinActionTrackOut"
         	pagecategory="BinActionTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.bin.action.BinActionTrackOutWizard">
         </wizard>
         <page
         	name="startPage"
	      	category="BinActionTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.bin.action.BinActionTrackOutStartPage"
	       	defaultDirect="trackOutListPage"
	       	isStart="true">
         </page>
         <page
         	name="trackOutListPage"
	      	category="BinActionTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
		 
		 <!-- CarriersCherkTrackIn 向导页 -->
         <wizard
         	name="CarriersCherkTrackIn"
         	pagecategory="CarriersCherkTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.TrackInWizard">
         </wizard>
         <page
         	name="selectEquipment"
         	category="CarriersCherkTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.SelectEquipmentPage"
         	defaultDirect="carriersCherk"
         	isStart="true">
         </page>
         <page
         	name="carriersCherk"
         	category="CarriersCherkTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.carriercheck.CarriersCheckPage"
         	defaultDirect="showStepOperation">
         </page>
         <page
         	name="showStepOperation"
         	category="CarriersCherkTrackIn"
         	class="com.glory.mes.wip.lot.run.trackin.ShowStepOperationPage"
         	defaultDirect="finish">
         </page>
         
         <!-- CarriersBindTrackOut 向导页 -->
         <wizard
         	name="CarriersBindTrackOut"
         	pagecategory="CarriersBindTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.TrackOutWizard">
         </wizard>
         
         <page
         	name="startPage"
	      	category="CarriersBindTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.carrierbind.CarriersBindStartPage"
	       	defaultDirect="carriersBindPage"
	        isStart="true">
         </page>
         <page 
         	name="scrapPage"
	      	category="CarriersBindTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.carrierbind.CarriersBindScrapPage"
	       	defaultDirect="carriersBindPage">
         </page>
         <page
         	name="reworkPage"
	      	category="CarriersBindTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.carrierbind.CarriersBindReworkPage"
	       	defaultDirect="carriersBindPage">
         </page>
         <page
         	name="carriersBindPage"
	      	category="CarriersBindTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.carrierbind.CarriersBindPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="CarriersBindTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
      
       <!-- LotCompAssignCarrierTrackOut 向导页 -->
         <wizard
         	name="LotCompAssignCarrierTrackOut"
         	pagecategory="LotCompAssignCarrierTrackOut"
         	class="com.glory.mes.wip.lot.run.trackout.assigncarrier.LotCompAssignCarrierWizard">
         </wizard>
         <page
         	name="startPage"
	      	category="LotCompAssignCarrierTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.assigncarrier.LotCompAssignCarrierStartPage"
	       	defaultDirect="lotCompAssignCarrierPage"
	        isStart="true">
         </page>
         <page
         	name="lotCompAssignCarrierPage"
	      	category="LotCompAssignCarrierTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.assigncarrier.LotCompAssignCarrierPage"
	       	defaultDirect="trackOutListPage">
         </page>
         <page
         	name="trackOutListPage"
	      	category="LotCompAssignCarrierTrackOut"
	       	class="com.glory.mes.wip.lot.run.trackout.TrackOutLotListPage"
	       	defaultDirect="finish">
         </page>
                 
    </extension>
    
  	<extension
         point="com.glory.framework.base.tree">
         <manager 
         	category="STAGE"
         	name = "L1"
         	class="com.glory.framework.base.entitymanager.tree.ParentChildRowEditorPage"
         	table="PRDStep">
         </manager>  
         <manager 
         	category="STAGE"
         	name = "L2"
         	class="com.glory.framework.base.entitymanager.tree.ParentChildRowEditorPage"
         	table="PRDStep">
         </manager>         
   </extension>
   
 	<extension
         point="com.glory.mes.wip.track.forms">
         <form
         	type="selectEquipment"
         	class="com.glory.mes.wip.lot.run.track.forms.SelectEquipmentForm">
         </form>
         <form
         	type="stepAttribute"
         	class="com.glory.mes.wip.lot.run.track.forms.StepAttributeForm">
         </form>
         <form
         	type="stepFutureNote"
         	class="com.glory.mes.wip.lot.run.track.forms.StepFutureNoteForm">
         </form>
         <form
         	type="stepOperationIn"
         	class="com.glory.mes.wip.lot.run.track.forms.StepOperationForm">
         </form>
         <form
         	type="stepOperationOut"
         	class="com.glory.mes.wip.lot.run.track.forms.StepOperationForm">
         </form>
          <form
         	type="scrapQty"
         	class="com.glory.mes.wip.lot.run.track.forms.ScrapQtyUnitForm">
         </form>
         <form
         	type="scrapComponent"
         	class="com.glory.mes.wip.lot.run.track.forms.ScrapComponentUnitForm">
         </form>
         <form
               class="com.glory.mes.wip.lot.run.track.forms.ConsoleLogForm"
               type="consoleLogForm">
         </form>
         <form
               class="com.glory.mes.wip.lot.run.track.forms.TrackTitleForm"
               type="trackTitleForm">
         </form>
         <form
               class="com.glory.mes.wip.lot.run.track.forms.LotTrackForm"
               type="lotTrackForm">
         </form>
         <form
               class="com.glory.mes.wip.lot.run.track.forms.MLotTrackForm"
               type="mLotTrackForm">
         </form>
    </extension>
    
    <extension
         point="com.glory.framework.base.images">
      <image
            id="schedule"
            src="icons/schedule.gif">
      </image>
      <image
            id="complete"
            src="icons/schedule.gif">
      </image>
      
      <image
            id="unschedule"
            src="icons/unschedule.gif">
      </image>
      <image
            id="start"
            src="icons/start.gif">
      </image>
      <image
            id="idendity"
            src="icons/identify.gif">
      </image>
      <image
            id="trackin"
            src="icons/trackin.gif">
      </image>
      <image
            id="trackout"
            src="icons/trackout.gif">
      </image>
      <image
            id="trackmove"
            src="icons/trackmove.png">
      </image>
      <image
            id="hold"
            src="icons/hold.gif">
      </image>
      <image
            id="release"
            src="icons/release.gif">
      </image>
      <image
            id="merge"
            src="icons/merge.gif">
      </image>
       <image
            id="split"
            src="icons/split.gif">
      </image>
      <image
            id="ship"
            src="icons/ship.png">
      </image>
       <image
            id="unship"
            src="icons/ship.png">
      </image>
      <image
            id="scrap"
            src="icons/scrap.gif">
      </image>
      <image
            id="unscrap"
            src="icons/unscrap.gif">
      </image>
       <image
            id="terminate"
            src="icons/terminate.gif">
      </image>
       <image
            id="unterminate"
            src="icons/unterminate.gif">
      </image>
      <image
            id="newpart"
            src="icons/newpart.gif">
      </image>
      <image
            id="trackin-dialog"
            src="icons/trackin-dialog.png">
      </image>
      <image
            id="abort-dialog"
            src="icons/abort-dialog.png">
      </image>
      <image
            id="trackout-dialog"
            src="icons/trackout-dialog.png">
      </image>
      <image
            id="area"
            src="icons/area.gif">
      </image>
      <image
            id="step"
            src="icons/step.gif">
      </image>
      <image
            id="stage"
            src="icons/stage.gif">
      </image>
      <image
            id="currentstep"
            src="icons/currentstep.gif">
      </image>
      <image
            id="selecteqp-dialog"
            src="icons/selecteqp-dialog.png">
      </image>
      <image
            id="monitor"
            src="icons/monitor.gif">
      </image>
      <image
            id="dcop"
            src="icons/dcop.gif">
      </image>
      <image
            id="abort"
            src="icons/abort.gif">
      </image>
      <image
            id="console"
            src="icons/console.gif">
      </image>
      <image
            id="run"
            src="icons/run.gif">
      </image>
      <image
            id="standby"
            src="icons/standby.gif">
      </image>
      <image
            id="erun"
            src="icons/erun.gif">
      </image>
      <image
            id="sdown"
            src="icons/sdown.gif">
      </image>
      <image
            id="udown"
            src="icons/udown.gif">
      </image>
      <image
            id="odown"
            src="icons/odown.gif">
      </image>
      <image
            id="steptimer"
            src="icons/timer_end.jpg">
      </image>
      <image
            id="endstep"
            src="icons/timer_end.jpg">
      </image>
      <image
            id="startstep"
            src="icons/time_start.gif">
      </image>
      <image
            id="eqp"
            src="icons/eqp.gif">
      </image>
      <image
            id="where"
            src="icons/where.gif">
      </image>
      <image
            id="movenext"
            src="icons/movenext.gif">
      </image>
      <image
            id="tcard"
            src="icons/tcard.gif">
      </image>
      <image
            id="label"
            src="icons/label.png">
      </image>
      <image
            id="common-dialog"
            src="icons/common_dialog.png">
      </image>
      <image
            id="select"
            src="icons/select.gif">
      </image>
      <image
            id="free"
            src="icons/free.gif">
      </image>
      <image
            id="if"
            src="icons/if.gif">
      </image>
      <image
            id="else"
            src="icons/else.gif">
      </image>
      <image
            id="endif"
            src="icons/endif.gif">
      </image>
      <image
            id="bylot"
            src="icons/bylot.png">
      </image>
      <image
            id="hold-lot"
            src="icons/hold-lot.png">
     </image>
      <image
            id="release-lot"
            src="icons/release-lot.png">
     </image>
     <image
            id="split-lot"
            src="icons/split-lot.png">
     </image>
     <image
            id="merge-lot"
            src="icons/merge-lot.png">
     </image>
     <image
            id="ship-lot"
            src="icons/ship-lot.png">
     </image>
     <image
            id="scrap-lot"
            src="icons/scrap-lot.png">
     </image>
     <image
            id="terminate-lot"
            src="icons/terminate-lot.png">
     </image>
     <image
            id="transation-history"
            src="icons/transation-history.png">
     </image>
     <image
            id="lot-detail"
            src="icons/lot-detail.png">
     </image>
     <image
            id="wip-query"
            src="icons/wip-query.png">
     </image>
     <image
            id="unship-lot"
            src="icons/unship-lot.png">
     </image>
     <image
            id="unscrap-lot"
            src="icons/unscrap-lot.png">
     </image>
     <image
            id="unterminate-lot"
            src="icons/unterminate-lot.png">
     </image>
     <image
            id="change-lot-info"
            src="icons/change-lot-info.png">
     </image>
     <image
            id="skip-lot"
            src="icons/skip-lot.gif">
     </image>
     <image
            id="unskip-lot"
            src="icons/unskip-lot.gif">
     </image>
     <image
            id="production_order"
            src="icons/production_order.png">
     </image>
     <image
            id="receive"
            src="icons/receive.gif">
     </image>
     <image
            id="newlot_start"
            src="icons/newlot_start.png">
     </image>
     <image
            id="return_lot"
            src="icons/return_lot.gif">
     </image>
     <image
            id="move_to_loc"
            src="icons/move_to_loc.gif">
     </image>
     <image
            id="rework"
            src="icons/rework.png">
     </image>
      <image
            id="future_search"
            src="icons/future_search.gif">
     </image>
     <image
            id="bonding"
            src="icons/bonding.gif">
     </image>
     <image
            id="unbonding"
            src="icons/unbonding.gif">
     </image>
     <image
            id="modify"
            src="icons/modify.gif">
     </image>
     <image
            id="returnto"
            src="icons/returnto.gif">
     </image>
     <image
            id="selected"
            src="icons/select.png">
      </image>
      <image
            id="identify"
            src="icons/identify.png">
      </image>
           
      <image
            id="big-trackin"
            src="icons/big-trackin.png">
      </image>
      <image
            id="big-trackout"
            src="icons/big-trackout.png">
      </image>
      <image
            id="big-abort"
            src="icons/big-abort.png">
      </image>
      <image
            id="big-close"
            src="icons/big-close.png">
      </image>
      <image
            id="big-refresh"
            src="icons/big-refresh.png">
      </image>
      <image
            id="big-fullscreen"
            src="icons/big-fullscreen.png">
      </image>
      <image
            id="big-unfullscreen"
            src="icons/big-unfullscreen.png">
      </image>
      <image
            id="background-blue"
            src="icons/blue.png">
      </image>
      <image
            id="big-equipment"
            src="icons/big-equipment.png">
      </image>
      <image
            id="big-user"
            src="icons/big-user.png">
      </image>
      <image
            id="big-step"
            src="icons/big-step.png">
      </image>
       <image
            id="subapp_wip"
            src="icons/subapp_wip.png">
       </image>
       <image
            id="subapp_wip_active"
            src="icons/subapp_wip_active.png">
       </image>
       <image
            id="sampling"
            src="icons/sampling.jpg">
       </image>
       <image
            id="assign"
            src="icons/assign.gif">
       </image>
       <image
            id="deassign"
            src="icons/deassign.gif">
       </image>
       <image
            id="outsource"
            src="icons/outsource.gif">
       </image>
       <image
            id="outsource_complete"
            src="icons/outsource_complete.gif">
       </image>
       <image
            id="disassembly"
            src="icons/disassembly.png">
       </image>
       <image
            id="wo_bind_eqp"
            src="icons/wo_bind_eqp.png">
       </image>
       <image
            id="supplement"
            src="icons/supplement.png">
       </image>
       
       <image
            id="lotstatus_no_hold"
            src="icons/lotstatus/holdstate/no_onhold.png">
       </image>
       <image
            id="lotstatus_hold"
            src="icons/lotstatus/holdstate/onhold.png">
       </image>
       <image
            id="lotstatus_priority_1"
            src="icons/lotstatus/priority/priority_1.png">
       </image>
       <image
            id="lotstatus_priority_2"
            src="icons/lotstatus/priority/priority_2.png">
       </image>
       <image
            id="lotstatus_priority_3"
            src="icons/lotstatus/priority/priority_3.png">
       </image>
       <image
            id="lotstatus_priority_4"
            src="icons/lotstatus/priority/priority_4.png">
       </image>
       <image
            id="lotstatus_priority_5"
            src="icons/lotstatus/priority/priority_5.png">
       </image>
       <image
            id="lotstatus_priority_6"
            src="icons/lotstatus/priority/priority_6.png">
       </image>
       <image
            id="lotstatus_priority_7"
            src="icons/lotstatus/priority/priority_7.png">
       </image>
       <image
            id="lotstatus_priority_8"
            src="icons/lotstatus/priority/priority_8.png">
       </image>
       <image
            id="lotstatus_priority_9"
            src="icons/lotstatus/priority/priority_9.png">
       </image>
       <image
            id="lotstatus_priority_10"
            src="icons/lotstatus/priority/priority_10.png">
       </image>
       <image
            id="lotstatus_priority_unknown"
            src="icons/lotstatus/priority/unknown.gif">
       </image>
       <image
            id="lotstatus_state_wait"
            src="icons/lotstatus/state/waitting.png">
       </image>
       <image
            id="lotstatus_state_disp"
            src="icons/lotstatus/state/disp.png">
       </image>
       <image
            id="lotstatus_state_run"
            src="icons/lotstatus/state/running.png">
       </image>
       <image
            id="lotstatus_state_unknown"
            src="icons/lotstatus/state/unknown.png">
       </image>
       <image
            id="reset"
            src="icons/reset.png">
       </image>
       <image
            id="bankin-lot"
            src="icons/bankin-lot.png">
       </image>
       <image
            id="bankout-lot"
            src="icons/bankout-lot.png">
       </image>
       
       <image
            id="header-text-lot"
            src="icons/header-text-lot.png">
       </image>
       <image
            id="header-text-op"
            src="icons/header-text-op.png">
       </image>
       <image
            id="header-text-carrier"
            src="icons/header-text-carrier.png">
       </image>
      <image
            id="new_wo_start"
            src="icons/new_wo_start.gif">
       </image>
      <image
            id="batch_merge"
            src="icons/batch_merge.png">
      </image>
      <image
            id="batch_split"
            src="icons/batch_split.png">
     </image>
     <image
            id="release_comp"
            src="icons/release_comp.png">
     </image>
     <image
            id="rework_lot"
            src="icons/rework_lot.png">
     </image>
     <image
            id="recipe_comp"
            src="icons/recipe_comp.gif">
     </image>
     <image
            id="production-history"
            src="icons/production-history.png">
     </image>
     <image
            id="comp-history"
            src="icons/comp-history.png">
     </image>
     <image
            id="wafer-history"
            src="icons/wafer-history.png">
     </image>
     <image
            id="bank-query"
            src="icons/bank-query.png">
     </image>
     <image
            id="mylot-query"
            src="icons/mylot-query.gif">
     </image>
          <image
            id="carrier-change"
            src="icons/carrier-change.png">
     </image>
          <image
            id="carrier-split"
            src="icons/carrier-split.gif">
     </image>
          <image
            id="carrier-merge"
            src="icons/carrier-merge.gif">
     </image>
          <image
            id="job-query"
            src="icons/job-query.gif">
     </image>
          <image
            id="job-alm"
            src="icons/job-alm.png">
     </image>
          <image
            id="job-hisquery"
            src="icons/job-hisquery.png">
     </image>
     
     <image
            id="auto-cdi"
            src="icons/auto-cdi.png">
      </image>
    </extension>
    
    <extension
         point="com.glory.mes.wip.contextmenu">
      <action
            id="Hold"
            seq="10"
            authority="Wip.HoldLot">
      </action>
      <action
            id="Release"
            seq="15"
            authority="Wip.ReleaseLot">
      </action>
      <action
            id="Split"
            seq="20"
            authority="Wip.SplitLot">
      </action>
      <action
            id="Merge"
            seq="25"
            authority="Wip.MergeLot">
      </action>
      <action
            id="Scrap"
            seq="30"
            authority="Wip.Scrap">
      </action>
      <action
            id="Detail"
            seq="40"
            authority="Wip.LotDetail">
      </action>
      <action
            id="History"
            seq="50"
            authority="Wip.LotHistory">
      </action>
      <action
            id="LotTrack"
            seq="60"
            authority="Wip.LotTrack">
      </action>
      <action
            id="ByEqpGlc"
            seq="70"
            authority="Wip.ByEqpGlc">
      </action>
      <action
            id="LotHisTrackOut"
            seq="80"
            authority="Wip.LotHisTrackOut">
      </action>
    </extension>
    <extension
          point="com.glory.mes.wip.track.dialogs">
       <dialog
             type="DefaultTrack">
          <header
                heighthint="70"
                xgrid="1">
          </header>
          <headerform
                colspan="1"
                id="trackTitleForm"
                rowspan="1">
          </headerform>
          <body
                xgrid="1">
          </body>
          <bodyform
                colspan="1"
                fonthight="20"
                id="lotTrackForm"
                rowspan="1">
          </bodyform>
          <bodyform
                colspan="1"
                heighthint="150"
                id="consoleLogForm"
                rowspan="1">
          </bodyform>
       </dialog>
       <dialog
             type="TrackStep">
          <header
                heighthint="70"
                xgrid="1">
          </header>
          <headerform
                colspan="1"
                id="trackTitleForm"
                rowspan="1">
          </headerform>
          <body
                xgrid="2">
          </body>
          <bodyform
                colspan="1"
                fonthight="20"
                id="lotTrackForm"
                rowspan="1">
          </bodyform>
          <bodyform
                colspan="1"
                id="stepOperationOut"
                rowspan="2"
                widthhint="400"
                heighthint="500">
          </bodyform>
          <bodyform
                colspan="1"
                heighthint="150"
                id="consoleLogForm"
                rowspan="1">
          </bodyform>
       </dialog>
    </extension>
    
    <extension
        point="com.glory.framework.base.dialogs">
         <manager
         	object="com.glory.mes.wip.lot.run.track.TrackExtensionDialog"
         	class="com.glory.mes.wip.lot.run.track.TrackExtensionDialog">
         </manager>
  	</extension>
    
	 <extension
         point="com.glory.framework.base.customfields">
         <composite 
         	name="CarrierLotCustomComposite"
         	class="com.glory.mes.wip.custom.CarrierLotCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="ShowCheckBox" description="是否显示复选框" datatype="boolean"></attribute>
         	<attribute name="ShowLotInput" description="是否根据批号查" datatype="boolean"></attribute>
         	<attribute name="NotShowCarrierInput" description="是否不展示载具输入框" datatype="boolean"></attribute>
         	<attribute name="ShowOperatorInput" description="是否需要操作员" datatype="boolean"></attribute>
         	<attribute name="ShowLotDetail" description="批次详情" datatype="boolean"></attribute>
         	<attribute name="ShowComponent" description="显示组件列表" datatype="boolean"></attribute>
         	<attribute name="LotListAutoSize" description="Lot列表是否AutoSize" datatype="boolean"></attribute>
         	<attribute name="TableHeigthHint" description="批次列表高度" datatype="string"></attribute>
         	<attribute name="LotTableName" description="批次动态表名称" datatype="string"></attribute>
         	<attribute name="CompTableName" description="组件动态表名称" datatype="string"></attribute>
         </composite>
         
         <composite 
         	name="CarrierAssignCustomComposite"
         	class="com.glory.mes.wip.custom.CarrierAssignCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="ShowCheckBox" description="是否显示复选框" datatype="boolean"></attribute>
         	<attribute name="ShowLotInput" description="是否根据批号查" datatype="boolean"></attribute>
         	<attribute name="NotShowCarrierInput" description="是否不展示载具输入框" datatype="boolean"></attribute>
         	<attribute name="ShowOperatorInput" description="是否需要操作员" datatype="boolean"></attribute>
         	<attribute name="ShowLotDetail" description="批次详情" datatype="boolean"></attribute>
         	<attribute name="ShowComponent" description="显示组件列表" datatype="boolean"></attribute>
         	<attribute name="LotListAutoSize" description="Lot列表是否AutoSize" datatype="boolean"></attribute>
         	<attribute name="LotTableName" description="批次动态表名称" datatype="string"></attribute>
         	<attribute name="TableHeigthHint" description="批次列表高度" datatype="string"></attribute>
         	<attribute name="CompTableName" description="组件动态表名称" datatype="string"></attribute>
         	<attribute name="ShowTargetCarrier" description="是否显示目标载具" datatype="boolean"></attribute>
         	<attribute name="TargetLotListAutoSize" description="目标载具Lot列表是否AutoSize" datatype="boolean"></attribute>
         	<attribute name="TargetTableHeigthHint" description="目标载具批次列表高度" datatype="string"></attribute>
         	<attribute name="TargetLotTableName" description="目标载具批次动态表名称" datatype="string"></attribute>
         </composite>  
         
         <composite 
         	name="ComponentAssignCustomComposite"
         	class="com.glory.mes.wip.custom.ComponentAssignCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="SourceShowCheckBox" description="Source是否显示复选框" datatype="boolean"></attribute>
         	<attribute name="SourceShowCarrier" description="Source是否根据载具查" datatype="boolean"></attribute>
         	<attribute name="SourceShowLot" description="Source是否根据批号查" datatype="boolean"></attribute>
         	<attribute name="SourceIsAsc" description="Source是否升序排序" datatype="boolean"></attribute>
         	<attribute name="SourceCarrierValidate" description="Source是否检查Carrier可用" datatype="boolean"></attribute>
         	<attribute name="SourceCarrierSize" description="Source最大显示数" datatype="int"></attribute>
         	<attribute name="SourceTableName" description="Source动态表" datatype="Strig"></attribute>
			<attribute name="SourceExtendTableName" description="Source扩展动态表" datatype="String"></attribute>
         	<attribute name="SourceItemAdapter" description="SourceItemAdapter" datatype="String"></attribute>
         	<attribute name="SourceIsMulti" description="Source是否显示多个" datatype="boolean"></attribute>
         	<attribute name="TargetShowCheckBox" description="Target是否显示复选框" datatype="boolean"></attribute>
         	<attribute name="TargetShowCarrier" description="Target是否根据载具查" datatype="boolean"></attribute>
         	<attribute name="TargetShowLot" description="Target是否根据批号查" datatype="boolean"></attribute>
         	<attribute name="TargetIsAsc" description="Target是否升序排序" datatype="boolean"></attribute>
         	<attribute name="TargetCarrierValidate" description="Target是否检查Carrier可用" datatype="boolean"></attribute>
         	<attribute name="TargetCarrierSize" description="Target最大显示数" datatype="int"></attribute>
         	<attribute name="TargetTableName" description="Target动态表" datatype="Strig"></attribute>
         	<attribute name="TargetExtendTableName" description="Target扩展动态表" datatype="String"></attribute>
         	<attribute name="TargetItemAdapter" description="TargetItemAdapter" datatype="String"></attribute>
         	<attribute name="TargetIsMulti" description="Target是否显示多个" datatype="boolean"></attribute>
         	<attribute name="NotShowBtnGo" description="不需要显示go按钮" datatype="boolean"></attribute>
     	 	<attribute name="FormHeight" description="组件表格高度" datatype="boolean"></attribute>
     	 	<attribute name="ShowSortFlag" description="是否展示sort列表" datatype="boolean"></attribute>
         	<attribute name="SortingFlag" description="是否默认Sorting" datatype="boolean"></attribute>
         	<attribute name="NoShowEquipmentFlag" description="是否不显示设备和端口号信息" datatype="boolean"></attribute>
         	<attribute name="ActionType" description="动作类型（Split/Merge）" datatype="string"></attribute>
        	<attribute name="SortingTableName" description="Sorting动态表" datatype="string"></attribute>
         </composite>
         	 
          <composite 
         	name="ComponentCustomComposite"
         	class="com.glory.mes.wip.custom.ComponentCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="ShowCheckBox" description="是否显示复选框" datatype="boolean"></attribute>
         	<attribute name="ShowCarrier" description="是否根据载具查" datatype="boolean"></attribute>
         	<attribute name="ShowLot" description="是否根据批号查" datatype="boolean"></attribute>
         	<attribute name="IsAsc" description="是否升序排序" datatype="boolean"></attribute>
         	<attribute name="CarrierValidate" description="是否检查Carrier可用" datatype="boolean"></attribute>
         	<attribute name="CarrierSize" description="最大显示数" datatype="int"></attribute>
         	<attribute name="TableName" description="动态表" datatype="String"></attribute>
         	<attribute name="ItemAdapter" description="ItemAdapter" datatype="String"></attribute>
         </composite>
         
          <composite 
         	name="LotTecnCustomComposite"   
         	class="com.glory.mes.wip.custom.LotTecnCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="LotTecnTableName" description="批次Tecn动态表" datatype="String"></attribute>
         	<attribute name="LotTecnEditTableName" description="批次Tecn编辑动态表" datatype="String"></attribute>
         	<attribute name="IsPersist" description="是否保存到数据库" datatype="boolean"></attribute>
         </composite>
         
         <composite 
         	name="FlowCustomComposite"
         	class="com.glory.mes.wip.custom.FlowCustomComposite"
         	description="在制流程控件"
         	category="WIP">
         	<attribute name="IsShowInput" description="是否显示输入控件" datatype="boolean"></attribute>
         	<attribute name="FlowType" description="流程类型Lot/Part/Process/Procedure" datatype="string"></attribute>
         	<attribute name="IsSearch" description="是否显示查询控件" datatype="boolean"></attribute>
         	<attribute name="IsMenu" description="是否不显示右键菜单" datatype="boolean"></attribute>
         	<attribute name="ItemHeight" description="控件高度" datatype="int"></attribute>
         	<attribute name="RefTableName" description="动态栏位参考表" datatype="string"></attribute>
         </composite>
         
         <composite 
         	name="NewPartFlowCustomComposite"
         	class="com.glory.mes.wip.custom.NewPartFlowCustomComposite"
         	description="改产品流程控件"
         	category="WIP">
         	<attribute name="IsShowInput" description="是否显示输入控件" datatype="boolean"></attribute>
         	<attribute name="FlowType" description="流程类型Lot/Part/Process/Procedure" datatype="string"></attribute>
         	<attribute name="IsSearch" description="是否显示查询控件" datatype="boolean"></attribute>
         	<attribute name="IsMenu" description="是否不显示右键菜单" datatype="boolean"></attribute>
         	<attribute name="ItemHeight" description="控件高度" datatype="int"></attribute>
         	<attribute name="RefTableName" description="动态栏位参考表" datatype="string"></attribute>
         	<attribute name="AtlProcessRefTable" description="可选流程动态栏位参考表" datatype="string"></attribute>
         </composite>
                  
         <composite 
         	name="ByCarrierEqpTreeCustomComposite"
         	class="com.glory.mes.wip.custom.ByCarrierEqpTreeCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="TreeId" description="TreeId" datatype="string"></attribute>
         </composite>
         
         <composite 
         	name="ByEqpEqpTreeCustomComposite"
         	class="com.glory.mes.wip.custom.ByEqpEqpTreeCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="TreeId" description="TreeId" datatype="string"></attribute>
         </composite>
         
         <composite 
         	name="ByEqpRunningLotsCustomComposite"
         	class="com.glory.mes.wip.custom.ByEqpRunningLotsCustomComposite"
         	description=""
         	category="WIP">
         	
         	<attribute name="TableName" description="动态表名称" datatype="string"></attribute>
         	<attribute name="AutoSize" description="是否AutoSize" datatype="boolean"></attribute>
         </composite>
         
         <composite 
         	name="ByEqpWaittingLotsCustomComposite"
         	class="com.glory.mes.wip.custom.ByEqpWaittingLotsCustomComposite"
         	description=""
         	category="WIP">
         	
         	<attribute name="TableName" description="动态表名称" datatype="string"></attribute>
         	<attribute name="AutoSize" description="是否AutoSize" datatype="boolean"></attribute>
         </composite>
         
         <composite 
         	name="ByEqpConsoleCustomComposite"
         	class="com.glory.mes.wip.custom.ByEqpConsoleCustomComposite"
         	description=""
         	category="WIP">
         </composite>
         
          <composite 
         	name="ByStepTreeCustomComposite"
         	class="com.glory.mes.wip.custom.ByStepTreeCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="TreeId" description="TreeId" datatype="string"></attribute>
         </composite>
         
          <composite 
         	name="ByStepEquipmentLedCustomComposite"
         	class="com.glory.mes.wip.custom.ByStepEquipmentLedCustomComposite"
         	description=""
         	category="WIP">
         </composite>
            
   		<composite 
         	name="SortingCustomComposite"
         	class="com.glory.mes.wip.custom.SortingCustomComposite"
         	description=""
         	category="WIP">
         	<attribute name="ShowSortFlag" description="是否整体显示" datatype="boolean"></attribute>
         	<attribute name="IsSortingFlag" description="是否默认Sorting" datatype="boolean"></attribute>
         	<attribute name="NoShowEquipmentFlag" description="是否不显示设备和端口号信息" datatype="boolean"></attribute>
         	<attribute name="ActionType" description="动作类型（Split/Merge）" datatype="string"></attribute>
         </composite>
         
         <composite 
         	name="LotListComposite"
         	class="com.glory.mes.wip.custom.LotListComposite"
         	description="批次表格控件"
         	category="WIP">
         	<attribute name="TableName" isMandatory="true" description="动态表名称" datatype="string"></attribute>
         	<attribute name="IsAutoSize" description="显示滚动条" datatype="boolean"></attribute>
         	<attribute name="ShowCheckBox" description="显示复选框" datatype="boolean"></attribute>
         	<attribute name="ShowIndex" description="显示索引列" datatype="boolean"></attribute>
         	<attribute name="SummaryColumnNames" description="求和列(使用';'分割)" datatype="string"></attribute>
         	<attribute name="FreezeColumnPosition" description="冻结列位置" datatype="string"></attribute>
         	<attribute name="FreezeRowPosition" description="冻结行位置" datatype="string"></attribute>
         	<!--<attribute name="ShowIconLegend" description="显示图标图例" datatype="boolean"></attribute>-->
         	<attribute name="ShowColorLegend" description="显示颜色图例" datatype="boolean"></attribute>
         	<attribute name="InitMemu" description="重载菜单（默认'Y'）" datatype="boolean"></attribute>
         	
         	<!-- 作为查询控件 -->
         	<attribute name="ShowCondition" description="显示查询条件" datatype="boolean"></attribute>
         	<attribute name="ShowQueryButton" description="显示查询按钮" datatype="boolean"></attribute>
         	<attribute name="IsCustomQuery" description="定制查询方法" datatype="boolean"></attribute>
         	<attribute name="ShowProgressPanel" description="显示查询进度条" datatype="boolean"></attribute>
         	<attribute name="QueryTimeOut" description="查询超时时间(Seconds)" datatype="int"></attribute>
         	<attribute name="ShowCount" description="显示结果数" datatype="boolean"></attribute>
         </composite>
         
         <composite 
         	name="LotFlagDefineComposite"
         	class="com.glory.mes.wip.custom.LotFlagDefineComposite"
         	description=""
         	category="WIP">
         </composite>
         
         <composite 
         	name="UnavailableEqpComposite"
         	class="com.glory.mes.wip.custom.UnavailableEqpComposite"
         	description=""
         	category="WIP">
         	<attribute name="CompositeHeight" description="控件高度" datatype="int" default="250"></attribute>
         </composite>
         
         <composite 
         	name="LotStepAttributeComposite"
         	class="com.glory.mes.wip.custom.LotStepAttributeComposite"
         	description=""
         	category="WIP">
         </composite>
         
         <composite 
         	name="TrackOutScrapComposite"
         	class="com.glory.mes.wip.custom.TrackOutScrapComposite"
         	description=""
         	category="WIP">
         </composite>
         
         <composite 
         	name="TrackOutReworkComposite"
         	class="com.glory.mes.wip.custom.TrackOutReworkComposite"
         	description=""
         	category="WIP">
         </composite>
   </extension>
   <extension
      point="com.glory.framework.base.tableitemadapters">
      <adapter
        name="bomItemAdapter"
        objectclass="com.glory.mes.mm.bom.model.Bom"
        adapterclass="com.glory.mes.mm.bom.BomItemAdapter">
      </adapter>
      <adapter
        name="SortingJobAssignCarrierAdapter"
        objectclass="com.glory.mes.wip.model.ComponentUnit"
        adapterclass="com.glory.mes.wip.lot.run.byeqp.sorting.SortingJobAssignCarrierAdapter">
      </adapter>
      <adapter
        name="ChildLotItemAdapter"
        objectclass="com.glory.mes.wip.model.Lot"
        adapterclass="com.glory.mes.wip.lot.run.bylot.ChildLotItemAdapter">
      </adapter>
      <adapter
        name="KittingTableAdapter"
        objectclass="com.glory.mes.mm.lot.model.EquipmentMaterial"
        adapterclass="com.glory.mes.mm.mlot.kitting.KittingTableAdapter">
      </adapter>  
       <adapter
        name="CdiActionTableItemAdapter"
        objectclass="com.glory.mes.wip.cdi.LotFlowCdiPoint"
        adapterclass="com.glory.mes.wip.track.config.CdiActionTableItemAdapter">
      </adapter>
      <adapter
        name="SelectEqpItemAdapter"
        objectclass="com.glory.mes.ras.eqp.Equipment"
        adapterclass="com.glory.mes.wip.lot.run.trackin.SelectEqpItemAdapter">
      </adapter>
      <adapter
        name="TrackOutLotListItemAdapter"
        objectclass="com.glory.mes.wip.model.Lot"
        adapterclass="com.glory.mes.wip.lot.run.trackout.TrackOutLotListItemAdapter">
      </adapter>
   </extension>
   
</plugin>
