<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ <PERSON>Boss, Home of Professional Open Source.
  ~ Copyright 2016 Red Hat, Inc., and individual contributors
  ~ as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<configuration>
    <authentication-client xmlns="urn:elytron:1.0">
        <authentication-rules>
                    <rule use-configuration="default" />
        </authentication-rules>
        <authentication-configurations>
            <configuration name="default">
                <sasl-mechanism-selector selector="DIGEST-MD5"/>
                <!-- Used for EJB over HTTP, remoting invocations will use transparent auth-->
                <set-user-name name="ejbuser" />
                <credentials>
                    <clear-password password="ejbuser_123" />
                </credentials>
             </configuration>
        </authentication-configurations>
    </authentication-client>
</configuration>
